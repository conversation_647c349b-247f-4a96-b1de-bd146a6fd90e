# Node.js dependencies
node_modules/

# Build output
dist/
build/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS generated files
.DS_Store
Thumbs.db

# IDE/editor settings
.vscode/
.idea/
*.swp
*.swo

# Environment files
.env
.env.*

# Misc
*.log
*.pid
*.seed
*.tgz

# Coverage
coverage/

# Backend Go binaries
/backend/main
/backend/*.exe
/backend/*.out
/backend/*.test

# Whitelist and runtime config
whitelist.json
runtime.json
