var Pt=Object.defineProperty;var Ot=(E,t,_)=>t in E?Pt(E,t,{enumerable:!0,configurable:!0,writable:!0,value:_}):E[t]=_;var Y=(E,t,_)=>Ot(E,typeof t!="symbol"?t+"":t,_);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const C of document.querySelectorAll('link[rel="modulepreload"]'))b(C);new MutationObserver(C=>{for(const x of C)if(x.type==="childList")for(const L of x.addedNodes)L.tagName==="LINK"&&L.rel==="modulepreload"&&b(L)}).observe(document,{childList:!0,subtree:!0});function _(C){const x={};return C.integrity&&(x.integrity=C.integrity),C.referrerPolicy&&(x.referrerPolicy=C.referrerPolicy),C.crossOrigin==="use-credentials"?x.credentials="include":C.crossOrigin==="anonymous"?x.credentials="omit":x.credentials="same-origin",x}function b(C){if(C.ep)return;C.ep=!0;const x=_(C);fetch(C.href,x)}})();var mt={exports:{}};(function(E,t){(function(_,b){E.exports=b()})(self,()=>(()=>{var _={4567:function(L,s,o){var l=this&&this.__decorate||function(r,h,f,v){var y,d=arguments.length,S=d<3?h:v===null?v=Object.getOwnPropertyDescriptor(h,f):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(r,h,f,v);else for(var A=r.length-1;A>=0;A--)(y=r[A])&&(S=(d<3?y(S):d>3?y(h,f,S):y(h,f))||S);return d>3&&S&&Object.defineProperty(h,f,S),S},c=this&&this.__param||function(r,h){return function(f,v){h(f,v,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.AccessibilityManager=void 0;const a=o(9042),u=o(6114),p=o(9924),m=o(844),g=o(5596),e=o(4725),n=o(3656);let i=s.AccessibilityManager=class extends m.Disposable{constructor(r,h){super(),this._terminal=r,this._renderService=h,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=document.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=document.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let f=0;f<this._terminal.rows;f++)this._rowElements[f]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[f]);if(this._topBoundaryFocusListener=f=>this._handleBoundaryFocus(f,0),this._bottomBoundaryFocusListener=f=>this._handleBoundaryFocus(f,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=document.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new p.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(f=>this._handleResize(f.rows))),this.register(this._terminal.onRender(f=>this._refreshRows(f.start,f.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(f=>this._handleChar(f))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(f=>this._handleTab(f))),this.register(this._terminal.onKey(f=>this._handleKey(f.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this._screenDprMonitor=new g.ScreenDprMonitor(window),this.register(this._screenDprMonitor),this._screenDprMonitor.setListener(()=>this._refreshRowsDimensions()),this.register((0,n.addDisposableDomListener)(window,"resize",()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,m.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(r){for(let h=0;h<r;h++)this._handleChar(" ")}_handleChar(r){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==r&&(this._charsToAnnounce+=r):this._charsToAnnounce+=r,r===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=a.tooMuchOutput)),u.isMac&&this._liveRegion.textContent&&this._liveRegion.textContent.length>0&&!this._liveRegion.parentNode&&setTimeout(()=>{this._accessibilityContainer.appendChild(this._liveRegion)},0))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0,u.isMac&&this._liveRegion.remove()}_handleKey(r){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(r)||this._charsToConsume.push(r)}_refreshRows(r,h){this._liveRegionDebouncer.refresh(r,h,this._terminal.rows)}_renderRows(r,h){const f=this._terminal.buffer,v=f.lines.length.toString();for(let y=r;y<=h;y++){const d=f.translateBufferLineToString(f.ydisp+y,!0),S=(f.ydisp+y+1).toString(),A=this._rowElements[y];A&&(d.length===0?A.innerText=" ":A.textContent=d,A.setAttribute("aria-posinset",S),A.setAttribute("aria-setsize",v))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(r,h){const f=r.target,v=this._rowElements[h===0?1:this._rowElements.length-2];if(f.getAttribute("aria-posinset")===(h===0?"1":`${this._terminal.buffer.lines.length}`)||r.relatedTarget!==v)return;let y,d;if(h===0?(y=f,d=this._rowElements.pop(),this._rowContainer.removeChild(d)):(y=this._rowElements.shift(),d=f,this._rowContainer.removeChild(y)),y.removeEventListener("focus",this._topBoundaryFocusListener),d.removeEventListener("focus",this._bottomBoundaryFocusListener),h===0){const S=this._createAccessibilityTreeNode();this._rowElements.unshift(S),this._rowContainer.insertAdjacentElement("afterbegin",S)}else{const S=this._createAccessibilityTreeNode();this._rowElements.push(S),this._rowContainer.appendChild(S)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(h===0?-1:1),this._rowElements[h===0?1:this._rowElements.length-2].focus(),r.preventDefault(),r.stopImmediatePropagation()}_handleResize(r){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let h=this._rowContainer.children.length;h<this._terminal.rows;h++)this._rowElements[h]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[h]);for(;this._rowElements.length>r;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const r=document.createElement("div");return r.setAttribute("role","listitem"),r.tabIndex=-1,this._refreshRowDimensions(r),r}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let r=0;r<this._terminal.rows;r++)this._refreshRowDimensions(this._rowElements[r])}}_refreshRowDimensions(r){r.style.height=`${this._renderService.dimensions.css.cell.height}px`}};s.AccessibilityManager=i=l([c(1,e.IRenderService)],i)},3614:(L,s)=>{function o(u){return u.replace(/\r?\n/g,"\r")}function l(u,p){return p?"\x1B[200~"+u+"\x1B[201~":u}function c(u,p,m,g){u=l(u=o(u),m.decPrivateModes.bracketedPasteMode&&g.rawOptions.ignoreBracketedPasteMode!==!0),m.triggerDataEvent(u,!0),p.value=""}function a(u,p,m){const g=m.getBoundingClientRect(),e=u.clientX-g.left-10,n=u.clientY-g.top-10;p.style.width="20px",p.style.height="20px",p.style.left=`${e}px`,p.style.top=`${n}px`,p.style.zIndex="1000",p.focus()}Object.defineProperty(s,"__esModule",{value:!0}),s.rightClickHandler=s.moveTextAreaUnderMouseCursor=s.paste=s.handlePasteEvent=s.copyHandler=s.bracketTextForPaste=s.prepareTextForTerminal=void 0,s.prepareTextForTerminal=o,s.bracketTextForPaste=l,s.copyHandler=function(u,p){u.clipboardData&&u.clipboardData.setData("text/plain",p.selectionText),u.preventDefault()},s.handlePasteEvent=function(u,p,m,g){u.stopPropagation(),u.clipboardData&&c(u.clipboardData.getData("text/plain"),p,m,g)},s.paste=c,s.moveTextAreaUnderMouseCursor=a,s.rightClickHandler=function(u,p,m,g,e){a(u,p,m),e&&g.rightClickSelect(u),p.value=g.selectionText,p.select()}},7239:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ColorContrastCache=void 0;const l=o(1505);s.ColorContrastCache=class{constructor(){this._color=new l.TwoKeyMap,this._css=new l.TwoKeyMap}setCss(c,a,u){this._css.set(c,a,u)}getCss(c,a){return this._css.get(c,a)}setColor(c,a,u){this._color.set(c,a,u)}getColor(c,a){return this._color.get(c,a)}clear(){this._color.clear(),this._css.clear()}}},3656:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.addDisposableDomListener=void 0,s.addDisposableDomListener=function(o,l,c,a){o.addEventListener(l,c,a);let u=!1;return{dispose:()=>{u||(u=!0,o.removeEventListener(l,c,a))}}}},6465:function(L,s,o){var l=this&&this.__decorate||function(e,n,i,r){var h,f=arguments.length,v=f<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,n,i,r);else for(var y=e.length-1;y>=0;y--)(h=e[y])&&(v=(f<3?h(v):f>3?h(n,i,v):h(n,i))||v);return f>3&&v&&Object.defineProperty(n,i,v),v},c=this&&this.__param||function(e,n){return function(i,r){n(i,r,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.Linkifier2=void 0;const a=o(3656),u=o(8460),p=o(844),m=o(2585);let g=s.Linkifier2=class extends p.Disposable{get currentLink(){return this._currentLink}constructor(e){super(),this._bufferService=e,this._linkProviders=[],this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new u.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new u.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,p.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,p.toDisposable)(()=>{this._lastMouseEvent=void 0})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0}))}registerLinkProvider(e){return this._linkProviders.push(e),{dispose:()=>{const n=this._linkProviders.indexOf(e);n!==-1&&this._linkProviders.splice(n,1)}}}attachToDom(e,n,i){this._element=e,this._mouseService=n,this._renderService=i,this.register((0,a.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,a.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,a.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,a.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(e){if(this._lastMouseEvent=e,!this._element||!this._mouseService)return;const n=this._positionFromMouseEvent(e,this._element,this._mouseService);if(!n)return;this._isMouseOut=!1;const i=e.composedPath();for(let r=0;r<i.length;r++){const h=i[r];if(h.classList.contains("xterm"))break;if(h.classList.contains("xterm-hover"))return}this._lastBufferCell&&n.x===this._lastBufferCell.x&&n.y===this._lastBufferCell.y||(this._handleHover(n),this._lastBufferCell=n)}_handleHover(e){if(this._activeLine!==e.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(e,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,e)||(this._clearCurrentLink(),this._askForLink(e,!0))}_askForLink(e,n){var i,r;this._activeProviderReplies&&n||((i=this._activeProviderReplies)===null||i===void 0||i.forEach(f=>{f==null||f.forEach(v=>{v.link.dispose&&v.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=e.y);let h=!1;for(const[f,v]of this._linkProviders.entries())n?!((r=this._activeProviderReplies)===null||r===void 0)&&r.get(f)&&(h=this._checkLinkProviderResult(f,e,h)):v.provideLinks(e.y,y=>{var d,S;if(this._isMouseOut)return;const A=y==null?void 0:y.map(I=>({link:I}));(d=this._activeProviderReplies)===null||d===void 0||d.set(f,A),h=this._checkLinkProviderResult(f,e,h),((S=this._activeProviderReplies)===null||S===void 0?void 0:S.size)===this._linkProviders.length&&this._removeIntersectingLinks(e.y,this._activeProviderReplies)})}_removeIntersectingLinks(e,n){const i=new Set;for(let r=0;r<n.size;r++){const h=n.get(r);if(h)for(let f=0;f<h.length;f++){const v=h[f],y=v.link.range.start.y<e?0:v.link.range.start.x,d=v.link.range.end.y>e?this._bufferService.cols:v.link.range.end.x;for(let S=y;S<=d;S++){if(i.has(S)){h.splice(f--,1);break}i.add(S)}}}}_checkLinkProviderResult(e,n,i){var r;if(!this._activeProviderReplies)return i;const h=this._activeProviderReplies.get(e);let f=!1;for(let v=0;v<e;v++)this._activeProviderReplies.has(v)&&!this._activeProviderReplies.get(v)||(f=!0);if(!f&&h){const v=h.find(y=>this._linkAtPosition(y.link,n));v&&(i=!0,this._handleNewLink(v))}if(this._activeProviderReplies.size===this._linkProviders.length&&!i)for(let v=0;v<this._activeProviderReplies.size;v++){const y=(r=this._activeProviderReplies.get(v))===null||r===void 0?void 0:r.find(d=>this._linkAtPosition(d.link,n));if(y){i=!0,this._handleNewLink(y);break}}return i}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(e){if(!this._element||!this._mouseService||!this._currentLink)return;const n=this._positionFromMouseEvent(e,this._element,this._mouseService);n&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,n)&&this._currentLink.link.activate(e,this._currentLink.link.text)}_clearCurrentLink(e,n){this._element&&this._currentLink&&this._lastMouseEvent&&(!e||!n||this._currentLink.link.range.start.y>=e&&this._currentLink.link.range.end.y<=n)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,p.disposeArray)(this._linkCacheDisposables))}_handleNewLink(e){if(!this._element||!this._lastMouseEvent||!this._mouseService)return;const n=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);n&&this._linkAtPosition(e.link,n)&&(this._currentLink=e,this._currentLink.state={decorations:{underline:e.link.decorations===void 0||e.link.decorations.underline,pointerCursor:e.link.decorations===void 0||e.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,e.link,this._lastMouseEvent),e.link.decorations={},Object.defineProperties(e.link.decorations,{pointerCursor:{get:()=>{var i,r;return(r=(i=this._currentLink)===null||i===void 0?void 0:i.state)===null||r===void 0?void 0:r.decorations.pointerCursor},set:i=>{var r,h;!((r=this._currentLink)===null||r===void 0)&&r.state&&this._currentLink.state.decorations.pointerCursor!==i&&(this._currentLink.state.decorations.pointerCursor=i,this._currentLink.state.isHovered&&((h=this._element)===null||h===void 0||h.classList.toggle("xterm-cursor-pointer",i)))}},underline:{get:()=>{var i,r;return(r=(i=this._currentLink)===null||i===void 0?void 0:i.state)===null||r===void 0?void 0:r.decorations.underline},set:i=>{var r,h,f;!((r=this._currentLink)===null||r===void 0)&&r.state&&((f=(h=this._currentLink)===null||h===void 0?void 0:h.state)===null||f===void 0?void 0:f.decorations.underline)!==i&&(this._currentLink.state.decorations.underline=i,this._currentLink.state.isHovered&&this._fireUnderlineEvent(e.link,i))}}}),this._renderService&&this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(i=>{if(!this._currentLink)return;const r=i.start===0?0:i.start+1+this._bufferService.buffer.ydisp,h=this._bufferService.buffer.ydisp+1+i.end;if(this._currentLink.link.range.start.y>=r&&this._currentLink.link.range.end.y<=h&&(this._clearCurrentLink(r,h),this._lastMouseEvent&&this._element)){const f=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);f&&this._askForLink(f,!1)}})))}_linkHover(e,n,i){var r;!((r=this._currentLink)===null||r===void 0)&&r.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(n,!0),this._currentLink.state.decorations.pointerCursor&&e.classList.add("xterm-cursor-pointer")),n.hover&&n.hover(i,n.text)}_fireUnderlineEvent(e,n){const i=e.range,r=this._bufferService.buffer.ydisp,h=this._createLinkUnderlineEvent(i.start.x-1,i.start.y-r-1,i.end.x,i.end.y-r-1,void 0);(n?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(h)}_linkLeave(e,n,i){var r;!((r=this._currentLink)===null||r===void 0)&&r.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(n,!1),this._currentLink.state.decorations.pointerCursor&&e.classList.remove("xterm-cursor-pointer")),n.leave&&n.leave(i,n.text)}_linkAtPosition(e,n){const i=e.range.start.y*this._bufferService.cols+e.range.start.x,r=e.range.end.y*this._bufferService.cols+e.range.end.x,h=n.y*this._bufferService.cols+n.x;return i<=h&&h<=r}_positionFromMouseEvent(e,n,i){const r=i.getCoords(e,n,this._bufferService.cols,this._bufferService.rows);if(r)return{x:r[0],y:r[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(e,n,i,r,h){return{x1:e,y1:n,x2:i,y2:r,cols:this._bufferService.cols,fg:h}}};s.Linkifier2=g=l([c(0,m.IBufferService)],g)},9042:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.tooMuchOutput=s.promptLabel=void 0,s.promptLabel="Terminal input",s.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(L,s,o){var l=this&&this.__decorate||function(g,e,n,i){var r,h=arguments.length,f=h<3?e:i===null?i=Object.getOwnPropertyDescriptor(e,n):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")f=Reflect.decorate(g,e,n,i);else for(var v=g.length-1;v>=0;v--)(r=g[v])&&(f=(h<3?r(f):h>3?r(e,n,f):r(e,n))||f);return h>3&&f&&Object.defineProperty(e,n,f),f},c=this&&this.__param||function(g,e){return function(n,i){e(n,i,g)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OscLinkProvider=void 0;const a=o(511),u=o(2585);let p=s.OscLinkProvider=class{constructor(g,e,n){this._bufferService=g,this._optionsService=e,this._oscLinkService=n}provideLinks(g,e){var n;const i=this._bufferService.buffer.lines.get(g-1);if(!i)return void e(void 0);const r=[],h=this._optionsService.rawOptions.linkHandler,f=new a.CellData,v=i.getTrimmedLength();let y=-1,d=-1,S=!1;for(let A=0;A<v;A++)if(d!==-1||i.hasContent(A)){if(i.loadCell(A,f),f.hasExtendedAttrs()&&f.extended.urlId){if(d===-1){d=A,y=f.extended.urlId;continue}S=f.extended.urlId!==y}else d!==-1&&(S=!0);if(S||d!==-1&&A===v-1){const I=(n=this._oscLinkService.getLinkData(y))===null||n===void 0?void 0:n.uri;if(I){const R={start:{x:d+1,y:g},end:{x:A+(S||A!==v-1?0:1),y:g}};let B=!1;if(!(h!=null&&h.allowNonHttpProtocols))try{const O=new URL(I);["http:","https:"].includes(O.protocol)||(B=!0)}catch{B=!0}B||r.push({text:I,range:R,activate:(O,$)=>h?h.activate(O,$,R):m(0,$),hover:(O,$)=>{var W;return(W=h==null?void 0:h.hover)===null||W===void 0?void 0:W.call(h,O,$,R)},leave:(O,$)=>{var W;return(W=h==null?void 0:h.leave)===null||W===void 0?void 0:W.call(h,O,$,R)}})}S=!1,f.hasExtendedAttrs()&&f.extended.urlId?(d=A,y=f.extended.urlId):(d=-1,y=-1)}}e(r)}};function m(g,e){if(confirm(`Do you want to navigate to ${e}?

WARNING: This link could potentially be dangerous`)){const n=window.open();if(n){try{n.opener=null}catch{}n.location.href=e}else console.warn("Opening link blocked as opener could not be cleared")}}s.OscLinkProvider=p=l([c(0,u.IBufferService),c(1,u.IOptionsService),c(2,u.IOscLinkService)],p)},6193:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.RenderDebouncer=void 0,s.RenderDebouncer=class{constructor(o,l){this._parentWindow=o,this._renderCallback=l,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._parentWindow.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(o){return this._refreshCallbacks.push(o),this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(o,l,c){this._rowCount=c,o=o!==void 0?o:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,o):o,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l,this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const o=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(o,l),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const o of this._refreshCallbacks)o(0);this._refreshCallbacks=[]}}},5596:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ScreenDprMonitor=void 0;const l=o(844);class c extends l.Disposable{constructor(u){super(),this._parentWindow=u,this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this.register((0,l.toDisposable)(()=>{this.clearListener()}))}setListener(u){this._listener&&this.clearListener(),this._listener=u,this._outerListener=()=>{this._listener&&(this._listener(this._parentWindow.devicePixelRatio,this._currentDevicePixelRatio),this._updateDpr())},this._updateDpr()}_updateDpr(){var u;this._outerListener&&((u=this._resolutionMediaMatchList)===null||u===void 0||u.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._listener&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._listener=void 0,this._outerListener=void 0)}}s.ScreenDprMonitor=c},3236:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Terminal=void 0;const l=o(3614),c=o(3656),a=o(6465),u=o(9042),p=o(3730),m=o(1680),g=o(3107),e=o(5744),n=o(2950),i=o(1296),r=o(428),h=o(4269),f=o(5114),v=o(8934),y=o(3230),d=o(9312),S=o(4725),A=o(6731),I=o(8055),R=o(8969),B=o(8460),O=o(844),$=o(6114),W=o(8437),z=o(2584),k=o(7399),D=o(5941),T=o(9074),M=o(2585),N=o(5435),U=o(4567),q=typeof window<"u"?window.document:null;class K extends R.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(w={}){super(w),this.browser=$,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new O.MutableDisposable),this._onCursorMove=this.register(new B.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new B.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new B.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new B.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new B.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new B.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new B.EventEmitter),this._onBlur=this.register(new B.EventEmitter),this._onA11yCharEmitter=this.register(new B.EventEmitter),this._onA11yTabEmitter=this.register(new B.EventEmitter),this._onWillOpen=this.register(new B.EventEmitter),this._setup(),this.linkifier2=this.register(this._instantiationService.createInstance(a.Linkifier2)),this.linkifier2.registerLinkProvider(this._instantiationService.createInstance(p.OscLinkProvider)),this._decorationService=this._instantiationService.createInstance(T.DecorationService),this._instantiationService.setService(M.IDecorationService,this._decorationService),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((P,F)=>this.refresh(P,F))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(P=>this._reportWindowsOptions(P))),this.register(this._inputHandler.onColor(P=>this._handleColorEvent(P))),this.register((0,B.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,B.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,B.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,B.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(P=>this._afterResize(P.cols,P.rows))),this.register((0,O.toDisposable)(()=>{var P,F;this._customKeyEventHandler=void 0,(F=(P=this.element)===null||P===void 0?void 0:P.parentNode)===null||F===void 0||F.removeChild(this.element)}))}_handleColorEvent(w){if(this._themeService)for(const P of w){let F,H="";switch(P.index){case 256:F="foreground",H="10";break;case 257:F="background",H="11";break;case 258:F="cursor",H="12";break;default:F="ansi",H="4;"+P.index}switch(P.type){case 0:const V=I.color.toColorRGB(F==="ansi"?this._themeService.colors.ansi[P.index]:this._themeService.colors[F]);this.coreService.triggerDataEvent(`${z.C0.ESC}]${H};${(0,D.toRgbString)(V)}${z.C1_ESCAPED.ST}`);break;case 1:if(F==="ansi")this._themeService.modifyColors(j=>j.ansi[P.index]=I.rgba.toColor(...P.color));else{const j=F;this._themeService.modifyColors(Z=>Z[j]=I.rgba.toColor(...P.color))}break;case 2:this._themeService.restoreColor(P.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(w){w?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(U.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(w){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(z.C0.ESC+"[I"),this.updateCursorStyle(w),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var w;return(w=this.textarea)===null||w===void 0?void 0:w.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(z.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const w=this.buffer.ybase+this.buffer.y,P=this.buffer.lines.get(w);if(!P)return;const F=Math.min(this.buffer.x,this.cols-1),H=this._renderService.dimensions.css.cell.height,V=P.getWidth(F),j=this._renderService.dimensions.css.cell.width*V,Z=this.buffer.y*this._renderService.dimensions.css.cell.height,se=F*this._renderService.dimensions.css.cell.width;this.textarea.style.left=se+"px",this.textarea.style.top=Z+"px",this.textarea.style.width=j+"px",this.textarea.style.height=H+"px",this.textarea.style.lineHeight=H+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,c.addDisposableDomListener)(this.element,"copy",P=>{this.hasSelection()&&(0,l.copyHandler)(P,this._selectionService)}));const w=P=>(0,l.handlePasteEvent)(P,this.textarea,this.coreService,this.optionsService);this.register((0,c.addDisposableDomListener)(this.textarea,"paste",w)),this.register((0,c.addDisposableDomListener)(this.element,"paste",w)),$.isFirefox?this.register((0,c.addDisposableDomListener)(this.element,"mousedown",P=>{P.button===2&&(0,l.rightClickHandler)(P,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,c.addDisposableDomListener)(this.element,"contextmenu",P=>{(0,l.rightClickHandler)(P,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),$.isLinux&&this.register((0,c.addDisposableDomListener)(this.element,"auxclick",P=>{P.button===1&&(0,l.moveTextAreaUnderMouseCursor)(P,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,c.addDisposableDomListener)(this.textarea,"keyup",w=>this._keyUp(w),!0)),this.register((0,c.addDisposableDomListener)(this.textarea,"keydown",w=>this._keyDown(w),!0)),this.register((0,c.addDisposableDomListener)(this.textarea,"keypress",w=>this._keyPress(w),!0)),this.register((0,c.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,c.addDisposableDomListener)(this.textarea,"compositionupdate",w=>this._compositionHelper.compositionupdate(w))),this.register((0,c.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,c.addDisposableDomListener)(this.textarea,"input",w=>this._inputEvent(w),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(w){var P;if(!w)throw new Error("Terminal requires a parent element.");w.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),this._document=w.ownerDocument,this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),w.appendChild(this.element);const F=q.createDocumentFragment();this._viewportElement=q.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),F.appendChild(this._viewportElement),this._viewportScrollArea=q.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=q.createElement("div"),this.screenElement.classList.add("xterm-screen"),this._helperContainer=q.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),F.appendChild(this.screenElement),this.textarea=q.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",u.promptLabel),$.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this._instantiationService.createInstance(f.CoreBrowserService,this.textarea,(P=this._document.defaultView)!==null&&P!==void 0?P:window),this._instantiationService.setService(S.ICoreBrowserService,this._coreBrowserService),this.register((0,c.addDisposableDomListener)(this.textarea,"focus",H=>this._handleTextAreaFocus(H))),this.register((0,c.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(r.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(S.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(A.ThemeService),this._instantiationService.setService(S.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(h.CharacterJoinerService),this._instantiationService.setService(S.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(y.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(S.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(H=>this._onRender.fire(H))),this.onResize(H=>this._renderService.resize(H.cols,H.rows)),this._compositionView=q.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(n.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this.element.appendChild(F);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this._mouseService=this._instantiationService.createInstance(v.MouseService),this._instantiationService.setService(S.IMouseService,this._mouseService),this.viewport=this._instantiationService.createInstance(m.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(H=>this.scrollLines(H.amount,H.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(d.SelectionService,this.element,this.screenElement,this.linkifier2)),this._instantiationService.setService(S.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(H=>this.scrollLines(H.amount,H.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(H=>this._renderService.handleSelectionChanged(H.start,H.end,H.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(H=>{this.textarea.value=H,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(H=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,c.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.linkifier2.attachToDom(this.screenElement,this._mouseService,this._renderService),this.register(this._instantiationService.createInstance(g.BufferDecorationRenderer,this.screenElement)),this.register((0,c.addDisposableDomListener)(this.element,"mousedown",H=>this._selectionService.handleMouseDown(H))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(U.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",H=>this._handleScreenReaderModeOptionChange(H))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",H=>{!this._overviewRulerRenderer&&H&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(i.DomRenderer,this.element,this.screenElement,this._viewportElement,this.linkifier2)}bindMouse(){const w=this,P=this.element;function F(j){const Z=w._mouseService.getMouseReportCoords(j,w.screenElement);if(!Z)return!1;let se,oe;switch(j.overrideType||j.type){case"mousemove":oe=32,j.buttons===void 0?(se=3,j.button!==void 0&&(se=j.button<3?j.button:3)):se=1&j.buttons?0:4&j.buttons?1:2&j.buttons?2:3;break;case"mouseup":oe=0,se=j.button<3?j.button:3;break;case"mousedown":oe=1,se=j.button<3?j.button:3;break;case"wheel":if(w.viewport.getLinesScrolled(j)===0)return!1;oe=j.deltaY<0?0:1,se=4;break;default:return!1}return!(oe===void 0||se===void 0||se>4)&&w.coreMouseService.triggerMouseEvent({col:Z.col,row:Z.row,x:Z.x,y:Z.y,button:se,action:oe,ctrl:j.ctrlKey,alt:j.altKey,shift:j.shiftKey})}const H={mouseup:null,wheel:null,mousedrag:null,mousemove:null},V={mouseup:j=>(F(j),j.buttons||(this._document.removeEventListener("mouseup",H.mouseup),H.mousedrag&&this._document.removeEventListener("mousemove",H.mousedrag)),this.cancel(j)),wheel:j=>(F(j),this.cancel(j,!0)),mousedrag:j=>{j.buttons&&F(j)},mousemove:j=>{j.buttons||F(j)}};this.register(this.coreMouseService.onProtocolChange(j=>{j?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(j)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&j?H.mousemove||(P.addEventListener("mousemove",V.mousemove),H.mousemove=V.mousemove):(P.removeEventListener("mousemove",H.mousemove),H.mousemove=null),16&j?H.wheel||(P.addEventListener("wheel",V.wheel,{passive:!1}),H.wheel=V.wheel):(P.removeEventListener("wheel",H.wheel),H.wheel=null),2&j?H.mouseup||(P.addEventListener("mouseup",V.mouseup),H.mouseup=V.mouseup):(this._document.removeEventListener("mouseup",H.mouseup),P.removeEventListener("mouseup",H.mouseup),H.mouseup=null),4&j?H.mousedrag||(H.mousedrag=V.mousedrag):(this._document.removeEventListener("mousemove",H.mousedrag),H.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,c.addDisposableDomListener)(P,"mousedown",j=>{if(j.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(j))return F(j),H.mouseup&&this._document.addEventListener("mouseup",H.mouseup),H.mousedrag&&this._document.addEventListener("mousemove",H.mousedrag),this.cancel(j)})),this.register((0,c.addDisposableDomListener)(P,"wheel",j=>{if(!H.wheel){if(!this.buffer.hasScrollback){const Z=this.viewport.getLinesScrolled(j);if(Z===0)return;const se=z.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(j.deltaY<0?"A":"B");let oe="";for(let be=0;be<Math.abs(Z);be++)oe+=se;return this.coreService.triggerDataEvent(oe,!0),this.cancel(j,!0)}return this.viewport.handleWheel(j)?this.cancel(j):void 0}},{passive:!1})),this.register((0,c.addDisposableDomListener)(P,"touchstart",j=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(j),this.cancel(j)},{passive:!0})),this.register((0,c.addDisposableDomListener)(P,"touchmove",j=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(j)?void 0:this.cancel(j)},{passive:!1}))}refresh(w,P){var F;(F=this._renderService)===null||F===void 0||F.refreshRows(w,P)}updateCursorStyle(w){var P;!((P=this._selectionService)===null||P===void 0)&&P.shouldColumnSelect(w)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(w,P,F=0){var H;F===1?(super.scrollLines(w,P,F),this.refresh(0,this.rows-1)):(H=this.viewport)===null||H===void 0||H.scrollLines(w)}paste(w){(0,l.paste)(w,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(w){this._customKeyEventHandler=w}registerLinkProvider(w){return this.linkifier2.registerLinkProvider(w)}registerCharacterJoiner(w){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const P=this._characterJoinerService.register(w);return this.refresh(0,this.rows-1),P}deregisterCharacterJoiner(w){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(w)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(w){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+w)}registerDecoration(w){return this._decorationService.registerDecoration(w)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(w,P,F){this._selectionService.setSelection(w,P,F)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var w;(w=this._selectionService)===null||w===void 0||w.clearSelection()}selectAll(){var w;(w=this._selectionService)===null||w===void 0||w.selectAll()}selectLines(w,P){var F;(F=this._selectionService)===null||F===void 0||F.selectLines(w,P)}_keyDown(w){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(w)===!1)return!1;const P=this.browser.isMac&&this.options.macOptionIsMeta&&w.altKey;if(!P&&!this._compositionHelper.keydown(w))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;P||w.key!=="Dead"&&w.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const F=(0,k.evaluateKeyboardEvent)(w,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(w),F.type===3||F.type===2){const H=this.rows-1;return this.scrollLines(F.type===2?-H:H),this.cancel(w,!0)}return F.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,w)||(F.cancel&&this.cancel(w,!0),!F.key||!!(w.key&&!w.ctrlKey&&!w.altKey&&!w.metaKey&&w.key.length===1&&w.key.charCodeAt(0)>=65&&w.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(F.key!==z.C0.ETX&&F.key!==z.C0.CR||(this.textarea.value=""),this._onKey.fire({key:F.key,domEvent:w}),this._showCursor(),this.coreService.triggerDataEvent(F.key,!0),!this.optionsService.rawOptions.screenReaderMode||w.altKey||w.ctrlKey?this.cancel(w,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(w,P){const F=w.isMac&&!this.options.macOptionIsMeta&&P.altKey&&!P.ctrlKey&&!P.metaKey||w.isWindows&&P.altKey&&P.ctrlKey&&!P.metaKey||w.isWindows&&P.getModifierState("AltGraph");return P.type==="keypress"?F:F&&(!P.keyCode||P.keyCode>47)}_keyUp(w){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(w)===!1||(function(P){return P.keyCode===16||P.keyCode===17||P.keyCode===18}(w)||this.focus(),this.updateCursorStyle(w),this._keyPressHandled=!1)}_keyPress(w){let P;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(w)===!1)return!1;if(this.cancel(w),w.charCode)P=w.charCode;else if(w.which===null||w.which===void 0)P=w.keyCode;else{if(w.which===0||w.charCode===0)return!1;P=w.which}return!(!P||(w.altKey||w.ctrlKey||w.metaKey)&&!this._isThirdLevelShift(this.browser,w)||(P=String.fromCharCode(P),this._onKey.fire({key:P,domEvent:w}),this._showCursor(),this.coreService.triggerDataEvent(P,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(w){if(w.data&&w.inputType==="insertText"&&(!w.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const P=w.data;return this.coreService.triggerDataEvent(P,!0),this.cancel(w),!0}return!1}resize(w,P){w!==this.cols||P!==this.rows?super.resize(w,P):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(w,P){var F,H;(F=this._charSizeService)===null||F===void 0||F.measure(),(H=this.viewport)===null||H===void 0||H.syncScrollArea(!0)}clear(){var w;if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let P=1;P<this.rows;P++)this.buffer.lines.push(this.buffer.getBlankLine(W.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),(w=this.viewport)===null||w===void 0||w.reset(),this.refresh(0,this.rows-1)}}reset(){var w,P;this.options.rows=this.rows,this.options.cols=this.cols;const F=this._customKeyEventHandler;this._setup(),super.reset(),(w=this._selectionService)===null||w===void 0||w.reset(),this._decorationService.reset(),(P=this.viewport)===null||P===void 0||P.reset(),this._customKeyEventHandler=F,this.refresh(0,this.rows-1)}clearTextureAtlas(){var w;(w=this._renderService)===null||w===void 0||w.clearTextureAtlas()}_reportFocus(){var w;!((w=this.element)===null||w===void 0)&&w.classList.contains("focus")?this.coreService.triggerDataEvent(z.C0.ESC+"[I"):this.coreService.triggerDataEvent(z.C0.ESC+"[O")}_reportWindowsOptions(w){if(this._renderService)switch(w){case N.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const P=this._renderService.dimensions.css.canvas.width.toFixed(0),F=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${z.C0.ESC}[4;${F};${P}t`);break;case N.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const H=this._renderService.dimensions.css.cell.width.toFixed(0),V=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${z.C0.ESC}[6;${V};${H}t`)}}cancel(w,P){if(this.options.cancelEvents||P)return w.preventDefault(),w.stopPropagation(),!1}}s.Terminal=K},9924:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TimeBasedDebouncer=void 0,s.TimeBasedDebouncer=class{constructor(o,l=1e3){this._renderCallback=o,this._debounceThresholdMS=l,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(o,l,c){this._rowCount=c,o=o!==void 0?o:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,o):o,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l;const a=Date.now();if(a-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=a,this._innerRefresh();else if(!this._additionalRefreshRequested){const u=a-this._lastRefreshMs,p=this._debounceThresholdMS-u;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},p)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const o=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(o,l)}}},1680:function(L,s,o){var l=this&&this.__decorate||function(n,i,r,h){var f,v=arguments.length,y=v<3?i:h===null?h=Object.getOwnPropertyDescriptor(i,r):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(n,i,r,h);else for(var d=n.length-1;d>=0;d--)(f=n[d])&&(y=(v<3?f(y):v>3?f(i,r,y):f(i,r))||y);return v>3&&y&&Object.defineProperty(i,r,y),y},c=this&&this.__param||function(n,i){return function(r,h){i(r,h,n)}};Object.defineProperty(s,"__esModule",{value:!0}),s.Viewport=void 0;const a=o(3656),u=o(4725),p=o(8460),m=o(844),g=o(2585);let e=s.Viewport=class extends m.Disposable{constructor(n,i,r,h,f,v,y,d){super(),this._viewportElement=n,this._scrollArea=i,this._bufferService=r,this._optionsService=h,this._charSizeService=f,this._renderService=v,this._coreBrowserService=y,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new p.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,a.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(S=>this._activeBuffer=S.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(S=>this._renderDimensions=S)),this._handleThemeChange(d.colors),this.register(d.onChangeColors(S=>this._handleThemeChange(S))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(n){this._viewportElement.style.backgroundColor=n.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(n){if(n)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderService.dimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderService.dimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const i=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderService.dimensions.css.canvas.height);this._lastRecordedBufferHeight!==i&&(this._lastRecordedBufferHeight=i,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const n=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==n&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=n),this._refreshAnimationFrame=null}syncScrollArea(n=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(n);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(n)}_handleScroll(n){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const i=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:i,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const n=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(n*(this._smoothScrollState.target-this._smoothScrollState.origin)),n<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(n,i){const r=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(i<0&&this._viewportElement.scrollTop!==0||i>0&&r<this._lastRecordedBufferHeight)||(n.cancelable&&n.preventDefault(),!1)}handleWheel(n){const i=this._getPixelsScrolled(n);return i!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+i:this._smoothScrollState.target+=i,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=i,this._bubbleScroll(n,i))}scrollLines(n){if(n!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const i=n*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+i,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:n,suppressScrollEvent:!1})}_getPixelsScrolled(n){if(n.deltaY===0||n.shiftKey)return 0;let i=this._applyScrollModifier(n.deltaY,n);return n.deltaMode===WheelEvent.DOM_DELTA_LINE?i*=this._currentRowHeight:n.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(i*=this._currentRowHeight*this._bufferService.rows),i}getBufferElements(n,i){var r;let h,f="";const v=[],y=i??this._bufferService.buffer.lines.length,d=this._bufferService.buffer.lines;for(let S=n;S<y;S++){const A=d.get(S);if(!A)continue;const I=(r=d.get(S+1))===null||r===void 0?void 0:r.isWrapped;if(f+=A.translateToString(!I),!I||S===d.length-1){const R=document.createElement("div");R.textContent=f,v.push(R),f.length>0&&(h=R),f=""}}return{bufferElements:v,cursorElement:h}}getLinesScrolled(n){if(n.deltaY===0||n.shiftKey)return 0;let i=this._applyScrollModifier(n.deltaY,n);return n.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(i/=this._currentRowHeight+0,this._wheelPartialScroll+=i,i=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):n.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(i*=this._bufferService.rows),i}_applyScrollModifier(n,i){const r=this._optionsService.rawOptions.fastScrollModifier;return r==="alt"&&i.altKey||r==="ctrl"&&i.ctrlKey||r==="shift"&&i.shiftKey?n*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:n*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(n){this._lastTouchY=n.touches[0].pageY}handleTouchMove(n){const i=this._lastTouchY-n.touches[0].pageY;return this._lastTouchY=n.touches[0].pageY,i!==0&&(this._viewportElement.scrollTop+=i,this._bubbleScroll(n,i))}};s.Viewport=e=l([c(2,g.IBufferService),c(3,g.IOptionsService),c(4,u.ICharSizeService),c(5,u.IRenderService),c(6,u.ICoreBrowserService),c(7,u.IThemeService)],e)},3107:function(L,s,o){var l=this&&this.__decorate||function(e,n,i,r){var h,f=arguments.length,v=f<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,n,i,r);else for(var y=e.length-1;y>=0;y--)(h=e[y])&&(v=(f<3?h(v):f>3?h(n,i,v):h(n,i))||v);return f>3&&v&&Object.defineProperty(n,i,v),v},c=this&&this.__param||function(e,n){return function(i,r){n(i,r,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.BufferDecorationRenderer=void 0;const a=o(3656),u=o(4725),p=o(844),m=o(2585);let g=s.BufferDecorationRenderer=class extends p.Disposable{constructor(e,n,i,r){super(),this._screenElement=e,this._bufferService=n,this._decorationService=i,this._renderService=r,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register((0,a.addDisposableDomListener)(window,"resize",()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(h=>this._removeDecoration(h))),this.register((0,p.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const e of this._decorationService.decorations)this._renderDecoration(e);this._dimensionsChanged=!1}_renderDecoration(e){this._refreshStyle(e),this._dimensionsChanged&&this._refreshXPosition(e)}_createElement(e){var n,i;const r=document.createElement("div");r.classList.add("xterm-decoration"),r.classList.toggle("xterm-decoration-top-layer",((n=e==null?void 0:e.options)===null||n===void 0?void 0:n.layer)==="top"),r.style.width=`${Math.round((e.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,r.style.height=(e.options.height||1)*this._renderService.dimensions.css.cell.height+"px",r.style.top=(e.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",r.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const h=(i=e.options.x)!==null&&i!==void 0?i:0;return h&&h>this._bufferService.cols&&(r.style.display="none"),this._refreshXPosition(e,r),r}_refreshStyle(e){const n=e.marker.line-this._bufferService.buffers.active.ydisp;if(n<0||n>=this._bufferService.rows)e.element&&(e.element.style.display="none",e.onRenderEmitter.fire(e.element));else{let i=this._decorationElements.get(e);i||(i=this._createElement(e),e.element=i,this._decorationElements.set(e,i),this._container.appendChild(i),e.onDispose(()=>{this._decorationElements.delete(e),i.remove()})),i.style.top=n*this._renderService.dimensions.css.cell.height+"px",i.style.display=this._altBufferIsActive?"none":"block",e.onRenderEmitter.fire(i)}}_refreshXPosition(e,n=e.element){var i;if(!n)return;const r=(i=e.options.x)!==null&&i!==void 0?i:0;(e.options.anchor||"left")==="right"?n.style.right=r?r*this._renderService.dimensions.css.cell.width+"px":"":n.style.left=r?r*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(e){var n;(n=this._decorationElements.get(e))===null||n===void 0||n.remove(),this._decorationElements.delete(e),e.dispose()}};s.BufferDecorationRenderer=g=l([c(1,m.IBufferService),c(2,m.IDecorationService),c(3,u.IRenderService)],g)},5871:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ColorZoneStore=void 0,s.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(o){if(o.options.overviewRulerOptions){for(const l of this._zones)if(l.color===o.options.overviewRulerOptions.color&&l.position===o.options.overviewRulerOptions.position){if(this._lineIntersectsZone(l,o.marker.line))return;if(this._lineAdjacentToZone(l,o.marker.line,o.options.overviewRulerOptions.position))return void this._addLineToZone(l,o.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=o.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=o.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=o.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=o.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:o.options.overviewRulerOptions.color,position:o.options.overviewRulerOptions.position,startBufferLine:o.marker.line,endBufferLine:o.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(o){this._linePadding=o}_lineIntersectsZone(o,l){return l>=o.startBufferLine&&l<=o.endBufferLine}_lineAdjacentToZone(o,l,c){return l>=o.startBufferLine-this._linePadding[c||"full"]&&l<=o.endBufferLine+this._linePadding[c||"full"]}_addLineToZone(o,l){o.startBufferLine=Math.min(o.startBufferLine,l),o.endBufferLine=Math.max(o.endBufferLine,l)}}},5744:function(L,s,o){var l=this&&this.__decorate||function(h,f,v,y){var d,S=arguments.length,A=S<3?f:y===null?y=Object.getOwnPropertyDescriptor(f,v):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")A=Reflect.decorate(h,f,v,y);else for(var I=h.length-1;I>=0;I--)(d=h[I])&&(A=(S<3?d(A):S>3?d(f,v,A):d(f,v))||A);return S>3&&A&&Object.defineProperty(f,v,A),A},c=this&&this.__param||function(h,f){return function(v,y){f(v,y,h)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OverviewRulerRenderer=void 0;const a=o(5871),u=o(3656),p=o(4725),m=o(844),g=o(2585),e={full:0,left:0,center:0,right:0},n={full:0,left:0,center:0,right:0},i={full:0,left:0,center:0,right:0};let r=s.OverviewRulerRenderer=class extends m.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(h,f,v,y,d,S,A){var I;super(),this._viewportElement=h,this._screenElement=f,this._bufferService=v,this._decorationService=y,this._renderService=d,this._optionsService=S,this._coreBrowseService=A,this._colorZoneStore=new a.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=document.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(I=this._viewportElement.parentElement)===null||I===void 0||I.insertBefore(this._canvas,this._viewportElement);const R=this._canvas.getContext("2d");if(!R)throw new Error("Ctx cannot be null");this._ctx=R,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,m.toDisposable)(()=>{var B;(B=this._canvas)===null||B===void 0||B.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register((0,u.addDisposableDomListener)(this._coreBrowseService.window,"resize",()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const h=Math.floor(this._canvas.width/3),f=Math.ceil(this._canvas.width/3);n.full=this._canvas.width,n.left=h,n.center=f,n.right=h,this._refreshDrawHeightConstants(),i.full=0,i.left=0,i.center=n.left,i.right=n.left+n.center}_refreshDrawHeightConstants(){e.full=Math.round(2*this._coreBrowseService.dpr);const h=this._canvas.height/this._bufferService.buffer.lines.length,f=Math.round(Math.max(Math.min(h,12),6)*this._coreBrowseService.dpr);e.left=f,e.center=f,e.right=f}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowseService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowseService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const f of this._decorationService.decorations)this._colorZoneStore.addDecoration(f);this._ctx.lineWidth=1;const h=this._colorZoneStore.zones;for(const f of h)f.position!=="full"&&this._renderColorZone(f);for(const f of h)f.position==="full"&&this._renderColorZone(f);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(h){this._ctx.fillStyle=h.color,this._ctx.fillRect(i[h.position||"full"],Math.round((this._canvas.height-1)*(h.startBufferLine/this._bufferService.buffers.active.lines.length)-e[h.position||"full"]/2),n[h.position||"full"],Math.round((this._canvas.height-1)*((h.endBufferLine-h.startBufferLine)/this._bufferService.buffers.active.lines.length)+e[h.position||"full"]))}_queueRefresh(h,f){this._shouldUpdateDimensions=h||this._shouldUpdateDimensions,this._shouldUpdateAnchor=f||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowseService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};s.OverviewRulerRenderer=r=l([c(2,g.IBufferService),c(3,g.IDecorationService),c(4,p.IRenderService),c(5,g.IOptionsService),c(6,p.ICoreBrowserService)],r)},2950:function(L,s,o){var l=this&&this.__decorate||function(g,e,n,i){var r,h=arguments.length,f=h<3?e:i===null?i=Object.getOwnPropertyDescriptor(e,n):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")f=Reflect.decorate(g,e,n,i);else for(var v=g.length-1;v>=0;v--)(r=g[v])&&(f=(h<3?r(f):h>3?r(e,n,f):r(e,n))||f);return h>3&&f&&Object.defineProperty(e,n,f),f},c=this&&this.__param||function(g,e){return function(n,i){e(n,i,g)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CompositionHelper=void 0;const a=o(4725),u=o(2585),p=o(2584);let m=s.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(g,e,n,i,r,h){this._textarea=g,this._compositionView=e,this._bufferService=n,this._optionsService=i,this._coreService=r,this._renderService=h,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(g){this._compositionView.textContent=g.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(g){if(this._isComposing||this._isSendingComposition){if(g.keyCode===229||g.keyCode===16||g.keyCode===17||g.keyCode===18)return!1;this._finalizeComposition(!1)}return g.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(g){if(this._compositionView.classList.remove("active"),this._isComposing=!1,g){const e={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let n;this._isSendingComposition=!1,e.start+=this._dataAlreadySent.length,n=this._isComposing?this._textarea.value.substring(e.start,e.end):this._textarea.value.substring(e.start),n.length>0&&this._coreService.triggerDataEvent(n,!0)}},0)}else{this._isSendingComposition=!1;const e=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(e,!0)}}_handleAnyTextareaChanges(){const g=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const e=this._textarea.value,n=e.replace(g,"");this._dataAlreadySent=n,e.length>g.length?this._coreService.triggerDataEvent(n,!0):e.length<g.length?this._coreService.triggerDataEvent(`${p.C0.DEL}`,!0):e.length===g.length&&e!==g&&this._coreService.triggerDataEvent(e,!0)}},0)}updateCompositionElements(g){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const e=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),n=this._renderService.dimensions.css.cell.height,i=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,r=e*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=r+"px",this._compositionView.style.top=i+"px",this._compositionView.style.height=n+"px",this._compositionView.style.lineHeight=n+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const h=this._compositionView.getBoundingClientRect();this._textarea.style.left=r+"px",this._textarea.style.top=i+"px",this._textarea.style.width=Math.max(h.width,1)+"px",this._textarea.style.height=Math.max(h.height,1)+"px",this._textarea.style.lineHeight=h.height+"px"}g||setTimeout(()=>this.updateCompositionElements(!0),0)}}};s.CompositionHelper=m=l([c(2,u.IBufferService),c(3,u.IOptionsService),c(4,u.ICoreService),c(5,a.IRenderService)],m)},9806:(L,s)=>{function o(l,c,a){const u=a.getBoundingClientRect(),p=l.getComputedStyle(a),m=parseInt(p.getPropertyValue("padding-left")),g=parseInt(p.getPropertyValue("padding-top"));return[c.clientX-u.left-m,c.clientY-u.top-g]}Object.defineProperty(s,"__esModule",{value:!0}),s.getCoords=s.getCoordsRelativeToElement=void 0,s.getCoordsRelativeToElement=o,s.getCoords=function(l,c,a,u,p,m,g,e,n){if(!m)return;const i=o(l,c,a);return i?(i[0]=Math.ceil((i[0]+(n?g/2:0))/g),i[1]=Math.ceil(i[1]/e),i[0]=Math.min(Math.max(i[0],1),u+(n?1:0)),i[1]=Math.min(Math.max(i[1],1),p),i):void 0}},9504:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.moveToCellSequence=void 0;const l=o(2584);function c(e,n,i,r){const h=e-a(e,i),f=n-a(n,i),v=Math.abs(h-f)-function(y,d,S){let A=0;const I=y-a(y,S),R=d-a(d,S);for(let B=0;B<Math.abs(I-R);B++){const O=u(y,d)==="A"?-1:1,$=S.buffer.lines.get(I+O*B);$!=null&&$.isWrapped&&A++}return A}(e,n,i);return g(v,m(u(e,n),r))}function a(e,n){let i=0,r=n.buffer.lines.get(e),h=r==null?void 0:r.isWrapped;for(;h&&e>=0&&e<n.rows;)i++,r=n.buffer.lines.get(--e),h=r==null?void 0:r.isWrapped;return i}function u(e,n){return e>n?"A":"B"}function p(e,n,i,r,h,f){let v=e,y=n,d="";for(;v!==i||y!==r;)v+=h?1:-1,h&&v>f.cols-1?(d+=f.buffer.translateBufferLineToString(y,!1,e,v),v=0,e=0,y++):!h&&v<0&&(d+=f.buffer.translateBufferLineToString(y,!1,0,e+1),v=f.cols-1,e=v,y--);return d+f.buffer.translateBufferLineToString(y,!1,e,v)}function m(e,n){const i=n?"O":"[";return l.C0.ESC+i+e}function g(e,n){e=Math.floor(e);let i="";for(let r=0;r<e;r++)i+=n;return i}s.moveToCellSequence=function(e,n,i,r){const h=i.buffer.x,f=i.buffer.y;if(!i.buffer.hasScrollback)return function(d,S,A,I,R,B){return c(S,I,R,B).length===0?"":g(p(d,S,d,S-a(S,R),!1,R).length,m("D",B))}(h,f,0,n,i,r)+c(f,n,i,r)+function(d,S,A,I,R,B){let O;O=c(S,I,R,B).length>0?I-a(I,R):S;const $=I,W=function(z,k,D,T,M,N){let U;return U=c(D,T,M,N).length>0?T-a(T,M):k,z<D&&U<=T||z>=D&&U<T?"C":"D"}(d,S,A,I,R,B);return g(p(d,O,A,$,W==="C",R).length,m(W,B))}(h,f,e,n,i,r);let v;if(f===n)return v=h>e?"D":"C",g(Math.abs(h-e),m(v,r));v=f>n?"D":"C";const y=Math.abs(f-n);return g(function(d,S){return S.cols-d}(f>n?e:h,i)+(y-1)*i.cols+1+((f>n?h:e)-1),m(v,r))}},1296:function(L,s,o){var l=this&&this.__decorate||function(R,B,O,$){var W,z=arguments.length,k=z<3?B:$===null?$=Object.getOwnPropertyDescriptor(B,O):$;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(R,B,O,$);else for(var D=R.length-1;D>=0;D--)(W=R[D])&&(k=(z<3?W(k):z>3?W(B,O,k):W(B,O))||k);return z>3&&k&&Object.defineProperty(B,O,k),k},c=this&&this.__param||function(R,B){return function(O,$){B(O,$,R)}};Object.defineProperty(s,"__esModule",{value:!0}),s.DomRenderer=void 0;const a=o(3787),u=o(2550),p=o(2223),m=o(6171),g=o(4725),e=o(8055),n=o(8460),i=o(844),r=o(2585),h="xterm-dom-renderer-owner-",f="xterm-rows",v="xterm-fg-",y="xterm-bg-",d="xterm-focus",S="xterm-selection";let A=1,I=s.DomRenderer=class extends i.Disposable{constructor(R,B,O,$,W,z,k,D,T,M){super(),this._element=R,this._screenElement=B,this._viewportElement=O,this._linkifier2=$,this._charSizeService=z,this._optionsService=k,this._bufferService=D,this._coreBrowserService=T,this._themeService=M,this._terminalClass=A++,this._rowElements=[],this.onRequestRedraw=this.register(new n.EventEmitter).event,this._rowContainer=document.createElement("div"),this._rowContainer.classList.add(f),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=document.createElement("div"),this._selectionContainer.classList.add(S),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,m.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors(N=>this._injectCss(N))),this._injectCss(this._themeService.colors),this._rowFactory=W.createInstance(a.DomRendererRowFactory,document),this._element.classList.add(h+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(N=>this._handleLinkHover(N))),this.register(this._linkifier2.onHideLinkUnderline(N=>this._handleLinkLeave(N))),this.register((0,i.toDisposable)(()=>{this._element.classList.remove(h+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new u.WidthCache(document),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const R=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*R,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*R),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/R),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/R),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const O of this._rowElements)O.style.width=`${this.dimensions.css.canvas.width}px`,O.style.height=`${this.dimensions.css.cell.height}px`,O.style.lineHeight=`${this.dimensions.css.cell.height}px`,O.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const B=`${this._terminalSelector} .${f} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=B,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(R){this._themeStyleElement||(this._themeStyleElement=document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let B=`${this._terminalSelector} .${f} { color: ${R.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;B+=`${this._terminalSelector} .${f} .xterm-dim { color: ${e.color.multiplyOpacity(R.foreground,.5).css};}`,B+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`,B+="@keyframes blink_box_shadow_"+this._terminalClass+" { 50% {  border-bottom-style: hidden; }}",B+="@keyframes blink_block_"+this._terminalClass+` { 0% {  background-color: ${R.cursor.css};  color: ${R.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${R.cursor.css}; }}`,B+=`${this._terminalSelector} .${f}.${d} .xterm-cursor.xterm-cursor-blink:not(.xterm-cursor-block) { animation: blink_box_shadow_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${f}.${d} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: blink_block_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-block { background-color: ${R.cursor.css}; color: ${R.cursorAccent.css};}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${R.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${R.cursor.css} inset;}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${R.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,B+=`${this._terminalSelector} .${S} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${S} div { position: absolute; background-color: ${R.selectionBackgroundOpaque.css};}${this._terminalSelector} .${S} div { position: absolute; background-color: ${R.selectionInactiveBackgroundOpaque.css};}`;for(const[O,$]of R.ansi.entries())B+=`${this._terminalSelector} .${v}${O} { color: ${$.css}; }${this._terminalSelector} .${v}${O}.xterm-dim { color: ${e.color.multiplyOpacity($,.5).css}; }${this._terminalSelector} .${y}${O} { background-color: ${$.css}; }`;B+=`${this._terminalSelector} .${v}${p.INVERTED_DEFAULT_COLOR} { color: ${e.color.opaque(R.background).css}; }${this._terminalSelector} .${v}${p.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${e.color.multiplyOpacity(e.color.opaque(R.background),.5).css}; }${this._terminalSelector} .${y}${p.INVERTED_DEFAULT_COLOR} { background-color: ${R.foreground.css}; }`,this._themeStyleElement.textContent=B}_setDefaultSpacing(){const R=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${R}px`,this._rowFactory.defaultSpacing=R}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(R,B){for(let O=this._rowElements.length;O<=B;O++){const $=document.createElement("div");this._rowContainer.appendChild($),this._rowElements.push($)}for(;this._rowElements.length>B;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(R,B){this._refreshRowElements(R,B),this._updateDimensions()}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(d)}handleFocus(){this._rowContainer.classList.add(d),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(R,B,O){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(R,B,O),this.renderRows(0,this._bufferService.rows-1),!R||!B)return;const $=R[1]-this._bufferService.buffer.ydisp,W=B[1]-this._bufferService.buffer.ydisp,z=Math.max($,0),k=Math.min(W,this._bufferService.rows-1);if(z>=this._bufferService.rows||k<0)return;const D=document.createDocumentFragment();if(O){const T=R[0]>B[0];D.appendChild(this._createSelectionElement(z,T?B[0]:R[0],T?R[0]:B[0],k-z+1))}else{const T=$===z?R[0]:0,M=z===W?B[0]:this._bufferService.cols;D.appendChild(this._createSelectionElement(z,T,M));const N=k-z-1;if(D.appendChild(this._createSelectionElement(z+1,0,this._bufferService.cols,N)),z!==k){const U=W===k?B[0]:this._bufferService.cols;D.appendChild(this._createSelectionElement(k,0,U))}}this._selectionContainer.appendChild(D)}_createSelectionElement(R,B,O,$=1){const W=document.createElement("div");return W.style.height=$*this.dimensions.css.cell.height+"px",W.style.top=R*this.dimensions.css.cell.height+"px",W.style.left=B*this.dimensions.css.cell.width+"px",W.style.width=this.dimensions.css.cell.width*(O-B)+"px",W}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const R of this._rowElements)R.replaceChildren()}renderRows(R,B){const O=this._bufferService.buffer,$=O.ybase+O.y,W=Math.min(O.x,this._bufferService.cols-1),z=this._optionsService.rawOptions.cursorBlink,k=this._optionsService.rawOptions.cursorStyle,D=this._optionsService.rawOptions.cursorInactiveStyle;for(let T=R;T<=B;T++){const M=T+O.ydisp,N=this._rowElements[T],U=O.lines.get(M);if(!N||!U)break;N.replaceChildren(...this._rowFactory.createRow(U,M,M===$,k,D,W,z,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${h}${this._terminalClass}`}_handleLinkHover(R){this._setCellUnderline(R.x1,R.x2,R.y1,R.y2,R.cols,!0)}_handleLinkLeave(R){this._setCellUnderline(R.x1,R.x2,R.y1,R.y2,R.cols,!1)}_setCellUnderline(R,B,O,$,W,z){O<0&&(R=0),$<0&&(B=0);const k=this._bufferService.rows-1;O=Math.max(Math.min(O,k),0),$=Math.max(Math.min($,k),0),W=Math.min(W,this._bufferService.cols);const D=this._bufferService.buffer,T=D.ybase+D.y,M=Math.min(D.x,W-1),N=this._optionsService.rawOptions.cursorBlink,U=this._optionsService.rawOptions.cursorStyle,q=this._optionsService.rawOptions.cursorInactiveStyle;for(let K=O;K<=$;++K){const Q=K+D.ydisp,w=this._rowElements[K],P=D.lines.get(Q);if(!w||!P)break;w.replaceChildren(...this._rowFactory.createRow(P,Q,Q===T,U,q,M,N,this.dimensions.css.cell.width,this._widthCache,z?K===O?R:0:-1,z?(K===$?B:W)-1:-1))}}};s.DomRenderer=I=l([c(4,r.IInstantiationService),c(5,g.ICharSizeService),c(6,r.IOptionsService),c(7,r.IBufferService),c(8,g.ICoreBrowserService),c(9,g.IThemeService)],I)},3787:function(L,s,o){var l=this&&this.__decorate||function(v,y,d,S){var A,I=arguments.length,R=I<3?y:S===null?S=Object.getOwnPropertyDescriptor(y,d):S;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")R=Reflect.decorate(v,y,d,S);else for(var B=v.length-1;B>=0;B--)(A=v[B])&&(R=(I<3?A(R):I>3?A(y,d,R):A(y,d))||R);return I>3&&R&&Object.defineProperty(y,d,R),R},c=this&&this.__param||function(v,y){return function(d,S){y(d,S,v)}};Object.defineProperty(s,"__esModule",{value:!0}),s.DomRendererRowFactory=void 0;const a=o(2223),u=o(643),p=o(511),m=o(2585),g=o(8055),e=o(4725),n=o(4269),i=o(6171),r=o(3734);let h=s.DomRendererRowFactory=class{constructor(v,y,d,S,A,I,R){this._document=v,this._characterJoinerService=y,this._optionsService=d,this._coreBrowserService=S,this._coreService=A,this._decorationService=I,this._themeService=R,this._workCell=new p.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(v,y,d){this._selectionStart=v,this._selectionEnd=y,this._columnSelectMode=d}createRow(v,y,d,S,A,I,R,B,O,$,W){const z=[],k=this._characterJoinerService.getJoinedCharacters(y),D=this._themeService.colors;let T,M=v.getNoBgTrimmedLength();d&&M<I+1&&(M=I+1);let N=0,U="",q=0,K=0,Q=0,w=!1,P=0,F=!1,H=0;const V=[],j=$!==-1&&W!==-1;for(let Z=0;Z<M;Z++){v.loadCell(Z,this._workCell);let se=this._workCell.getWidth();if(se===0)continue;let oe=!1,be=Z,X=this._workCell;if(k.length>0&&Z===k[0][0]){oe=!0;const ee=k.shift();X=new n.JoinedCellData(this._workCell,v.translateToString(!0,ee[0],ee[1]),ee[1]-ee[0]),be=ee[1]-1,se=X.getWidth()}const ke=this._isCellInSelection(Z,y),Ke=d&&Z===I,qe=j&&Z>=$&&Z<=W;let Ve=!1;this._decorationService.forEachDecorationAtCell(Z,y,void 0,ee=>{Ve=!0});let Pe=X.getChars()||u.WHITESPACE_CELL_CHAR;if(Pe===" "&&(X.isUnderline()||X.isOverline())&&(Pe=" "),H=se*B-O.get(Pe,X.isBold(),X.isItalic()),T){if(N&&(ke&&F||!ke&&!F&&X.bg===q)&&(ke&&F&&D.selectionForeground||X.fg===K)&&X.extended.ext===Q&&qe===w&&H===P&&!Ke&&!oe&&!Ve){U+=Pe,N++;continue}N&&(T.textContent=U),T=this._document.createElement("span"),N=0,U=""}else T=this._document.createElement("span");if(q=X.bg,K=X.fg,Q=X.extended.ext,w=qe,P=H,F=ke,oe&&I>=Z&&I<=be&&(I=Z),!this._coreService.isCursorHidden&&Ke){if(V.push("xterm-cursor"),this._coreBrowserService.isFocused)R&&V.push("xterm-cursor-blink"),V.push(S==="bar"?"xterm-cursor-bar":S==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(A)switch(A){case"outline":V.push("xterm-cursor-outline");break;case"block":V.push("xterm-cursor-block");break;case"bar":V.push("xterm-cursor-bar");break;case"underline":V.push("xterm-cursor-underline")}}if(X.isBold()&&V.push("xterm-bold"),X.isItalic()&&V.push("xterm-italic"),X.isDim()&&V.push("xterm-dim"),U=X.isInvisible()?u.WHITESPACE_CELL_CHAR:X.getChars()||u.WHITESPACE_CELL_CHAR,X.isUnderline()&&(V.push(`xterm-underline-${X.extended.underlineStyle}`),U===" "&&(U=" "),!X.isUnderlineColorDefault()))if(X.isUnderlineColorRGB())T.style.textDecorationColor=`rgb(${r.AttributeData.toColorRGB(X.getUnderlineColor()).join(",")})`;else{let ee=X.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&X.isBold()&&ee<8&&(ee+=8),T.style.textDecorationColor=D.ansi[ee].css}X.isOverline()&&(V.push("xterm-overline"),U===" "&&(U=" ")),X.isStrikethrough()&&V.push("xterm-strikethrough"),qe&&(T.style.textDecoration="underline");let le=X.getFgColor(),Ee=X.getFgColorMode(),ce=X.getBgColor(),xe=X.getBgColorMode();const Xe=!!X.isInverse();if(Xe){const ee=le;le=ce,ce=ee;const Mt=Ee;Ee=xe,xe=Mt}let ge,Ge,pe,Le=!1;switch(this._decorationService.forEachDecorationAtCell(Z,y,void 0,ee=>{ee.options.layer!=="top"&&Le||(ee.backgroundColorRGB&&(xe=50331648,ce=ee.backgroundColorRGB.rgba>>8&16777215,ge=ee.backgroundColorRGB),ee.foregroundColorRGB&&(Ee=50331648,le=ee.foregroundColorRGB.rgba>>8&16777215,Ge=ee.foregroundColorRGB),Le=ee.options.layer==="top")}),!Le&&ke&&(ge=this._coreBrowserService.isFocused?D.selectionBackgroundOpaque:D.selectionInactiveBackgroundOpaque,ce=ge.rgba>>8&16777215,xe=50331648,Le=!0,D.selectionForeground&&(Ee=50331648,le=D.selectionForeground.rgba>>8&16777215,Ge=D.selectionForeground)),Le&&V.push("xterm-decoration-top"),xe){case 16777216:case 33554432:pe=D.ansi[ce],V.push(`xterm-bg-${ce}`);break;case 50331648:pe=g.rgba.toColor(ce>>16,ce>>8&255,255&ce),this._addStyle(T,`background-color:#${f((ce>>>0).toString(16),"0",6)}`);break;default:Xe?(pe=D.foreground,V.push(`xterm-bg-${a.INVERTED_DEFAULT_COLOR}`)):pe=D.background}switch(ge||X.isDim()&&(ge=g.color.multiplyOpacity(pe,.5)),Ee){case 16777216:case 33554432:X.isBold()&&le<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(le+=8),this._applyMinimumContrast(T,pe,D.ansi[le],X,ge,void 0)||V.push(`xterm-fg-${le}`);break;case 50331648:const ee=g.rgba.toColor(le>>16&255,le>>8&255,255&le);this._applyMinimumContrast(T,pe,ee,X,ge,Ge)||this._addStyle(T,`color:#${f(le.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(T,pe,D.foreground,X,ge,void 0)||Xe&&V.push(`xterm-fg-${a.INVERTED_DEFAULT_COLOR}`)}V.length&&(T.className=V.join(" "),V.length=0),Ke||oe||Ve?T.textContent=U:N++,H!==this.defaultSpacing&&(T.style.letterSpacing=`${H}px`),z.push(T),Z=be}return T&&N&&(T.textContent=U),z}_applyMinimumContrast(v,y,d,S,A,I){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,i.excludeFromContrastRatioDemands)(S.getCode()))return!1;const R=this._getContrastCache(S);let B;if(A||I||(B=R.getColor(y.rgba,d.rgba)),B===void 0){const O=this._optionsService.rawOptions.minimumContrastRatio/(S.isDim()?2:1);B=g.color.ensureContrastRatio(A||y,I||d,O),R.setColor((A||y).rgba,(I||d).rgba,B??null)}return!!B&&(this._addStyle(v,`color:${B.css}`),!0)}_getContrastCache(v){return v.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(v,y){v.setAttribute("style",`${v.getAttribute("style")||""}${y};`)}_isCellInSelection(v,y){const d=this._selectionStart,S=this._selectionEnd;return!(!d||!S)&&(this._columnSelectMode?d[0]<=S[0]?v>=d[0]&&y>=d[1]&&v<S[0]&&y<=S[1]:v<d[0]&&y>=d[1]&&v>=S[0]&&y<=S[1]:y>d[1]&&y<S[1]||d[1]===S[1]&&y===d[1]&&v>=d[0]&&v<S[0]||d[1]<S[1]&&y===S[1]&&v<S[0]||d[1]<S[1]&&y===d[1]&&v>=d[0])}};function f(v,y,d){for(;v.length<d;)v=y+v;return v}s.DomRendererRowFactory=h=l([c(1,e.ICharacterJoinerService),c(2,m.IOptionsService),c(3,e.ICoreBrowserService),c(4,m.ICoreService),c(5,m.IDecorationService),c(6,e.IThemeService)],h)},2550:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WidthCache=void 0,s.WidthCache=class{constructor(o){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=o.createElement("div"),this._container.style.position="absolute",this._container.style.top="-50000px",this._container.style.width="50000px",this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const l=o.createElement("span"),c=o.createElement("span");c.style.fontWeight="bold";const a=o.createElement("span");a.style.fontStyle="italic";const u=o.createElement("span");u.style.fontWeight="bold",u.style.fontStyle="italic",this._measureElements=[l,c,a,u],this._container.appendChild(l),this._container.appendChild(c),this._container.appendChild(a),this._container.appendChild(u),o.body.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(o,l,c,a){o===this._font&&l===this._fontSize&&c===this._weight&&a===this._weightBold||(this._font=o,this._fontSize=l,this._weight=c,this._weightBold=a,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${c}`,this._measureElements[1].style.fontWeight=`${a}`,this._measureElements[2].style.fontWeight=`${c}`,this._measureElements[3].style.fontWeight=`${a}`,this.clear())}get(o,l,c){let a=0;if(!l&&!c&&o.length===1&&(a=o.charCodeAt(0))<256)return this._flat[a]!==-9999?this._flat[a]:this._flat[a]=this._measure(o,0);let u=o;l&&(u+="B"),c&&(u+="I");let p=this._holey.get(u);if(p===void 0){let m=0;l&&(m|=1),c&&(m|=2),p=this._measure(o,m),this._holey.set(u,p)}return p}_measure(o,l){const c=this._measureElements[l];return c.textContent=o.repeat(32),c.offsetWidth/32}}},2223:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TEXT_BASELINE=s.DIM_OPACITY=s.INVERTED_DEFAULT_COLOR=void 0;const l=o(6114);s.INVERTED_DEFAULT_COLOR=257,s.DIM_OPACITY=.5,s.TEXT_BASELINE=l.isFirefox||l.isLegacyEdge?"bottom":"ideographic"},6171:(L,s)=>{function o(l){return 57508<=l&&l<=57558}Object.defineProperty(s,"__esModule",{value:!0}),s.createRenderDimensions=s.excludeFromContrastRatioDemands=s.isRestrictedPowerlineGlyph=s.isPowerlineGlyph=s.throwIfFalsy=void 0,s.throwIfFalsy=function(l){if(!l)throw new Error("value must not be falsy");return l},s.isPowerlineGlyph=o,s.isRestrictedPowerlineGlyph=function(l){return 57520<=l&&l<=57527},s.excludeFromContrastRatioDemands=function(l){return o(l)||function(c){return 9472<=c&&c<=9631}(l)},s.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}}},456:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionModel=void 0,s.SelectionModel=class{constructor(o){this._bufferService=o,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const o=this.selectionStart[0]+this.selectionStartLength;return o>this._bufferService.cols?o%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)-1]:[o%this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)]:[o,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const o=this.selectionStart[0]+this.selectionStartLength;return o>this._bufferService.cols?[o%this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)]:[Math.max(o,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const o=this.selectionStart,l=this.selectionEnd;return!(!o||!l)&&(o[1]>l[1]||o[1]===l[1]&&o[0]>l[0])}handleTrim(o){return this.selectionStart&&(this.selectionStart[1]-=o),this.selectionEnd&&(this.selectionEnd[1]-=o),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(L,s,o){var l=this&&this.__decorate||function(e,n,i,r){var h,f=arguments.length,v=f<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,n,i,r);else for(var y=e.length-1;y>=0;y--)(h=e[y])&&(v=(f<3?h(v):f>3?h(n,i,v):h(n,i))||v);return f>3&&v&&Object.defineProperty(n,i,v),v},c=this&&this.__param||function(e,n){return function(i,r){n(i,r,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharSizeService=void 0;const a=o(2585),u=o(8460),p=o(844);let m=s.CharSizeService=class extends p.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(e,n,i){super(),this._optionsService=i,this.width=0,this.height=0,this._onCharSizeChange=this.register(new u.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event,this._measureStrategy=new g(e,n,this._optionsService),this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const e=this._measureStrategy.measure();e.width===this.width&&e.height===this.height||(this.width=e.width,this.height=e.height,this._onCharSizeChange.fire())}};s.CharSizeService=m=l([c(2,a.IOptionsService)],m);class g{constructor(n,i,r){this._document=n,this._parentElement=i,this._optionsService=r,this._result={width:0,height:0},this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`;const n={height:Number(this._measureElement.offsetHeight),width:Number(this._measureElement.offsetWidth)};return n.width!==0&&n.height!==0&&(this._result.width=n.width/32,this._result.height=Math.ceil(n.height)),this._result}}},4269:function(L,s,o){var l=this&&this.__decorate||function(n,i,r,h){var f,v=arguments.length,y=v<3?i:h===null?h=Object.getOwnPropertyDescriptor(i,r):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(n,i,r,h);else for(var d=n.length-1;d>=0;d--)(f=n[d])&&(y=(v<3?f(y):v>3?f(i,r,y):f(i,r))||y);return v>3&&y&&Object.defineProperty(i,r,y),y},c=this&&this.__param||function(n,i){return function(r,h){i(r,h,n)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharacterJoinerService=s.JoinedCellData=void 0;const a=o(3734),u=o(643),p=o(511),m=o(2585);class g extends a.AttributeData{constructor(i,r,h){super(),this.content=0,this.combinedData="",this.fg=i.fg,this.bg=i.bg,this.combinedData=r,this._width=h}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(i){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.JoinedCellData=g;let e=s.CharacterJoinerService=class vt{constructor(i){this._bufferService=i,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new p.CellData}register(i){const r={id:this._nextCharacterJoinerId++,handler:i};return this._characterJoiners.push(r),r.id}deregister(i){for(let r=0;r<this._characterJoiners.length;r++)if(this._characterJoiners[r].id===i)return this._characterJoiners.splice(r,1),!0;return!1}getJoinedCharacters(i){if(this._characterJoiners.length===0)return[];const r=this._bufferService.buffer.lines.get(i);if(!r||r.length===0)return[];const h=[],f=r.translateToString(!0);let v=0,y=0,d=0,S=r.getFg(0),A=r.getBg(0);for(let I=0;I<r.getTrimmedLength();I++)if(r.loadCell(I,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==S||this._workCell.bg!==A){if(I-v>1){const R=this._getJoinedRanges(f,d,y,r,v);for(let B=0;B<R.length;B++)h.push(R[B])}v=I,d=y,S=this._workCell.fg,A=this._workCell.bg}y+=this._workCell.getChars().length||u.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-v>1){const I=this._getJoinedRanges(f,d,y,r,v);for(let R=0;R<I.length;R++)h.push(I[R])}return h}_getJoinedRanges(i,r,h,f,v){const y=i.substring(r,h);let d=[];try{d=this._characterJoiners[0].handler(y)}catch(S){console.error(S)}for(let S=1;S<this._characterJoiners.length;S++)try{const A=this._characterJoiners[S].handler(y);for(let I=0;I<A.length;I++)vt._mergeRanges(d,A[I])}catch(A){console.error(A)}return this._stringRangesToCellRanges(d,f,v),d}_stringRangesToCellRanges(i,r,h){let f=0,v=!1,y=0,d=i[f];if(d){for(let S=h;S<this._bufferService.cols;S++){const A=r.getWidth(S),I=r.getString(S).length||u.WHITESPACE_CELL_CHAR.length;if(A!==0){if(!v&&d[0]<=y&&(d[0]=S,v=!0),d[1]<=y){if(d[1]=S,d=i[++f],!d)break;d[0]<=y?(d[0]=S,v=!0):v=!1}y+=I}}d&&(d[1]=this._bufferService.cols)}}static _mergeRanges(i,r){let h=!1;for(let f=0;f<i.length;f++){const v=i[f];if(h){if(r[1]<=v[0])return i[f-1][1]=r[1],i;if(r[1]<=v[1])return i[f-1][1]=Math.max(r[1],v[1]),i.splice(f,1),i;i.splice(f,1),f--}else{if(r[1]<=v[0])return i.splice(f,0,r),i;if(r[1]<=v[1])return v[0]=Math.min(r[0],v[0]),i;r[0]<v[1]&&(v[0]=Math.min(r[0],v[0]),h=!0)}}return h?i[i.length-1][1]=r[1]:i.push(r),i}};s.CharacterJoinerService=e=l([c(0,m.IBufferService)],e)},5114:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CoreBrowserService=void 0,s.CoreBrowserService=class{constructor(o,l){this._textarea=o,this.window=l,this._isFocused=!1,this._cachedIsFocused=void 0,this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}},8934:function(L,s,o){var l=this&&this.__decorate||function(m,g,e,n){var i,r=arguments.length,h=r<3?g:n===null?n=Object.getOwnPropertyDescriptor(g,e):n;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")h=Reflect.decorate(m,g,e,n);else for(var f=m.length-1;f>=0;f--)(i=m[f])&&(h=(r<3?i(h):r>3?i(g,e,h):i(g,e))||h);return r>3&&h&&Object.defineProperty(g,e,h),h},c=this&&this.__param||function(m,g){return function(e,n){g(e,n,m)}};Object.defineProperty(s,"__esModule",{value:!0}),s.MouseService=void 0;const a=o(4725),u=o(9806);let p=s.MouseService=class{constructor(m,g){this._renderService=m,this._charSizeService=g}getCoords(m,g,e,n,i){return(0,u.getCoords)(window,m,g,e,n,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,i)}getMouseReportCoords(m,g){const e=(0,u.getCoordsRelativeToElement)(window,m,g);if(this._charSizeService.hasValidSize)return e[0]=Math.min(Math.max(e[0],0),this._renderService.dimensions.css.canvas.width-1),e[1]=Math.min(Math.max(e[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(e[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(e[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(e[0]),y:Math.floor(e[1])}}};s.MouseService=p=l([c(0,a.IRenderService),c(1,a.ICharSizeService)],p)},3230:function(L,s,o){var l=this&&this.__decorate||function(h,f,v,y){var d,S=arguments.length,A=S<3?f:y===null?y=Object.getOwnPropertyDescriptor(f,v):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")A=Reflect.decorate(h,f,v,y);else for(var I=h.length-1;I>=0;I--)(d=h[I])&&(A=(S<3?d(A):S>3?d(f,v,A):d(f,v))||A);return S>3&&A&&Object.defineProperty(f,v,A),A},c=this&&this.__param||function(h,f){return function(v,y){f(v,y,h)}};Object.defineProperty(s,"__esModule",{value:!0}),s.RenderService=void 0;const a=o(3656),u=o(6193),p=o(5596),m=o(4725),g=o(8460),e=o(844),n=o(7226),i=o(2585);let r=s.RenderService=class extends e.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(h,f,v,y,d,S,A,I){if(super(),this._rowCount=h,this._charSizeService=y,this._renderer=this.register(new e.MutableDisposable),this._pausedResizeTask=new n.DebouncedIdleTask,this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new g.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new g.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new g.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new g.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new u.RenderDebouncer(A.window,(R,B)=>this._renderRows(R,B)),this.register(this._renderDebouncer),this._screenDprMonitor=new p.ScreenDprMonitor(A.window),this._screenDprMonitor.setListener(()=>this.handleDevicePixelRatioChange()),this.register(this._screenDprMonitor),this.register(S.onResize(()=>this._fullRefresh())),this.register(S.buffers.onBufferActivate(()=>{var R;return(R=this._renderer.value)===null||R===void 0?void 0:R.clear()})),this.register(v.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(d.onDecorationRegistered(()=>this._fullRefresh())),this.register(d.onDecorationRemoved(()=>this._fullRefresh())),this.register(v.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio"],()=>{this.clear(),this.handleResize(S.cols,S.rows),this._fullRefresh()})),this.register(v.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(S.buffer.y,S.buffer.y,!0))),this.register((0,a.addDisposableDomListener)(A.window,"resize",()=>this.handleDevicePixelRatioChange())),this.register(I.onChangeColors(()=>this._fullRefresh())),"IntersectionObserver"in A.window){const R=new A.window.IntersectionObserver(B=>this._handleIntersectionChange(B[B.length-1]),{threshold:0});R.observe(f),this.register({dispose:()=>R.disconnect()})}}_handleIntersectionChange(h){this._isPaused=h.isIntersecting===void 0?h.intersectionRatio===0:!h.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(h,f,v=!1){this._isPaused?this._needsFullRefresh=!0:(v||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(h,f,this._rowCount))}_renderRows(h,f){this._renderer.value&&(h=Math.min(h,this._rowCount-1),f=Math.min(f,this._rowCount-1),this._renderer.value.renderRows(h,f),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:h,end:f}),this._onRender.fire({start:h,end:f}),this._isNextRenderRedrawOnly=!0)}resize(h,f){this._rowCount=f,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(h){this._renderer.value=h,this._renderer.value.onRequestRedraw(f=>this.refreshRows(f.start,f.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh()}addRefreshCallback(h){return this._renderDebouncer.addRefreshCallback(h)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var h,f;this._renderer.value&&((f=(h=this._renderer.value).clearTextureAtlas)===null||f===void 0||f.call(h),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(h,f){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>this._renderer.value.handleResize(h,f)):this._renderer.value.handleResize(h,f),this._fullRefresh())}handleCharSizeChanged(){var h;(h=this._renderer.value)===null||h===void 0||h.handleCharSizeChanged()}handleBlur(){var h;(h=this._renderer.value)===null||h===void 0||h.handleBlur()}handleFocus(){var h;(h=this._renderer.value)===null||h===void 0||h.handleFocus()}handleSelectionChanged(h,f,v){var y;this._selectionState.start=h,this._selectionState.end=f,this._selectionState.columnSelectMode=v,(y=this._renderer.value)===null||y===void 0||y.handleSelectionChanged(h,f,v)}handleCursorMove(){var h;(h=this._renderer.value)===null||h===void 0||h.handleCursorMove()}clear(){var h;(h=this._renderer.value)===null||h===void 0||h.clear()}};s.RenderService=r=l([c(2,i.IOptionsService),c(3,m.ICharSizeService),c(4,i.IDecorationService),c(5,i.IBufferService),c(6,m.ICoreBrowserService),c(7,m.IThemeService)],r)},9312:function(L,s,o){var l=this&&this.__decorate||function(d,S,A,I){var R,B=arguments.length,O=B<3?S:I===null?I=Object.getOwnPropertyDescriptor(S,A):I;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")O=Reflect.decorate(d,S,A,I);else for(var $=d.length-1;$>=0;$--)(R=d[$])&&(O=(B<3?R(O):B>3?R(S,A,O):R(S,A))||O);return B>3&&O&&Object.defineProperty(S,A,O),O},c=this&&this.__param||function(d,S){return function(A,I){S(A,I,d)}};Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionService=void 0;const a=o(9806),u=o(9504),p=o(456),m=o(4725),g=o(8460),e=o(844),n=o(6114),i=o(4841),r=o(511),h=o(2585),f=" ",v=new RegExp(f,"g");let y=s.SelectionService=class extends e.Disposable{constructor(d,S,A,I,R,B,O,$,W){super(),this._element=d,this._screenElement=S,this._linkifier=A,this._bufferService=I,this._coreService=R,this._mouseService=B,this._optionsService=O,this._renderService=$,this._coreBrowserService=W,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new r.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new g.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new g.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new g.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new g.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=z=>this._handleMouseMove(z),this._mouseUpListener=z=>this._handleMouseUp(z),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(z=>this._handleTrim(z)),this.register(this._bufferService.buffers.onBufferActivate(z=>this._handleBufferActivate(z))),this.enable(),this._model=new p.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,e.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const d=this._model.finalSelectionStart,S=this._model.finalSelectionEnd;return!(!d||!S||d[0]===S[0]&&d[1]===S[1])}get selectionText(){const d=this._model.finalSelectionStart,S=this._model.finalSelectionEnd;if(!d||!S)return"";const A=this._bufferService.buffer,I=[];if(this._activeSelectionMode===3){if(d[0]===S[0])return"";const R=d[0]<S[0]?d[0]:S[0],B=d[0]<S[0]?S[0]:d[0];for(let O=d[1];O<=S[1];O++){const $=A.translateBufferLineToString(O,!0,R,B);I.push($)}}else{const R=d[1]===S[1]?S[0]:void 0;I.push(A.translateBufferLineToString(d[1],!0,d[0],R));for(let B=d[1]+1;B<=S[1]-1;B++){const O=A.lines.get(B),$=A.translateBufferLineToString(B,!0);O!=null&&O.isWrapped?I[I.length-1]+=$:I.push($)}if(d[1]!==S[1]){const B=A.lines.get(S[1]),O=A.translateBufferLineToString(S[1],!0,0,S[0]);B&&B.isWrapped?I[I.length-1]+=O:I.push(O)}}return I.map(R=>R.replace(v," ")).join(n.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(d){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),n.isLinux&&d&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(d){const S=this._getMouseBufferCoords(d),A=this._model.finalSelectionStart,I=this._model.finalSelectionEnd;return!!(A&&I&&S)&&this._areCoordsInSelection(S,A,I)}isCellInSelection(d,S){const A=this._model.finalSelectionStart,I=this._model.finalSelectionEnd;return!(!A||!I)&&this._areCoordsInSelection([d,S],A,I)}_areCoordsInSelection(d,S,A){return d[1]>S[1]&&d[1]<A[1]||S[1]===A[1]&&d[1]===S[1]&&d[0]>=S[0]&&d[0]<A[0]||S[1]<A[1]&&d[1]===A[1]&&d[0]<A[0]||S[1]<A[1]&&d[1]===S[1]&&d[0]>=S[0]}_selectWordAtCursor(d,S){var A,I;const R=(I=(A=this._linkifier.currentLink)===null||A===void 0?void 0:A.link)===null||I===void 0?void 0:I.range;if(R)return this._model.selectionStart=[R.start.x-1,R.start.y-1],this._model.selectionStartLength=(0,i.getRangeLength)(R,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const B=this._getMouseBufferCoords(d);return!!B&&(this._selectWordAt(B,S),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(d,S){this._model.clearSelection(),d=Math.max(d,0),S=Math.min(S,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,d],this._model.selectionEnd=[this._bufferService.cols,S],this.refresh(),this._onSelectionChange.fire()}_handleTrim(d){this._model.handleTrim(d)&&this.refresh()}_getMouseBufferCoords(d){const S=this._mouseService.getCoords(d,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(S)return S[0]--,S[1]--,S[1]+=this._bufferService.buffer.ydisp,S}_getMouseEventScrollAmount(d){let S=(0,a.getCoordsRelativeToElement)(this._coreBrowserService.window,d,this._screenElement)[1];const A=this._renderService.dimensions.css.canvas.height;return S>=0&&S<=A?0:(S>A&&(S-=A),S=Math.min(Math.max(S,-50),50),S/=50,S/Math.abs(S)+Math.round(14*S))}shouldForceSelection(d){return n.isMac?d.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:d.shiftKey}handleMouseDown(d){if(this._mouseDownTimeStamp=d.timeStamp,(d.button!==2||!this.hasSelection)&&d.button===0){if(!this._enabled){if(!this.shouldForceSelection(d))return;d.stopPropagation()}d.preventDefault(),this._dragScrollAmount=0,this._enabled&&d.shiftKey?this._handleIncrementalClick(d):d.detail===1?this._handleSingleClick(d):d.detail===2?this._handleDoubleClick(d):d.detail===3&&this._handleTripleClick(d),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(d){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(d))}_handleSingleClick(d){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(d)?3:0,this._model.selectionStart=this._getMouseBufferCoords(d),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const S=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);S&&S.length!==this._model.selectionStart[0]&&S.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(d){this._selectWordAtCursor(d,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(d){const S=this._getMouseBufferCoords(d);S&&(this._activeSelectionMode=2,this._selectLineAt(S[1]))}shouldColumnSelect(d){return d.altKey&&!(n.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(d){if(d.stopImmediatePropagation(),!this._model.selectionStart)return;const S=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(d),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(d),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const A=this._bufferService.buffer;if(this._model.selectionEnd[1]<A.lines.length){const I=A.lines.get(this._model.selectionEnd[1]);I&&I.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]++}S&&S[0]===this._model.selectionEnd[0]&&S[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const d=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(d.ydisp+this._bufferService.rows,d.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=d.ydisp),this.refresh()}}_handleMouseUp(d){const S=d.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&S<500&&d.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const A=this._mouseService.getCoords(d,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(A&&A[0]!==void 0&&A[1]!==void 0){const I=(0,u.moveToCellSequence)(A[0]-1,A[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(I,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const d=this._model.finalSelectionStart,S=this._model.finalSelectionEnd,A=!(!d||!S||d[0]===S[0]&&d[1]===S[1]);A?d&&S&&(this._oldSelectionStart&&this._oldSelectionEnd&&d[0]===this._oldSelectionStart[0]&&d[1]===this._oldSelectionStart[1]&&S[0]===this._oldSelectionEnd[0]&&S[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(d,S,A)):this._oldHasSelection&&this._fireOnSelectionChange(d,S,A)}_fireOnSelectionChange(d,S,A){this._oldSelectionStart=d,this._oldSelectionEnd=S,this._oldHasSelection=A,this._onSelectionChange.fire()}_handleBufferActivate(d){this.clearSelection(),this._trimListener.dispose(),this._trimListener=d.activeBuffer.lines.onTrim(S=>this._handleTrim(S))}_convertViewportColToCharacterIndex(d,S){let A=S;for(let I=0;S>=I;I++){const R=d.loadCell(I,this._workCell).getChars().length;this._workCell.getWidth()===0?A--:R>1&&S!==I&&(A+=R-1)}return A}setSelection(d,S,A){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[d,S],this._model.selectionStartLength=A,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(d){this._isClickInSelection(d)||(this._selectWordAtCursor(d,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(d,S,A=!0,I=!0){if(d[0]>=this._bufferService.cols)return;const R=this._bufferService.buffer,B=R.lines.get(d[1]);if(!B)return;const O=R.translateBufferLineToString(d[1],!1);let $=this._convertViewportColToCharacterIndex(B,d[0]),W=$;const z=d[0]-$;let k=0,D=0,T=0,M=0;if(O.charAt($)===" "){for(;$>0&&O.charAt($-1)===" ";)$--;for(;W<O.length&&O.charAt(W+1)===" ";)W++}else{let q=d[0],K=d[0];B.getWidth(q)===0&&(k++,q--),B.getWidth(K)===2&&(D++,K++);const Q=B.getString(K).length;for(Q>1&&(M+=Q-1,W+=Q-1);q>0&&$>0&&!this._isCharWordSeparator(B.loadCell(q-1,this._workCell));){B.loadCell(q-1,this._workCell);const w=this._workCell.getChars().length;this._workCell.getWidth()===0?(k++,q--):w>1&&(T+=w-1,$-=w-1),$--,q--}for(;K<B.length&&W+1<O.length&&!this._isCharWordSeparator(B.loadCell(K+1,this._workCell));){B.loadCell(K+1,this._workCell);const w=this._workCell.getChars().length;this._workCell.getWidth()===2?(D++,K++):w>1&&(M+=w-1,W+=w-1),W++,K++}}W++;let N=$+z-k+T,U=Math.min(this._bufferService.cols,W-$+k+D-T-M);if(S||O.slice($,W).trim()!==""){if(A&&N===0&&B.getCodePoint(0)!==32){const q=R.lines.get(d[1]-1);if(q&&B.isWrapped&&q.getCodePoint(this._bufferService.cols-1)!==32){const K=this._getWordAt([this._bufferService.cols-1,d[1]-1],!1,!0,!1);if(K){const Q=this._bufferService.cols-K.start;N-=Q,U+=Q}}}if(I&&N+U===this._bufferService.cols&&B.getCodePoint(this._bufferService.cols-1)!==32){const q=R.lines.get(d[1]+1);if(q!=null&&q.isWrapped&&q.getCodePoint(0)!==32){const K=this._getWordAt([0,d[1]+1],!1,!1,!0);K&&(U+=K.length)}}return{start:N,length:U}}}_selectWordAt(d,S){const A=this._getWordAt(d,S);if(A){for(;A.start<0;)A.start+=this._bufferService.cols,d[1]--;this._model.selectionStart=[A.start,d[1]],this._model.selectionStartLength=A.length}}_selectToWordAt(d){const S=this._getWordAt(d,!0);if(S){let A=d[1];for(;S.start<0;)S.start+=this._bufferService.cols,A--;if(!this._model.areSelectionValuesReversed())for(;S.start+S.length>this._bufferService.cols;)S.length-=this._bufferService.cols,A++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?S.start:S.start+S.length,A]}}_isCharWordSeparator(d){return d.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(d.getChars())>=0}_selectLineAt(d){const S=this._bufferService.buffer.getWrappedRangeForLine(d),A={start:{x:0,y:S.first},end:{x:this._bufferService.cols-1,y:S.last}};this._model.selectionStart=[0,S.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,i.getRangeLength)(A,this._bufferService.cols)}};s.SelectionService=y=l([c(3,h.IBufferService),c(4,h.ICoreService),c(5,m.IMouseService),c(6,h.IOptionsService),c(7,m.IRenderService),c(8,m.ICoreBrowserService)],y)},4725:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.IThemeService=s.ICharacterJoinerService=s.ISelectionService=s.IRenderService=s.IMouseService=s.ICoreBrowserService=s.ICharSizeService=void 0;const l=o(8343);s.ICharSizeService=(0,l.createDecorator)("CharSizeService"),s.ICoreBrowserService=(0,l.createDecorator)("CoreBrowserService"),s.IMouseService=(0,l.createDecorator)("MouseService"),s.IRenderService=(0,l.createDecorator)("RenderService"),s.ISelectionService=(0,l.createDecorator)("SelectionService"),s.ICharacterJoinerService=(0,l.createDecorator)("CharacterJoinerService"),s.IThemeService=(0,l.createDecorator)("ThemeService")},6731:function(L,s,o){var l=this&&this.__decorate||function(y,d,S,A){var I,R=arguments.length,B=R<3?d:A===null?A=Object.getOwnPropertyDescriptor(d,S):A;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")B=Reflect.decorate(y,d,S,A);else for(var O=y.length-1;O>=0;O--)(I=y[O])&&(B=(R<3?I(B):R>3?I(d,S,B):I(d,S))||B);return R>3&&B&&Object.defineProperty(d,S,B),B},c=this&&this.__param||function(y,d){return function(S,A){d(S,A,y)}};Object.defineProperty(s,"__esModule",{value:!0}),s.ThemeService=s.DEFAULT_ANSI_COLORS=void 0;const a=o(7239),u=o(8055),p=o(8460),m=o(844),g=o(2585),e=u.css.toColor("#ffffff"),n=u.css.toColor("#000000"),i=u.css.toColor("#ffffff"),r=u.css.toColor("#000000"),h={css:"rgba(255, 255, 255, 0.3)",rgba:4294967117};s.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const y=[u.css.toColor("#2e3436"),u.css.toColor("#cc0000"),u.css.toColor("#4e9a06"),u.css.toColor("#c4a000"),u.css.toColor("#3465a4"),u.css.toColor("#75507b"),u.css.toColor("#06989a"),u.css.toColor("#d3d7cf"),u.css.toColor("#555753"),u.css.toColor("#ef2929"),u.css.toColor("#8ae234"),u.css.toColor("#fce94f"),u.css.toColor("#729fcf"),u.css.toColor("#ad7fa8"),u.css.toColor("#34e2e2"),u.css.toColor("#eeeeec")],d=[0,95,135,175,215,255];for(let S=0;S<216;S++){const A=d[S/36%6|0],I=d[S/6%6|0],R=d[S%6];y.push({css:u.channels.toCss(A,I,R),rgba:u.channels.toRgba(A,I,R)})}for(let S=0;S<24;S++){const A=8+10*S;y.push({css:u.channels.toCss(A,A,A),rgba:u.channels.toRgba(A,A,A)})}return y})());let f=s.ThemeService=class extends m.Disposable{get colors(){return this._colors}constructor(y){super(),this._optionsService=y,this._contrastCache=new a.ColorContrastCache,this._halfContrastCache=new a.ColorContrastCache,this._onChangeColors=this.register(new p.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:e,background:n,cursor:i,cursorAccent:r,selectionForeground:void 0,selectionBackgroundTransparent:h,selectionBackgroundOpaque:u.color.blend(n,h),selectionInactiveBackgroundTransparent:h,selectionInactiveBackgroundOpaque:u.color.blend(n,h),ansi:s.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(y={}){const d=this._colors;if(d.foreground=v(y.foreground,e),d.background=v(y.background,n),d.cursor=v(y.cursor,i),d.cursorAccent=v(y.cursorAccent,r),d.selectionBackgroundTransparent=v(y.selectionBackground,h),d.selectionBackgroundOpaque=u.color.blend(d.background,d.selectionBackgroundTransparent),d.selectionInactiveBackgroundTransparent=v(y.selectionInactiveBackground,d.selectionBackgroundTransparent),d.selectionInactiveBackgroundOpaque=u.color.blend(d.background,d.selectionInactiveBackgroundTransparent),d.selectionForeground=y.selectionForeground?v(y.selectionForeground,u.NULL_COLOR):void 0,d.selectionForeground===u.NULL_COLOR&&(d.selectionForeground=void 0),u.color.isOpaque(d.selectionBackgroundTransparent)&&(d.selectionBackgroundTransparent=u.color.opacity(d.selectionBackgroundTransparent,.3)),u.color.isOpaque(d.selectionInactiveBackgroundTransparent)&&(d.selectionInactiveBackgroundTransparent=u.color.opacity(d.selectionInactiveBackgroundTransparent,.3)),d.ansi=s.DEFAULT_ANSI_COLORS.slice(),d.ansi[0]=v(y.black,s.DEFAULT_ANSI_COLORS[0]),d.ansi[1]=v(y.red,s.DEFAULT_ANSI_COLORS[1]),d.ansi[2]=v(y.green,s.DEFAULT_ANSI_COLORS[2]),d.ansi[3]=v(y.yellow,s.DEFAULT_ANSI_COLORS[3]),d.ansi[4]=v(y.blue,s.DEFAULT_ANSI_COLORS[4]),d.ansi[5]=v(y.magenta,s.DEFAULT_ANSI_COLORS[5]),d.ansi[6]=v(y.cyan,s.DEFAULT_ANSI_COLORS[6]),d.ansi[7]=v(y.white,s.DEFAULT_ANSI_COLORS[7]),d.ansi[8]=v(y.brightBlack,s.DEFAULT_ANSI_COLORS[8]),d.ansi[9]=v(y.brightRed,s.DEFAULT_ANSI_COLORS[9]),d.ansi[10]=v(y.brightGreen,s.DEFAULT_ANSI_COLORS[10]),d.ansi[11]=v(y.brightYellow,s.DEFAULT_ANSI_COLORS[11]),d.ansi[12]=v(y.brightBlue,s.DEFAULT_ANSI_COLORS[12]),d.ansi[13]=v(y.brightMagenta,s.DEFAULT_ANSI_COLORS[13]),d.ansi[14]=v(y.brightCyan,s.DEFAULT_ANSI_COLORS[14]),d.ansi[15]=v(y.brightWhite,s.DEFAULT_ANSI_COLORS[15]),y.extendedAnsi){const S=Math.min(d.ansi.length-16,y.extendedAnsi.length);for(let A=0;A<S;A++)d.ansi[A+16]=v(y.extendedAnsi[A],s.DEFAULT_ANSI_COLORS[A+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(y){this._restoreColor(y),this._onChangeColors.fire(this.colors)}_restoreColor(y){if(y!==void 0)switch(y){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[y]=this._restoreColors.ansi[y]}else for(let d=0;d<this._restoreColors.ansi.length;++d)this._colors.ansi[d]=this._restoreColors.ansi[d]}modifyColors(y){y(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function v(y,d){if(y!==void 0)try{return u.css.toColor(y)}catch{}return d}s.ThemeService=f=l([c(0,g.IOptionsService)],f)},6349:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CircularList=void 0;const l=o(8460),c=o(844);class a extends c.Disposable{constructor(p){super(),this._maxLength=p,this.onDeleteEmitter=this.register(new l.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new l.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new l.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(p){if(this._maxLength===p)return;const m=new Array(p);for(let g=0;g<Math.min(p,this.length);g++)m[g]=this._array[this._getCyclicIndex(g)];this._array=m,this._maxLength=p,this._startIndex=0}get length(){return this._length}set length(p){if(p>this._length)for(let m=this._length;m<p;m++)this._array[m]=void 0;this._length=p}get(p){return this._array[this._getCyclicIndex(p)]}set(p,m){this._array[this._getCyclicIndex(p)]=m}push(p){this._array[this._getCyclicIndex(this._length)]=p,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(p,m,...g){if(m){for(let e=p;e<this._length-m;e++)this._array[this._getCyclicIndex(e)]=this._array[this._getCyclicIndex(e+m)];this._length-=m,this.onDeleteEmitter.fire({index:p,amount:m})}for(let e=this._length-1;e>=p;e--)this._array[this._getCyclicIndex(e+g.length)]=this._array[this._getCyclicIndex(e)];for(let e=0;e<g.length;e++)this._array[this._getCyclicIndex(p+e)]=g[e];if(g.length&&this.onInsertEmitter.fire({index:p,amount:g.length}),this._length+g.length>this._maxLength){const e=this._length+g.length-this._maxLength;this._startIndex+=e,this._length=this._maxLength,this.onTrimEmitter.fire(e)}else this._length+=g.length}trimStart(p){p>this._length&&(p=this._length),this._startIndex+=p,this._length-=p,this.onTrimEmitter.fire(p)}shiftElements(p,m,g){if(!(m<=0)){if(p<0||p>=this._length)throw new Error("start argument out of range");if(p+g<0)throw new Error("Cannot shift elements in list beyond index 0");if(g>0){for(let n=m-1;n>=0;n--)this.set(p+n+g,this.get(p+n));const e=p+m+g-this._length;if(e>0)for(this._length+=e;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let e=0;e<m;e++)this.set(p+e+g,this.get(p+e))}}_getCyclicIndex(p){return(this._startIndex+p)%this._maxLength}}s.CircularList=a},1439:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.clone=void 0,s.clone=function o(l,c=5){if(typeof l!="object")return l;const a=Array.isArray(l)?[]:{};for(const u in l)a[u]=c<=1?l[u]:l[u]&&o(l[u],c-1);return a}},8055:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.contrastRatio=s.toPaddedHex=s.rgba=s.rgb=s.css=s.color=s.channels=s.NULL_COLOR=void 0;const l=o(6114);let c=0,a=0,u=0,p=0;var m,g,e,n,i;function r(f){const v=f.toString(16);return v.length<2?"0"+v:v}function h(f,v){return f<v?(v+.05)/(f+.05):(f+.05)/(v+.05)}s.NULL_COLOR={css:"#00000000",rgba:0},function(f){f.toCss=function(v,y,d,S){return S!==void 0?`#${r(v)}${r(y)}${r(d)}${r(S)}`:`#${r(v)}${r(y)}${r(d)}`},f.toRgba=function(v,y,d,S=255){return(v<<24|y<<16|d<<8|S)>>>0}}(m||(s.channels=m={})),function(f){function v(y,d){return p=Math.round(255*d),[c,a,u]=i.toChannels(y.rgba),{css:m.toCss(c,a,u,p),rgba:m.toRgba(c,a,u,p)}}f.blend=function(y,d){if(p=(255&d.rgba)/255,p===1)return{css:d.css,rgba:d.rgba};const S=d.rgba>>24&255,A=d.rgba>>16&255,I=d.rgba>>8&255,R=y.rgba>>24&255,B=y.rgba>>16&255,O=y.rgba>>8&255;return c=R+Math.round((S-R)*p),a=B+Math.round((A-B)*p),u=O+Math.round((I-O)*p),{css:m.toCss(c,a,u),rgba:m.toRgba(c,a,u)}},f.isOpaque=function(y){return(255&y.rgba)==255},f.ensureContrastRatio=function(y,d,S){const A=i.ensureContrastRatio(y.rgba,d.rgba,S);if(A)return i.toColor(A>>24&255,A>>16&255,A>>8&255)},f.opaque=function(y){const d=(255|y.rgba)>>>0;return[c,a,u]=i.toChannels(d),{css:m.toCss(c,a,u),rgba:d}},f.opacity=v,f.multiplyOpacity=function(y,d){return p=255&y.rgba,v(y,p*d/255)},f.toColorRGB=function(y){return[y.rgba>>24&255,y.rgba>>16&255,y.rgba>>8&255]}}(g||(s.color=g={})),function(f){let v,y;if(!l.isNode){const d=document.createElement("canvas");d.width=1,d.height=1;const S=d.getContext("2d",{willReadFrequently:!0});S&&(v=S,v.globalCompositeOperation="copy",y=v.createLinearGradient(0,0,1,1))}f.toColor=function(d){if(d.match(/#[\da-f]{3,8}/i))switch(d.length){case 4:return c=parseInt(d.slice(1,2).repeat(2),16),a=parseInt(d.slice(2,3).repeat(2),16),u=parseInt(d.slice(3,4).repeat(2),16),i.toColor(c,a,u);case 5:return c=parseInt(d.slice(1,2).repeat(2),16),a=parseInt(d.slice(2,3).repeat(2),16),u=parseInt(d.slice(3,4).repeat(2),16),p=parseInt(d.slice(4,5).repeat(2),16),i.toColor(c,a,u,p);case 7:return{css:d,rgba:(parseInt(d.slice(1),16)<<8|255)>>>0};case 9:return{css:d,rgba:parseInt(d.slice(1),16)>>>0}}const S=d.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(S)return c=parseInt(S[1]),a=parseInt(S[2]),u=parseInt(S[3]),p=Math.round(255*(S[5]===void 0?1:parseFloat(S[5]))),i.toColor(c,a,u,p);if(!v||!y)throw new Error("css.toColor: Unsupported css format");if(v.fillStyle=y,v.fillStyle=d,typeof v.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(v.fillRect(0,0,1,1),[c,a,u,p]=v.getImageData(0,0,1,1).data,p!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:m.toRgba(c,a,u,p),css:d}}}(e||(s.css=e={})),function(f){function v(y,d,S){const A=y/255,I=d/255,R=S/255;return .2126*(A<=.03928?A/12.92:Math.pow((A+.055)/1.055,2.4))+.7152*(I<=.03928?I/12.92:Math.pow((I+.055)/1.055,2.4))+.0722*(R<=.03928?R/12.92:Math.pow((R+.055)/1.055,2.4))}f.relativeLuminance=function(y){return v(y>>16&255,y>>8&255,255&y)},f.relativeLuminance2=v}(n||(s.rgb=n={})),function(f){function v(d,S,A){const I=d>>24&255,R=d>>16&255,B=d>>8&255;let O=S>>24&255,$=S>>16&255,W=S>>8&255,z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));for(;z<A&&(O>0||$>0||W>0);)O-=Math.max(0,Math.ceil(.1*O)),$-=Math.max(0,Math.ceil(.1*$)),W-=Math.max(0,Math.ceil(.1*W)),z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));return(O<<24|$<<16|W<<8|255)>>>0}function y(d,S,A){const I=d>>24&255,R=d>>16&255,B=d>>8&255;let O=S>>24&255,$=S>>16&255,W=S>>8&255,z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));for(;z<A&&(O<255||$<255||W<255);)O=Math.min(255,O+Math.ceil(.1*(255-O))),$=Math.min(255,$+Math.ceil(.1*(255-$))),W=Math.min(255,W+Math.ceil(.1*(255-W))),z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));return(O<<24|$<<16|W<<8|255)>>>0}f.ensureContrastRatio=function(d,S,A){const I=n.relativeLuminance(d>>8),R=n.relativeLuminance(S>>8);if(h(I,R)<A){if(R<I){const $=v(d,S,A),W=h(I,n.relativeLuminance($>>8));if(W<A){const z=y(d,S,A);return W>h(I,n.relativeLuminance(z>>8))?$:z}return $}const B=y(d,S,A),O=h(I,n.relativeLuminance(B>>8));if(O<A){const $=v(d,S,A);return O>h(I,n.relativeLuminance($>>8))?B:$}return B}},f.reduceLuminance=v,f.increaseLuminance=y,f.toChannels=function(d){return[d>>24&255,d>>16&255,d>>8&255,255&d]},f.toColor=function(d,S,A,I){return{css:m.toCss(d,S,A,I),rgba:m.toRgba(d,S,A,I)}}}(i||(s.rgba=i={})),s.toPaddedHex=r,s.contrastRatio=h},8969:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CoreTerminal=void 0;const l=o(844),c=o(2585),a=o(4348),u=o(7866),p=o(744),m=o(7302),g=o(6975),e=o(8460),n=o(1753),i=o(1480),r=o(7994),h=o(9282),f=o(5435),v=o(5981),y=o(2660);let d=!1;class S extends l.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new e.EventEmitter),this._onScroll.event(I=>{var R;(R=this._onScrollApi)===null||R===void 0||R.fire(I.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(I){for(const R in I)this.optionsService.options[R]=I[R]}constructor(I){super(),this._windowsWrappingHeuristics=this.register(new l.MutableDisposable),this._onBinary=this.register(new e.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new e.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new e.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new e.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new e.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new e.EventEmitter),this._instantiationService=new a.InstantiationService,this.optionsService=this.register(new m.OptionsService(I)),this._instantiationService.setService(c.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(p.BufferService)),this._instantiationService.setService(c.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(u.LogService)),this._instantiationService.setService(c.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(g.CoreService)),this._instantiationService.setService(c.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(n.CoreMouseService)),this._instantiationService.setService(c.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(i.UnicodeService)),this._instantiationService.setService(c.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(r.CharsetService),this._instantiationService.setService(c.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(y.OscLinkService),this._instantiationService.setService(c.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new f.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,e.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,e.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,e.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,e.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(R=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(R=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new v.WriteBuffer((R,B)=>this._inputHandler.parse(R,B))),this.register((0,e.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(I,R){this._writeBuffer.write(I,R)}writeSync(I,R){this._logService.logLevel<=c.LogLevelEnum.WARN&&!d&&(this._logService.warn("writeSync is unreliable and will be removed soon."),d=!0),this._writeBuffer.writeSync(I,R)}resize(I,R){isNaN(I)||isNaN(R)||(I=Math.max(I,p.MINIMUM_COLS),R=Math.max(R,p.MINIMUM_ROWS),this._bufferService.resize(I,R))}scroll(I,R=!1){this._bufferService.scroll(I,R)}scrollLines(I,R,B){this._bufferService.scrollLines(I,R,B)}scrollPages(I){this.scrollLines(I*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(I){const R=I-this._bufferService.buffer.ydisp;R!==0&&this.scrollLines(R)}registerEscHandler(I,R){return this._inputHandler.registerEscHandler(I,R)}registerDcsHandler(I,R){return this._inputHandler.registerDcsHandler(I,R)}registerCsiHandler(I,R){return this._inputHandler.registerCsiHandler(I,R)}registerOscHandler(I,R){return this._inputHandler.registerOscHandler(I,R)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let I=!1;const R=this.optionsService.rawOptions.windowsPty;R&&R.buildNumber!==void 0&&R.buildNumber!==void 0?I=R.backend==="conpty"&&R.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(I=!0),I?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const I=[];I.push(this.onLineFeed(h.updateWindowsModeWrappedState.bind(null,this._bufferService))),I.push(this.registerCsiHandler({final:"H"},()=>((0,h.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,l.toDisposable)(()=>{for(const R of I)R.dispose()})}}}s.CoreTerminal=S},8460:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.forwardEvent=s.EventEmitter=void 0,s.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=o=>(this._listeners.push(o),{dispose:()=>{if(!this._disposed){for(let l=0;l<this._listeners.length;l++)if(this._listeners[l]===o)return void this._listeners.splice(l,1)}}})),this._event}fire(o,l){const c=[];for(let a=0;a<this._listeners.length;a++)c.push(this._listeners[a]);for(let a=0;a<c.length;a++)c[a].call(void 0,o,l)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},s.forwardEvent=function(o,l){return o(c=>l.fire(c))}},5435:function(L,s,o){var l=this&&this.__decorate||function(z,k,D,T){var M,N=arguments.length,U=N<3?k:T===null?T=Object.getOwnPropertyDescriptor(k,D):T;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")U=Reflect.decorate(z,k,D,T);else for(var q=z.length-1;q>=0;q--)(M=z[q])&&(U=(N<3?M(U):N>3?M(k,D,U):M(k,D))||U);return N>3&&U&&Object.defineProperty(k,D,U),U},c=this&&this.__param||function(z,k){return function(D,T){k(D,T,z)}};Object.defineProperty(s,"__esModule",{value:!0}),s.InputHandler=s.WindowsOptionsReportType=void 0;const a=o(2584),u=o(7116),p=o(2015),m=o(844),g=o(482),e=o(8437),n=o(8460),i=o(643),r=o(511),h=o(3734),f=o(2585),v=o(6242),y=o(6351),d=o(5941),S={"(":0,")":1,"*":2,"+":3,"-":1,".":2},A=131072;function I(z,k){if(z>24)return k.setWinLines||!1;switch(z){case 1:return!!k.restoreWin;case 2:return!!k.minimizeWin;case 3:return!!k.setWinPosition;case 4:return!!k.setWinSizePixels;case 5:return!!k.raiseWin;case 6:return!!k.lowerWin;case 7:return!!k.refreshWin;case 8:return!!k.setWinSizeChars;case 9:return!!k.maximizeWin;case 10:return!!k.fullscreenWin;case 11:return!!k.getWinState;case 13:return!!k.getWinPosition;case 14:return!!k.getWinSizePixels;case 15:return!!k.getScreenSizePixels;case 16:return!!k.getCellSizePixels;case 18:return!!k.getWinSizeChars;case 19:return!!k.getScreenSizeChars;case 20:return!!k.getIconTitle;case 21:return!!k.getWinTitle;case 22:return!!k.pushTitle;case 23:return!!k.popTitle;case 24:return!!k.setWinLines}return!1}var R;(function(z){z[z.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",z[z.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(R||(s.WindowsOptionsReportType=R={}));let B=0;class O extends m.Disposable{getAttrData(){return this._curAttrData}constructor(k,D,T,M,N,U,q,K,Q=new p.EscapeSequenceParser){super(),this._bufferService=k,this._charsetService=D,this._coreService=T,this._logService=M,this._optionsService=N,this._oscLinkService=U,this._coreMouseService=q,this._unicodeService=K,this._parser=Q,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new g.StringToUtf32,this._utf8Decoder=new g.Utf8ToUtf32,this._workCell=new r.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new n.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new n.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new n.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new n.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new n.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new n.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new n.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new n.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new n.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new n.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new n.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new n.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new n.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new $(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(w=>this._activeBuffer=w.activeBuffer)),this._parser.setCsiHandlerFallback((w,P)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(w),params:P.toArray()})}),this._parser.setEscHandlerFallback(w=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(w)})}),this._parser.setExecuteHandlerFallback(w=>{this._logService.debug("Unknown EXECUTE code: ",{code:w})}),this._parser.setOscHandlerFallback((w,P,F)=>{this._logService.debug("Unknown OSC code: ",{identifier:w,action:P,data:F})}),this._parser.setDcsHandlerFallback((w,P,F)=>{P==="HOOK"&&(F=F.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(w),action:P,payload:F})}),this._parser.setPrintHandler((w,P,F)=>this.print(w,P,F)),this._parser.registerCsiHandler({final:"@"},w=>this.insertChars(w)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},w=>this.scrollLeft(w)),this._parser.registerCsiHandler({final:"A"},w=>this.cursorUp(w)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},w=>this.scrollRight(w)),this._parser.registerCsiHandler({final:"B"},w=>this.cursorDown(w)),this._parser.registerCsiHandler({final:"C"},w=>this.cursorForward(w)),this._parser.registerCsiHandler({final:"D"},w=>this.cursorBackward(w)),this._parser.registerCsiHandler({final:"E"},w=>this.cursorNextLine(w)),this._parser.registerCsiHandler({final:"F"},w=>this.cursorPrecedingLine(w)),this._parser.registerCsiHandler({final:"G"},w=>this.cursorCharAbsolute(w)),this._parser.registerCsiHandler({final:"H"},w=>this.cursorPosition(w)),this._parser.registerCsiHandler({final:"I"},w=>this.cursorForwardTab(w)),this._parser.registerCsiHandler({final:"J"},w=>this.eraseInDisplay(w,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},w=>this.eraseInDisplay(w,!0)),this._parser.registerCsiHandler({final:"K"},w=>this.eraseInLine(w,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},w=>this.eraseInLine(w,!0)),this._parser.registerCsiHandler({final:"L"},w=>this.insertLines(w)),this._parser.registerCsiHandler({final:"M"},w=>this.deleteLines(w)),this._parser.registerCsiHandler({final:"P"},w=>this.deleteChars(w)),this._parser.registerCsiHandler({final:"S"},w=>this.scrollUp(w)),this._parser.registerCsiHandler({final:"T"},w=>this.scrollDown(w)),this._parser.registerCsiHandler({final:"X"},w=>this.eraseChars(w)),this._parser.registerCsiHandler({final:"Z"},w=>this.cursorBackwardTab(w)),this._parser.registerCsiHandler({final:"`"},w=>this.charPosAbsolute(w)),this._parser.registerCsiHandler({final:"a"},w=>this.hPositionRelative(w)),this._parser.registerCsiHandler({final:"b"},w=>this.repeatPrecedingCharacter(w)),this._parser.registerCsiHandler({final:"c"},w=>this.sendDeviceAttributesPrimary(w)),this._parser.registerCsiHandler({prefix:">",final:"c"},w=>this.sendDeviceAttributesSecondary(w)),this._parser.registerCsiHandler({final:"d"},w=>this.linePosAbsolute(w)),this._parser.registerCsiHandler({final:"e"},w=>this.vPositionRelative(w)),this._parser.registerCsiHandler({final:"f"},w=>this.hVPosition(w)),this._parser.registerCsiHandler({final:"g"},w=>this.tabClear(w)),this._parser.registerCsiHandler({final:"h"},w=>this.setMode(w)),this._parser.registerCsiHandler({prefix:"?",final:"h"},w=>this.setModePrivate(w)),this._parser.registerCsiHandler({final:"l"},w=>this.resetMode(w)),this._parser.registerCsiHandler({prefix:"?",final:"l"},w=>this.resetModePrivate(w)),this._parser.registerCsiHandler({final:"m"},w=>this.charAttributes(w)),this._parser.registerCsiHandler({final:"n"},w=>this.deviceStatus(w)),this._parser.registerCsiHandler({prefix:"?",final:"n"},w=>this.deviceStatusPrivate(w)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},w=>this.softReset(w)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},w=>this.setCursorStyle(w)),this._parser.registerCsiHandler({final:"r"},w=>this.setScrollRegion(w)),this._parser.registerCsiHandler({final:"s"},w=>this.saveCursor(w)),this._parser.registerCsiHandler({final:"t"},w=>this.windowOptions(w)),this._parser.registerCsiHandler({final:"u"},w=>this.restoreCursor(w)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},w=>this.insertColumns(w)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},w=>this.deleteColumns(w)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},w=>this.selectProtected(w)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},w=>this.requestMode(w,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},w=>this.requestMode(w,!1)),this._parser.setExecuteHandler(a.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(a.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(a.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(a.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(a.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(a.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(a.C1.IND,()=>this.index()),this._parser.setExecuteHandler(a.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(a.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new v.OscHandler(w=>(this.setTitle(w),this.setIconName(w),!0))),this._parser.registerOscHandler(1,new v.OscHandler(w=>this.setIconName(w))),this._parser.registerOscHandler(2,new v.OscHandler(w=>this.setTitle(w))),this._parser.registerOscHandler(4,new v.OscHandler(w=>this.setOrReportIndexedColor(w))),this._parser.registerOscHandler(8,new v.OscHandler(w=>this.setHyperlink(w))),this._parser.registerOscHandler(10,new v.OscHandler(w=>this.setOrReportFgColor(w))),this._parser.registerOscHandler(11,new v.OscHandler(w=>this.setOrReportBgColor(w))),this._parser.registerOscHandler(12,new v.OscHandler(w=>this.setOrReportCursorColor(w))),this._parser.registerOscHandler(104,new v.OscHandler(w=>this.restoreIndexedColor(w))),this._parser.registerOscHandler(110,new v.OscHandler(w=>this.restoreFgColor(w))),this._parser.registerOscHandler(111,new v.OscHandler(w=>this.restoreBgColor(w))),this._parser.registerOscHandler(112,new v.OscHandler(w=>this.restoreCursorColor(w))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const w in u.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:w},()=>this.selectCharset("("+w)),this._parser.registerEscHandler({intermediates:")",final:w},()=>this.selectCharset(")"+w)),this._parser.registerEscHandler({intermediates:"*",final:w},()=>this.selectCharset("*"+w)),this._parser.registerEscHandler({intermediates:"+",final:w},()=>this.selectCharset("+"+w)),this._parser.registerEscHandler({intermediates:"-",final:w},()=>this.selectCharset("-"+w)),this._parser.registerEscHandler({intermediates:".",final:w},()=>this.selectCharset("."+w)),this._parser.registerEscHandler({intermediates:"/",final:w},()=>this.selectCharset("/"+w));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(w=>(this._logService.error("Parsing error: ",w),w)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new y.DcsHandler((w,P)=>this.requestStatusString(w,P)))}_preserveStack(k,D,T,M){this._parseStack.paused=!0,this._parseStack.cursorStartX=k,this._parseStack.cursorStartY=D,this._parseStack.decodedLength=T,this._parseStack.position=M}_logSlowResolvingAsync(k){this._logService.logLevel<=f.LogLevelEnum.WARN&&Promise.race([k,new Promise((D,T)=>setTimeout(()=>T("#SLOW_TIMEOUT"),5e3))]).catch(D=>{if(D!=="#SLOW_TIMEOUT")throw D;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(k,D){let T,M=this._activeBuffer.x,N=this._activeBuffer.y,U=0;const q=this._parseStack.paused;if(q){if(T=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,D))return this._logSlowResolvingAsync(T),T;M=this._parseStack.cursorStartX,N=this._parseStack.cursorStartY,this._parseStack.paused=!1,k.length>A&&(U=this._parseStack.position+A)}if(this._logService.logLevel<=f.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof k=="string"?` "${k}"`:` "${Array.prototype.map.call(k,K=>String.fromCharCode(K)).join("")}"`),typeof k=="string"?k.split("").map(K=>K.charCodeAt(0)):k),this._parseBuffer.length<k.length&&this._parseBuffer.length<A&&(this._parseBuffer=new Uint32Array(Math.min(k.length,A))),q||this._dirtyRowTracker.clearRange(),k.length>A)for(let K=U;K<k.length;K+=A){const Q=K+A<k.length?K+A:k.length,w=typeof k=="string"?this._stringDecoder.decode(k.substring(K,Q),this._parseBuffer):this._utf8Decoder.decode(k.subarray(K,Q),this._parseBuffer);if(T=this._parser.parse(this._parseBuffer,w))return this._preserveStack(M,N,w,K),this._logSlowResolvingAsync(T),T}else if(!q){const K=typeof k=="string"?this._stringDecoder.decode(k,this._parseBuffer):this._utf8Decoder.decode(k,this._parseBuffer);if(T=this._parser.parse(this._parseBuffer,K))return this._preserveStack(M,N,K,0),this._logSlowResolvingAsync(T),T}this._activeBuffer.x===M&&this._activeBuffer.y===N||this._onCursorMove.fire(),this._onRequestRefreshRows.fire(this._dirtyRowTracker.start,this._dirtyRowTracker.end)}print(k,D,T){let M,N;const U=this._charsetService.charset,q=this._optionsService.rawOptions.screenReaderMode,K=this._bufferService.cols,Q=this._coreService.decPrivateModes.wraparound,w=this._coreService.modes.insertMode,P=this._curAttrData;let F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&T-D>0&&F.getWidth(this._activeBuffer.x-1)===2&&F.setCellFromCodePoint(this._activeBuffer.x-1,0,1,P.fg,P.bg,P.extended);for(let H=D;H<T;++H){if(M=k[H],N=this._unicodeService.wcwidth(M),M<127&&U){const V=U[String.fromCharCode(M)];V&&(M=V.charCodeAt(0))}if(q&&this._onA11yChar.fire((0,g.stringFromCodePoint)(M)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),N||!this._activeBuffer.x){if(this._activeBuffer.x+N-1>=K){if(Q){for(;this._activeBuffer.x<K;)F.setCellFromCodePoint(this._activeBuffer.x++,0,1,P.fg,P.bg,P.extended);this._activeBuffer.x=0,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)}else if(this._activeBuffer.x=K-1,N===2)continue}if(w&&(F.insertCells(this._activeBuffer.x,N,this._activeBuffer.getNullCell(P),P),F.getWidth(K-1)===2&&F.setCellFromCodePoint(K-1,i.NULL_CELL_CODE,i.NULL_CELL_WIDTH,P.fg,P.bg,P.extended)),F.setCellFromCodePoint(this._activeBuffer.x++,M,N,P.fg,P.bg,P.extended),N>0)for(;--N;)F.setCellFromCodePoint(this._activeBuffer.x++,0,0,P.fg,P.bg,P.extended)}else F.getWidth(this._activeBuffer.x-1)?F.addCodepointToCell(this._activeBuffer.x-1,M):F.addCodepointToCell(this._activeBuffer.x-2,M)}T-D>0&&(F.loadCell(this._activeBuffer.x-1,this._workCell),this._workCell.getWidth()===2||this._workCell.getCode()>65535?this._parser.precedingCodepoint=0:this._workCell.isCombined()?this._parser.precedingCodepoint=this._workCell.getChars().charCodeAt(0):this._parser.precedingCodepoint=this._workCell.content),this._activeBuffer.x<K&&T-D>0&&F.getWidth(this._activeBuffer.x)===0&&!F.hasContent(this._activeBuffer.x)&&F.setCellFromCodePoint(this._activeBuffer.x,0,1,P.fg,P.bg,P.extended),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(k,D){return k.final!=="t"||k.prefix||k.intermediates?this._parser.registerCsiHandler(k,D):this._parser.registerCsiHandler(k,T=>!I(T.params[0],this._optionsService.rawOptions.windowOptions)||D(T))}registerDcsHandler(k,D){return this._parser.registerDcsHandler(k,new y.DcsHandler(D))}registerEscHandler(k,D){return this._parser.registerEscHandler(k,D)}registerOscHandler(k,D){return this._parser.registerOscHandler(k,new v.OscHandler(D))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var k;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&(!((k=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))===null||k===void 0)&&k.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);D.hasWidth(this._activeBuffer.x)&&!D.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const k=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-k),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(k=this._bufferService.cols-1){this._activeBuffer.x=Math.min(k,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(k,D){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=k,this._activeBuffer.y=this._activeBuffer.scrollTop+D):(this._activeBuffer.x=k,this._activeBuffer.y=D),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(k,D){this._restrictCursor(),this._setCursor(this._activeBuffer.x+k,this._activeBuffer.y+D)}cursorUp(k){const D=this._activeBuffer.y-this._activeBuffer.scrollTop;return D>=0?this._moveCursor(0,-Math.min(D,k.params[0]||1)):this._moveCursor(0,-(k.params[0]||1)),!0}cursorDown(k){const D=this._activeBuffer.scrollBottom-this._activeBuffer.y;return D>=0?this._moveCursor(0,Math.min(D,k.params[0]||1)):this._moveCursor(0,k.params[0]||1),!0}cursorForward(k){return this._moveCursor(k.params[0]||1,0),!0}cursorBackward(k){return this._moveCursor(-(k.params[0]||1),0),!0}cursorNextLine(k){return this.cursorDown(k),this._activeBuffer.x=0,!0}cursorPrecedingLine(k){return this.cursorUp(k),this._activeBuffer.x=0,!0}cursorCharAbsolute(k){return this._setCursor((k.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(k){return this._setCursor(k.length>=2?(k.params[1]||1)-1:0,(k.params[0]||1)-1),!0}charPosAbsolute(k){return this._setCursor((k.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(k){return this._moveCursor(k.params[0]||1,0),!0}linePosAbsolute(k){return this._setCursor(this._activeBuffer.x,(k.params[0]||1)-1),!0}vPositionRelative(k){return this._moveCursor(0,k.params[0]||1),!0}hVPosition(k){return this.cursorPosition(k),!0}tabClear(k){const D=k.params[0];return D===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:D===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(k){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let D=k.params[0]||1;for(;D--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(k){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let D=k.params[0]||1;for(;D--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(k){const D=k.params[0];return D===1&&(this._curAttrData.bg|=536870912),D!==2&&D!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(k,D,T,M=!1,N=!1){const U=this._activeBuffer.lines.get(this._activeBuffer.ybase+k);U.replaceCells(D,T,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData(),N),M&&(U.isWrapped=!1)}_resetBufferLine(k,D=!1){const T=this._activeBuffer.lines.get(this._activeBuffer.ybase+k);T&&(T.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),D),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+k),T.isWrapped=!1)}eraseInDisplay(k,D=!1){let T;switch(this._restrictCursor(this._bufferService.cols),k.params[0]){case 0:for(T=this._activeBuffer.y,this._dirtyRowTracker.markDirty(T),this._eraseInBufferLine(T++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,D);T<this._bufferService.rows;T++)this._resetBufferLine(T,D);this._dirtyRowTracker.markDirty(T);break;case 1:for(T=this._activeBuffer.y,this._dirtyRowTracker.markDirty(T),this._eraseInBufferLine(T,0,this._activeBuffer.x+1,!0,D),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(T+1).isWrapped=!1);T--;)this._resetBufferLine(T,D);this._dirtyRowTracker.markDirty(0);break;case 2:for(T=this._bufferService.rows,this._dirtyRowTracker.markDirty(T-1);T--;)this._resetBufferLine(T,D);this._dirtyRowTracker.markDirty(0);break;case 3:const M=this._activeBuffer.lines.length-this._bufferService.rows;M>0&&(this._activeBuffer.lines.trimStart(M),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-M,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-M,0),this._onScroll.fire(0))}return!0}eraseInLine(k,D=!1){switch(this._restrictCursor(this._bufferService.cols),k.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,D);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,D);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,D)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(k){this._restrictCursor();let D=k.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const T=this._activeBuffer.ybase+this._activeBuffer.y,M=this._bufferService.rows-1-this._activeBuffer.scrollBottom,N=this._bufferService.rows-1+this._activeBuffer.ybase-M+1;for(;D--;)this._activeBuffer.lines.splice(N-1,1),this._activeBuffer.lines.splice(T,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(k){this._restrictCursor();let D=k.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const T=this._activeBuffer.ybase+this._activeBuffer.y;let M;for(M=this._bufferService.rows-1-this._activeBuffer.scrollBottom,M=this._bufferService.rows-1+this._activeBuffer.ybase-M;D--;)this._activeBuffer.lines.splice(T,1),this._activeBuffer.lines.splice(M,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(k){this._restrictCursor();const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return D&&(D.insertCells(this._activeBuffer.x,k.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(k){this._restrictCursor();const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return D&&(D.deleteCells(this._activeBuffer.x,k.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(k){let D=k.params[0]||1;for(;D--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(k){let D=k.params[0]||1;for(;D--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(e.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.deleteCells(0,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.insertCells(0,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.insertCells(this._activeBuffer.x,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.deleteCells(this._activeBuffer.x,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(k){this._restrictCursor();const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return D&&(D.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(k.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(k){if(!this._parser.precedingCodepoint)return!0;const D=k.params[0]||1,T=new Uint32Array(D);for(let M=0;M<D;++M)T[M]=this._parser.precedingCodepoint;return this.print(T,0,T.length),!0}sendDeviceAttributesPrimary(k){return k.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(a.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(a.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(k){return k.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(a.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(a.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(k.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(a.C0.ESC+"[>83;40003;0c")),!0}_is(k){return(this._optionsService.rawOptions.termName+"").indexOf(k)===0}setMode(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,u.DEFAULT_CHARSET),this._charsetService.setgCharset(1,u.DEFAULT_CHARSET),this._charsetService.setgCharset(2,u.DEFAULT_CHARSET),this._charsetService.setgCharset(3,u.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),k.params[D]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(k,D){const T=this._coreService.decPrivateModes,{activeProtocol:M,activeEncoding:N}=this._coreMouseService,U=this._coreService,{buffers:q,cols:K}=this._bufferService,{active:Q,alt:w}=q,P=this._optionsService.rawOptions,F=Z=>Z?1:2,H=k.params[0];return V=H,j=D?H===2?4:H===4?F(U.modes.insertMode):H===12?3:H===20?F(P.convertEol):0:H===1?F(T.applicationCursorKeys):H===3?P.windowOptions.setWinLines?K===80?2:K===132?1:0:0:H===6?F(T.origin):H===7?F(T.wraparound):H===8?3:H===9?F(M==="X10"):H===12?F(P.cursorBlink):H===25?F(!U.isCursorHidden):H===45?F(T.reverseWraparound):H===66?F(T.applicationKeypad):H===67?4:H===1e3?F(M==="VT200"):H===1002?F(M==="DRAG"):H===1003?F(M==="ANY"):H===1004?F(T.sendFocus):H===1005?4:H===1006?F(N==="SGR"):H===1015?4:H===1016?F(N==="SGR_PIXELS"):H===1048?1:H===47||H===1047||H===1049?F(Q===w):H===2004?F(T.bracketedPasteMode):0,U.triggerDataEvent(`${a.C0.ESC}[${D?"":"?"}${V};${j}$y`),!0;var V,j}_updateAttrColor(k,D,T,M,N){return D===2?(k|=50331648,k&=-16777216,k|=h.AttributeData.fromColorRGB([T,M,N])):D===5&&(k&=-50331904,k|=33554432|255&T),k}_extractColor(k,D,T){const M=[0,0,-1,0,0,0];let N=0,U=0;do{if(M[U+N]=k.params[D+U],k.hasSubParams(D+U)){const q=k.getSubParams(D+U);let K=0;do M[1]===5&&(N=1),M[U+K+1+N]=q[K];while(++K<q.length&&K+U+1+N<M.length);break}if(M[1]===5&&U+N>=2||M[1]===2&&U+N>=5)break;M[1]&&(N=1)}while(++U+D<k.length&&U+N<M.length);for(let q=2;q<M.length;++q)M[q]===-1&&(M[q]=0);switch(M[0]){case 38:T.fg=this._updateAttrColor(T.fg,M[1],M[3],M[4],M[5]);break;case 48:T.bg=this._updateAttrColor(T.bg,M[1],M[3],M[4],M[5]);break;case 58:T.extended=T.extended.clone(),T.extended.underlineColor=this._updateAttrColor(T.extended.underlineColor,M[1],M[3],M[4],M[5])}return U}_processUnderline(k,D){D.extended=D.extended.clone(),(!~k||k>5)&&(k=1),D.extended.underlineStyle=k,D.fg|=268435456,k===0&&(D.fg&=-268435457),D.updateExtended()}_processSGR0(k){k.fg=e.DEFAULT_ATTR_DATA.fg,k.bg=e.DEFAULT_ATTR_DATA.bg,k.extended=k.extended.clone(),k.extended.underlineStyle=0,k.extended.underlineColor&=-67108864,k.updateExtended()}charAttributes(k){if(k.length===1&&k.params[0]===0)return this._processSGR0(this._curAttrData),!0;const D=k.length;let T;const M=this._curAttrData;for(let N=0;N<D;N++)T=k.params[N],T>=30&&T<=37?(M.fg&=-50331904,M.fg|=16777216|T-30):T>=40&&T<=47?(M.bg&=-50331904,M.bg|=16777216|T-40):T>=90&&T<=97?(M.fg&=-50331904,M.fg|=16777224|T-90):T>=100&&T<=107?(M.bg&=-50331904,M.bg|=16777224|T-100):T===0?this._processSGR0(M):T===1?M.fg|=134217728:T===3?M.bg|=67108864:T===4?(M.fg|=268435456,this._processUnderline(k.hasSubParams(N)?k.getSubParams(N)[0]:1,M)):T===5?M.fg|=536870912:T===7?M.fg|=67108864:T===8?M.fg|=1073741824:T===9?M.fg|=2147483648:T===2?M.bg|=134217728:T===21?this._processUnderline(2,M):T===22?(M.fg&=-134217729,M.bg&=-134217729):T===23?M.bg&=-67108865:T===24?(M.fg&=-268435457,this._processUnderline(0,M)):T===25?M.fg&=-536870913:T===27?M.fg&=-67108865:T===28?M.fg&=-1073741825:T===29?M.fg&=2147483647:T===39?(M.fg&=-67108864,M.fg|=16777215&e.DEFAULT_ATTR_DATA.fg):T===49?(M.bg&=-67108864,M.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):T===38||T===48||T===58?N+=this._extractColor(k,N,M):T===53?M.bg|=1073741824:T===55?M.bg&=-1073741825:T===59?(M.extended=M.extended.clone(),M.extended.underlineColor=-1,M.updateExtended()):T===100?(M.fg&=-67108864,M.fg|=16777215&e.DEFAULT_ATTR_DATA.fg,M.bg&=-67108864,M.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",T);return!0}deviceStatus(k){switch(k.params[0]){case 5:this._coreService.triggerDataEvent(`${a.C0.ESC}[0n`);break;case 6:const D=this._activeBuffer.y+1,T=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${a.C0.ESC}[${D};${T}R`)}return!0}deviceStatusPrivate(k){if(k.params[0]===6){const D=this._activeBuffer.y+1,T=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${a.C0.ESC}[?${D};${T}R`)}return!0}softReset(k){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(k){const D=k.params[0]||1;switch(D){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const T=D%2==1;return this._optionsService.options.cursorBlink=T,!0}setScrollRegion(k){const D=k.params[0]||1;let T;return(k.length<2||(T=k.params[1])>this._bufferService.rows||T===0)&&(T=this._bufferService.rows),T>D&&(this._activeBuffer.scrollTop=D-1,this._activeBuffer.scrollBottom=T-1,this._setCursor(0,0)),!0}windowOptions(k){if(!I(k.params[0],this._optionsService.rawOptions.windowOptions))return!0;const D=k.length>1?k.params[1]:0;switch(k.params[0]){case 14:D!==2&&this._onRequestWindowsOptionsReport.fire(R.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(R.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${a.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:D!==0&&D!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),D!==0&&D!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:D!==0&&D!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),D!==0&&D!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(k){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(k){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(k){return this._windowTitle=k,this._onTitleChange.fire(k),!0}setIconName(k){return this._iconName=k,!0}setOrReportIndexedColor(k){const D=[],T=k.split(";");for(;T.length>1;){const M=T.shift(),N=T.shift();if(/^\d+$/.exec(M)){const U=parseInt(M);if(W(U))if(N==="?")D.push({type:0,index:U});else{const q=(0,d.parseColor)(N);q&&D.push({type:1,index:U,color:q})}}}return D.length&&this._onColor.fire(D),!0}setHyperlink(k){const D=k.split(";");return!(D.length<2)&&(D[1]?this._createHyperlink(D[0],D[1]):!D[0]&&this._finishHyperlink())}_createHyperlink(k,D){this._getCurrentLinkId()&&this._finishHyperlink();const T=k.split(":");let M;const N=T.findIndex(U=>U.startsWith("id="));return N!==-1&&(M=T[N].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:M,uri:D}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(k,D){const T=k.split(";");for(let M=0;M<T.length&&!(D>=this._specialColors.length);++M,++D)if(T[M]==="?")this._onColor.fire([{type:0,index:this._specialColors[D]}]);else{const N=(0,d.parseColor)(T[M]);N&&this._onColor.fire([{type:1,index:this._specialColors[D],color:N}])}return!0}setOrReportFgColor(k){return this._setOrReportSpecialColor(k,0)}setOrReportBgColor(k){return this._setOrReportSpecialColor(k,1)}setOrReportCursorColor(k){return this._setOrReportSpecialColor(k,2)}restoreIndexedColor(k){if(!k)return this._onColor.fire([{type:2}]),!0;const D=[],T=k.split(";");for(let M=0;M<T.length;++M)if(/^\d+$/.exec(T[M])){const N=parseInt(T[M]);W(N)&&D.push({type:2,index:N})}return D.length&&this._onColor.fire(D),!0}restoreFgColor(k){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(k){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(k){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,u.DEFAULT_CHARSET),!0}selectCharset(k){return k.length!==2?(this.selectDefaultCharset(),!0):(k[0]==="/"||this._charsetService.setgCharset(S[k[0]],u.CHARSETS[k[1]]||u.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const k=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,k,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(k){return this._charsetService.setgLevel(k),!0}screenAlignmentPattern(){const k=new r.CellData;k.content=4194373,k.fg=this._curAttrData.fg,k.bg=this._curAttrData.bg,this._setCursor(0,0);for(let D=0;D<this._bufferService.rows;++D){const T=this._activeBuffer.ybase+this._activeBuffer.y+D,M=this._activeBuffer.lines.get(T);M&&(M.fill(k),M.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(k,D){const T=this._bufferService.buffer,M=this._optionsService.rawOptions;return(N=>(this._coreService.triggerDataEvent(`${a.C0.ESC}${N}${a.C0.ESC}\\`),!0))(k==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:k==='"p'?'P1$r61;1"p':k==="r"?`P1$r${T.scrollTop+1};${T.scrollBottom+1}r`:k==="m"?"P1$r0m":k===" q"?`P1$r${{block:2,underline:4,bar:6}[M.cursorStyle]-(M.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(k,D){this._dirtyRowTracker.markRangeDirty(k,D)}}s.InputHandler=O;let $=class{constructor(z){this._bufferService=z,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(z){z<this.start?this.start=z:z>this.end&&(this.end=z)}markRangeDirty(z,k){z>k&&(B=z,z=k,k=B),z<this.start&&(this.start=z),k>this.end&&(this.end=k)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function W(z){return 0<=z&&z<256}$=l([c(0,f.IBufferService)],$)},844:(L,s)=>{function o(l){for(const c of l)c.dispose();l.length=0}Object.defineProperty(s,"__esModule",{value:!0}),s.getDisposeArrayDisposable=s.disposeArray=s.toDisposable=s.MutableDisposable=s.Disposable=void 0,s.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const l of this._disposables)l.dispose();this._disposables.length=0}register(l){return this._disposables.push(l),l}unregister(l){const c=this._disposables.indexOf(l);c!==-1&&this._disposables.splice(c,1)}},s.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(l){var c;this._isDisposed||l===this._value||((c=this._value)===null||c===void 0||c.dispose(),this._value=l)}clear(){this.value=void 0}dispose(){var l;this._isDisposed=!0,(l=this._value)===null||l===void 0||l.dispose(),this._value=void 0}},s.toDisposable=function(l){return{dispose:l}},s.disposeArray=o,s.getDisposeArrayDisposable=function(l){return{dispose:()=>o(l)}}},1505:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.FourKeyMap=s.TwoKeyMap=void 0;class o{constructor(){this._data={}}set(c,a,u){this._data[c]||(this._data[c]={}),this._data[c][a]=u}get(c,a){return this._data[c]?this._data[c][a]:void 0}clear(){this._data={}}}s.TwoKeyMap=o,s.FourKeyMap=class{constructor(){this._data=new o}set(l,c,a,u,p){this._data.get(l,c)||this._data.set(l,c,new o),this._data.get(l,c).set(a,u,p)}get(l,c,a,u){var p;return(p=this._data.get(l,c))===null||p===void 0?void 0:p.get(a,u)}clear(){this._data.clear()}}},6114:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.isChromeOS=s.isLinux=s.isWindows=s.isIphone=s.isIpad=s.isMac=s.getSafariVersion=s.isSafari=s.isLegacyEdge=s.isFirefox=s.isNode=void 0,s.isNode=typeof navigator>"u";const o=s.isNode?"node":navigator.userAgent,l=s.isNode?"node":navigator.platform;s.isFirefox=o.includes("Firefox"),s.isLegacyEdge=o.includes("Edge"),s.isSafari=/^((?!chrome|android).)*safari/i.test(o),s.getSafariVersion=function(){if(!s.isSafari)return 0;const c=o.match(/Version\/(\d+)/);return c===null||c.length<2?0:parseInt(c[1])},s.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(l),s.isIpad=l==="iPad",s.isIphone=l==="iPhone",s.isWindows=["Windows","Win16","Win32","WinCE"].includes(l),s.isLinux=l.indexOf("Linux")>=0,s.isChromeOS=/\bCrOS\b/.test(o)},6106:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SortedList=void 0;let o=0;s.SortedList=class{constructor(l){this._getKey=l,this._array=[]}clear(){this._array.length=0}insert(l){this._array.length!==0?(o=this._search(this._getKey(l)),this._array.splice(o,0,l)):this._array.push(l)}delete(l){if(this._array.length===0)return!1;const c=this._getKey(l);if(c===void 0||(o=this._search(c),o===-1)||this._getKey(this._array[o])!==c)return!1;do if(this._array[o]===l)return this._array.splice(o,1),!0;while(++o<this._array.length&&this._getKey(this._array[o])===c);return!1}*getKeyIterator(l){if(this._array.length!==0&&(o=this._search(l),!(o<0||o>=this._array.length)&&this._getKey(this._array[o])===l))do yield this._array[o];while(++o<this._array.length&&this._getKey(this._array[o])===l)}forEachByKey(l,c){if(this._array.length!==0&&(o=this._search(l),!(o<0||o>=this._array.length)&&this._getKey(this._array[o])===l))do c(this._array[o]);while(++o<this._array.length&&this._getKey(this._array[o])===l)}values(){return[...this._array].values()}_search(l){let c=0,a=this._array.length-1;for(;a>=c;){let u=c+a>>1;const p=this._getKey(this._array[u]);if(p>l)a=u-1;else{if(!(p<l)){for(;u>0&&this._getKey(this._array[u-1])===l;)u--;return u}c=u+1}}return c}}},7226:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DebouncedIdleTask=s.IdleTaskQueue=s.PriorityTaskQueue=void 0;const l=o(6114);class c{constructor(){this._tasks=[],this._i=0}enqueue(p){this._tasks.push(p),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(p){this._idleCallback=void 0;let m=0,g=0,e=p.timeRemaining(),n=0;for(;this._i<this._tasks.length;){if(m=Date.now(),this._tasks[this._i]()||this._i++,m=Math.max(1,Date.now()-m),g=Math.max(m,g),n=p.timeRemaining(),1.5*g>n)return e-m<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(e-m))}ms`),void this._start();e=n}this.clear()}}class a extends c{_requestCallback(p){return setTimeout(()=>p(this._createDeadline(16)))}_cancelCallback(p){clearTimeout(p)}_createDeadline(p){const m=Date.now()+p;return{timeRemaining:()=>Math.max(0,m-Date.now())}}}s.PriorityTaskQueue=a,s.IdleTaskQueue=!l.isNode&&"requestIdleCallback"in window?class extends c{_requestCallback(u){return requestIdleCallback(u)}_cancelCallback(u){cancelIdleCallback(u)}}:a,s.DebouncedIdleTask=class{constructor(){this._queue=new s.IdleTaskQueue}set(u){this._queue.clear(),this._queue.enqueue(u)}flush(){this._queue.flush()}}},9282:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.updateWindowsModeWrappedState=void 0;const l=o(643);s.updateWindowsModeWrappedState=function(c){const a=c.buffer.lines.get(c.buffer.ybase+c.buffer.y-1),u=a==null?void 0:a.get(c.cols-1),p=c.buffer.lines.get(c.buffer.ybase+c.buffer.y);p&&u&&(p.isWrapped=u[l.CHAR_DATA_CODE_INDEX]!==l.NULL_CELL_CODE&&u[l.CHAR_DATA_CODE_INDEX]!==l.WHITESPACE_CELL_CODE)}},3734:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ExtendedAttrs=s.AttributeData=void 0;class o{constructor(){this.fg=0,this.bg=0,this.extended=new l}static toColorRGB(a){return[a>>>16&255,a>>>8&255,255&a]}static fromColorRGB(a){return(255&a[0])<<16|(255&a[1])<<8|255&a[2]}clone(){const a=new o;return a.fg=this.fg,a.bg=this.bg,a.extended=this.extended.clone(),a}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}}s.AttributeData=o;class l{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(a){this._ext=a}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(a){this._ext&=-469762049,this._ext|=a<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(a){this._ext&=-67108864,this._ext|=67108863&a}get urlId(){return this._urlId}set urlId(a){this._urlId=a}constructor(a=0,u=0){this._ext=0,this._urlId=0,this._ext=a,this._urlId=u}clone(){return new l(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}s.ExtendedAttrs=l},9092:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Buffer=s.MAX_BUFFER_SIZE=void 0;const l=o(6349),c=o(7226),a=o(3734),u=o(8437),p=o(4634),m=o(511),g=o(643),e=o(4863),n=o(7116);s.MAX_BUFFER_SIZE=4294967295,s.Buffer=class{constructor(i,r,h){this._hasScrollback=i,this._optionsService=r,this._bufferService=h,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=u.DEFAULT_ATTR_DATA.clone(),this.savedCharset=n.DEFAULT_CHARSET,this.markers=[],this._nullCell=m.CellData.fromCharData([0,g.NULL_CELL_CHAR,g.NULL_CELL_WIDTH,g.NULL_CELL_CODE]),this._whitespaceCell=m.CellData.fromCharData([0,g.WHITESPACE_CELL_CHAR,g.WHITESPACE_CELL_WIDTH,g.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new c.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(i){return i?(this._nullCell.fg=i.fg,this._nullCell.bg=i.bg,this._nullCell.extended=i.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new a.ExtendedAttrs),this._nullCell}getWhitespaceCell(i){return i?(this._whitespaceCell.fg=i.fg,this._whitespaceCell.bg=i.bg,this._whitespaceCell.extended=i.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new a.ExtendedAttrs),this._whitespaceCell}getBlankLine(i,r){return new u.BufferLine(this._bufferService.cols,this.getNullCell(i),r)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const i=this.ybase+this.y-this.ydisp;return i>=0&&i<this._rows}_getCorrectBufferLength(i){if(!this._hasScrollback)return i;const r=i+this._optionsService.rawOptions.scrollback;return r>s.MAX_BUFFER_SIZE?s.MAX_BUFFER_SIZE:r}fillViewportRows(i){if(this.lines.length===0){i===void 0&&(i=u.DEFAULT_ATTR_DATA);let r=this._rows;for(;r--;)this.lines.push(this.getBlankLine(i))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(i,r){const h=this.getNullCell(u.DEFAULT_ATTR_DATA);let f=0;const v=this._getCorrectBufferLength(r);if(v>this.lines.maxLength&&(this.lines.maxLength=v),this.lines.length>0){if(this._cols<i)for(let d=0;d<this.lines.length;d++)f+=+this.lines.get(d).resize(i,h);let y=0;if(this._rows<r)for(let d=this._rows;d<r;d++)this.lines.length<r+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new u.BufferLine(i,h)):this.ybase>0&&this.lines.length<=this.ybase+this.y+y+1?(this.ybase--,y++,this.ydisp>0&&this.ydisp--):this.lines.push(new u.BufferLine(i,h)));else for(let d=this._rows;d>r;d--)this.lines.length>r+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(v<this.lines.maxLength){const d=this.lines.length-v;d>0&&(this.lines.trimStart(d),this.ybase=Math.max(this.ybase-d,0),this.ydisp=Math.max(this.ydisp-d,0),this.savedY=Math.max(this.savedY-d,0)),this.lines.maxLength=v}this.x=Math.min(this.x,i-1),this.y=Math.min(this.y,r-1),y&&(this.y+=y),this.savedX=Math.min(this.savedX,i-1),this.scrollTop=0}if(this.scrollBottom=r-1,this._isReflowEnabled&&(this._reflow(i,r),this._cols>i))for(let y=0;y<this.lines.length;y++)f+=+this.lines.get(y).resize(i,h);this._cols=i,this._rows=r,this._memoryCleanupQueue.clear(),f>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let i=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,i=!1);let r=0;for(;this._memoryCleanupPosition<this.lines.length;)if(r+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),r>100)return!0;return i}get _isReflowEnabled(){const i=this._optionsService.rawOptions.windowsPty;return i&&i.buildNumber?this._hasScrollback&&i.backend==="conpty"&&i.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(i,r){this._cols!==i&&(i>this._cols?this._reflowLarger(i,r):this._reflowSmaller(i,r))}_reflowLarger(i,r){const h=(0,p.reflowLargerGetLinesToRemove)(this.lines,this._cols,i,this.ybase+this.y,this.getNullCell(u.DEFAULT_ATTR_DATA));if(h.length>0){const f=(0,p.reflowLargerCreateNewLayout)(this.lines,h);(0,p.reflowLargerApplyNewLayout)(this.lines,f.layout),this._reflowLargerAdjustViewport(i,r,f.countRemoved)}}_reflowLargerAdjustViewport(i,r,h){const f=this.getNullCell(u.DEFAULT_ATTR_DATA);let v=h;for(;v-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<r&&this.lines.push(new u.BufferLine(i,f))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-h,0)}_reflowSmaller(i,r){const h=this.getNullCell(u.DEFAULT_ATTR_DATA),f=[];let v=0;for(let y=this.lines.length-1;y>=0;y--){let d=this.lines.get(y);if(!d||!d.isWrapped&&d.getTrimmedLength()<=i)continue;const S=[d];for(;d.isWrapped&&y>0;)d=this.lines.get(--y),S.unshift(d);const A=this.ybase+this.y;if(A>=y&&A<y+S.length)continue;const I=S[S.length-1].getTrimmedLength(),R=(0,p.reflowSmallerGetNewLineLengths)(S,this._cols,i),B=R.length-S.length;let O;O=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+B):Math.max(0,this.lines.length-this.lines.maxLength+B);const $=[];for(let M=0;M<B;M++){const N=this.getBlankLine(u.DEFAULT_ATTR_DATA,!0);$.push(N)}$.length>0&&(f.push({start:y+S.length+v,newLines:$}),v+=$.length),S.push(...$);let W=R.length-1,z=R[W];z===0&&(W--,z=R[W]);let k=S.length-B-1,D=I;for(;k>=0;){const M=Math.min(D,z);if(S[W]===void 0)break;if(S[W].copyCellsFrom(S[k],D-M,z-M,M,!0),z-=M,z===0&&(W--,z=R[W]),D-=M,D===0){k--;const N=Math.max(k,0);D=(0,p.getWrappedLineTrimmedLength)(S,N,this._cols)}}for(let M=0;M<S.length;M++)R[M]<i&&S[M].setCell(R[M],h);let T=B-O;for(;T-- >0;)this.ybase===0?this.y<r-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+v)-r&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+B,this.ybase+r-1)}if(f.length>0){const y=[],d=[];for(let W=0;W<this.lines.length;W++)d.push(this.lines.get(W));const S=this.lines.length;let A=S-1,I=0,R=f[I];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+v);let B=0;for(let W=Math.min(this.lines.maxLength-1,S+v-1);W>=0;W--)if(R&&R.start>A+B){for(let z=R.newLines.length-1;z>=0;z--)this.lines.set(W--,R.newLines[z]);W++,y.push({index:A+1,amount:R.newLines.length}),B+=R.newLines.length,R=f[++I]}else this.lines.set(W,d[A--]);let O=0;for(let W=y.length-1;W>=0;W--)y[W].index+=O,this.lines.onInsertEmitter.fire(y[W]),O+=y[W].amount;const $=Math.max(0,S+v-this.lines.maxLength);$>0&&this.lines.onTrimEmitter.fire($)}}translateBufferLineToString(i,r,h=0,f){const v=this.lines.get(i);return v?v.translateToString(r,h,f):""}getWrappedRangeForLine(i){let r=i,h=i;for(;r>0&&this.lines.get(r).isWrapped;)r--;for(;h+1<this.lines.length&&this.lines.get(h+1).isWrapped;)h++;return{first:r,last:h}}setupTabStops(i){for(i!=null?this.tabs[i]||(i=this.prevStop(i)):(this.tabs={},i=0);i<this._cols;i+=this._optionsService.rawOptions.tabStopWidth)this.tabs[i]=!0}prevStop(i){for(i==null&&(i=this.x);!this.tabs[--i]&&i>0;);return i>=this._cols?this._cols-1:i<0?0:i}nextStop(i){for(i==null&&(i=this.x);!this.tabs[++i]&&i<this._cols;);return i>=this._cols?this._cols-1:i<0?0:i}clearMarkers(i){this._isClearing=!0;for(let r=0;r<this.markers.length;r++)this.markers[r].line===i&&(this.markers[r].dispose(),this.markers.splice(r--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let i=0;i<this.markers.length;i++)this.markers[i].dispose(),this.markers.splice(i--,1);this._isClearing=!1}addMarker(i){const r=new e.Marker(i);return this.markers.push(r),r.register(this.lines.onTrim(h=>{r.line-=h,r.line<0&&r.dispose()})),r.register(this.lines.onInsert(h=>{r.line>=h.index&&(r.line+=h.amount)})),r.register(this.lines.onDelete(h=>{r.line>=h.index&&r.line<h.index+h.amount&&r.dispose(),r.line>h.index&&(r.line-=h.amount)})),r.register(r.onDispose(()=>this._removeMarker(r))),r}_removeMarker(i){this._isClearing||this.markers.splice(this.markers.indexOf(i),1)}}},8437:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferLine=s.DEFAULT_ATTR_DATA=void 0;const l=o(3734),c=o(511),a=o(643),u=o(482);s.DEFAULT_ATTR_DATA=Object.freeze(new l.AttributeData);let p=0;class m{constructor(e,n,i=!1){this.isWrapped=i,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*e);const r=n||c.CellData.fromCharData([0,a.NULL_CELL_CHAR,a.NULL_CELL_WIDTH,a.NULL_CELL_CODE]);for(let h=0;h<e;++h)this.setCell(h,r);this.length=e}get(e){const n=this._data[3*e+0],i=2097151&n;return[this._data[3*e+1],2097152&n?this._combined[e]:i?(0,u.stringFromCodePoint)(i):"",n>>22,2097152&n?this._combined[e].charCodeAt(this._combined[e].length-1):i]}set(e,n){this._data[3*e+1]=n[a.CHAR_DATA_ATTR_INDEX],n[a.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[e]=n[1],this._data[3*e+0]=2097152|e|n[a.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*e+0]=n[a.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|n[a.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(e){return this._data[3*e+0]>>22}hasWidth(e){return 12582912&this._data[3*e+0]}getFg(e){return this._data[3*e+1]}getBg(e){return this._data[3*e+2]}hasContent(e){return 4194303&this._data[3*e+0]}getCodePoint(e){const n=this._data[3*e+0];return 2097152&n?this._combined[e].charCodeAt(this._combined[e].length-1):2097151&n}isCombined(e){return 2097152&this._data[3*e+0]}getString(e){const n=this._data[3*e+0];return 2097152&n?this._combined[e]:2097151&n?(0,u.stringFromCodePoint)(2097151&n):""}isProtected(e){return 536870912&this._data[3*e+2]}loadCell(e,n){return p=3*e,n.content=this._data[p+0],n.fg=this._data[p+1],n.bg=this._data[p+2],2097152&n.content&&(n.combinedData=this._combined[e]),268435456&n.bg&&(n.extended=this._extendedAttrs[e]),n}setCell(e,n){2097152&n.content&&(this._combined[e]=n.combinedData),268435456&n.bg&&(this._extendedAttrs[e]=n.extended),this._data[3*e+0]=n.content,this._data[3*e+1]=n.fg,this._data[3*e+2]=n.bg}setCellFromCodePoint(e,n,i,r,h,f){268435456&h&&(this._extendedAttrs[e]=f),this._data[3*e+0]=n|i<<22,this._data[3*e+1]=r,this._data[3*e+2]=h}addCodepointToCell(e,n){let i=this._data[3*e+0];2097152&i?this._combined[e]+=(0,u.stringFromCodePoint)(n):(2097151&i?(this._combined[e]=(0,u.stringFromCodePoint)(2097151&i)+(0,u.stringFromCodePoint)(n),i&=-2097152,i|=2097152):i=n|4194304,this._data[3*e+0]=i)}insertCells(e,n,i,r){if((e%=this.length)&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),n<this.length-e){const h=new c.CellData;for(let f=this.length-e-n-1;f>=0;--f)this.setCell(e+n+f,this.loadCell(e+f,h));for(let f=0;f<n;++f)this.setCell(e+f,i)}else for(let h=e;h<this.length;++h)this.setCell(h,i);this.getWidth(this.length-1)===2&&this.setCellFromCodePoint(this.length-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs)}deleteCells(e,n,i,r){if(e%=this.length,n<this.length-e){const h=new c.CellData;for(let f=0;f<this.length-e-n;++f)this.setCell(e+f,this.loadCell(e+n+f,h));for(let f=this.length-n;f<this.length;++f)this.setCell(f,i)}else for(let h=e;h<this.length;++h)this.setCell(h,i);e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),this.getWidth(e)!==0||this.hasContent(e)||this.setCellFromCodePoint(e,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs)}replaceCells(e,n,i,r,h=!1){if(h)for(e&&this.getWidth(e-1)===2&&!this.isProtected(e-1)&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),n<this.length&&this.getWidth(n-1)===2&&!this.isProtected(n)&&this.setCellFromCodePoint(n,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs);e<n&&e<this.length;)this.isProtected(e)||this.setCell(e,i),e++;else for(e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),n<this.length&&this.getWidth(n-1)===2&&this.setCellFromCodePoint(n,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs);e<n&&e<this.length;)this.setCell(e++,i)}resize(e,n){if(e===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const i=3*e;if(e>this.length){if(this._data.buffer.byteLength>=4*i)this._data=new Uint32Array(this._data.buffer,0,i);else{const r=new Uint32Array(i);r.set(this._data),this._data=r}for(let r=this.length;r<e;++r)this.setCell(r,n)}else{this._data=this._data.subarray(0,i);const r=Object.keys(this._combined);for(let f=0;f<r.length;f++){const v=parseInt(r[f],10);v>=e&&delete this._combined[v]}const h=Object.keys(this._extendedAttrs);for(let f=0;f<h.length;f++){const v=parseInt(h[f],10);v>=e&&delete this._extendedAttrs[v]}}return this.length=e,4*i*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const e=new Uint32Array(this._data.length);return e.set(this._data),this._data=e,1}return 0}fill(e,n=!1){if(n)for(let i=0;i<this.length;++i)this.isProtected(i)||this.setCell(i,e);else{this._combined={},this._extendedAttrs={};for(let i=0;i<this.length;++i)this.setCell(i,e)}}copyFrom(e){this.length!==e.length?this._data=new Uint32Array(e._data):this._data.set(e._data),this.length=e.length,this._combined={};for(const n in e._combined)this._combined[n]=e._combined[n];this._extendedAttrs={};for(const n in e._extendedAttrs)this._extendedAttrs[n]=e._extendedAttrs[n];this.isWrapped=e.isWrapped}clone(){const e=new m(0);e._data=new Uint32Array(this._data),e.length=this.length;for(const n in this._combined)e._combined[n]=this._combined[n];for(const n in this._extendedAttrs)e._extendedAttrs[n]=this._extendedAttrs[n];return e.isWrapped=this.isWrapped,e}getTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0])return e+(this._data[3*e+0]>>22);return 0}getNoBgTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0]||50331648&this._data[3*e+2])return e+(this._data[3*e+0]>>22);return 0}copyCellsFrom(e,n,i,r,h){const f=e._data;if(h)for(let y=r-1;y>=0;y--){for(let d=0;d<3;d++)this._data[3*(i+y)+d]=f[3*(n+y)+d];268435456&f[3*(n+y)+2]&&(this._extendedAttrs[i+y]=e._extendedAttrs[n+y])}else for(let y=0;y<r;y++){for(let d=0;d<3;d++)this._data[3*(i+y)+d]=f[3*(n+y)+d];268435456&f[3*(n+y)+2]&&(this._extendedAttrs[i+y]=e._extendedAttrs[n+y])}const v=Object.keys(e._combined);for(let y=0;y<v.length;y++){const d=parseInt(v[y],10);d>=n&&(this._combined[d-n+i]=e._combined[d])}}translateToString(e=!1,n=0,i=this.length){e&&(i=Math.min(i,this.getTrimmedLength()));let r="";for(;n<i;){const h=this._data[3*n+0],f=2097151&h;r+=2097152&h?this._combined[n]:f?(0,u.stringFromCodePoint)(f):a.WHITESPACE_CELL_CHAR,n+=h>>22||1}return r}}s.BufferLine=m},4841:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.getRangeLength=void 0,s.getRangeLength=function(o,l){if(o.start.y>o.end.y)throw new Error(`Buffer range end (${o.end.x}, ${o.end.y}) cannot be before start (${o.start.x}, ${o.start.y})`);return l*(o.end.y-o.start.y)+(o.end.x-o.start.x+1)}},4634:(L,s)=>{function o(l,c,a){if(c===l.length-1)return l[c].getTrimmedLength();const u=!l[c].hasContent(a-1)&&l[c].getWidth(a-1)===1,p=l[c+1].getWidth(0)===2;return u&&p?a-1:a}Object.defineProperty(s,"__esModule",{value:!0}),s.getWrappedLineTrimmedLength=s.reflowSmallerGetNewLineLengths=s.reflowLargerApplyNewLayout=s.reflowLargerCreateNewLayout=s.reflowLargerGetLinesToRemove=void 0,s.reflowLargerGetLinesToRemove=function(l,c,a,u,p){const m=[];for(let g=0;g<l.length-1;g++){let e=g,n=l.get(++e);if(!n.isWrapped)continue;const i=[l.get(g)];for(;e<l.length&&n.isWrapped;)i.push(n),n=l.get(++e);if(u>=g&&u<e){g+=i.length-1;continue}let r=0,h=o(i,r,c),f=1,v=0;for(;f<i.length;){const d=o(i,f,c),S=d-v,A=a-h,I=Math.min(S,A);i[r].copyCellsFrom(i[f],v,h,I,!1),h+=I,h===a&&(r++,h=0),v+=I,v===d&&(f++,v=0),h===0&&r!==0&&i[r-1].getWidth(a-1)===2&&(i[r].copyCellsFrom(i[r-1],a-1,h++,1,!1),i[r-1].setCell(a-1,p))}i[r].replaceCells(h,a,p);let y=0;for(let d=i.length-1;d>0&&(d>r||i[d].getTrimmedLength()===0);d--)y++;y>0&&(m.push(g+i.length-y),m.push(y)),g+=i.length-1}return m},s.reflowLargerCreateNewLayout=function(l,c){const a=[];let u=0,p=c[u],m=0;for(let g=0;g<l.length;g++)if(p===g){const e=c[++u];l.onDeleteEmitter.fire({index:g-m,amount:e}),g+=e-1,m+=e,p=c[++u]}else a.push(g);return{layout:a,countRemoved:m}},s.reflowLargerApplyNewLayout=function(l,c){const a=[];for(let u=0;u<c.length;u++)a.push(l.get(c[u]));for(let u=0;u<a.length;u++)l.set(u,a[u]);l.length=c.length},s.reflowSmallerGetNewLineLengths=function(l,c,a){const u=[],p=l.map((n,i)=>o(l,i,c)).reduce((n,i)=>n+i);let m=0,g=0,e=0;for(;e<p;){if(p-e<a){u.push(p-e);break}m+=a;const n=o(l,g,c);m>n&&(m-=n,g++);const i=l[g].getWidth(m-1)===2;i&&m--;const r=i?a-1:a;u.push(r),e+=r}return u},s.getWrappedLineTrimmedLength=o},5295:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferSet=void 0;const l=o(8460),c=o(844),a=o(9092);class u extends c.Disposable{constructor(m,g){super(),this._optionsService=m,this._bufferService=g,this._onBufferActivate=this.register(new l.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new a.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new a.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(m){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(m),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(m,g){this._normal.resize(m,g),this._alt.resize(m,g),this.setupTabStops(m)}setupTabStops(m){this._normal.setupTabStops(m),this._alt.setupTabStops(m)}}s.BufferSet=u},511:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CellData=void 0;const l=o(482),c=o(643),a=o(3734);class u extends a.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new a.ExtendedAttrs,this.combinedData=""}static fromCharData(m){const g=new u;return g.setFromCharData(m),g}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,l.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(m){this.fg=m[c.CHAR_DATA_ATTR_INDEX],this.bg=0;let g=!1;if(m[c.CHAR_DATA_CHAR_INDEX].length>2)g=!0;else if(m[c.CHAR_DATA_CHAR_INDEX].length===2){const e=m[c.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=e&&e<=56319){const n=m[c.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=n&&n<=57343?this.content=1024*(e-55296)+n-56320+65536|m[c.CHAR_DATA_WIDTH_INDEX]<<22:g=!0}else g=!0}else this.content=m[c.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|m[c.CHAR_DATA_WIDTH_INDEX]<<22;g&&(this.combinedData=m[c.CHAR_DATA_CHAR_INDEX],this.content=2097152|m[c.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.CellData=u},643:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WHITESPACE_CELL_CODE=s.WHITESPACE_CELL_WIDTH=s.WHITESPACE_CELL_CHAR=s.NULL_CELL_CODE=s.NULL_CELL_WIDTH=s.NULL_CELL_CHAR=s.CHAR_DATA_CODE_INDEX=s.CHAR_DATA_WIDTH_INDEX=s.CHAR_DATA_CHAR_INDEX=s.CHAR_DATA_ATTR_INDEX=s.DEFAULT_EXT=s.DEFAULT_ATTR=s.DEFAULT_COLOR=void 0,s.DEFAULT_COLOR=0,s.DEFAULT_ATTR=256|s.DEFAULT_COLOR<<9,s.DEFAULT_EXT=0,s.CHAR_DATA_ATTR_INDEX=0,s.CHAR_DATA_CHAR_INDEX=1,s.CHAR_DATA_WIDTH_INDEX=2,s.CHAR_DATA_CODE_INDEX=3,s.NULL_CELL_CHAR="",s.NULL_CELL_WIDTH=1,s.NULL_CELL_CODE=0,s.WHITESPACE_CELL_CHAR=" ",s.WHITESPACE_CELL_WIDTH=1,s.WHITESPACE_CELL_CODE=32},4863:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Marker=void 0;const l=o(8460),c=o(844);class a{get id(){return this._id}constructor(p){this.line=p,this.isDisposed=!1,this._disposables=[],this._id=a._nextId++,this._onDispose=this.register(new l.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,c.disposeArray)(this._disposables),this._disposables.length=0)}register(p){return this._disposables.push(p),p}}s.Marker=a,a._nextId=1},7116:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DEFAULT_CHARSET=s.CHARSETS=void 0,s.CHARSETS={},s.DEFAULT_CHARSET=s.CHARSETS.B,s.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},s.CHARSETS.A={"#":"£"},s.CHARSETS.B=void 0,s.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},s.CHARSETS.C=s.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},s.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},s.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},s.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},s.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},s.CHARSETS.E=s.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},s.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},s.CHARSETS.H=s.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},s.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(L,s)=>{var o,l,c;Object.defineProperty(s,"__esModule",{value:!0}),s.C1_ESCAPED=s.C1=s.C0=void 0,function(a){a.NUL="\0",a.SOH="",a.STX="",a.ETX="",a.EOT="",a.ENQ="",a.ACK="",a.BEL="\x07",a.BS="\b",a.HT="	",a.LF=`
`,a.VT="\v",a.FF="\f",a.CR="\r",a.SO="",a.SI="",a.DLE="",a.DC1="",a.DC2="",a.DC3="",a.DC4="",a.NAK="",a.SYN="",a.ETB="",a.CAN="",a.EM="",a.SUB="",a.ESC="\x1B",a.FS="",a.GS="",a.RS="",a.US="",a.SP=" ",a.DEL=""}(o||(s.C0=o={})),function(a){a.PAD="",a.HOP="",a.BPH="",a.NBH="",a.IND="",a.NEL="",a.SSA="",a.ESA="",a.HTS="",a.HTJ="",a.VTS="",a.PLD="",a.PLU="",a.RI="",a.SS2="",a.SS3="",a.DCS="",a.PU1="",a.PU2="",a.STS="",a.CCH="",a.MW="",a.SPA="",a.EPA="",a.SOS="",a.SGCI="",a.SCI="",a.CSI="",a.ST="",a.OSC="",a.PM="",a.APC=""}(l||(s.C1=l={})),function(a){a.ST=`${o.ESC}\\`}(c||(s.C1_ESCAPED=c={}))},7399:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.evaluateKeyboardEvent=void 0;const l=o(2584),c={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};s.evaluateKeyboardEvent=function(a,u,p,m){const g={type:0,cancel:!1,key:void 0},e=(a.shiftKey?1:0)|(a.altKey?2:0)|(a.ctrlKey?4:0)|(a.metaKey?8:0);switch(a.keyCode){case 0:a.key==="UIKeyInputUpArrow"?g.key=u?l.C0.ESC+"OA":l.C0.ESC+"[A":a.key==="UIKeyInputLeftArrow"?g.key=u?l.C0.ESC+"OD":l.C0.ESC+"[D":a.key==="UIKeyInputRightArrow"?g.key=u?l.C0.ESC+"OC":l.C0.ESC+"[C":a.key==="UIKeyInputDownArrow"&&(g.key=u?l.C0.ESC+"OB":l.C0.ESC+"[B");break;case 8:if(a.altKey){g.key=l.C0.ESC+l.C0.DEL;break}g.key=l.C0.DEL;break;case 9:if(a.shiftKey){g.key=l.C0.ESC+"[Z";break}g.key=l.C0.HT,g.cancel=!0;break;case 13:g.key=a.altKey?l.C0.ESC+l.C0.CR:l.C0.CR,g.cancel=!0;break;case 27:g.key=l.C0.ESC,a.altKey&&(g.key=l.C0.ESC+l.C0.ESC),g.cancel=!0;break;case 37:if(a.metaKey)break;e?(g.key=l.C0.ESC+"[1;"+(e+1)+"D",g.key===l.C0.ESC+"[1;3D"&&(g.key=l.C0.ESC+(p?"b":"[1;5D"))):g.key=u?l.C0.ESC+"OD":l.C0.ESC+"[D";break;case 39:if(a.metaKey)break;e?(g.key=l.C0.ESC+"[1;"+(e+1)+"C",g.key===l.C0.ESC+"[1;3C"&&(g.key=l.C0.ESC+(p?"f":"[1;5C"))):g.key=u?l.C0.ESC+"OC":l.C0.ESC+"[C";break;case 38:if(a.metaKey)break;e?(g.key=l.C0.ESC+"[1;"+(e+1)+"A",p||g.key!==l.C0.ESC+"[1;3A"||(g.key=l.C0.ESC+"[1;5A")):g.key=u?l.C0.ESC+"OA":l.C0.ESC+"[A";break;case 40:if(a.metaKey)break;e?(g.key=l.C0.ESC+"[1;"+(e+1)+"B",p||g.key!==l.C0.ESC+"[1;3B"||(g.key=l.C0.ESC+"[1;5B")):g.key=u?l.C0.ESC+"OB":l.C0.ESC+"[B";break;case 45:a.shiftKey||a.ctrlKey||(g.key=l.C0.ESC+"[2~");break;case 46:g.key=e?l.C0.ESC+"[3;"+(e+1)+"~":l.C0.ESC+"[3~";break;case 36:g.key=e?l.C0.ESC+"[1;"+(e+1)+"H":u?l.C0.ESC+"OH":l.C0.ESC+"[H";break;case 35:g.key=e?l.C0.ESC+"[1;"+(e+1)+"F":u?l.C0.ESC+"OF":l.C0.ESC+"[F";break;case 33:a.shiftKey?g.type=2:a.ctrlKey?g.key=l.C0.ESC+"[5;"+(e+1)+"~":g.key=l.C0.ESC+"[5~";break;case 34:a.shiftKey?g.type=3:a.ctrlKey?g.key=l.C0.ESC+"[6;"+(e+1)+"~":g.key=l.C0.ESC+"[6~";break;case 112:g.key=e?l.C0.ESC+"[1;"+(e+1)+"P":l.C0.ESC+"OP";break;case 113:g.key=e?l.C0.ESC+"[1;"+(e+1)+"Q":l.C0.ESC+"OQ";break;case 114:g.key=e?l.C0.ESC+"[1;"+(e+1)+"R":l.C0.ESC+"OR";break;case 115:g.key=e?l.C0.ESC+"[1;"+(e+1)+"S":l.C0.ESC+"OS";break;case 116:g.key=e?l.C0.ESC+"[15;"+(e+1)+"~":l.C0.ESC+"[15~";break;case 117:g.key=e?l.C0.ESC+"[17;"+(e+1)+"~":l.C0.ESC+"[17~";break;case 118:g.key=e?l.C0.ESC+"[18;"+(e+1)+"~":l.C0.ESC+"[18~";break;case 119:g.key=e?l.C0.ESC+"[19;"+(e+1)+"~":l.C0.ESC+"[19~";break;case 120:g.key=e?l.C0.ESC+"[20;"+(e+1)+"~":l.C0.ESC+"[20~";break;case 121:g.key=e?l.C0.ESC+"[21;"+(e+1)+"~":l.C0.ESC+"[21~";break;case 122:g.key=e?l.C0.ESC+"[23;"+(e+1)+"~":l.C0.ESC+"[23~";break;case 123:g.key=e?l.C0.ESC+"[24;"+(e+1)+"~":l.C0.ESC+"[24~";break;default:if(!a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)if(p&&!m||!a.altKey||a.metaKey)!p||a.altKey||a.ctrlKey||a.shiftKey||!a.metaKey?a.key&&!a.ctrlKey&&!a.altKey&&!a.metaKey&&a.keyCode>=48&&a.key.length===1?g.key=a.key:a.key&&a.ctrlKey&&(a.key==="_"&&(g.key=l.C0.US),a.key==="@"&&(g.key=l.C0.NUL)):a.keyCode===65&&(g.type=1);else{const n=c[a.keyCode],i=n==null?void 0:n[a.shiftKey?1:0];if(i)g.key=l.C0.ESC+i;else if(a.keyCode>=65&&a.keyCode<=90){const r=a.ctrlKey?a.keyCode-64:a.keyCode+32;let h=String.fromCharCode(r);a.shiftKey&&(h=h.toUpperCase()),g.key=l.C0.ESC+h}else if(a.keyCode===32)g.key=l.C0.ESC+(a.ctrlKey?l.C0.NUL:" ");else if(a.key==="Dead"&&a.code.startsWith("Key")){let r=a.code.slice(3,4);a.shiftKey||(r=r.toLowerCase()),g.key=l.C0.ESC+r,g.cancel=!0}}else a.keyCode>=65&&a.keyCode<=90?g.key=String.fromCharCode(a.keyCode-64):a.keyCode===32?g.key=l.C0.NUL:a.keyCode>=51&&a.keyCode<=55?g.key=String.fromCharCode(a.keyCode-51+27):a.keyCode===56?g.key=l.C0.DEL:a.keyCode===219?g.key=l.C0.ESC:a.keyCode===220?g.key=l.C0.FS:a.keyCode===221&&(g.key=l.C0.GS)}return g}},482:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Utf8ToUtf32=s.StringToUtf32=s.utf32ToString=s.stringFromCodePoint=void 0,s.stringFromCodePoint=function(o){return o>65535?(o-=65536,String.fromCharCode(55296+(o>>10))+String.fromCharCode(o%1024+56320)):String.fromCharCode(o)},s.utf32ToString=function(o,l=0,c=o.length){let a="";for(let u=l;u<c;++u){let p=o[u];p>65535?(p-=65536,a+=String.fromCharCode(55296+(p>>10))+String.fromCharCode(p%1024+56320)):a+=String.fromCharCode(p)}return a},s.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(o,l){const c=o.length;if(!c)return 0;let a=0,u=0;if(this._interim){const p=o.charCodeAt(u++);56320<=p&&p<=57343?l[a++]=1024*(this._interim-55296)+p-56320+65536:(l[a++]=this._interim,l[a++]=p),this._interim=0}for(let p=u;p<c;++p){const m=o.charCodeAt(p);if(55296<=m&&m<=56319){if(++p>=c)return this._interim=m,a;const g=o.charCodeAt(p);56320<=g&&g<=57343?l[a++]=1024*(m-55296)+g-56320+65536:(l[a++]=m,l[a++]=g)}else m!==65279&&(l[a++]=m)}return a}},s.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(o,l){const c=o.length;if(!c)return 0;let a,u,p,m,g=0,e=0,n=0;if(this.interim[0]){let h=!1,f=this.interim[0];f&=(224&f)==192?31:(240&f)==224?15:7;let v,y=0;for(;(v=63&this.interim[++y])&&y<4;)f<<=6,f|=v;const d=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,S=d-y;for(;n<S;){if(n>=c)return 0;if(v=o[n++],(192&v)!=128){n--,h=!0;break}this.interim[y++]=v,f<<=6,f|=63&v}h||(d===2?f<128?n--:l[g++]=f:d===3?f<2048||f>=55296&&f<=57343||f===65279||(l[g++]=f):f<65536||f>1114111||(l[g++]=f)),this.interim.fill(0)}const i=c-4;let r=n;for(;r<c;){for(;!(!(r<i)||128&(a=o[r])||128&(u=o[r+1])||128&(p=o[r+2])||128&(m=o[r+3]));)l[g++]=a,l[g++]=u,l[g++]=p,l[g++]=m,r+=4;if(a=o[r++],a<128)l[g++]=a;else if((224&a)==192){if(r>=c)return this.interim[0]=a,g;if(u=o[r++],(192&u)!=128){r--;continue}if(e=(31&a)<<6|63&u,e<128){r--;continue}l[g++]=e}else if((240&a)==224){if(r>=c)return this.interim[0]=a,g;if(u=o[r++],(192&u)!=128){r--;continue}if(r>=c)return this.interim[0]=a,this.interim[1]=u,g;if(p=o[r++],(192&p)!=128){r--;continue}if(e=(15&a)<<12|(63&u)<<6|63&p,e<2048||e>=55296&&e<=57343||e===65279)continue;l[g++]=e}else if((248&a)==240){if(r>=c)return this.interim[0]=a,g;if(u=o[r++],(192&u)!=128){r--;continue}if(r>=c)return this.interim[0]=a,this.interim[1]=u,g;if(p=o[r++],(192&p)!=128){r--;continue}if(r>=c)return this.interim[0]=a,this.interim[1]=u,this.interim[2]=p,g;if(m=o[r++],(192&m)!=128){r--;continue}if(e=(7&a)<<18|(63&u)<<12|(63&p)<<6|63&m,e<65536||e>1114111)continue;l[g++]=e}}return g}}},225:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeV6=void 0;const o=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],l=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let c;s.UnicodeV6=class{constructor(){if(this.version="6",!c){c=new Uint8Array(65536),c.fill(1),c[0]=0,c.fill(0,1,32),c.fill(0,127,160),c.fill(2,4352,4448),c[9001]=2,c[9002]=2,c.fill(2,11904,42192),c[12351]=1,c.fill(2,44032,55204),c.fill(2,63744,64256),c.fill(2,65040,65050),c.fill(2,65072,65136),c.fill(2,65280,65377),c.fill(2,65504,65511);for(let a=0;a<o.length;++a)c.fill(0,o[a][0],o[a][1]+1)}}wcwidth(a){return a<32?0:a<127?1:a<65536?c[a]:function(u,p){let m,g=0,e=p.length-1;if(u<p[0][0]||u>p[e][1])return!1;for(;e>=g;)if(m=g+e>>1,u>p[m][1])g=m+1;else{if(!(u<p[m][0]))return!0;e=m-1}return!1}(a,l)?0:a>=131072&&a<=196605||a>=196608&&a<=262141?2:1}}},5981:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WriteBuffer=void 0;const l=o(8460),c=o(844);class a extends c.Disposable{constructor(p){super(),this._action=p,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new l.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(p,m){if(m!==void 0&&this._syncCalls>m)return void(this._syncCalls=0);if(this._pendingData+=p.length,this._writeBuffer.push(p),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let g;for(this._isSyncWriting=!0;g=this._writeBuffer.shift();){this._action(g);const e=this._callbacks.shift();e&&e()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(p,m){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=p.length,this._writeBuffer.push(p),this._callbacks.push(m),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=p.length,this._writeBuffer.push(p),this._callbacks.push(m)}_innerWrite(p=0,m=!0){const g=p||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const e=this._writeBuffer[this._bufferOffset],n=this._action(e,m);if(n){const r=h=>Date.now()-g>=12?setTimeout(()=>this._innerWrite(0,h)):this._innerWrite(g,h);return void n.catch(h=>(queueMicrotask(()=>{throw h}),Promise.resolve(!1))).then(r)}const i=this._callbacks[this._bufferOffset];if(i&&i(),this._bufferOffset++,this._pendingData-=e.length,Date.now()-g>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}s.WriteBuffer=a},5941:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.toRgbString=s.parseColor=void 0;const o=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,l=/^[\da-f]+$/;function c(a,u){const p=a.toString(16),m=p.length<2?"0"+p:p;switch(u){case 4:return p[0];case 8:return m;case 12:return(m+m).slice(0,3);default:return m+m}}s.parseColor=function(a){if(!a)return;let u=a.toLowerCase();if(u.indexOf("rgb:")===0){u=u.slice(4);const p=o.exec(u);if(p){const m=p[1]?15:p[4]?255:p[7]?4095:65535;return[Math.round(parseInt(p[1]||p[4]||p[7]||p[10],16)/m*255),Math.round(parseInt(p[2]||p[5]||p[8]||p[11],16)/m*255),Math.round(parseInt(p[3]||p[6]||p[9]||p[12],16)/m*255)]}}else if(u.indexOf("#")===0&&(u=u.slice(1),l.exec(u)&&[3,6,9,12].includes(u.length))){const p=u.length/3,m=[0,0,0];for(let g=0;g<3;++g){const e=parseInt(u.slice(p*g,p*g+p),16);m[g]=p===1?e<<4:p===2?e:p===3?e>>4:e>>8}return m}},s.toRgbString=function(a,u=16){const[p,m,g]=a;return`rgb:${c(p,u)}/${c(m,u)}/${c(g,u)}`}},5770:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.PAYLOAD_LIMIT=void 0,s.PAYLOAD_LIMIT=1e7},6351:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DcsHandler=s.DcsParser=void 0;const l=o(482),c=o(8742),a=o(5770),u=[];s.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=u,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=u}registerHandler(m,g){this._handlers[m]===void 0&&(this._handlers[m]=[]);const e=this._handlers[m];return e.push(g),{dispose:()=>{const n=e.indexOf(g);n!==-1&&e.splice(n,1)}}}clearHandler(m){this._handlers[m]&&delete this._handlers[m]}setHandlerFallback(m){this._handlerFb=m}reset(){if(this._active.length)for(let m=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;m>=0;--m)this._active[m].unhook(!1);this._stack.paused=!1,this._active=u,this._ident=0}hook(m,g){if(this.reset(),this._ident=m,this._active=this._handlers[m]||u,this._active.length)for(let e=this._active.length-1;e>=0;e--)this._active[e].hook(g);else this._handlerFb(this._ident,"HOOK",g)}put(m,g,e){if(this._active.length)for(let n=this._active.length-1;n>=0;n--)this._active[n].put(m,g,e);else this._handlerFb(this._ident,"PUT",(0,l.utf32ToString)(m,g,e))}unhook(m,g=!0){if(this._active.length){let e=!1,n=this._active.length-1,i=!1;if(this._stack.paused&&(n=this._stack.loopPosition-1,e=g,i=this._stack.fallThrough,this._stack.paused=!1),!i&&e===!1){for(;n>=0&&(e=this._active[n].unhook(m),e!==!0);n--)if(e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=n,this._stack.fallThrough=!1,e;n--}for(;n>=0;n--)if(e=this._active[n].unhook(!1),e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=n,this._stack.fallThrough=!0,e}else this._handlerFb(this._ident,"UNHOOK",m);this._active=u,this._ident=0}};const p=new c.Params;p.addParam(0),s.DcsHandler=class{constructor(m){this._handler=m,this._data="",this._params=p,this._hitLimit=!1}hook(m){this._params=m.length>1||m.params[0]?m.clone():p,this._data="",this._hitLimit=!1}put(m,g,e){this._hitLimit||(this._data+=(0,l.utf32ToString)(m,g,e),this._data.length>a.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(m){let g=!1;if(this._hitLimit)g=!1;else if(m&&(g=this._handler(this._data,this._params),g instanceof Promise))return g.then(e=>(this._params=p,this._data="",this._hitLimit=!1,e));return this._params=p,this._data="",this._hitLimit=!1,g}}},2015:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.EscapeSequenceParser=s.VT500_TRANSITION_TABLE=s.TransitionTable=void 0;const l=o(844),c=o(8742),a=o(6242),u=o(6351);class p{constructor(n){this.table=new Uint8Array(n)}setDefault(n,i){this.table.fill(n<<4|i)}add(n,i,r,h){this.table[i<<8|n]=r<<4|h}addMany(n,i,r,h){for(let f=0;f<n.length;f++)this.table[i<<8|n[f]]=r<<4|h}}s.TransitionTable=p;const m=160;s.VT500_TRANSITION_TABLE=function(){const e=new p(4095),n=Array.apply(null,Array(256)).map((y,d)=>d),i=(y,d)=>n.slice(y,d),r=i(32,127),h=i(0,24);h.push(25),h.push.apply(h,i(28,32));const f=i(0,14);let v;for(v in e.setDefault(1,0),e.addMany(r,0,2,0),f)e.addMany([24,26,153,154],v,3,0),e.addMany(i(128,144),v,3,0),e.addMany(i(144,152),v,3,0),e.add(156,v,0,0),e.add(27,v,11,1),e.add(157,v,4,8),e.addMany([152,158,159],v,0,7),e.add(155,v,11,3),e.add(144,v,11,9);return e.addMany(h,0,3,0),e.addMany(h,1,3,1),e.add(127,1,0,1),e.addMany(h,8,0,8),e.addMany(h,3,3,3),e.add(127,3,0,3),e.addMany(h,4,3,4),e.add(127,4,0,4),e.addMany(h,6,3,6),e.addMany(h,5,3,5),e.add(127,5,0,5),e.addMany(h,2,3,2),e.add(127,2,0,2),e.add(93,1,4,8),e.addMany(r,8,5,8),e.add(127,8,5,8),e.addMany([156,27,24,26,7],8,6,0),e.addMany(i(28,32),8,0,8),e.addMany([88,94,95],1,0,7),e.addMany(r,7,0,7),e.addMany(h,7,0,7),e.add(156,7,0,0),e.add(127,7,0,7),e.add(91,1,11,3),e.addMany(i(64,127),3,7,0),e.addMany(i(48,60),3,8,4),e.addMany([60,61,62,63],3,9,4),e.addMany(i(48,60),4,8,4),e.addMany(i(64,127),4,7,0),e.addMany([60,61,62,63],4,0,6),e.addMany(i(32,64),6,0,6),e.add(127,6,0,6),e.addMany(i(64,127),6,0,0),e.addMany(i(32,48),3,9,5),e.addMany(i(32,48),5,9,5),e.addMany(i(48,64),5,0,6),e.addMany(i(64,127),5,7,0),e.addMany(i(32,48),4,9,5),e.addMany(i(32,48),1,9,2),e.addMany(i(32,48),2,9,2),e.addMany(i(48,127),2,10,0),e.addMany(i(48,80),1,10,0),e.addMany(i(81,88),1,10,0),e.addMany([89,90,92],1,10,0),e.addMany(i(96,127),1,10,0),e.add(80,1,11,9),e.addMany(h,9,0,9),e.add(127,9,0,9),e.addMany(i(28,32),9,0,9),e.addMany(i(32,48),9,9,12),e.addMany(i(48,60),9,8,10),e.addMany([60,61,62,63],9,9,10),e.addMany(h,11,0,11),e.addMany(i(32,128),11,0,11),e.addMany(i(28,32),11,0,11),e.addMany(h,10,0,10),e.add(127,10,0,10),e.addMany(i(28,32),10,0,10),e.addMany(i(48,60),10,8,10),e.addMany([60,61,62,63],10,0,11),e.addMany(i(32,48),10,9,12),e.addMany(h,12,0,12),e.add(127,12,0,12),e.addMany(i(28,32),12,0,12),e.addMany(i(32,48),12,9,12),e.addMany(i(48,64),12,0,11),e.addMany(i(64,127),12,12,13),e.addMany(i(64,127),10,12,13),e.addMany(i(64,127),9,12,13),e.addMany(h,13,13,13),e.addMany(r,13,13,13),e.add(127,13,0,13),e.addMany([27,156,24,26],13,14,0),e.add(m,0,2,0),e.add(m,8,5,8),e.add(m,6,0,6),e.add(m,11,0,11),e.add(m,13,13,13),e}();class g extends l.Disposable{constructor(n=s.VT500_TRANSITION_TABLE){super(),this._transitions=n,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new c.Params,this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._printHandlerFb=(i,r,h)=>{},this._executeHandlerFb=i=>{},this._csiHandlerFb=(i,r)=>{},this._escHandlerFb=i=>{},this._errorHandlerFb=i=>i,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,l.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new a.OscParser),this._dcsParser=this.register(new u.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(n,i=[64,126]){let r=0;if(n.prefix){if(n.prefix.length>1)throw new Error("only one byte as prefix supported");if(r=n.prefix.charCodeAt(0),r&&60>r||r>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(n.intermediates){if(n.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let f=0;f<n.intermediates.length;++f){const v=n.intermediates.charCodeAt(f);if(32>v||v>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");r<<=8,r|=v}}if(n.final.length!==1)throw new Error("final must be a single byte");const h=n.final.charCodeAt(0);if(i[0]>h||h>i[1])throw new Error(`final must be in range ${i[0]} .. ${i[1]}`);return r<<=8,r|=h,r}identToString(n){const i=[];for(;n;)i.push(String.fromCharCode(255&n)),n>>=8;return i.reverse().join("")}setPrintHandler(n){this._printHandler=n}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(n,i){const r=this._identifier(n,[48,126]);this._escHandlers[r]===void 0&&(this._escHandlers[r]=[]);const h=this._escHandlers[r];return h.push(i),{dispose:()=>{const f=h.indexOf(i);f!==-1&&h.splice(f,1)}}}clearEscHandler(n){this._escHandlers[this._identifier(n,[48,126])]&&delete this._escHandlers[this._identifier(n,[48,126])]}setEscHandlerFallback(n){this._escHandlerFb=n}setExecuteHandler(n,i){this._executeHandlers[n.charCodeAt(0)]=i}clearExecuteHandler(n){this._executeHandlers[n.charCodeAt(0)]&&delete this._executeHandlers[n.charCodeAt(0)]}setExecuteHandlerFallback(n){this._executeHandlerFb=n}registerCsiHandler(n,i){const r=this._identifier(n);this._csiHandlers[r]===void 0&&(this._csiHandlers[r]=[]);const h=this._csiHandlers[r];return h.push(i),{dispose:()=>{const f=h.indexOf(i);f!==-1&&h.splice(f,1)}}}clearCsiHandler(n){this._csiHandlers[this._identifier(n)]&&delete this._csiHandlers[this._identifier(n)]}setCsiHandlerFallback(n){this._csiHandlerFb=n}registerDcsHandler(n,i){return this._dcsParser.registerHandler(this._identifier(n),i)}clearDcsHandler(n){this._dcsParser.clearHandler(this._identifier(n))}setDcsHandlerFallback(n){this._dcsParser.setHandlerFallback(n)}registerOscHandler(n,i){return this._oscParser.registerHandler(n,i)}clearOscHandler(n){this._oscParser.clearHandler(n)}setOscHandlerFallback(n){this._oscParser.setHandlerFallback(n)}setErrorHandler(n){this._errorHandler=n}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(n,i,r,h,f){this._parseStack.state=n,this._parseStack.handlers=i,this._parseStack.handlerPos=r,this._parseStack.transition=h,this._parseStack.chunkPos=f}parse(n,i,r){let h,f=0,v=0,y=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,y=this._parseStack.chunkPos+1;else{if(r===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const d=this._parseStack.handlers;let S=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(r===!1&&S>-1){for(;S>=0&&(h=d[S](this._params),h!==!0);S--)if(h instanceof Promise)return this._parseStack.handlerPos=S,h}this._parseStack.handlers=[];break;case 4:if(r===!1&&S>-1){for(;S>=0&&(h=d[S](),h!==!0);S--)if(h instanceof Promise)return this._parseStack.handlerPos=S,h}this._parseStack.handlers=[];break;case 6:if(f=n[this._parseStack.chunkPos],h=this._dcsParser.unhook(f!==24&&f!==26,r),h)return h;f===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(f=n[this._parseStack.chunkPos],h=this._oscParser.end(f!==24&&f!==26,r),h)return h;f===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,y=this._parseStack.chunkPos+1,this.precedingCodepoint=0,this.currentState=15&this._parseStack.transition}for(let d=y;d<i;++d){switch(f=n[d],v=this._transitions.table[this.currentState<<8|(f<160?f:m)],v>>4){case 2:for(let B=d+1;;++B){if(B>=i||(f=n[B])<32||f>126&&f<m){this._printHandler(n,d,B),d=B-1;break}if(++B>=i||(f=n[B])<32||f>126&&f<m){this._printHandler(n,d,B),d=B-1;break}if(++B>=i||(f=n[B])<32||f>126&&f<m){this._printHandler(n,d,B),d=B-1;break}if(++B>=i||(f=n[B])<32||f>126&&f<m){this._printHandler(n,d,B),d=B-1;break}}break;case 3:this._executeHandlers[f]?this._executeHandlers[f]():this._executeHandlerFb(f),this.precedingCodepoint=0;break;case 0:break;case 1:if(this._errorHandler({position:d,code:f,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const S=this._csiHandlers[this._collect<<8|f];let A=S?S.length-1:-1;for(;A>=0&&(h=S[A](this._params),h!==!0);A--)if(h instanceof Promise)return this._preserveStack(3,S,A,v,d),h;A<0&&this._csiHandlerFb(this._collect<<8|f,this._params),this.precedingCodepoint=0;break;case 8:do switch(f){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(f-48)}while(++d<i&&(f=n[d])>47&&f<60);d--;break;case 9:this._collect<<=8,this._collect|=f;break;case 10:const I=this._escHandlers[this._collect<<8|f];let R=I?I.length-1:-1;for(;R>=0&&(h=I[R](),h!==!0);R--)if(h instanceof Promise)return this._preserveStack(4,I,R,v,d),h;R<0&&this._escHandlerFb(this._collect<<8|f),this.precedingCodepoint=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|f,this._params);break;case 13:for(let B=d+1;;++B)if(B>=i||(f=n[B])===24||f===26||f===27||f>127&&f<m){this._dcsParser.put(n,d,B),d=B-1;break}break;case 14:if(h=this._dcsParser.unhook(f!==24&&f!==26),h)return this._preserveStack(6,[],0,v,d),h;f===27&&(v|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0;break;case 4:this._oscParser.start();break;case 5:for(let B=d+1;;B++)if(B>=i||(f=n[B])<32||f>127&&f<m){this._oscParser.put(n,d,B),d=B-1;break}break;case 6:if(h=this._oscParser.end(f!==24&&f!==26),h)return this._preserveStack(5,[],0,v,d),h;f===27&&(v|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0}this.currentState=15&v}}}s.EscapeSequenceParser=g},6242:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.OscHandler=s.OscParser=void 0;const l=o(5770),c=o(482),a=[];s.OscParser=class{constructor(){this._state=0,this._active=a,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(u,p){this._handlers[u]===void 0&&(this._handlers[u]=[]);const m=this._handlers[u];return m.push(p),{dispose:()=>{const g=m.indexOf(p);g!==-1&&m.splice(g,1)}}}clearHandler(u){this._handlers[u]&&delete this._handlers[u]}setHandlerFallback(u){this._handlerFb=u}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=a}reset(){if(this._state===2)for(let u=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;u>=0;--u)this._active[u].end(!1);this._stack.paused=!1,this._active=a,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||a,this._active.length)for(let u=this._active.length-1;u>=0;u--)this._active[u].start();else this._handlerFb(this._id,"START")}_put(u,p,m){if(this._active.length)for(let g=this._active.length-1;g>=0;g--)this._active[g].put(u,p,m);else this._handlerFb(this._id,"PUT",(0,c.utf32ToString)(u,p,m))}start(){this.reset(),this._state=1}put(u,p,m){if(this._state!==3){if(this._state===1)for(;p<m;){const g=u[p++];if(g===59){this._state=2,this._start();break}if(g<48||57<g)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+g-48}this._state===2&&m-p>0&&this._put(u,p,m)}}end(u,p=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let m=!1,g=this._active.length-1,e=!1;if(this._stack.paused&&(g=this._stack.loopPosition-1,m=p,e=this._stack.fallThrough,this._stack.paused=!1),!e&&m===!1){for(;g>=0&&(m=this._active[g].end(u),m!==!0);g--)if(m instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=g,this._stack.fallThrough=!1,m;g--}for(;g>=0;g--)if(m=this._active[g].end(!1),m instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=g,this._stack.fallThrough=!0,m}else this._handlerFb(this._id,"END",u);this._active=a,this._id=-1,this._state=0}}},s.OscHandler=class{constructor(u){this._handler=u,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(u,p,m){this._hitLimit||(this._data+=(0,c.utf32ToString)(u,p,m),this._data.length>l.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(u){let p=!1;if(this._hitLimit)p=!1;else if(u&&(p=this._handler(this._data),p instanceof Promise))return p.then(m=>(this._data="",this._hitLimit=!1,m));return this._data="",this._hitLimit=!1,p}}},8742:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Params=void 0;const o=2147483647;class l{static fromArray(a){const u=new l;if(!a.length)return u;for(let p=Array.isArray(a[0])?1:0;p<a.length;++p){const m=a[p];if(Array.isArray(m))for(let g=0;g<m.length;++g)u.addSubParam(m[g]);else u.addParam(m)}return u}constructor(a=32,u=32){if(this.maxLength=a,this.maxSubParamsLength=u,u>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(a),this.length=0,this._subParams=new Int32Array(u),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(a),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const a=new l(this.maxLength,this.maxSubParamsLength);return a.params.set(this.params),a.length=this.length,a._subParams.set(this._subParams),a._subParamsLength=this._subParamsLength,a._subParamsIdx.set(this._subParamsIdx),a._rejectDigits=this._rejectDigits,a._rejectSubDigits=this._rejectSubDigits,a._digitIsSub=this._digitIsSub,a}toArray(){const a=[];for(let u=0;u<this.length;++u){a.push(this.params[u]);const p=this._subParamsIdx[u]>>8,m=255&this._subParamsIdx[u];m-p>0&&a.push(Array.prototype.slice.call(this._subParams,p,m))}return a}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(a){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(a<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=a>o?o:a}}addSubParam(a){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(a<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=a>o?o:a,this._subParamsIdx[this.length-1]++}}hasSubParams(a){return(255&this._subParamsIdx[a])-(this._subParamsIdx[a]>>8)>0}getSubParams(a){const u=this._subParamsIdx[a]>>8,p=255&this._subParamsIdx[a];return p-u>0?this._subParams.subarray(u,p):null}getSubParamsAll(){const a={};for(let u=0;u<this.length;++u){const p=this._subParamsIdx[u]>>8,m=255&this._subParamsIdx[u];m-p>0&&(a[u]=this._subParams.slice(p,m))}return a}addDigit(a){let u;if(this._rejectDigits||!(u=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const p=this._digitIsSub?this._subParams:this.params,m=p[u-1];p[u-1]=~m?Math.min(10*m+a,o):a}}s.Params=l},5741:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.AddonManager=void 0,s.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let o=this._addons.length-1;o>=0;o--)this._addons[o].instance.dispose()}loadAddon(o,l){const c={instance:l,dispose:l.dispose,isDisposed:!1};this._addons.push(c),l.dispose=()=>this._wrappedAddonDispose(c),l.activate(o)}_wrappedAddonDispose(o){if(o.isDisposed)return;let l=-1;for(let c=0;c<this._addons.length;c++)if(this._addons[c]===o){l=c;break}if(l===-1)throw new Error("Could not dispose an addon that has not been loaded");o.isDisposed=!0,o.dispose.apply(o.instance),this._addons.splice(l,1)}}},8771:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferApiView=void 0;const l=o(3785),c=o(511);s.BufferApiView=class{constructor(a,u){this._buffer=a,this.type=u}init(a){return this._buffer=a,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(a){const u=this._buffer.lines.get(a);if(u)return new l.BufferLineApiView(u)}getNullCell(){return new c.CellData}}},3785:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferLineApiView=void 0;const l=o(511);s.BufferLineApiView=class{constructor(c){this._line=c}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(c,a){if(!(c<0||c>=this._line.length))return a?(this._line.loadCell(c,a),a):this._line.loadCell(c,new l.CellData)}translateToString(c,a,u){return this._line.translateToString(c,a,u)}}},8285:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferNamespaceApi=void 0;const l=o(8771),c=o(8460),a=o(844);class u extends a.Disposable{constructor(m){super(),this._core=m,this._onBufferChange=this.register(new c.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new l.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new l.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}s.BufferNamespaceApi=u},7975:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ParserApi=void 0,s.ParserApi=class{constructor(o){this._core=o}registerCsiHandler(o,l){return this._core.registerCsiHandler(o,c=>l(c.toArray()))}addCsiHandler(o,l){return this.registerCsiHandler(o,l)}registerDcsHandler(o,l){return this._core.registerDcsHandler(o,(c,a)=>l(c,a.toArray()))}addDcsHandler(o,l){return this.registerDcsHandler(o,l)}registerEscHandler(o,l){return this._core.registerEscHandler(o,l)}addEscHandler(o,l){return this.registerEscHandler(o,l)}registerOscHandler(o,l){return this._core.registerOscHandler(o,l)}addOscHandler(o,l){return this.registerOscHandler(o,l)}}},7090:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeApi=void 0,s.UnicodeApi=class{constructor(o){this._core=o}register(o){this._core.unicodeService.register(o)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(o){this._core.unicodeService.activeVersion=o}}},744:function(L,s,o){var l=this&&this.__decorate||function(e,n,i,r){var h,f=arguments.length,v=f<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,n,i,r);else for(var y=e.length-1;y>=0;y--)(h=e[y])&&(v=(f<3?h(v):f>3?h(n,i,v):h(n,i))||v);return f>3&&v&&Object.defineProperty(n,i,v),v},c=this&&this.__param||function(e,n){return function(i,r){n(i,r,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.BufferService=s.MINIMUM_ROWS=s.MINIMUM_COLS=void 0;const a=o(8460),u=o(844),p=o(5295),m=o(2585);s.MINIMUM_COLS=2,s.MINIMUM_ROWS=1;let g=s.BufferService=class extends u.Disposable{get buffer(){return this.buffers.active}constructor(e){super(),this.isUserScrolling=!1,this._onResize=this.register(new a.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new a.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(e.rawOptions.cols||0,s.MINIMUM_COLS),this.rows=Math.max(e.rawOptions.rows||0,s.MINIMUM_ROWS),this.buffers=this.register(new p.BufferSet(e,this))}resize(e,n){this.cols=e,this.rows=n,this.buffers.resize(e,n),this._onResize.fire({cols:e,rows:n})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(e,n=!1){const i=this.buffer;let r;r=this._cachedBlankLine,r&&r.length===this.cols&&r.getFg(0)===e.fg&&r.getBg(0)===e.bg||(r=i.getBlankLine(e,n),this._cachedBlankLine=r),r.isWrapped=n;const h=i.ybase+i.scrollTop,f=i.ybase+i.scrollBottom;if(i.scrollTop===0){const v=i.lines.isFull;f===i.lines.length-1?v?i.lines.recycle().copyFrom(r):i.lines.push(r.clone()):i.lines.splice(f+1,0,r.clone()),v?this.isUserScrolling&&(i.ydisp=Math.max(i.ydisp-1,0)):(i.ybase++,this.isUserScrolling||i.ydisp++)}else{const v=f-h+1;i.lines.shiftElements(h+1,v-1,-1),i.lines.set(f,r.clone())}this.isUserScrolling||(i.ydisp=i.ybase),this._onScroll.fire(i.ydisp)}scrollLines(e,n,i){const r=this.buffer;if(e<0){if(r.ydisp===0)return;this.isUserScrolling=!0}else e+r.ydisp>=r.ybase&&(this.isUserScrolling=!1);const h=r.ydisp;r.ydisp=Math.max(Math.min(r.ydisp+e,r.ybase),0),h!==r.ydisp&&(n||this._onScroll.fire(r.ydisp))}};s.BufferService=g=l([c(0,m.IOptionsService)],g)},7994:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CharsetService=void 0,s.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(o){this.glevel=o,this.charset=this._charsets[o]}setgCharset(o,l){this._charsets[o]=l,this.glevel===o&&(this.charset=l)}}},1753:function(L,s,o){var l=this&&this.__decorate||function(r,h,f,v){var y,d=arguments.length,S=d<3?h:v===null?v=Object.getOwnPropertyDescriptor(h,f):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(r,h,f,v);else for(var A=r.length-1;A>=0;A--)(y=r[A])&&(S=(d<3?y(S):d>3?y(h,f,S):y(h,f))||S);return d>3&&S&&Object.defineProperty(h,f,S),S},c=this&&this.__param||function(r,h){return function(f,v){h(f,v,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CoreMouseService=void 0;const a=o(2585),u=o(8460),p=o(844),m={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:r=>r.button!==4&&r.action===1&&(r.ctrl=!1,r.alt=!1,r.shift=!1,!0)},VT200:{events:19,restrict:r=>r.action!==32},DRAG:{events:23,restrict:r=>r.action!==32||r.button!==3},ANY:{events:31,restrict:r=>!0}};function g(r,h){let f=(r.ctrl?16:0)|(r.shift?4:0)|(r.alt?8:0);return r.button===4?(f|=64,f|=r.action):(f|=3&r.button,4&r.button&&(f|=64),8&r.button&&(f|=128),r.action===32?f|=32:r.action!==0||h||(f|=3)),f}const e=String.fromCharCode,n={DEFAULT:r=>{const h=[g(r,!1)+32,r.col+32,r.row+32];return h[0]>255||h[1]>255||h[2]>255?"":`\x1B[M${e(h[0])}${e(h[1])}${e(h[2])}`},SGR:r=>{const h=r.action===0&&r.button!==4?"m":"M";return`\x1B[<${g(r,!0)};${r.col};${r.row}${h}`},SGR_PIXELS:r=>{const h=r.action===0&&r.button!==4?"m":"M";return`\x1B[<${g(r,!0)};${r.x};${r.y}${h}`}};let i=s.CoreMouseService=class extends p.Disposable{constructor(r,h){super(),this._bufferService=r,this._coreService=h,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new u.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const f of Object.keys(m))this.addProtocol(f,m[f]);for(const f of Object.keys(n))this.addEncoding(f,n[f]);this.reset()}addProtocol(r,h){this._protocols[r]=h}addEncoding(r,h){this._encodings[r]=h}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(r){if(!this._protocols[r])throw new Error(`unknown protocol "${r}"`);this._activeProtocol=r,this._onProtocolChange.fire(this._protocols[r].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(r){if(!this._encodings[r])throw new Error(`unknown encoding "${r}"`);this._activeEncoding=r}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(r){if(r.col<0||r.col>=this._bufferService.cols||r.row<0||r.row>=this._bufferService.rows||r.button===4&&r.action===32||r.button===3&&r.action!==32||r.button!==4&&(r.action===2||r.action===3)||(r.col++,r.row++,r.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,r,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(r))return!1;const h=this._encodings[this._activeEncoding](r);return h&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(h):this._coreService.triggerDataEvent(h,!0)),this._lastEvent=r,!0}explainEvents(r){return{down:!!(1&r),up:!!(2&r),drag:!!(4&r),move:!!(8&r),wheel:!!(16&r)}}_equalEvents(r,h,f){if(f){if(r.x!==h.x||r.y!==h.y)return!1}else if(r.col!==h.col||r.row!==h.row)return!1;return r.button===h.button&&r.action===h.action&&r.ctrl===h.ctrl&&r.alt===h.alt&&r.shift===h.shift}};s.CoreMouseService=i=l([c(0,a.IBufferService),c(1,a.ICoreService)],i)},6975:function(L,s,o){var l=this&&this.__decorate||function(i,r,h,f){var v,y=arguments.length,d=y<3?r:f===null?f=Object.getOwnPropertyDescriptor(r,h):f;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")d=Reflect.decorate(i,r,h,f);else for(var S=i.length-1;S>=0;S--)(v=i[S])&&(d=(y<3?v(d):y>3?v(r,h,d):v(r,h))||d);return y>3&&d&&Object.defineProperty(r,h,d),d},c=this&&this.__param||function(i,r){return function(h,f){r(h,f,i)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CoreService=void 0;const a=o(1439),u=o(8460),p=o(844),m=o(2585),g=Object.freeze({insertMode:!1}),e=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let n=s.CoreService=class extends p.Disposable{constructor(i,r,h){super(),this._bufferService=i,this._logService=r,this._optionsService=h,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new u.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new u.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new u.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new u.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,a.clone)(g),this.decPrivateModes=(0,a.clone)(e)}reset(){this.modes=(0,a.clone)(g),this.decPrivateModes=(0,a.clone)(e)}triggerDataEvent(i,r=!1){if(this._optionsService.rawOptions.disableStdin)return;const h=this._bufferService.buffer;r&&this._optionsService.rawOptions.scrollOnUserInput&&h.ybase!==h.ydisp&&this._onRequestScrollToBottom.fire(),r&&this._onUserInput.fire(),this._logService.debug(`sending data "${i}"`,()=>i.split("").map(f=>f.charCodeAt(0))),this._onData.fire(i)}triggerBinaryEvent(i){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${i}"`,()=>i.split("").map(r=>r.charCodeAt(0))),this._onBinary.fire(i))}};s.CoreService=n=l([c(0,m.IBufferService),c(1,m.ILogService),c(2,m.IOptionsService)],n)},9074:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DecorationService=void 0;const l=o(8055),c=o(8460),a=o(844),u=o(6106);let p=0,m=0;class g extends a.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new u.SortedList(i=>i==null?void 0:i.marker.line),this._onDecorationRegistered=this.register(new c.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new c.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,a.toDisposable)(()=>this.reset()))}registerDecoration(i){if(i.marker.isDisposed)return;const r=new e(i);if(r){const h=r.marker.onDispose(()=>r.dispose());r.onDispose(()=>{r&&(this._decorations.delete(r)&&this._onDecorationRemoved.fire(r),h.dispose())}),this._decorations.insert(r),this._onDecorationRegistered.fire(r)}return r}reset(){for(const i of this._decorations.values())i.dispose();this._decorations.clear()}*getDecorationsAtCell(i,r,h){var f,v,y;let d=0,S=0;for(const A of this._decorations.getKeyIterator(r))d=(f=A.options.x)!==null&&f!==void 0?f:0,S=d+((v=A.options.width)!==null&&v!==void 0?v:1),i>=d&&i<S&&(!h||((y=A.options.layer)!==null&&y!==void 0?y:"bottom")===h)&&(yield A)}forEachDecorationAtCell(i,r,h,f){this._decorations.forEachByKey(r,v=>{var y,d,S;p=(y=v.options.x)!==null&&y!==void 0?y:0,m=p+((d=v.options.width)!==null&&d!==void 0?d:1),i>=p&&i<m&&(!h||((S=v.options.layer)!==null&&S!==void 0?S:"bottom")===h)&&f(v)})}}s.DecorationService=g;class e extends a.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=l.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=l.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(i){super(),this.options=i,this.onRenderEmitter=this.register(new c.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new c.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=i.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.InstantiationService=s.ServiceCollection=void 0;const l=o(2585),c=o(8343);class a{constructor(...p){this._entries=new Map;for(const[m,g]of p)this.set(m,g)}set(p,m){const g=this._entries.get(p);return this._entries.set(p,m),g}forEach(p){for(const[m,g]of this._entries.entries())p(m,g)}has(p){return this._entries.has(p)}get(p){return this._entries.get(p)}}s.ServiceCollection=a,s.InstantiationService=class{constructor(){this._services=new a,this._services.set(l.IInstantiationService,this)}setService(u,p){this._services.set(u,p)}getService(u){return this._services.get(u)}createInstance(u,...p){const m=(0,c.getServiceDependencies)(u).sort((n,i)=>n.index-i.index),g=[];for(const n of m){const i=this._services.get(n.id);if(!i)throw new Error(`[createInstance] ${u.name} depends on UNKNOWN service ${n.id}.`);g.push(i)}const e=m.length>0?m[0].index:p.length;if(p.length!==e)throw new Error(`[createInstance] First service dependency of ${u.name} at position ${e+1} conflicts with ${p.length} static arguments`);return new u(...p,...g)}}},7866:function(L,s,o){var l=this&&this.__decorate||function(e,n,i,r){var h,f=arguments.length,v=f<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,n,i,r);else for(var y=e.length-1;y>=0;y--)(h=e[y])&&(v=(f<3?h(v):f>3?h(n,i,v):h(n,i))||v);return f>3&&v&&Object.defineProperty(n,i,v),v},c=this&&this.__param||function(e,n){return function(i,r){n(i,r,e)}};Object.defineProperty(s,"__esModule",{value:!0}),s.traceCall=s.setTraceLogger=s.LogService=void 0;const a=o(844),u=o(2585),p={trace:u.LogLevelEnum.TRACE,debug:u.LogLevelEnum.DEBUG,info:u.LogLevelEnum.INFO,warn:u.LogLevelEnum.WARN,error:u.LogLevelEnum.ERROR,off:u.LogLevelEnum.OFF};let m,g=s.LogService=class extends a.Disposable{get logLevel(){return this._logLevel}constructor(e){super(),this._optionsService=e,this._logLevel=u.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),m=this}_updateLogLevel(){this._logLevel=p[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(e){for(let n=0;n<e.length;n++)typeof e[n]=="function"&&(e[n]=e[n]())}_log(e,n,i){this._evalLazyOptionalParams(i),e.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+n,...i)}trace(e,...n){var i,r;this._logLevel<=u.LogLevelEnum.TRACE&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.trace.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.log,e,n)}debug(e,...n){var i,r;this._logLevel<=u.LogLevelEnum.DEBUG&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.debug.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.log,e,n)}info(e,...n){var i,r;this._logLevel<=u.LogLevelEnum.INFO&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.info.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.info,e,n)}warn(e,...n){var i,r;this._logLevel<=u.LogLevelEnum.WARN&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.warn.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.warn,e,n)}error(e,...n){var i,r;this._logLevel<=u.LogLevelEnum.ERROR&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.error.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.error,e,n)}};s.LogService=g=l([c(0,u.IOptionsService)],g),s.setTraceLogger=function(e){m=e},s.traceCall=function(e,n,i){if(typeof i.value!="function")throw new Error("not supported");const r=i.value;i.value=function(...h){if(m.logLevel!==u.LogLevelEnum.TRACE)return r.apply(this,h);m.trace(`GlyphRenderer#${r.name}(${h.map(v=>JSON.stringify(v)).join(", ")})`);const f=r.apply(this,h);return m.trace(`GlyphRenderer#${r.name} return`,f),f}}},7302:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.OptionsService=s.DEFAULT_OPTIONS=void 0;const l=o(8460),c=o(844),a=o(6114);s.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rightClickSelectsWord:a.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const u=["normal","bold","100","200","300","400","500","600","700","800","900"];class p extends c.Disposable{constructor(g){super(),this._onOptionChange=this.register(new l.EventEmitter),this.onOptionChange=this._onOptionChange.event;const e=Object.assign({},s.DEFAULT_OPTIONS);for(const n in g)if(n in e)try{const i=g[n];e[n]=this._sanitizeAndValidateOption(n,i)}catch(i){console.error(i)}this.rawOptions=e,this.options=Object.assign({},e),this._setupOptions()}onSpecificOptionChange(g,e){return this.onOptionChange(n=>{n===g&&e(this.rawOptions[g])})}onMultipleOptionChange(g,e){return this.onOptionChange(n=>{g.indexOf(n)!==-1&&e()})}_setupOptions(){const g=n=>{if(!(n in s.DEFAULT_OPTIONS))throw new Error(`No option with key "${n}"`);return this.rawOptions[n]},e=(n,i)=>{if(!(n in s.DEFAULT_OPTIONS))throw new Error(`No option with key "${n}"`);i=this._sanitizeAndValidateOption(n,i),this.rawOptions[n]!==i&&(this.rawOptions[n]=i,this._onOptionChange.fire(n))};for(const n in this.rawOptions){const i={get:g.bind(this,n),set:e.bind(this,n)};Object.defineProperty(this.options,n,i)}}_sanitizeAndValidateOption(g,e){switch(g){case"cursorStyle":if(e||(e=s.DEFAULT_OPTIONS[g]),!function(n){return n==="block"||n==="underline"||n==="bar"}(e))throw new Error(`"${e}" is not a valid value for ${g}`);break;case"wordSeparator":e||(e=s.DEFAULT_OPTIONS[g]);break;case"fontWeight":case"fontWeightBold":if(typeof e=="number"&&1<=e&&e<=1e3)break;e=u.includes(e)?e:s.DEFAULT_OPTIONS[g];break;case"cursorWidth":e=Math.floor(e);case"lineHeight":case"tabStopWidth":if(e<1)throw new Error(`${g} cannot be less than 1, value: ${e}`);break;case"minimumContrastRatio":e=Math.max(1,Math.min(21,Math.round(10*e)/10));break;case"scrollback":if((e=Math.min(e,4294967295))<0)throw new Error(`${g} cannot be less than 0, value: ${e}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(e<=0)throw new Error(`${g} cannot be less than or equal to 0, value: ${e}`);break;case"rows":case"cols":if(!e&&e!==0)throw new Error(`${g} must be numeric, value: ${e}`);break;case"windowsPty":e=e??{}}return e}}s.OptionsService=p},2660:function(L,s,o){var l=this&&this.__decorate||function(p,m,g,e){var n,i=arguments.length,r=i<3?m:e===null?e=Object.getOwnPropertyDescriptor(m,g):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(p,m,g,e);else for(var h=p.length-1;h>=0;h--)(n=p[h])&&(r=(i<3?n(r):i>3?n(m,g,r):n(m,g))||r);return i>3&&r&&Object.defineProperty(m,g,r),r},c=this&&this.__param||function(p,m){return function(g,e){m(g,e,p)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OscLinkService=void 0;const a=o(2585);let u=s.OscLinkService=class{constructor(p){this._bufferService=p,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(p){const m=this._bufferService.buffer;if(p.id===void 0){const h=m.addMarker(m.ybase+m.y),f={data:p,id:this._nextId++,lines:[h]};return h.onDispose(()=>this._removeMarkerFromLink(f,h)),this._dataByLinkId.set(f.id,f),f.id}const g=p,e=this._getEntryIdKey(g),n=this._entriesWithId.get(e);if(n)return this.addLineToLink(n.id,m.ybase+m.y),n.id;const i=m.addMarker(m.ybase+m.y),r={id:this._nextId++,key:this._getEntryIdKey(g),data:g,lines:[i]};return i.onDispose(()=>this._removeMarkerFromLink(r,i)),this._entriesWithId.set(r.key,r),this._dataByLinkId.set(r.id,r),r.id}addLineToLink(p,m){const g=this._dataByLinkId.get(p);if(g&&g.lines.every(e=>e.line!==m)){const e=this._bufferService.buffer.addMarker(m);g.lines.push(e),e.onDispose(()=>this._removeMarkerFromLink(g,e))}}getLinkData(p){var m;return(m=this._dataByLinkId.get(p))===null||m===void 0?void 0:m.data}_getEntryIdKey(p){return`${p.id};;${p.uri}`}_removeMarkerFromLink(p,m){const g=p.lines.indexOf(m);g!==-1&&(p.lines.splice(g,1),p.lines.length===0&&(p.data.id!==void 0&&this._entriesWithId.delete(p.key),this._dataByLinkId.delete(p.id)))}};s.OscLinkService=u=l([c(0,a.IBufferService)],u)},8343:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.createDecorator=s.getServiceDependencies=s.serviceRegistry=void 0;const o="di$target",l="di$dependencies";s.serviceRegistry=new Map,s.getServiceDependencies=function(c){return c[l]||[]},s.createDecorator=function(c){if(s.serviceRegistry.has(c))return s.serviceRegistry.get(c);const a=function(u,p,m){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(g,e,n){e[o]===e?e[l].push({id:g,index:n}):(e[l]=[{id:g,index:n}],e[o]=e)})(a,u,m)};return a.toString=()=>c,s.serviceRegistry.set(c,a),a}},2585:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.IDecorationService=s.IUnicodeService=s.IOscLinkService=s.IOptionsService=s.ILogService=s.LogLevelEnum=s.IInstantiationService=s.ICharsetService=s.ICoreService=s.ICoreMouseService=s.IBufferService=void 0;const l=o(8343);var c;s.IBufferService=(0,l.createDecorator)("BufferService"),s.ICoreMouseService=(0,l.createDecorator)("CoreMouseService"),s.ICoreService=(0,l.createDecorator)("CoreService"),s.ICharsetService=(0,l.createDecorator)("CharsetService"),s.IInstantiationService=(0,l.createDecorator)("InstantiationService"),function(a){a[a.TRACE=0]="TRACE",a[a.DEBUG=1]="DEBUG",a[a.INFO=2]="INFO",a[a.WARN=3]="WARN",a[a.ERROR=4]="ERROR",a[a.OFF=5]="OFF"}(c||(s.LogLevelEnum=c={})),s.ILogService=(0,l.createDecorator)("LogService"),s.IOptionsService=(0,l.createDecorator)("OptionsService"),s.IOscLinkService=(0,l.createDecorator)("OscLinkService"),s.IUnicodeService=(0,l.createDecorator)("UnicodeService"),s.IDecorationService=(0,l.createDecorator)("DecorationService")},1480:(L,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeService=void 0;const l=o(8460),c=o(225);s.UnicodeService=class{constructor(){this._providers=Object.create(null),this._active="",this._onChange=new l.EventEmitter,this.onChange=this._onChange.event;const a=new c.UnicodeV6;this.register(a),this._active=a.version,this._activeProvider=a}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(a){if(!this._providers[a])throw new Error(`unknown Unicode version "${a}"`);this._active=a,this._activeProvider=this._providers[a],this._onChange.fire(a)}register(a){this._providers[a.version]=a}wcwidth(a){return this._activeProvider.wcwidth(a)}getStringCellWidth(a){let u=0;const p=a.length;for(let m=0;m<p;++m){let g=a.charCodeAt(m);if(55296<=g&&g<=56319){if(++m>=p)return u+this.wcwidth(g);const e=a.charCodeAt(m);56320<=e&&e<=57343?g=1024*(g-55296)+e-56320+65536:u+=this.wcwidth(e)}u+=this.wcwidth(g)}return u}}}},b={};function C(L){var s=b[L];if(s!==void 0)return s.exports;var o=b[L]={exports:{}};return _[L].call(o.exports,o,o.exports,C),o.exports}var x={};return(()=>{var L=x;Object.defineProperty(L,"__esModule",{value:!0}),L.Terminal=void 0;const s=C(9042),o=C(3236),l=C(844),c=C(5741),a=C(8285),u=C(7975),p=C(7090),m=["cols","rows"];class g extends l.Disposable{constructor(n){super(),this._core=this.register(new o.Terminal(n)),this._addonManager=this.register(new c.AddonManager),this._publicOptions=Object.assign({},this._core.options);const i=h=>this._core.options[h],r=(h,f)=>{this._checkReadonlyOptions(h),this._core.options[h]=f};for(const h in this._core.options){const f={get:i.bind(this,h),set:r.bind(this,h)};Object.defineProperty(this._publicOptions,h,f)}}_checkReadonlyOptions(n){if(m.includes(n))throw new Error(`Option "${n}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new u.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new p.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new a.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const n=this._core.coreService.decPrivateModes;let i="none";switch(this._core.coreMouseService.activeProtocol){case"X10":i="x10";break;case"VT200":i="vt200";break;case"DRAG":i="drag";break;case"ANY":i="any"}return{applicationCursorKeysMode:n.applicationCursorKeys,applicationKeypadMode:n.applicationKeypad,bracketedPasteMode:n.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:i,originMode:n.origin,reverseWraparoundMode:n.reverseWraparound,sendFocusMode:n.sendFocus,wraparoundMode:n.wraparound}}get options(){return this._publicOptions}set options(n){for(const i in n)this._publicOptions[i]=n[i]}blur(){this._core.blur()}focus(){this._core.focus()}resize(n,i){this._verifyIntegers(n,i),this._core.resize(n,i)}open(n){this._core.open(n)}attachCustomKeyEventHandler(n){this._core.attachCustomKeyEventHandler(n)}registerLinkProvider(n){return this._core.registerLinkProvider(n)}registerCharacterJoiner(n){return this._checkProposedApi(),this._core.registerCharacterJoiner(n)}deregisterCharacterJoiner(n){this._checkProposedApi(),this._core.deregisterCharacterJoiner(n)}registerMarker(n=0){return this._verifyIntegers(n),this._core.registerMarker(n)}registerDecoration(n){var i,r,h;return this._checkProposedApi(),this._verifyPositiveIntegers((i=n.x)!==null&&i!==void 0?i:0,(r=n.width)!==null&&r!==void 0?r:0,(h=n.height)!==null&&h!==void 0?h:0),this._core.registerDecoration(n)}hasSelection(){return this._core.hasSelection()}select(n,i,r){this._verifyIntegers(n,i,r),this._core.select(n,i,r)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(n,i){this._verifyIntegers(n,i),this._core.selectLines(n,i)}dispose(){super.dispose()}scrollLines(n){this._verifyIntegers(n),this._core.scrollLines(n)}scrollPages(n){this._verifyIntegers(n),this._core.scrollPages(n)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(n){this._verifyIntegers(n),this._core.scrollToLine(n)}clear(){this._core.clear()}write(n,i){this._core.write(n,i)}writeln(n,i){this._core.write(n),this._core.write(`\r
`,i)}paste(n){this._core.paste(n)}refresh(n,i){this._verifyIntegers(n,i),this._core.refresh(n,i)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(n){this._addonManager.loadAddon(this,n)}static get strings(){return s}_verifyIntegers(...n){for(const i of n)if(i===1/0||isNaN(i)||i%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...n){for(const i of n)if(i&&(i===1/0||isNaN(i)||i%1!=0||i<0))throw new Error("This API only accepts positive integers")}}L.Terminal=g})(),x})())})(mt);var Ht=mt.exports,St={exports:{}};(function(E,t){(function(_,b){E.exports=b()})(self,()=>(()=>{var _={};return(()=>{var b=_;Object.defineProperty(b,"__esModule",{value:!0}),b.FitAddon=void 0,b.FitAddon=class{activate(C){this._terminal=C}dispose(){}fit(){const C=this.proposeDimensions();if(!C||!this._terminal||isNaN(C.cols)||isNaN(C.rows))return;const x=this._terminal._core;this._terminal.rows===C.rows&&this._terminal.cols===C.cols||(x._renderService.clear(),this._terminal.resize(C.cols,C.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const C=this._terminal._core,x=C._renderService.dimensions;if(x.css.cell.width===0||x.css.cell.height===0)return;const L=this._terminal.options.scrollback===0?0:C.viewport.scrollBarWidth,s=window.getComputedStyle(this._terminal.element.parentElement),o=parseInt(s.getPropertyValue("height")),l=Math.max(0,parseInt(s.getPropertyValue("width"))),c=window.getComputedStyle(this._terminal.element),a=o-(parseInt(c.getPropertyValue("padding-top"))+parseInt(c.getPropertyValue("padding-bottom"))),u=l-(parseInt(c.getPropertyValue("padding-right"))+parseInt(c.getPropertyValue("padding-left")))-L;return{cols:Math.max(2,Math.floor(u/x.css.cell.width)),rows:Math.max(1,Math.floor(a/x.css.cell.height))}}}})(),_})())})(St);var Ft=St.exports,bt={exports:{}};(function(E,t){(function(_,b){E.exports=b()})(self,()=>(()=>{var _={6:(L,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.LinkComputer=s.WebLinkProvider=void 0,s.WebLinkProvider=class{constructor(l,c,a,u={}){this._terminal=l,this._regex=c,this._handler=a,this._options=u}provideLinks(l,c){const a=o.computeLink(l,this._regex,this._terminal,this._handler);c(this._addCallbacks(a))}_addCallbacks(l){return l.map(c=>(c.leave=this._options.leave,c.hover=(a,u)=>{if(this._options.hover){const{range:p}=c;this._options.hover(a,u,p)}},c))}};class o{static computeLink(c,a,u,p){const m=new RegExp(a.source,(a.flags||"")+"g"),[g,e]=o._getWindowedLineStrings(c-1,u),n=g.join("");let i;const r=[];for(;i=m.exec(n);){const h=i[0];try{const A=new URL(h),I=decodeURI(A.toString());if(h!==I&&h+"/"!==I)continue}catch{continue}const[f,v]=o._mapStrIdx(u,e,0,i.index),[y,d]=o._mapStrIdx(u,f,v,h.length);if(f===-1||v===-1||y===-1||d===-1)continue;const S={start:{x:v+1,y:f+1},end:{x:d,y:y+1}};r.push({range:S,text:h,activate:p})}return r}static _getWindowedLineStrings(c,a){let u,p=c,m=c,g=0,e="";const n=[];if(u=a.buffer.active.getLine(c)){const i=u.translateToString(!0);if(u.isWrapped&&i[0]!==" "){for(g=0;(u=a.buffer.active.getLine(--p))&&g<2048&&(e=u.translateToString(!0),g+=e.length,n.push(e),u.isWrapped&&e.indexOf(" ")===-1););n.reverse()}for(n.push(i),g=0;(u=a.buffer.active.getLine(++m))&&u.isWrapped&&g<2048&&(e=u.translateToString(!0),g+=e.length,n.push(e),e.indexOf(" ")===-1););}return[n,p]}static _mapStrIdx(c,a,u,p){const m=c.buffer.active,g=m.getNullCell();let e=u;for(;p;){const n=m.getLine(a);if(!n)return[-1,-1];for(let i=e;i<n.length;++i){n.getCell(i,g);const r=g.getChars();if(g.getWidth()&&(p-=r.length||1,i===n.length-1&&r==="")){const h=m.getLine(a+1);h&&h.isWrapped&&(h.getCell(0,g),g.getWidth()===2&&(p+=1))}if(p<0)return[a,i]}a++,e=0}return[a,e]}}s.LinkComputer=o}},b={};function C(L){var s=b[L];if(s!==void 0)return s.exports;var o=b[L]={exports:{}};return _[L](o,o.exports,C),o.exports}var x={};return(()=>{var L=x;Object.defineProperty(L,"__esModule",{value:!0}),L.WebLinksAddon=void 0;const s=C(6),o=/https?:[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;function l(c,a){const u=window.open();if(u){try{u.opener=null}catch{}u.location.href=a}else console.warn("Opening link blocked as opener could not be cleared")}L.WebLinksAddon=class{constructor(c=l,a={}){this._handler=c,this._options=a}activate(c){this._terminal=c;const a=this._options,u=a.urlRegex||o;this._linkProvider=this._terminal.registerLinkProvider(new s.WebLinkProvider(this._terminal,u,this._handler,a))}dispose(){var c;(c=this._linkProvider)===null||c===void 0||c.dispose()}}})(),x})())})(bt);var $t=bt.exports;class zt{constructor(){this.terminal=null,this.socket=null,this.buffer=[],this.isReady=!1,this.debugLogging=!1}initialize(t,_){this.terminal=t,this.socket=_,this.isReady=!0,this.terminal.onData(b=>{this.buffer.push(b)}),console.log("XTermService initialized")}isServiceReady(){return this.isReady&&this.terminal&&this.socket&&this.socket.readyState===WebSocket.OPEN}async sendRawData(t){if(!this.isServiceReady())return console.error("XTermService not ready"),!1;try{return this.socket.send(JSON.stringify({type:"input",data:t})),!0}catch(_){return console.error("Failed to send data:",_),!1}}async sendKey(t){const b={Enter:"\r",Tab:"	",Backspace:"",Delete:"\x1B[3~",Escape:"\x1B",ArrowUp:"\x1B[A",ArrowDown:"\x1B[B",ArrowRight:"\x1B[C",ArrowLeft:"\x1B[D",Home:"\x1B[H",End:"\x1B[F",PageUp:"\x1B[5~",PageDown:"\x1B[6~",F1:"\x1BOP",F2:"\x1BOQ",F3:"\x1BOR",F4:"\x1BOS",F5:"\x1B[15~",F6:"\x1B[17~",F7:"\x1B[18~",F8:"\x1B[19~",F9:"\x1B[20~",F10:"\x1B[21~",F11:"\x1B[23~",F12:"\x1B[24~"}[t]||t;return await this.sendRawData(b)}async sendKeyCombo(t){const _=t.split("+").map(C=>C.trim());let b="";if(_.length===2){const[C,x]=_,L=x.toLowerCase();switch(C.toLowerCase()){case"ctrl":L>="a"&&L<="z"?b=String.fromCharCode(L.charCodeAt(0)-96):b={c:"",d:"",z:"",l:"\f",r:"",u:"",k:"\v",w:"",y:"",p:"",n:"",f:"",b:"",a:"",e:""}[L]||"";break;case"alt":b="\x1B"+x;break;case"shift":b=x.toUpperCase();break}}return b?await this.sendRawData(b):(console.error("Unsupported key combination:",t),!1)}async sendCommand(t,_=!0){let b=await this.sendRawData(t);return b&&_&&(b=await this.sendKey("Enter")),b}async sendCommands(t,_=100){for(const b of t){if(!await this.sendCommand(b))return console.error("Failed to send command:",b),!1;_>0&&await new Promise(x=>setTimeout(x,_))}return!0}getCurrentLineContent(){if(!this.terminal)return"";const t=this.terminal.buffer.active,_=t.cursorY,b=t.getLine(_);return b?b.translateToString(!0):""}getLineContent(t){if(!this.terminal)return"";const b=this.terminal.buffer.active.getLine(t);return b?b.translateToString(!0):""}getLineRangeContent(t,_){if(!this.terminal)return[];const b=this.terminal.buffer.active,C=[];for(let x=t;x<=_&&x<b.length;x++){const L=b.getLine(x);C.push(L?L.translateToString(!0):"")}return C}getAllContent(){if(this.debugLogging&&console.log("[XTermService] getAllContent called"),!this.terminal)return console.warn("[XTermService] Terminal not available"),[];const t=this.terminal.buffer.active;this.debugLogging&&console.log("[XTermService] Buffer info:",{length:t.length,baseY:t.baseY,cursorY:t.cursorY,cursorX:t.cursorX});const _=[];let b=0;for(let C=0;C<t.length;C++){const x=t.getLine(C),L=x?x.translateToString(!0):"";_.push(L),L.trim()!==""&&b++}return this.debugLogging&&console.log("[XTermService] Content retrieved:",{totalLines:_.length,nonEmptyLines:b,firstLine:_[0]||"(empty)",lastLine:_[_.length-1]||"(empty)",sampleLines:_.slice(0,5).map((C,x)=>`${x}: "${C}"`),totalCharacters:_.join("").length}),_}getAllContentAsString(t=`
`){return this.getAllContent().join(t)}async clearScreen(){return await this.sendKeyCombo("Ctrl+L")}getTerminalDimensions(){return this.terminal?{cols:this.terminal.cols,rows:this.terminal.rows}:{cols:0,rows:0}}getCursorPosition(){if(!this.terminal)return{x:0,y:0};const t=this.terminal.buffer.active;return{x:t.cursorX,y:t.cursorY}}setDebugLogging(t){this.debugLogging=t,console.log(`[XTermService] Debug logging ${t?"enabled":"disabled"}`)}}const ie=new zt;class Wt{constructor(){this.dialog=null,this.headerElement=null,this.closeBtn=null,this.minimizeBtn=null,this.maximizeBtn=null,this.aiBtn=null,this.pauseResumeBtn=null,this.pauseIcon=null,this.playIcon=null,this.isVisible=!1,this.isMinimized=!1,this.isMaximized=!1,this.isDragging=!1,this.dragOffset={x:0,y:0},this.originalPosition={top:20,right:20},this.originalSize={width:400,height:500},this.onShow=null,this.onHide=null,this.onMinimize=null,this.onMaximize=null,this.onPauseResume=null}initialize(){return this.dialog=document.getElementById("aiDialog"),this.headerElement=document.getElementById("aiDialogHeader"),this.closeBtn=document.getElementById("aiDialogClose"),this.minimizeBtn=document.getElementById("aiDialogMinimize"),this.maximizeBtn=document.getElementById("aiDialogMaximize"),this.aiBtn=document.getElementById("aiBtn"),this.pauseResumeBtn=document.getElementById("aiPauseResume"),this.pauseIcon=document.getElementById("pauseIcon"),this.playIcon=document.getElementById("playIcon"),!this.dialog||!this.headerElement||!this.closeBtn||!this.minimizeBtn||!this.maximizeBtn||!this.aiBtn||!this.pauseResumeBtn||!this.pauseIcon||!this.playIcon?(console.error("Dialog UI elements not found"),!1):(this.setupEventListeners(),console.log("Dialog UI Manager initialized"),!0)}setupEventListeners(){this.aiBtn.addEventListener("click",()=>this.toggleDialog()),this.closeBtn.addEventListener("click",()=>this.hideDialog()),this.minimizeBtn.addEventListener("click",()=>this.toggleMinimize()),this.maximizeBtn.addEventListener("click",()=>this.toggleMaximize()),this.pauseResumeBtn.addEventListener("click",()=>this.togglePauseResume()),this.setupDragging(),document.addEventListener("keydown",t=>{t.key==="Escape"&&this.isVisible&&this.hideDialog()})}toggleDialog(){this.isVisible?this.hideDialog():this.showDialog()}showDialog(){this.isVisible=!0,this.dialog.classList.remove("ai-dialog-hidden"),this.onShow&&this.onShow(),console.log("AI Dialog shown")}hideDialog(){this.isVisible=!1,this.dialog.classList.add("ai-dialog-hidden"),this.onHide&&this.onHide(),console.log("AI Dialog hidden")}toggleMinimize(){this.isMinimized=!this.isMinimized,this.isMinimized?(this.dialog.classList.add("minimized"),this.minimizeBtn.textContent="+",this.minimizeBtn.title="Restore"):(this.dialog.classList.remove("minimized"),this.minimizeBtn.textContent="−",this.minimizeBtn.title="Minimize"),this.onMinimize&&this.onMinimize(this.isMinimized),console.log("AI Dialog minimized:",this.isMinimized)}toggleMaximize(){this.isMaximized=!this.isMaximized,this.isMaximized?(this.dialog.classList.add("maximized"),this.maximizeBtn.textContent="❐",this.maximizeBtn.title="Restore"):(this.dialog.classList.remove("maximized"),this.maximizeBtn.textContent="□",this.maximizeBtn.title="Maximize"),this.onMaximize&&this.onMaximize(this.isMaximized),console.log("AI Dialog maximized:",this.isMaximized)}togglePauseResume(){this.onPauseResume&&this.onPauseResume()}updatePauseResumeButton(t){t?(this.pauseIcon.style.display="none",this.playIcon.style.display="block",this.pauseResumeBtn.classList.add("paused"),this.pauseResumeBtn.title="Resume Agent"):(this.pauseIcon.style.display="block",this.playIcon.style.display="none",this.pauseResumeBtn.classList.remove("paused"),this.pauseResumeBtn.title="Pause Agent")}setPauseResumeButtonEnabled(t){this.pauseResumeBtn.disabled=!t}setSendButtonToTerminate(){const t=document.getElementById("aiSend"),_=document.getElementById("sendIcon"),b=document.getElementById("stopIcon");t&&_&&b&&(_.style.display="none",b.style.display="block",t.classList.add("terminate"),t.title="Terminate Agent")}setSendButtonToSend(){const t=document.getElementById("aiSend"),_=document.getElementById("sendIcon"),b=document.getElementById("stopIcon");t&&_&&b&&(_.style.display="block",b.style.display="none",t.classList.remove("terminate"),t.title="Send message")}isSendButtonInTerminateMode(){const t=document.getElementById("aiSend");return t?t.classList.contains("terminate"):!1}setupDragging(){this.headerElement.addEventListener("mousedown",t=>{(t.target===this.headerElement||t.target.tagName==="H3")&&this.startDragging(t)}),document.addEventListener("mousemove",t=>{this.isDragging&&this.drag(t)}),document.addEventListener("mouseup",()=>{this.isDragging&&this.stopDragging()})}startDragging(t){if(this.isMaximized)return;this.isDragging=!0,this.dialog.classList.add("dragging");const _=this.dialog.getBoundingClientRect();this.dragOffset.x=t.clientX-_.left,this.dragOffset.y=t.clientY-_.top,t.preventDefault()}drag(t){if(!this.isDragging)return;const _=t.clientX-this.dragOffset.x,b=t.clientY-this.dragOffset.y,C=window.innerWidth-this.dialog.offsetWidth,x=window.innerHeight-this.dialog.offsetHeight,L=Math.max(0,Math.min(_,C)),s=Math.max(0,Math.min(b,x));this.dialog.style.left=L+"px",this.dialog.style.top=s+"px",this.dialog.style.right="auto",this.dialog.style.bottom="auto"}stopDragging(){this.isDragging=!1,this.dialog.classList.remove("dragging")}isDialogVisible(){return this.isVisible}setCallbacks(t){this.onShow=t.onShow||null,this.onHide=t.onHide||null,this.onMinimize=t.onMinimize||null,this.onMaximize=t.onMaximize||null,this.onPauseResume=t.onPauseResume||null}}const re=new Wt;function tt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Se=tt();function Ct(E){Se=E}var Be={exec:()=>null};function J(E,t=""){let _=typeof E=="string"?E:E.source,b={replace:(C,x)=>{let L=typeof x=="string"?x:x.source;return L=L.replace(ne.caret,"$1"),_=_.replace(C,L),b},getRegex:()=>new RegExp(_,t)};return b}var ne={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:E=>new RegExp(`^( {0,3}${E})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}#`),htmlBeginRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}<(?:[a-z].*>|!--)`,"i")},Nt=/^(?:[ \t]*(?:\n|$))+/,Ut=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,jt=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Me=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Kt=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,st=/(?:[*+-]|\d{1,9}[.)])/,yt=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,wt=J(yt).replace(/bull/g,st).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),qt=J(yt).replace(/bull/g,st).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),it=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Vt=/^[^\n]+/,rt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Xt=J(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",rt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Gt=J(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,st).getRegex(),Ue="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",nt=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,Jt=J("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",nt).replace("tag",Ue).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),kt=J(it).replace("hr",Me).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ue).getRegex(),Zt=J(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",kt).getRegex(),ot={blockquote:Zt,code:Ut,def:Xt,fences:jt,heading:Kt,hr:Me,html:Jt,lheading:wt,list:Gt,newline:Nt,paragraph:kt,table:Be,text:Vt},ct=J("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Me).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ue).getRegex(),Yt={...ot,lheading:qt,table:ct,paragraph:J(it).replace("hr",Me).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ct).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ue).getRegex()},Qt={...ot,html:J(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",nt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Be,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:J(it).replace("hr",Me).replace("heading",` *#{1,6} *[^
]`).replace("lheading",wt).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},es=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ts=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Et=/^( {2,}|\\)\n(?!\s*$)/,ss=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,je=/[\p{P}\p{S}]/u,at=/[\s\p{P}\p{S}]/u,xt=/[^\s\p{P}\p{S}]/u,is=J(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,at).getRegex(),Lt=/(?!~)[\p{P}\p{S}]/u,rs=/(?!~)[\s\p{P}\p{S}]/u,ns=/(?:[^\s\p{P}\p{S}]|~)/u,os=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,At=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,as=J(At,"u").replace(/punct/g,je).getRegex(),ls=J(At,"u").replace(/punct/g,Lt).getRegex(),Rt="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",hs=J(Rt,"gu").replace(/notPunctSpace/g,xt).replace(/punctSpace/g,at).replace(/punct/g,je).getRegex(),cs=J(Rt,"gu").replace(/notPunctSpace/g,ns).replace(/punctSpace/g,rs).replace(/punct/g,Lt).getRegex(),us=J("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,xt).replace(/punctSpace/g,at).replace(/punct/g,je).getRegex(),ds=J(/\\(punct)/,"gu").replace(/punct/g,je).getRegex(),fs=J(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),_s=J(nt).replace("(?:-->|$)","-->").getRegex(),gs=J("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",_s).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ze=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,ps=J(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",ze).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Dt=J(/^!?\[(label)\]\[(ref)\]/).replace("label",ze).replace("ref",rt).getRegex(),Tt=J(/^!?\[(ref)\](?:\[\])?/).replace("ref",rt).getRegex(),ms=J("reflink|nolink(?!\\()","g").replace("reflink",Dt).replace("nolink",Tt).getRegex(),lt={_backpedal:Be,anyPunctuation:ds,autolink:fs,blockSkip:os,br:Et,code:ts,del:Be,emStrongLDelim:as,emStrongRDelimAst:hs,emStrongRDelimUnd:us,escape:es,link:ps,nolink:Tt,punctuation:is,reflink:Dt,reflinkSearch:ms,tag:gs,text:ss,url:Be},vs={...lt,link:J(/^!?\[(label)\]\((.*?)\)/).replace("label",ze).getRegex(),reflink:J(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ze).getRegex()},Ze={...lt,emStrongRDelimAst:cs,emStrongLDelim:ls,url:J(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Ss={...Ze,br:J(Et).replace("{2,}","*").getRegex(),text:J(Ze.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Oe={normal:ot,gfm:Yt,pedantic:Qt},Ae={normal:lt,gfm:Ze,breaks:Ss,pedantic:vs},bs={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ut=E=>bs[E];function de(E,t){if(t){if(ne.escapeTest.test(E))return E.replace(ne.escapeReplace,ut)}else if(ne.escapeTestNoEncode.test(E))return E.replace(ne.escapeReplaceNoEncode,ut);return E}function dt(E){try{E=encodeURI(E).replace(ne.percentDecode,"%")}catch{return null}return E}function ft(E,t){var x;let _=E.replace(ne.findPipe,(L,s,o)=>{let l=!1,c=s;for(;--c>=0&&o[c]==="\\";)l=!l;return l?"|":" |"}),b=_.split(ne.splitPipe),C=0;if(b[0].trim()||b.shift(),b.length>0&&!((x=b.at(-1))!=null&&x.trim())&&b.pop(),t)if(b.length>t)b.splice(t);else for(;b.length<t;)b.push("");for(;C<b.length;C++)b[C]=b[C].trim().replace(ne.slashPipe,"|");return b}function Re(E,t,_){let b=E.length;if(b===0)return"";let C=0;for(;C<b&&E.charAt(b-C-1)===t;)C++;return E.slice(0,b-C)}function Cs(E,t){if(E.indexOf(t[1])===-1)return-1;let _=0;for(let b=0;b<E.length;b++)if(E[b]==="\\")b++;else if(E[b]===t[0])_++;else if(E[b]===t[1]&&(_--,_<0))return b;return _>0?-2:-1}function _t(E,t,_,b,C){let x=t.href,L=t.title||null,s=E[1].replace(C.other.outputLinkReplace,"$1");b.state.inLink=!0;let o={type:E[0].charAt(0)==="!"?"image":"link",raw:_,href:x,title:L,text:s,tokens:b.inlineTokens(s)};return b.state.inLink=!1,o}function ys(E,t,_){let b=E.match(_.other.indentCodeCompensation);if(b===null)return t;let C=b[1];return t.split(`
`).map(x=>{let L=x.match(_.other.beginningSpace);if(L===null)return x;let[s]=L;return s.length>=C.length?x.slice(C.length):x}).join(`
`)}var We=class{constructor(E){Y(this,"options");Y(this,"rules");Y(this,"lexer");this.options=E||Se}space(E){let t=this.rules.block.newline.exec(E);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(E){let t=this.rules.block.code.exec(E);if(t){let _=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?_:Re(_,`
`)}}}fences(E){let t=this.rules.block.fences.exec(E);if(t){let _=t[0],b=ys(_,t[3]||"",this.rules);return{type:"code",raw:_,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:b}}}heading(E){let t=this.rules.block.heading.exec(E);if(t){let _=t[2].trim();if(this.rules.other.endingHash.test(_)){let b=Re(_,"#");(this.options.pedantic||!b||this.rules.other.endingSpaceChar.test(b))&&(_=b.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:_,tokens:this.lexer.inline(_)}}}hr(E){let t=this.rules.block.hr.exec(E);if(t)return{type:"hr",raw:Re(t[0],`
`)}}blockquote(E){let t=this.rules.block.blockquote.exec(E);if(t){let _=Re(t[0],`
`).split(`
`),b="",C="",x=[];for(;_.length>0;){let L=!1,s=[],o;for(o=0;o<_.length;o++)if(this.rules.other.blockquoteStart.test(_[o]))s.push(_[o]),L=!0;else if(!L)s.push(_[o]);else break;_=_.slice(o);let l=s.join(`
`),c=l.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");b=b?`${b}
${l}`:l,C=C?`${C}
${c}`:c;let a=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(c,x,!0),this.lexer.state.top=a,_.length===0)break;let u=x.at(-1);if((u==null?void 0:u.type)==="code")break;if((u==null?void 0:u.type)==="blockquote"){let p=u,m=p.raw+`
`+_.join(`
`),g=this.blockquote(m);x[x.length-1]=g,b=b.substring(0,b.length-p.raw.length)+g.raw,C=C.substring(0,C.length-p.text.length)+g.text;break}else if((u==null?void 0:u.type)==="list"){let p=u,m=p.raw+`
`+_.join(`
`),g=this.list(m);x[x.length-1]=g,b=b.substring(0,b.length-u.raw.length)+g.raw,C=C.substring(0,C.length-p.raw.length)+g.raw,_=m.substring(x.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:b,tokens:x,text:C}}}list(E){let t=this.rules.block.list.exec(E);if(t){let _=t[1].trim(),b=_.length>1,C={type:"list",raw:"",ordered:b,start:b?+_.slice(0,-1):"",loose:!1,items:[]};_=b?`\\d{1,9}\\${_.slice(-1)}`:`\\${_}`,this.options.pedantic&&(_=b?_:"[*+-]");let x=this.rules.other.listItemRegex(_),L=!1;for(;E;){let o=!1,l="",c="";if(!(t=x.exec(E))||this.rules.block.hr.test(E))break;l=t[0],E=E.substring(l.length);let a=t[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,n=>" ".repeat(3*n.length)),u=E.split(`
`,1)[0],p=!a.trim(),m=0;if(this.options.pedantic?(m=2,c=a.trimStart()):p?m=t[1].length+1:(m=t[2].search(this.rules.other.nonSpaceChar),m=m>4?1:m,c=a.slice(m),m+=t[1].length),p&&this.rules.other.blankLine.test(u)&&(l+=u+`
`,E=E.substring(u.length+1),o=!0),!o){let n=this.rules.other.nextBulletRegex(m),i=this.rules.other.hrRegex(m),r=this.rules.other.fencesBeginRegex(m),h=this.rules.other.headingBeginRegex(m),f=this.rules.other.htmlBeginRegex(m);for(;E;){let v=E.split(`
`,1)[0],y;if(u=v,this.options.pedantic?(u=u.replace(this.rules.other.listReplaceNesting,"  "),y=u):y=u.replace(this.rules.other.tabCharGlobal,"    "),r.test(u)||h.test(u)||f.test(u)||n.test(u)||i.test(u))break;if(y.search(this.rules.other.nonSpaceChar)>=m||!u.trim())c+=`
`+y.slice(m);else{if(p||a.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||r.test(a)||h.test(a)||i.test(a))break;c+=`
`+u}!p&&!u.trim()&&(p=!0),l+=v+`
`,E=E.substring(v.length+1),a=y.slice(m)}}C.loose||(L?C.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(L=!0));let g=null,e;this.options.gfm&&(g=this.rules.other.listIsTask.exec(c),g&&(e=g[0]!=="[ ] ",c=c.replace(this.rules.other.listReplaceTask,""))),C.items.push({type:"list_item",raw:l,task:!!g,checked:e,loose:!1,text:c,tokens:[]}),C.raw+=l}let s=C.items.at(-1);if(s)s.raw=s.raw.trimEnd(),s.text=s.text.trimEnd();else return;C.raw=C.raw.trimEnd();for(let o=0;o<C.items.length;o++)if(this.lexer.state.top=!1,C.items[o].tokens=this.lexer.blockTokens(C.items[o].text,[]),!C.loose){let l=C.items[o].tokens.filter(a=>a.type==="space"),c=l.length>0&&l.some(a=>this.rules.other.anyLine.test(a.raw));C.loose=c}if(C.loose)for(let o=0;o<C.items.length;o++)C.items[o].loose=!0;return C}}html(E){let t=this.rules.block.html.exec(E);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(E){let t=this.rules.block.def.exec(E);if(t){let _=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),b=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",C=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:_,raw:t[0],href:b,title:C}}}table(E){var L;let t=this.rules.block.table.exec(E);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let _=ft(t[1]),b=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),C=(L=t[3])!=null&&L.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],x={type:"table",raw:t[0],header:[],align:[],rows:[]};if(_.length===b.length){for(let s of b)this.rules.other.tableAlignRight.test(s)?x.align.push("right"):this.rules.other.tableAlignCenter.test(s)?x.align.push("center"):this.rules.other.tableAlignLeft.test(s)?x.align.push("left"):x.align.push(null);for(let s=0;s<_.length;s++)x.header.push({text:_[s],tokens:this.lexer.inline(_[s]),header:!0,align:x.align[s]});for(let s of C)x.rows.push(ft(s,x.header.length).map((o,l)=>({text:o,tokens:this.lexer.inline(o),header:!1,align:x.align[l]})));return x}}lheading(E){let t=this.rules.block.lheading.exec(E);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(E){let t=this.rules.block.paragraph.exec(E);if(t){let _=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:_,tokens:this.lexer.inline(_)}}}text(E){let t=this.rules.block.text.exec(E);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(E){let t=this.rules.inline.escape.exec(E);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(E){let t=this.rules.inline.tag.exec(E);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(E){let t=this.rules.inline.link.exec(E);if(t){let _=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(_)){if(!this.rules.other.endAngleBracket.test(_))return;let x=Re(_.slice(0,-1),"\\");if((_.length-x.length)%2===0)return}else{let x=Cs(t[2],"()");if(x===-2)return;if(x>-1){let L=(t[0].indexOf("!")===0?5:4)+t[1].length+x;t[2]=t[2].substring(0,x),t[0]=t[0].substring(0,L).trim(),t[3]=""}}let b=t[2],C="";if(this.options.pedantic){let x=this.rules.other.pedanticHrefTitle.exec(b);x&&(b=x[1],C=x[3])}else C=t[3]?t[3].slice(1,-1):"";return b=b.trim(),this.rules.other.startAngleBracket.test(b)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(_)?b=b.slice(1):b=b.slice(1,-1)),_t(t,{href:b&&b.replace(this.rules.inline.anyPunctuation,"$1"),title:C&&C.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(E,t){let _;if((_=this.rules.inline.reflink.exec(E))||(_=this.rules.inline.nolink.exec(E))){let b=(_[2]||_[1]).replace(this.rules.other.multipleSpaceGlobal," "),C=t[b.toLowerCase()];if(!C){let x=_[0].charAt(0);return{type:"text",raw:x,text:x}}return _t(_,C,_[0],this.lexer,this.rules)}}emStrong(E,t,_=""){let b=this.rules.inline.emStrongLDelim.exec(E);if(!(!b||b[3]&&_.match(this.rules.other.unicodeAlphaNumeric))&&(!(b[1]||b[2])||!_||this.rules.inline.punctuation.exec(_))){let C=[...b[0]].length-1,x,L,s=C,o=0,l=b[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,t=t.slice(-1*E.length+C);(b=l.exec(t))!=null;){if(x=b[1]||b[2]||b[3]||b[4]||b[5]||b[6],!x)continue;if(L=[...x].length,b[3]||b[4]){s+=L;continue}else if((b[5]||b[6])&&C%3&&!((C+L)%3)){o+=L;continue}if(s-=L,s>0)continue;L=Math.min(L,L+s+o);let c=[...b[0]][0].length,a=E.slice(0,C+b.index+c+L);if(Math.min(C,L)%2){let p=a.slice(1,-1);return{type:"em",raw:a,text:p,tokens:this.lexer.inlineTokens(p)}}let u=a.slice(2,-2);return{type:"strong",raw:a,text:u,tokens:this.lexer.inlineTokens(u)}}}}codespan(E){let t=this.rules.inline.code.exec(E);if(t){let _=t[2].replace(this.rules.other.newLineCharGlobal," "),b=this.rules.other.nonSpaceChar.test(_),C=this.rules.other.startingSpaceChar.test(_)&&this.rules.other.endingSpaceChar.test(_);return b&&C&&(_=_.substring(1,_.length-1)),{type:"codespan",raw:t[0],text:_}}}br(E){let t=this.rules.inline.br.exec(E);if(t)return{type:"br",raw:t[0]}}del(E){let t=this.rules.inline.del.exec(E);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(E){let t=this.rules.inline.autolink.exec(E);if(t){let _,b;return t[2]==="@"?(_=t[1],b="mailto:"+_):(_=t[1],b=_),{type:"link",raw:t[0],text:_,href:b,tokens:[{type:"text",raw:_,text:_}]}}}url(E){var _;let t;if(t=this.rules.inline.url.exec(E)){let b,C;if(t[2]==="@")b=t[0],C="mailto:"+b;else{let x;do x=t[0],t[0]=((_=this.rules.inline._backpedal.exec(t[0]))==null?void 0:_[0])??"";while(x!==t[0]);b=t[0],t[1]==="www."?C="http://"+t[0]:C=t[0]}return{type:"link",raw:t[0],text:b,href:C,tokens:[{type:"text",raw:b,text:b}]}}}inlineText(E){let t=this.rules.inline.text.exec(E);if(t){let _=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:_}}}},fe=class Ye{constructor(t){Y(this,"tokens");Y(this,"options");Y(this,"state");Y(this,"tokenizer");Y(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Se,this.options.tokenizer=this.options.tokenizer||new We,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let _={other:ne,block:Oe.normal,inline:Ae.normal};this.options.pedantic?(_.block=Oe.pedantic,_.inline=Ae.pedantic):this.options.gfm&&(_.block=Oe.gfm,this.options.breaks?_.inline=Ae.breaks:_.inline=Ae.gfm),this.tokenizer.rules=_}static get rules(){return{block:Oe,inline:Ae}}static lex(t,_){return new Ye(_).lex(t)}static lexInline(t,_){return new Ye(_).inlineTokens(t)}lex(t){t=t.replace(ne.carriageReturn,`
`),this.blockTokens(t,this.tokens);for(let _=0;_<this.inlineQueue.length;_++){let b=this.inlineQueue[_];this.inlineTokens(b.src,b.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,_=[],b=!1){var C,x,L;for(this.options.pedantic&&(t=t.replace(ne.tabCharGlobal,"    ").replace(ne.spaceLine,""));t;){let s;if((x=(C=this.options.extensions)==null?void 0:C.block)!=null&&x.some(l=>(s=l.call({lexer:this},t,_))?(t=t.substring(s.raw.length),_.push(s),!0):!1))continue;if(s=this.tokenizer.space(t)){t=t.substring(s.raw.length);let l=_.at(-1);s.raw.length===1&&l!==void 0?l.raw+=`
`:_.push(s);continue}if(s=this.tokenizer.code(t)){t=t.substring(s.raw.length);let l=_.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.at(-1).src=l.text):_.push(s);continue}if(s=this.tokenizer.fences(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.heading(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.hr(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.blockquote(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.list(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.html(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.def(t)){t=t.substring(s.raw.length);let l=_.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(t)){t=t.substring(s.raw.length),_.push(s);continue}if(s=this.tokenizer.lheading(t)){t=t.substring(s.raw.length),_.push(s);continue}let o=t;if((L=this.options.extensions)!=null&&L.startBlock){let l=1/0,c=t.slice(1),a;this.options.extensions.startBlock.forEach(u=>{a=u.call({lexer:this},c),typeof a=="number"&&a>=0&&(l=Math.min(l,a))}),l<1/0&&l>=0&&(o=t.substring(0,l+1))}if(this.state.top&&(s=this.tokenizer.paragraph(o))){let l=_.at(-1);b&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):_.push(s),b=o.length!==t.length,t=t.substring(s.raw.length);continue}if(s=this.tokenizer.text(t)){t=t.substring(s.raw.length);let l=_.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):_.push(s);continue}if(t){let l="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,_}inline(t,_=[]){return this.inlineQueue.push({src:t,tokens:_}),_}inlineTokens(t,_=[]){var s,o,l;let b=t,C=null;if(this.tokens.links){let c=Object.keys(this.tokens.links);if(c.length>0)for(;(C=this.tokenizer.rules.inline.reflinkSearch.exec(b))!=null;)c.includes(C[0].slice(C[0].lastIndexOf("[")+1,-1))&&(b=b.slice(0,C.index)+"["+"a".repeat(C[0].length-2)+"]"+b.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(C=this.tokenizer.rules.inline.anyPunctuation.exec(b))!=null;)b=b.slice(0,C.index)+"++"+b.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(C=this.tokenizer.rules.inline.blockSkip.exec(b))!=null;)b=b.slice(0,C.index)+"["+"a".repeat(C[0].length-2)+"]"+b.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let x=!1,L="";for(;t;){x||(L=""),x=!1;let c;if((o=(s=this.options.extensions)==null?void 0:s.inline)!=null&&o.some(u=>(c=u.call({lexer:this},t,_))?(t=t.substring(c.raw.length),_.push(c),!0):!1))continue;if(c=this.tokenizer.escape(t)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.tag(t)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.link(t)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(c.raw.length);let u=_.at(-1);c.type==="text"&&(u==null?void 0:u.type)==="text"?(u.raw+=c.raw,u.text+=c.text):_.push(c);continue}if(c=this.tokenizer.emStrong(t,b,L)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.codespan(t)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.br(t)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.del(t)){t=t.substring(c.raw.length),_.push(c);continue}if(c=this.tokenizer.autolink(t)){t=t.substring(c.raw.length),_.push(c);continue}if(!this.state.inLink&&(c=this.tokenizer.url(t))){t=t.substring(c.raw.length),_.push(c);continue}let a=t;if((l=this.options.extensions)!=null&&l.startInline){let u=1/0,p=t.slice(1),m;this.options.extensions.startInline.forEach(g=>{m=g.call({lexer:this},p),typeof m=="number"&&m>=0&&(u=Math.min(u,m))}),u<1/0&&u>=0&&(a=t.substring(0,u+1))}if(c=this.tokenizer.inlineText(a)){t=t.substring(c.raw.length),c.raw.slice(-1)!=="_"&&(L=c.raw.slice(-1)),x=!0;let u=_.at(-1);(u==null?void 0:u.type)==="text"?(u.raw+=c.raw,u.text+=c.text):_.push(c);continue}if(t){let u="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(u);break}else throw new Error(u)}}return _}},Ne=class{constructor(E){Y(this,"options");Y(this,"parser");this.options=E||Se}space(E){return""}code({text:E,lang:t,escaped:_}){var x;let b=(x=(t||"").match(ne.notSpaceStart))==null?void 0:x[0],C=E.replace(ne.endingNewline,"")+`
`;return b?'<pre><code class="language-'+de(b)+'">'+(_?C:de(C,!0))+`</code></pre>
`:"<pre><code>"+(_?C:de(C,!0))+`</code></pre>
`}blockquote({tokens:E}){return`<blockquote>
${this.parser.parse(E)}</blockquote>
`}html({text:E}){return E}heading({tokens:E,depth:t}){return`<h${t}>${this.parser.parseInline(E)}</h${t}>
`}hr(E){return`<hr>
`}list(E){let t=E.ordered,_=E.start,b="";for(let L=0;L<E.items.length;L++){let s=E.items[L];b+=this.listitem(s)}let C=t?"ol":"ul",x=t&&_!==1?' start="'+_+'"':"";return"<"+C+x+`>
`+b+"</"+C+`>
`}listitem(E){var _;let t="";if(E.task){let b=this.checkbox({checked:!!E.checked});E.loose?((_=E.tokens[0])==null?void 0:_.type)==="paragraph"?(E.tokens[0].text=b+" "+E.tokens[0].text,E.tokens[0].tokens&&E.tokens[0].tokens.length>0&&E.tokens[0].tokens[0].type==="text"&&(E.tokens[0].tokens[0].text=b+" "+de(E.tokens[0].tokens[0].text),E.tokens[0].tokens[0].escaped=!0)):E.tokens.unshift({type:"text",raw:b+" ",text:b+" ",escaped:!0}):t+=b+" "}return t+=this.parser.parse(E.tokens,!!E.loose),`<li>${t}</li>
`}checkbox({checked:E}){return"<input "+(E?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:E}){return`<p>${this.parser.parseInline(E)}</p>
`}table(E){let t="",_="";for(let C=0;C<E.header.length;C++)_+=this.tablecell(E.header[C]);t+=this.tablerow({text:_});let b="";for(let C=0;C<E.rows.length;C++){let x=E.rows[C];_="";for(let L=0;L<x.length;L++)_+=this.tablecell(x[L]);b+=this.tablerow({text:_})}return b&&(b=`<tbody>${b}</tbody>`),`<table>
<thead>
`+t+`</thead>
`+b+`</table>
`}tablerow({text:E}){return`<tr>
${E}</tr>
`}tablecell(E){let t=this.parser.parseInline(E.tokens),_=E.header?"th":"td";return(E.align?`<${_} align="${E.align}">`:`<${_}>`)+t+`</${_}>
`}strong({tokens:E}){return`<strong>${this.parser.parseInline(E)}</strong>`}em({tokens:E}){return`<em>${this.parser.parseInline(E)}</em>`}codespan({text:E}){return`<code>${de(E,!0)}</code>`}br(E){return"<br>"}del({tokens:E}){return`<del>${this.parser.parseInline(E)}</del>`}link({href:E,title:t,tokens:_}){let b=this.parser.parseInline(_),C=dt(E);if(C===null)return b;E=C;let x='<a href="'+E+'"';return t&&(x+=' title="'+de(t)+'"'),x+=">"+b+"</a>",x}image({href:E,title:t,text:_,tokens:b}){b&&(_=this.parser.parseInline(b,this.parser.textRenderer));let C=dt(E);if(C===null)return de(_);E=C;let x=`<img src="${E}" alt="${_}"`;return t&&(x+=` title="${de(t)}"`),x+=">",x}text(E){return"tokens"in E&&E.tokens?this.parser.parseInline(E.tokens):"escaped"in E&&E.escaped?E.text:de(E.text)}},ht=class{strong({text:E}){return E}em({text:E}){return E}codespan({text:E}){return E}del({text:E}){return E}html({text:E}){return E}text({text:E}){return E}link({text:E}){return""+E}image({text:E}){return""+E}br(){return""}},_e=class Qe{constructor(t){Y(this,"options");Y(this,"renderer");Y(this,"textRenderer");this.options=t||Se,this.options.renderer=this.options.renderer||new Ne,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new ht}static parse(t,_){return new Qe(_).parse(t)}static parseInline(t,_){return new Qe(_).parseInline(t)}parse(t,_=!0){var C,x;let b="";for(let L=0;L<t.length;L++){let s=t[L];if((x=(C=this.options.extensions)==null?void 0:C.renderers)!=null&&x[s.type]){let l=s,c=this.options.extensions.renderers[l.type].call({parser:this},l);if(c!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){b+=c||"";continue}}let o=s;switch(o.type){case"space":{b+=this.renderer.space(o);continue}case"hr":{b+=this.renderer.hr(o);continue}case"heading":{b+=this.renderer.heading(o);continue}case"code":{b+=this.renderer.code(o);continue}case"table":{b+=this.renderer.table(o);continue}case"blockquote":{b+=this.renderer.blockquote(o);continue}case"list":{b+=this.renderer.list(o);continue}case"html":{b+=this.renderer.html(o);continue}case"paragraph":{b+=this.renderer.paragraph(o);continue}case"text":{let l=o,c=this.renderer.text(l);for(;L+1<t.length&&t[L+1].type==="text";)l=t[++L],c+=`
`+this.renderer.text(l);_?b+=this.renderer.paragraph({type:"paragraph",raw:c,text:c,tokens:[{type:"text",raw:c,text:c,escaped:!0}]}):b+=c;continue}default:{let l='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return b}parseInline(t,_=this.renderer){var C,x;let b="";for(let L=0;L<t.length;L++){let s=t[L];if((x=(C=this.options.extensions)==null?void 0:C.renderers)!=null&&x[s.type]){let l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){b+=l||"";continue}}let o=s;switch(o.type){case"escape":{b+=_.text(o);break}case"html":{b+=_.html(o);break}case"link":{b+=_.link(o);break}case"image":{b+=_.image(o);break}case"strong":{b+=_.strong(o);break}case"em":{b+=_.em(o);break}case"codespan":{b+=_.codespan(o);break}case"br":{b+=_.br(o);break}case"del":{b+=_.del(o);break}case"text":{b+=_.text(o);break}default:{let l='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return b}},Je,Fe=(Je=class{constructor(E){Y(this,"options");Y(this,"block");this.options=E||Se}preprocess(E){return E}postprocess(E){return E}processAllTokens(E){return E}provideLexer(){return this.block?fe.lex:fe.lexInline}provideParser(){return this.block?_e.parse:_e.parseInline}},Y(Je,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Je),ws=class{constructor(...E){Y(this,"defaults",tt());Y(this,"options",this.setOptions);Y(this,"parse",this.parseMarkdown(!0));Y(this,"parseInline",this.parseMarkdown(!1));Y(this,"Parser",_e);Y(this,"Renderer",Ne);Y(this,"TextRenderer",ht);Y(this,"Lexer",fe);Y(this,"Tokenizer",We);Y(this,"Hooks",Fe);this.use(...E)}walkTokens(E,t){var b,C;let _=[];for(let x of E)switch(_=_.concat(t.call(this,x)),x.type){case"table":{let L=x;for(let s of L.header)_=_.concat(this.walkTokens(s.tokens,t));for(let s of L.rows)for(let o of s)_=_.concat(this.walkTokens(o.tokens,t));break}case"list":{let L=x;_=_.concat(this.walkTokens(L.items,t));break}default:{let L=x;(C=(b=this.defaults.extensions)==null?void 0:b.childTokens)!=null&&C[L.type]?this.defaults.extensions.childTokens[L.type].forEach(s=>{let o=L[s].flat(1/0);_=_.concat(this.walkTokens(o,t))}):L.tokens&&(_=_.concat(this.walkTokens(L.tokens,t)))}}return _}use(...E){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return E.forEach(_=>{let b={..._};if(b.async=this.defaults.async||b.async||!1,_.extensions&&(_.extensions.forEach(C=>{if(!C.name)throw new Error("extension name required");if("renderer"in C){let x=t.renderers[C.name];x?t.renderers[C.name]=function(...L){let s=C.renderer.apply(this,L);return s===!1&&(s=x.apply(this,L)),s}:t.renderers[C.name]=C.renderer}if("tokenizer"in C){if(!C.level||C.level!=="block"&&C.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let x=t[C.level];x?x.unshift(C.tokenizer):t[C.level]=[C.tokenizer],C.start&&(C.level==="block"?t.startBlock?t.startBlock.push(C.start):t.startBlock=[C.start]:C.level==="inline"&&(t.startInline?t.startInline.push(C.start):t.startInline=[C.start]))}"childTokens"in C&&C.childTokens&&(t.childTokens[C.name]=C.childTokens)}),b.extensions=t),_.renderer){let C=this.defaults.renderer||new Ne(this.defaults);for(let x in _.renderer){if(!(x in C))throw new Error(`renderer '${x}' does not exist`);if(["options","parser"].includes(x))continue;let L=x,s=_.renderer[L],o=C[L];C[L]=(...l)=>{let c=s.apply(C,l);return c===!1&&(c=o.apply(C,l)),c||""}}b.renderer=C}if(_.tokenizer){let C=this.defaults.tokenizer||new We(this.defaults);for(let x in _.tokenizer){if(!(x in C))throw new Error(`tokenizer '${x}' does not exist`);if(["options","rules","lexer"].includes(x))continue;let L=x,s=_.tokenizer[L],o=C[L];C[L]=(...l)=>{let c=s.apply(C,l);return c===!1&&(c=o.apply(C,l)),c}}b.tokenizer=C}if(_.hooks){let C=this.defaults.hooks||new Fe;for(let x in _.hooks){if(!(x in C))throw new Error(`hook '${x}' does not exist`);if(["options","block"].includes(x))continue;let L=x,s=_.hooks[L],o=C[L];Fe.passThroughHooks.has(x)?C[L]=l=>{if(this.defaults.async)return Promise.resolve(s.call(C,l)).then(a=>o.call(C,a));let c=s.call(C,l);return o.call(C,c)}:C[L]=(...l)=>{let c=s.apply(C,l);return c===!1&&(c=o.apply(C,l)),c}}b.hooks=C}if(_.walkTokens){let C=this.defaults.walkTokens,x=_.walkTokens;b.walkTokens=function(L){let s=[];return s.push(x.call(this,L)),C&&(s=s.concat(C.call(this,L))),s}}this.defaults={...this.defaults,...b}}),this}setOptions(E){return this.defaults={...this.defaults,...E},this}lexer(E,t){return fe.lex(E,t??this.defaults)}parser(E,t){return _e.parse(E,t??this.defaults)}parseMarkdown(E){return(t,_)=>{let b={..._},C={...this.defaults,...b},x=this.onError(!!C.silent,!!C.async);if(this.defaults.async===!0&&b.async===!1)return x(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return x(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return x(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));C.hooks&&(C.hooks.options=C,C.hooks.block=E);let L=C.hooks?C.hooks.provideLexer():E?fe.lex:fe.lexInline,s=C.hooks?C.hooks.provideParser():E?_e.parse:_e.parseInline;if(C.async)return Promise.resolve(C.hooks?C.hooks.preprocess(t):t).then(o=>L(o,C)).then(o=>C.hooks?C.hooks.processAllTokens(o):o).then(o=>C.walkTokens?Promise.all(this.walkTokens(o,C.walkTokens)).then(()=>o):o).then(o=>s(o,C)).then(o=>C.hooks?C.hooks.postprocess(o):o).catch(x);try{C.hooks&&(t=C.hooks.preprocess(t));let o=L(t,C);C.hooks&&(o=C.hooks.processAllTokens(o)),C.walkTokens&&this.walkTokens(o,C.walkTokens);let l=s(o,C);return C.hooks&&(l=C.hooks.postprocess(l)),l}catch(o){return x(o)}}}onError(E,t){return _=>{if(_.message+=`
Please report this to https://github.com/markedjs/marked.`,E){let b="<p>An error occurred:</p><pre>"+de(_.message+"",!0)+"</pre>";return t?Promise.resolve(b):b}if(t)return Promise.reject(_);throw _}}},ve=new ws;function G(E,t){return ve.parse(E,t)}G.options=G.setOptions=function(E){return ve.setOptions(E),G.defaults=ve.defaults,Ct(G.defaults),G};G.getDefaults=tt;G.defaults=Se;G.use=function(...E){return ve.use(...E),G.defaults=ve.defaults,Ct(G.defaults),G};G.walkTokens=function(E,t){return ve.walkTokens(E,t)};G.parseInline=ve.parseInline;G.Parser=_e;G.parser=_e.parse;G.Renderer=Ne;G.TextRenderer=ht;G.Lexer=fe;G.lexer=fe.lex;G.Tokenizer=We;G.Hooks=Fe;G.parse=G;G.options;G.setOptions;G.use;G.walkTokens;G.parseInline;_e.parse;fe.lex;class ks{constructor(){this.messagesContainer=null,this.messages=[],this.currentStreamingMessage=null,this.streamingMessageElement=null,this.currentToolCalls=[],this.toolCallElements=new Map,G.setOptions({breaks:!0,gfm:!0,sanitize:!1})}initialize(){return this.messagesContainer=document.getElementById("aiMessages"),this.messagesContainer?(console.log("Message Renderer initialized"),!0):(console.error("Messages container not found"),!1)}addMessage(t,_){const b={content:t,sender:_,timestamp:new Date};this.messages.push(b),this.renderMessage(b),this.scrollToBottom()}renderMessage(t){const _=document.createElement("div");_.className=`ai-message ai-message-${t.sender}`;const b=document.createElement("div");b.className="ai-message-content",t.sender==="assistant"?b.innerHTML=G.parse(t.content):b.innerHTML=this.formatUserMessage(t.content),_.appendChild(b),this.messagesContainer.appendChild(_)}formatUserMessage(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"<br>")}startStreamingMessage(){const t=document.createElement("div");t.className="ai-message ai-message-assistant streaming";const _=document.createElement("div");_.className="ai-message-content",_.innerHTML='<span class="streaming-cursor">▋</span>',t.appendChild(_),this.messagesContainer.appendChild(t),this.currentStreamingMessage="",this.streamingMessageElement=_,this.currentToolCalls=[],this.toolCallElements.clear(),this.scrollToBottom()}handleStreamingChunk(t){if(this.streamingMessageElement)if(t.type==="content"){this.currentStreamingMessage+=t.content;const _=G.parse(this.currentStreamingMessage);this.streamingMessageElement.innerHTML=_+'<span class="streaming-cursor">▋</span>',this.scrollToBottom()}else t.type==="tool_call"&&this.handleToolCallChunk(t)}handleToolCallChunk(t){var s,o,l;const _=t.toolCall,b=_.index||0;if(!this.toolCallElements.has(b)){const c=document.createElement("div");c.className="tool-call-container",c.innerHTML=`
        <div class="tool-call-header">
          <span class="tool-call-icon">🔧</span>
          <span class="tool-call-name">${((s=_.function)==null?void 0:s.name)||"Loading..."}</span>
          <span class="tool-call-status">Preparing...</span>
        </div>
        <div class="tool-call-args"></div>
      `,this.streamingMessageElement.appendChild(c),this.toolCallElements.set(b,c)}const C=this.toolCallElements.get(b),x=C.querySelector(".tool-call-name"),L=C.querySelector(".tool-call-args");if((o=_.function)!=null&&o.name&&(x.textContent=_.function.name),(l=_.function)!=null&&l.arguments)try{const c=JSON.parse(_.function.arguments);L.innerHTML=`<pre><code>${JSON.stringify(c,null,2)}</code></pre>`}catch{L.textContent=_.function.arguments}this.scrollToBottom()}handleToolCall(t){t.type==="start"?this.updateToolCallStatus(t.toolCall,"Executing...","executing"):t.type==="complete"?(this.updateToolCallStatus(t.toolCall,"Completed","completed"),this.showToolCallResult(t.toolCall,t.result)):t.type==="error"&&(this.updateToolCallStatus(t.toolCall,"Error","error"),this.showToolCallResult(t.toolCall,{error:t.error}))}updateToolCallStatus(t,_,b){for(const[,C]of this.toolCallElements){const x=C.querySelector(".tool-call-name");if(x&&x.textContent===t.function.name){const L=C.querySelector(".tool-call-status");L.textContent=_,L.className=`tool-call-status ${b}`;break}}}showToolCallResult(t,_){for(const[,b]of this.toolCallElements){const C=b.querySelector(".tool-call-name");if(C&&C.textContent===t.function.name){let x=b.querySelector(".tool-call-result");x||(x=document.createElement("div"),x.className="tool-call-result",b.appendChild(x)),x.innerHTML=`<pre><code>${JSON.stringify(_,null,2)}</code></pre>`;break}}this.scrollToBottom()}handleStreamingComplete(t){if(this.streamingMessageElement){const _=this.streamingMessageElement.querySelector(".streaming-cursor");_&&_.remove(),this.preserveToolCallsAsMessages();const b=this.streamingMessageElement.closest(".ai-message");b&&b.classList.remove("streaming")}this.currentStreamingMessage=null,this.streamingMessageElement=null,this.currentToolCalls=[],this.toolCallElements.clear(),console.log("Streaming complete:",t)}preserveToolCallsAsMessages(){if(this.toolCallElements.size===0)return;const t=this.streamingMessageElement.closest(".ai-message"),_=[];for(let C=0;C<this.toolCallElements.size;C++)this.toolCallElements.has(C)&&_.push(this.toolCallElements.get(C));_.forEach(C=>{C.parentNode===this.streamingMessageElement&&C.remove()});let b=t;_.forEach(C=>{const x=document.createElement("div");x.className="ai-message ai-message-tool";const L=document.createElement("div");L.className="ai-message-content";const s=C.cloneNode(!0);L.appendChild(s),x.appendChild(L),b.parentNode.insertBefore(x,b.nextSibling),b=x,this.messages.push({content:C.outerHTML,sender:"tool",timestamp:new Date})}),this.scrollToBottom()}handleStreamingError(t){if(console.error("Streaming error:",t),this.streamingMessageElement){this.preserveToolCallsAsMessages(),this.streamingMessageElement.innerHTML=`<p class="error-message">Error: ${t.message}</p>`;const _=this.streamingMessageElement.closest(".ai-message");_&&(_.classList.remove("streaming"),_.classList.add("error"))}this.currentStreamingMessage=null,this.streamingMessageElement=null,this.currentToolCalls=[],this.toolCallElements.clear()}scrollToBottom(){this.messagesContainer.scrollTop=this.messagesContainer.scrollHeight}clearMessages(){this.messages=[],this.messagesContainer.innerHTML=`
      <div class="ai-message ai-message-assistant">
        <div class="ai-message-content">
          <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
        </div>
      </div>
    `}getMessages(){return[...this.messages]}}const ue=new ks;class Es{constructor(t={}){this.systemPrompt=t.systemPrompt||this.getDefaultSystemPrompt(),this.sessionId=t.sessionId||this.generateSessionId(),this.model=t.model||"gpt-3.5-turbo",this.tools=t.tools||[],this.maxToolCallIterations=t.maxToolCallIterations||30,this.config={apiKey:t.apiKey||"",endpoint:t.endpoint||"https://api.openai.com/v1/chat/completions",maxTokens:t.maxTokens||1e3,temperature:t.temperature||.7,stream:t.stream!==!1},this.messages=[],this.toolCallCount=0,this.isReady=!1,this.isPaused=!1,this.isTerminated=!1,this.pausePromise=null,this.pauseResolve=null,this.isProcessing=!1,this.onChunk=t.onChunk||null,this.onToolCall=t.onToolCall||null,this.onComplete=t.onComplete||null,this.onError=t.onError||null}getDefaultSystemPrompt(){return`你是一个交互式终端（macOS系统/bin/bash）自动化专家，正在与一个交互式终端协同工作，努力完成用户提交的任务。你有一个向终端输入和获取终端输出的工具箱，要灵活运用该工具，尽最大努力完成用户提交的任务，尽量自主决策，不要过多向用户询问。

你的工具箱包含以下能力：
1. **terminal_input** - 向终端发送输入
   - single_key: 发送单个按键（如 Enter, Tab, Escape, ArrowUp 等）
   - key_combo: 发送组合键（如 Ctrl+C, Ctrl+Z, Alt+F4 等）
   - command_line: 发送单个命令行（如 ls -la, pwd, cat file.txt）
   - command_group: 发送多个命令序列

2. **terminal_output** - 获取终端输出（**优先使用 full_screen 模式**）
   - current_line: 获取光标所在行内容
   - multiple_lines: 获取最近几行内容
   - **full_screen: 获取全屏内容（推荐首选，提供完整上下文）**
   - line_range: 获取指定行范围内容

3. **wait** - 等待指定时间
   - 在需要等待命令执行、界面加载等场景时使用

例如你可以精准使用工具完成以下场景：

**场景1：中断正在运行的程序**
- 使用 terminal_input 工具，key_combo 模式发送 Ctrl+C

**场景2：查找并编辑文件内容**
- 使用 command_line 模式输入：vi /tmp/nginx.conf
- 使用 wait 工具等待 vi 加载完成
- 使用 terminal_output 获取全屏输出，定位光标和目标字符串
- 如果未找到，使用 single_key 模式导航到下一页
- 找到后，使用命令序列移动光标到目标位置

**场景3：SSH连接远程服务器**
- 输入SSH连接命令
- 检查输出是否有信任主机提示
- 如有提示则输入"yes"，否则输入密码
- 执行远程命令

**场景4：文件操作和管理**
- 使用 ls 命令查看目录内容
- 使用 cd 命令切换目录
- 使用 cp, mv, rm 等命令进行文件操作
- 使用 grep, find 等命令搜索文件

**场景5：系统监控和诊断**
- 使用 ps, top 命令查看进程
- 使用 df, du 命令查看磁盘使用
- 使用 netstat, lsof 命令查看网络连接

**场景6：状态感知和上下文理解**
- 开始任务前：使用 full_screen 模式了解当前终端完整状态
- 命令执行后：使用 full_screen 模式确认执行结果和新状态
- 遇到问题时：使用 full_screen 模式获取完整错误信息和上下文
- 定期检查：在长时间操作过程中定期使用 full_screen 模式保持状态同步

要点：
- 积极主动，自主决策，高效使用工具完成任务
- **频繁查看全屏输出**，全面了解当前终端状态和上下文
- 在执行任何操作前，先使用 terminal_output 工具的 full_screen 模式获取完整终端内容
- 根据终端输出动态调整策略，确保对当前状态有准确理解
- 合理使用等待时间，确保命令执行完成后再获取输出
- 优先使用安全的只读命令进行探索
- 在执行可能有风险的操作前，先获取当前状态信息

**重要提醒**：
- 每次与用户交互开始时，立即使用 full_screen 模式查看完整终端状态
- 执行命令后，使用 full_screen 模式确认命令执行结果
- 遇到错误或异常时，使用 full_screen 模式获取完整错误信息
- 定期检查全屏内容，确保对终端状态的感知是最新和准确的`}generateSessionId(){return"agent_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)}async initialize(){if(!this.config.apiKey)throw new Error("API key is required");try{return await this.testConnection(),this.isReady=!0,console.log("AI Agent initialized successfully"),!0}catch(t){throw console.error("Failed to initialize AI Agent:",t),t}}async testConnection(){const t=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.model,messages:[{role:"user",content:"Hello"}],max_tokens:10,stream:!1})});if(!t.ok)throw new Error(`API test failed: ${t.status} ${t.statusText}`);return!0}addTool(t){this.tools.push(t)}removeTool(t){this.tools=this.tools.filter(_=>_.function.name!==t)}getToolDefinitions(){return this.tools.map(t=>({type:"function",function:t.function}))}async processMessage(t,_={}){if(!this.isReady)throw new Error("Agent not initialized");if(this.isTerminated)throw new Error("Agent has been terminated");this.isProcessing=!0;try{return this.messages.push({role:"user",content:t,timestamp:new Date().toISOString()}),this.toolCallCount=0,await this.runConversationLoop(_)}catch(b){throw this.onError&&this.onError(b),b}finally{this.isProcessing=!1}}async runConversationLoop(t={}){let _=0;for(;_<this.maxToolCallIterations;){if(this.isTerminated)throw console.log("Conversation loop terminated by user"),new Error("Agent terminated by user");if(this.isPaused&&(console.log("Agent paused, waiting for resume..."),await this.waitForResume(6e5)),this.isTerminated)throw console.log("Conversation loop terminated during pause"),new Error("Agent terminated by user");_++;const b=[{role:"system",content:this.systemPrompt},...this.messages.map(x=>({role:x.role,content:x.content,tool_calls:x.tool_calls,tool_call_id:x.tool_call_id}))];Object.keys(t).length>0&&b.push({role:"system",content:`Current context: ${JSON.stringify(t,null,2)}`});const C=await this.callOpenAI(b);if(this.isTerminated)throw console.log("Conversation loop terminated after API call"),new Error("Agent terminated by user");if(C.finish_reason==="stop")return this.onComplete&&this.onComplete(C),C;if(C.finish_reason==="tool_calls"){await this.executeToolCalls(C.tool_calls);continue}else if(C.finish_reason==="length")return console.warn("Token limit reached"),this.onComplete&&this.onComplete(C),C}throw new Error(`Maximum tool call iterations (${this.maxToolCallIterations}) exceeded`)}async callOpenAI(t){const _={model:this.model,messages:t,max_tokens:this.config.maxTokens,temperature:this.config.temperature,stream:this.config.stream};this.tools.length>0&&(_.tools=this.getToolDefinitions(),_.tool_choice="auto");const b=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify(_)});if(!b.ok)throw new Error(`API request failed: ${b.status} ${b.statusText}`);if(this.config.stream)return await this.handleStreamingResponse(b);{const C=await b.json(),x=C.choices[0].message;return this.messages.push({role:"assistant",content:x.content,tool_calls:x.tool_calls,timestamp:new Date().toISOString()}),{content:x.content,tool_calls:x.tool_calls,finish_reason:C.choices[0].finish_reason}}}async handleStreamingResponse(t){var s,o,l,c;const _=t.body.getReader(),b=new TextDecoder;let C="",x=[],L=null;try{for(;;){const{done:a,value:u}=await _.read();if(a)break;const m=b.decode(u).split(`
`);for(const g of m)if(g.startsWith("data: ")){const e=g.slice(6);if(e==="[DONE]")break;try{const n=JSON.parse(e),i=(s=n.choices[0])==null?void 0:s.delta;if(i&&(i.content&&(C+=i.content,this.onChunk&&this.onChunk({type:"content",content:i.content,fullContent:C})),i.tool_calls))for(const r of i.tool_calls)x[r.index]||(x[r.index]={id:r.id,type:r.type,function:{name:"",arguments:""}}),(o=r.function)!=null&&o.name&&(x[r.index].function.name+=r.function.name),(l=r.function)!=null&&l.arguments&&(x[r.index].function.arguments+=r.function.arguments),this.onChunk&&this.onChunk({type:"tool_call",toolCall:r,allToolCalls:x});(c=n.choices[0])!=null&&c.finish_reason&&(L=n.choices[0].finish_reason)}catch{continue}}}}finally{_.releaseLock()}return this.messages.push({role:"assistant",content:C,tool_calls:x.length>0?x:void 0,timestamp:new Date().toISOString()}),{content:C,tool_calls:x.length>0?x:void 0,finish_reason:L}}async executeToolCalls(t){if(!(!t||t.length===0))for(const _ of t){if(this.isTerminated)throw console.log("Tool execution terminated by user"),new Error("Agent terminated by user");try{this.toolCallCount++,this.onToolCall&&this.onToolCall({type:"start",toolCall:_,count:this.toolCallCount});const b=this.tools.find(L=>L.function.name===_.function.name);if(!b)throw new Error(`Tool not found: ${_.function.name}`);let C={};try{C=JSON.parse(_.function.arguments)}catch{throw new Error(`Invalid tool arguments: ${_.function.arguments}`)}const x=await b.execute(C);if(this.isTerminated)throw console.log("Tool execution terminated after tool completion"),new Error("Agent terminated by user");this.messages.push({role:"tool",content:JSON.stringify(x),tool_call_id:_.id,timestamp:new Date().toISOString()}),this.onToolCall&&this.onToolCall({type:"complete",toolCall:_,result:x,count:this.toolCallCount})}catch(b){if(console.error(`Tool execution error for ${_.function.name}:`,b),this.messages.push({role:"tool",content:JSON.stringify({error:b.message}),tool_call_id:_.id,timestamp:new Date().toISOString()}),this.onToolCall&&this.onToolCall({type:"error",toolCall:_,error:b.message,count:this.toolCallCount}),b.message==="Agent terminated by user")throw b}}}clearSession(){this.messages=[],this.toolCallCount=0}getMessages(){return[...this.messages]}updateConfig(t){this.config={...this.config,...t}}getStats(){return{sessionId:this.sessionId,model:this.model,toolCount:this.tools.length,messageCount:this.messages.length,toolCallCount:this.toolCallCount,maxIterations:this.maxToolCallIterations,isReady:this.isReady,isPaused:this.isPaused,isTerminated:this.isTerminated,isProcessing:this.isProcessing}}pause(){if(this.isPaused){console.log("Agent is already paused");return}this.isPaused=!0,this.pausePromise=new Promise(t=>{this.pauseResolve=t}),console.log(`Agent ${this.sessionId} paused`)}resume(){if(!this.isPaused){console.log("Agent is not paused");return}this.isPaused=!1,this.pauseResolve&&(this.pauseResolve(),this.pauseResolve=null),this.pausePromise=null,console.log(`Agent ${this.sessionId} resumed`)}terminate(){this.isTerminated=!0,this.isProcessing=!1,this.isPaused&&this.resume(),console.log(`Agent ${this.sessionId} terminated`)}resetTermination(){this.isTerminated=!1,console.log(`Agent ${this.sessionId} termination reset`)}getIsPaused(){return this.isPaused}getIsTerminated(){return this.isTerminated}getIsProcessing(){return this.isProcessing}async waitForResume(t=6e5){if(!this.isPaused)return;const _=new Promise((b,C)=>{setTimeout(()=>C(new Error("Pause timeout exceeded")),t)});try{await Promise.race([this.pausePromise,_])}catch(b){console.warn(`Agent ${this.sessionId} pause timeout:`,b.message),this.resume()}}}class xs{constructor(){this.agents=new Map,this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7,stream:!0}}createAgent(t,_={}){const b={...this.defaultConfig,..._},C=new Es({sessionId:t,...b});return this.agents.set(t,C),C}getAgent(t){return this.agents.get(t)}removeAgent(t){return this.agents.delete(t)}getAllSessions(){return Array.from(this.agents.keys())}updateDefaultConfig(t){this.defaultConfig={...this.defaultConfig,...t}}getStats(){return{agentCount:this.agents.size,sessions:Array.from(this.agents.keys()),defaultConfig:{...this.defaultConfig,apiKey:this.defaultConfig.apiKey?"***":""}}}}let Ls=class{constructor(){this.storageKey="ai-agent-config",this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7,stream:!0,maxToolCallIterations:30}}load(){try{const t=localStorage.getItem(this.storageKey);if(t)return{...this.defaultConfig,...JSON.parse(t)}}catch(t){console.warn("Failed to load AI agent config from localStorage:",t)}return{...this.defaultConfig}}save(t){try{return localStorage.setItem(this.storageKey,JSON.stringify(t)),!0}catch(_){return console.error("Failed to save AI agent config to localStorage:",_),!1}}reset(){try{return localStorage.removeItem(this.storageKey),{...this.defaultConfig}}catch(t){return console.error("Failed to reset AI agent config:",t),{...this.defaultConfig}}}validate(t){const _=[];return(!t.endpoint||!t.endpoint.startsWith("http"))&&_.push("Invalid endpoint URL"),(!t.model||t.model.trim()==="")&&_.push("Model ID is required"),(!t.apiKey||t.apiKey.trim()==="")&&_.push("API Key is required"),t.maxTokens&&(t.maxTokens<1||t.maxTokens>4e3)&&_.push("Max tokens must be between 1 and 4000"),t.temperature&&(t.temperature<0||t.temperature>2)&&_.push("Temperature must be between 0 and 2"),t.maxToolCallIterations&&(t.maxToolCallIterations<1||t.maxToolCallIterations>100)&&_.push("Max tool call iterations must be between 1 and 100"),{valid:_.length===0,errors:_}}};const me=new xs,De=new Ls;const As={function:{name:"terminal_input",description:"Send input to the terminal. Supports single keys, key combinations, command lines, or command sequences.",parameters:{type:"object",properties:{mode:{type:"string",enum:["single_key","key_combo","command_line","command_group"],description:"Input mode: single_key for individual keys, key_combo for key combinations, command_line for single commands, command_group for multiple commands"},single_key:{type:"string",description:"Single key to send (required when mode is single_key). Examples: Enter, Tab, Escape, ArrowUp, etc."},key_combo:{type:"string",description:"Key combination to send (required when mode is key_combo). Examples: Ctrl+C, Ctrl+Z, Alt+F4, etc."},command_line:{type:"string",description:"Command line to send (required when mode is command_line). Examples: ls -la, pwd, cat file.txt"},command_group:{type:"array",items:{type:"string"},description:"Array of commands to send sequentially (required when mode is command_group)"},execute:{type:"boolean",default:!0,description:"Whether to execute command immediately (press Enter). Only applies to command_line and command_group modes"},delay:{type:"number",default:100,description:"Delay in milliseconds between commands in command_group mode"}},required:["mode"],oneOf:[{properties:{mode:{const:"single_key"}},required:["single_key"]},{properties:{mode:{const:"key_combo"}},required:["key_combo"]},{properties:{mode:{const:"command_line"}},required:["command_line"]},{properties:{mode:{const:"command_group"}},required:["command_group"]}]}},async execute(E){try{if(!ie.isServiceReady())return{success:!1,error:"Terminal service is not ready",mode:E.mode};const{mode:t,single_key:_,key_combo:b,command_line:C,command_group:x,execute:L=!0,delay:s=100}=E;let o;switch(t){case"single_key":return _?(o=await ie.sendKey(_),{success:o,mode:"single_key",key:_,message:o?`Sent key: ${_}`:`Failed to send key: ${_}`}):{success:!1,error:"single_key is required for single_key mode"};case"key_combo":return b?(o=await ie.sendKeyCombo(b),{success:o,mode:"key_combo",combination:b,message:o?`Sent key combination: ${b}`:`Failed to send key combination: ${b}`}):{success:!1,error:"key_combo is required for key_combo mode"};case"command_line":return C?(o=await ie.sendCommand(C,L),{success:o,mode:"command_line",command:C,executed:L,message:o?`${L?"Executed":"Sent"} command: ${C}`:`Failed to send command: ${C}`}):{success:!1,error:"command_line is required for command_line mode"};case"command_group":return!x||!Array.isArray(x)?{success:!1,error:"command_group must be an array for command_group mode"}:(o=await ie.sendCommands(x,s),{success:o,mode:"command_group",commands:x,delay:s,executed:L,message:o?`Executed ${x.length} commands with ${s}ms delay`:"Failed to execute command group"});default:return{success:!1,error:`Invalid mode: ${t}. Must be one of: single_key, key_combo, command_line, command_group`}}}catch(t){return{success:!1,error:t.message,mode:E.mode}}}},Rs={function:{name:"terminal_output",description:"Get output content from the terminal. Can retrieve single line, multiple lines, or full screen content.",parameters:{type:"object",properties:{mode:{type:"string",enum:["current_line","multiple_lines","full_screen","line_range"],description:"Output mode: current_line for cursor line, multiple_lines for recent lines, full_screen for all content, line_range for specific range"},line_count:{type:"number",minimum:1,maximum:100,description:"Number of recent lines to retrieve (required for multiple_lines mode)"},start_line:{type:"number",minimum:0,description:"Starting line number for line_range mode (0-based)"},end_line:{type:"number",minimum:0,description:"Ending line number for line_range mode (0-based, inclusive)"}},required:["mode"],oneOf:[{properties:{mode:{const:"current_line"}}},{properties:{mode:{const:"multiple_lines"}},required:["line_count"]},{properties:{mode:{const:"full_screen"}}},{properties:{mode:{const:"line_range"}},required:["start_line","end_line"]}]}},async execute(E){try{if(!ie.isServiceReady())return{success:!1,error:"Terminal service is not ready",mode:E.mode};const{mode:t,line_count:_,start_line:b,end_line:C}=E;switch(t){case"current_line":const x=ie.getCurrentLineContent(),L=ie.getCursorPosition();return`${x}`,x.length,{success:!0,mode:"current_line",content:x,cursor_position:L,message:`Retrieved current line content (${x.length} characters at position ${L.x},${L.y})`};case"multiple_lines":if(!_||_<1)return{success:!1,error:"line_count must be a positive number for multiple_lines mode"};const s=ie.getCursorPosition(),o=Math.max(0,s.y-_+1),l=ie.getLineRangeContent(o,s.y);return s.y,l.length,l.slice(0,3),{success:!0,mode:"multiple_lines",content:l,content_string:l.join(`
`),line_count:l.length,start_line:o,end_line:s.y,message:`Retrieved ${l.length} recent lines (from line ${o} to ${s.y})`};case"full_screen":if(!ie.getAllContent)return console.error("[TerminalTools] xtermService.getAllContent method not available"),{success:!1,error:"getAllContent method not available on xtermService"};if(!ie.getTerminalDimensions)return console.error("[TerminalTools] xtermService.getTerminalDimensions method not available"),{success:!1,error:"getTerminalDimensions method not available on xtermService"};const c=ie.getAllContent(),a=ie.getTerminalDimensions();return c.length,c.slice(0,3),c.slice(-3),c.join(`
`).length,c.length===0?void 0:c.every(p=>p.trim()==="")&&void 0,{success:!0,mode:"full_screen",content:c,content_string:c.join(`
`),line_count:c.length,terminal_dimensions:a,message:`Retrieved full screen content (${c.length} lines, ${a.cols}x${a.rows})`};case"line_range":if(b===void 0||C===void 0)return{success:!1,error:"start_line and end_line are required for line_range mode"};if(b<0||C<b)return{success:!1,error:"Invalid line range: start_line must be >= 0 and end_line must be >= start_line"};const u=ie.getLineRangeContent(b,C);return{success:!0,mode:"line_range",content:u,content_string:u.join(`
`),line_count:u.length,start_line:b,end_line:C,message:`Retrieved lines ${b} to ${C} (${u.length} lines)`};default:return{success:!1,error:`Invalid mode: ${t}. Must be one of: current_line, multiple_lines, full_screen, line_range`}}}catch(t){return{success:!1,error:t.message,mode:E.mode}}}},Ds={function:{name:"wait",description:"Wait for a specified duration before continuing. Useful when waiting for commands to complete or UI to load.",parameters:{type:"object",properties:{duration:{type:"number",minimum:100,maximum:3e4,description:"Duration to wait in milliseconds (100ms to 30s)"},reason:{type:"string",description:"Optional reason for waiting (for logging/debugging purposes)"}},required:["duration"]}},async execute(E){try{const{duration:t,reason:_}=E;if(t<100||t>3e4)return{success:!1,error:"Duration must be between 100ms and 30000ms (30 seconds)",duration:t};const b=Date.now();await new Promise(x=>setTimeout(x,t));const C=Date.now()-b;return{success:!0,duration:t,actual_duration:C,reason:_||"No reason specified",message:`Waited for ${C}ms${_?` (${_})`:""}`}}catch(t){return{success:!1,error:t.message,duration:E.duration}}}};function Ts(E){return E.addTool(As),E.addTool(Rs),E.addTool(Ds),E}class Bs{constructor(){this.sessionId=this.generateSessionId(),this.currentAgent=null,this.onChunk=null,this.onToolCall=null,this.onComplete=null,this.onError=null}async initialize(){return console.log("AI Agent Integration initialized"),!0}async sendMessage(t,_={}){let b=me.getAgent(this.sessionId);if(!b){const C=De.load();b=me.createAgent(this.sessionId,{...C,onChunk:x=>this.handleChunk(x),onToolCall:x=>this.handleToolCall(x),onComplete:x=>this.handleComplete(x),onError:x=>this.handleError(x)}),Ts(b),await b.initialize(),this.currentAgent=b}try{await b.processMessage(t,_)}catch(C){this.handleError(C)}}handleChunk(t){this.onChunk&&this.onChunk(t)}handleToolCall(t){this.onToolCall&&this.onToolCall(t)}handleComplete(t){this.onComplete&&this.onComplete(t)}handleError(t){this.onError&&this.onError(t)}async configureAgent(t){try{const _=De.validate(t);if(!_.valid)throw new Error(`Invalid configuration: ${_.errors.join(", ")}`);return De.save(t),me.updateDefaultConfig(t),this.currentAgent&&(me.removeAgent(this.sessionId),this.currentAgent=null),{success:!0,message:"AI agent configured successfully"}}catch(_){return console.error("Error configuring AI agent:",_),{success:!1,error:_.message}}}getAgentConfig(){return De.load()}async testAgentConnection(){try{const t=De.load(),_=me.createAgent("test-session",t);await _.initialize();const b=await _.processMessage("Hello, this is a test message.",{});return me.removeAgent("test-session"),{success:!0,message:"Connection test successful",response:b.content}}catch(t){return console.error("AI agent connection test failed:",t),{success:!1,error:t.message}}}getAgentStats(){return this.currentAgent?this.currentAgent.getStats():me.getStats()}clearSession(){this.currentAgent&&this.currentAgent.clearSession()}resetSession(){this.currentAgent&&(me.removeAgent(this.sessionId),this.currentAgent=null),this.sessionId=this.generateSessionId()}getSessionHistory(){return this.currentAgent?this.currentAgent.getMessages():[]}generateSessionId(){return"session_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)}getSessionId(){return this.sessionId}setCallbacks(t){this.onChunk=t.onChunk||null,this.onToolCall=t.onToolCall||null,this.onComplete=t.onComplete||null,this.onError=t.onError||null}isAgentReady(){return this.currentAgent&&this.currentAgent.isReady}pauseAgent(){return this.currentAgent?(this.currentAgent.pause(),!0):!1}resumeAgent(){return this.currentAgent?(this.currentAgent.resume(),!0):!1}terminateAgent(){return this.currentAgent?(this.currentAgent.terminate(),!0):!1}resetAgentTermination(){return this.currentAgent?(this.currentAgent.resetTermination(),!0):!1}isAgentPaused(){return this.currentAgent?this.currentAgent.getIsPaused():!1}isAgentTerminated(){return this.currentAgent?this.currentAgent.getIsTerminated():!1}isAgentProcessing(){return this.currentAgent?this.currentAgent.getIsProcessing():!1}getAgentControlState(){return this.currentAgent?{isPaused:this.currentAgent.getIsPaused(),isTerminated:this.currentAgent.getIsTerminated(),isProcessing:this.currentAgent.getIsProcessing(),isReady:this.currentAgent.isReady}:{isPaused:!1,isTerminated:!1,isProcessing:!1,isReady:!1}}getTerminalContext(){const t={};if(window.xtermService&&window.xtermService.isServiceReady())try{t.currentLine=window.xtermService.getCurrentLineContent(),t.terminalDimensions=window.xtermService.getTerminalDimensions(),t.cursorPosition=window.xtermService.getCursorPosition();const _=window.xtermService.getLineRangeContent(Math.max(0,t.cursorPosition.y-5),t.cursorPosition.y);t.recentOutput=_.join(`
`)}catch(_){console.warn("Error getting terminal context:",_)}return t}}const te=new Bs;class Is{constructor(){}initialize(){return console.log("Terminal Integration initialized"),!0}async executeCommands(t){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for command execution"),[];const _=[];for(const b of t)try{console.log("Executing AI-requested command:",b);const C=window.xtermService.getCursorPosition();b.type==="single_key"?await window.xtermService.sendKey(b.key):b.type==="key_combo"?await window.xtermService.sendKeyCombo(b.combination):b.type==="command"?await window.xtermService.sendCommand(b.command,b.execute!==!1):b.type==="commands"&&await window.xtermService.sendCommands(b.commands,b.delay||100),await new Promise(s=>setTimeout(s,500));const x=window.xtermService.getCursorPosition();let L="";x.y>C.y&&(L=window.xtermService.getLineRangeContent(C.y,x.y).join(`
`).trim()),_.push({command:b,output:L,success:!0}),t.length>1&&await new Promise(s=>setTimeout(s,200))}catch(C){console.error("Error executing command in terminal:",C),_.push({command:b,error:C.message,success:!1})}return console.log("AI command execution results:",_),_}async provideExecutionFeedback(t){try{const b=`Command execution results:

${t.map(C=>C.success?`Command executed: ${JSON.stringify(C.command)}
Output: ${C.output||"(no output)"}`:`Command failed: ${JSON.stringify(C.command)}
Error: ${C.error}`).join(`

`)}`;console.log("Providing execution feedback to AI:",b)}catch(_){console.error("Error providing execution feedback:",_)}}async executeCommand(t){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for command execution"),!1;try{return await window.xtermService.sendCommand(t,!0),console.log("Manual command executed:",t),!0}catch(_){return console.error("Error executing manual command:",_),!1}}async sendTextToTerminal(t){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for text input"),!1;try{return await window.xtermService.sendRawData(t),console.log("Text sent to terminal:",t),!0}catch(_){return console.error("Error sending text to terminal:",_),!1}}getTerminalOutput(t=10){if(!window.xtermService||!window.xtermService.isServiceReady())return"";try{const _=window.xtermService.getCursorPosition(),b=Math.max(0,_.y-t);return window.xtermService.getLineRangeContent(b,_.y).join(`
`).trim()}catch(_){return console.error("Error getting terminal output:",_),""}}getTerminalContext(){const t={};if(window.xtermService&&window.xtermService.isServiceReady())try{t.currentLine=window.xtermService.getCurrentLineContent(),t.terminalDimensions=window.xtermService.getTerminalDimensions(),t.cursorPosition=window.xtermService.getCursorPosition();const _=window.xtermService.getLineRangeContent(Math.max(0,t.cursorPosition.y-5),t.cursorPosition.y);t.recentOutput=_.join(`
`)}catch(_){console.warn("Error getting terminal context:",_)}return t}isTerminalReady(){return window.xtermService&&window.xtermService.isServiceReady()}}const Ce=new Is;class Ms{constructor(){this.input=null,this.sendBtn=null,this.isInitialized=!1}initialize(){if(this.input=document.getElementById("aiInput"),this.sendBtn=document.getElementById("aiSend"),!this.input||!this.sendBtn)return console.error("AI Dialog input elements not found"),!1;const t=re.initialize(),_=ue.initialize(),b=te.initialize(),C=Ce.initialize();return!t||!_||!b||!C?(console.error("Failed to initialize AI Dialog modules"),!1):(this.setupEventListeners(),this.setupCallbacks(),this.setupAutoResize(),this.isInitialized=!0,console.log("Refactored AI Dialog Service initialized"),!0)}setupEventListeners(){this.sendBtn.addEventListener("click",()=>this.handleSendButtonClick()),this.input.addEventListener("keydown",t=>{t.key==="Enter"&&!t.shiftKey&&(t.preventDefault(),this.handleSendButtonClick())})}setupCallbacks(){re.setCallbacks({onShow:()=>{this.input.focus(),this.updateUIState()},onHide:()=>{},onPauseResume:()=>{this.handlePauseResume()}}),te.setCallbacks({onChunk:t=>ue.handleStreamingChunk(t),onToolCall:t=>ue.handleToolCall(t),onComplete:t=>{ue.handleStreamingComplete(t),this.handleAgentComplete()},onError:t=>{ue.handleStreamingError(t),this.handleAgentComplete()}})}setupAutoResize(){this.input.addEventListener("input",()=>{this.input.style.height="auto",this.input.style.height=Math.min(this.input.scrollHeight,120)+"px"})}handleSendButtonClick(){re.isSendButtonInTerminateMode()?this.terminateAgent():this.sendMessage()}async sendMessage(){const t=this.input.value.trim();if(t){te.resetAgentTermination(),ue.addMessage(t,"user"),this.input.value="",this.input.style.height="auto",re.setSendButtonToTerminate(),ue.startStreamingMessage();try{const _=Ce.getTerminalContext();await te.sendMessage(t,_)}catch(_){console.error("Error sending message to AI agent:",_),ue.handleStreamingError(_),this.handleAgentComplete()}}}terminateAgent(){console.log("Terminating agent..."),te.terminateAgent(),this.handleAgentComplete()}handleAgentComplete(){re.setSendButtonToSend(),this.sendBtn.disabled=!1,this.updateUIState()}handlePauseResume(){te.getAgentControlState().isPaused?(te.resumeAgent(),console.log("Agent resumed")):(te.pauseAgent(),console.log("Agent paused")),this.updateUIState()}updateUIState(){const t=te.getAgentControlState();re.updatePauseResumeButton(t.isPaused),re.setPauseResumeButtonEnabled(t.isReady),!t.isProcessing&&!re.isSendButtonInTerminateMode()&&(this.sendBtn.disabled=!1)}show(){re.showDialog(),this.updateUIState()}hide(){re.hideDialog()}toggle(){re.toggleDialog()}isVisible(){return re.isDialogVisible()}clearMessages(){ue.clearMessages()}getMessages(){return ue.getMessages()}async configureAIAgent(t){return await te.configureAgent(t)}getAIAgentConfig(){return te.getAgentConfig()}async testAIAgentConnection(){return await te.testAgentConnection()}getAIAgentStats(){return te.getAgentStats()}clearSession(){te.clearSession(),this.clearMessages()}resetSession(){te.resetSession(),this.clearMessages()}getSessionHistory(){return te.getSessionHistory()}getSessionId(){return te.getSessionId()}async executeCommand(t){return await Ce.executeCommand(t)}async sendTextToTerminal(t){return await Ce.sendTextToTerminal(t)}getTerminalOutput(t=10){return Ce.getTerminalOutput(t)}isServiceInitialized(){return this.isInitialized}isAIAgentReady(){return te.isAgentReady()}isTerminalReady(){return Ce.isTerminalReady()}getServiceStatus(){return{initialized:this.isInitialized,dialogVisible:this.isVisible(),aiAgentReady:this.isAIAgentReady(),terminalReady:this.isTerminalReady(),sessionId:this.getSessionId(),messageCount:this.getMessages().length}}getAgentControlState(){return te.getAgentControlState()}pauseAgent(){return te.pauseAgent()}resumeAgent(){return te.resumeAgent()}terminateAgent(){return te.terminateAgent()}setSendButtonToTerminate(){return re.setSendButtonToTerminate()}setSendButtonToSend(){return re.setSendButtonToSend()}isSendButtonInTerminateMode(){return re.isSendButtonInTerminateMode()}}const Ie=new Ms;class Ps{constructor(){this.config={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7},this.sessions=new Map,this.isReady=!1,this.systemPrompt=`You are a helpful AI assistant for a web terminal application. You can help users with:

1. **Terminal Commands**: Explain, suggest, and help execute terminal commands
2. **File Operations**: Help with file management, navigation, and manipulation  
3. **System Administration**: Provide guidance on system tasks and troubleshooting
4. **Programming**: Help with coding tasks, debugging, and development workflows

You have access to a web terminal where you can suggest commands for the user to execute.
When suggesting commands, always:
- Explain what the command does
- Mention any potential risks or side effects
- Provide context about when and why to use it

You can suggest commands by mentioning them in your response. The system may automatically execute safe commands you suggest.

Current context: Web terminal environment with xterm.js frontend.`}async initialize(t={}){if(this.config={...this.config,...t},!this.config.apiKey)return console.warn("AI Agent: No API key provided. Please set API key before using."),!1;try{return await this.testConnection(),this.isReady=!0,console.log("Frontend AI Agent initialized successfully"),!0}catch(_){return console.error("Failed to initialize AI Agent:",_),this.isReady=!1,!1}}async testConnection(){const t=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model,messages:[{role:"user",content:"Hello"}],max_tokens:10})});if(!t.ok)throw new Error(`API test failed: ${t.status} ${t.statusText}`);return!0}updateConfig(t){this.config={...this.config,...t},console.log("AI Agent configuration updated:",this.config)}getConfig(){const{apiKey:t,..._}=this.config;return{..._,apiKey:t?"***":""}}isAgentReady(){return this.isReady&&this.config.apiKey}async processMessage(t,_={}){var L;if(!this.isAgentReady())throw new Error("AI Agent not ready. Please check configuration.");const b=_.sessionId||"default",C=_.context||{},x=this.getSession(b);x.messages.push({role:"user",content:t,timestamp:new Date().toISOString()});try{const s=[{role:"system",content:this.systemPrompt},...x.messages.map(u=>({role:u.role,content:u.content}))];Object.keys(C).length>0&&s.push({role:"system",content:`Current context: ${JSON.stringify(C,null,2)}`});const o=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model,messages:s,max_tokens:this.config.maxTokens,temperature:this.config.temperature})});if(!o.ok)throw new Error(`API request failed: ${o.status} ${o.statusText}`);const l=await o.json(),c=l.choices[0].message.content;x.messages.push({role:"assistant",content:c,timestamp:new Date().toISOString()});const a=this.extractCommandsFromResponse(c);return{content:c,commands:a,sessionId:b,timestamp:new Date().toISOString(),tokensUsed:((L=l.usage)==null?void 0:L.total_tokens)||0}}catch(s){throw console.error("Error processing message:",s),s}}extractCommandsFromResponse(t){const _=[],b=[/```(?:bash|shell|sh)?\s*\n([^`]+)\n```/gi,/`([a-z]+(?:\s+[-\w\.\/]+)*)`/gi];for(const x of b){let L;for(;(L=x.exec(t))!==null;){const s=L[1].trim();this.isValidCommand(s)&&_.push({type:"command",command:s,execute:!0})}}return _.filter((x,L,s)=>L===s.findIndex(o=>o.command===x.command)).slice(0,3)}isValidCommand(t){const _=t.split(" ")[0];return["ls","pwd","whoami","date","uptime","uname","which","cat","head","tail","grep","find","wc","sort","uniq","ps","top","df","du","free","echo","history","clear"].includes(_)&&t.length<100&&!t.includes("&&")&&!t.includes("||")&&!t.includes(";")}getSession(t){this.sessions.has(t)||this.sessions.set(t,{id:t,messages:[],createdAt:new Date().toISOString(),lastActivity:new Date().toISOString()});const _=this.sessions.get(t);return _.lastActivity=new Date().toISOString(),_}getSessionHistory(t){const _=this.sessions.get(t);return _?_.messages:[]}clearSession(t){this.sessions.delete(t)}getAllSessions(){return Array.from(this.sessions.keys())}getStats(){return{ready:this.isReady,model:this.config.model,endpoint:this.config.endpoint,activeSessions:this.sessions.size,totalMessages:Array.from(this.sessions.values()).reduce((t,_)=>t+_.messages.length,0)}}}class Os{constructor(){this.storageKey="ai-agent-config",this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7}}load(){try{const t=localStorage.getItem(this.storageKey);if(t)return{...this.defaultConfig,...JSON.parse(t)}}catch(t){console.warn("Failed to load AI agent config from localStorage:",t)}return{...this.defaultConfig}}save(t){try{return localStorage.setItem(this.storageKey,JSON.stringify(t)),!0}catch(_){return console.error("Failed to save AI agent config to localStorage:",_),!1}}reset(){try{return localStorage.removeItem(this.storageKey),{...this.defaultConfig}}catch(t){return console.error("Failed to reset AI agent config:",t),{...this.defaultConfig}}}validate(t){const _=[];return(!t.endpoint||!t.endpoint.startsWith("http"))&&_.push("Invalid endpoint URL"),(!t.model||t.model.trim()==="")&&_.push("Model ID is required"),(!t.apiKey||t.apiKey.trim()==="")&&_.push("API Key is required"),t.maxTokens&&(t.maxTokens<1||t.maxTokens>4e3)&&_.push("Max tokens must be between 1 and 4000"),t.temperature&&(t.temperature<0||t.temperature>2)&&_.push("Temperature must be between 0 and 2"),{valid:_.length===0,errors:_}}}const et=new Os,Hs=new Ps;class Fs{constructor(){this.isVisible=!1,this.dialog=null,this.form=null,this.statusElement=null,this.settingsBtn=null,this.closeBtn=null}initialize(){return this.dialog=document.getElementById("settingsDialog"),this.form=document.getElementById("aiSettingsForm"),this.statusElement=document.getElementById("settingsStatus"),this.settingsBtn=document.getElementById("settingsBtn"),this.closeBtn=document.getElementById("settingsDialogClose"),!this.dialog||!this.form||!this.statusElement||!this.settingsBtn||!this.closeBtn?(console.error("Settings dialog elements not found"),!1):(this.setupEventListeners(),this.loadCurrentSettings(),console.log("Settings Dialog Service initialized"),!0)}setupEventListeners(){this.settingsBtn.addEventListener("click",()=>this.show()),this.closeBtn.addEventListener("click",()=>this.hide()),this.dialog.addEventListener("click",t=>{t.target===this.dialog&&this.hide()}),this.form.addEventListener("submit",t=>{t.preventDefault(),this.saveSettings()}),document.getElementById("testConnection").addEventListener("click",()=>{this.testConnection()}),document.getElementById("resetSettings").addEventListener("click",()=>{this.resetSettings()}),document.addEventListener("keydown",t=>{t.key==="Escape"&&this.isVisible&&this.hide()})}loadCurrentSettings(){const t=et.load();document.getElementById("apiKey").value=t.apiKey||"",document.getElementById("endpoint").value=t.endpoint||"",document.getElementById("model").value=t.model||"",document.getElementById("maxTokens").value=t.maxTokens||"",document.getElementById("temperature").value=t.temperature||""}show(){this.isVisible=!0,this.dialog.classList.remove("settings-dialog-hidden"),this.loadCurrentSettings(),this.clearStatus(),document.getElementById("apiKey").focus()}hide(){this.isVisible=!1,this.dialog.classList.add("settings-dialog-hidden"),this.clearStatus()}async saveSettings(){try{const t=new FormData(this.form),_={apiKey:t.get("apiKey").trim(),endpoint:t.get("endpoint").trim(),model:t.get("model").trim(),maxTokens:parseInt(t.get("maxTokens"))||1e3,temperature:parseFloat(t.get("temperature"))||.7},b=await Ie.configureAIAgent(_);b.success?(this.showStatus("Settings saved successfully!","success"),setTimeout(()=>{this.hide()},1500)):this.showStatus(`Error: ${b.error}`,"error")}catch(t){console.error("Error saving settings:",t),this.showStatus(`Error saving settings: ${t.message}`,"error")}}async testConnection(){try{this.showStatus("Testing connection...","info");const t=new FormData(this.form),_={apiKey:t.get("apiKey").trim(),endpoint:t.get("endpoint").trim(),model:t.get("model").trim(),maxTokens:parseInt(t.get("maxTokens"))||1e3,temperature:parseFloat(t.get("temperature"))||.7};await Ie.configureAIAgent(_);const b=await Ie.testAIAgentConnection();b.success?this.showStatus("Connection test successful!","success"):this.showStatus(`Connection test failed: ${b.error}`,"error")}catch(t){console.error("Error testing connection:",t),this.showStatus(`Connection test failed: ${t.message}`,"error")}}resetSettings(){if(confirm("Are you sure you want to reset all settings to defaults?")){const t=et.reset();document.getElementById("apiKey").value=t.apiKey,document.getElementById("endpoint").value=t.endpoint,document.getElementById("model").value=t.model,document.getElementById("maxTokens").value=t.maxTokens,document.getElementById("temperature").value=t.temperature,this.showStatus("Settings reset to defaults","success")}}showStatus(t,_="info"){this.statusElement.textContent=t,this.statusElement.className=`settings-status ${_}`}clearStatus(){this.statusElement.textContent="",this.statusElement.className="settings-status"}isDialogVisible(){return this.isVisible}}const Bt=new Fs;class $s{constructor(){this.container=null,this.keyboardElement=null,this.isVisible=!1,this.currentLayout="default",this.shiftPressed=!1,this.ctrlPressed=!1,this.altPressed=!1,this.onKeyPress=null,this.layouts={default:[["`","1","2","3","4","5","6","7","8","9","0","-","=","Backspace"],["Tab","q","w","e","r","t","y","u","i","o","p","[","]","\\"],["CapsLock","a","s","d","f","g","h","j","k","l",";","'","Enter"],["Shift","z","x","c","v","b","n","m",",",".","/","Shift"],["Ctrl","Alt","Space","Alt","Ctrl","ArrowLeft","ArrowUp","ArrowDown","ArrowRight"]],shift:[["~","!","@","#","$","%","^","&","*","(",")","_","+","Backspace"],["Tab","Q","W","E","R","T","Y","U","I","O","P","{","}","|"],["CapsLock","A","S","D","F","G","H","J","K","L",":",'"',"Enter"],["Shift","Z","X","C","V","B","N","M","<",">","?","Shift"],["Ctrl","Alt","Space","Alt","Ctrl","ArrowLeft","ArrowUp","ArrowDown","ArrowRight"]],function:[["Esc","F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12","Delete"],["Insert","Home","PageUp","","","","","","","","","","",""],["Delete","End","PageDown","","","","","","","","","","",""],["","","","","","","","","","","","","",""],["","","","","","","","","","","","","",""]]},this.keyDisplayNames={Backspace:"⌫",Tab:"⇥",CapsLock:"⇪",Enter:"⏎",Shift:"⇧",Ctrl:"Ctrl",Alt:"Alt",Space:"␣",ArrowLeft:"←",ArrowUp:"↑",ArrowDown:"↓",ArrowRight:"→",Esc:"Esc",Delete:"Del",Insert:"Ins",Home:"Home",End:"End",PageUp:"PgUp",PageDown:"PgDn"}}initialize(t,_){return this.container=document.getElementById(t),this.onKeyPress=_,this.container?(this.createKeyboard(),this.setupEventListeners(),console.log("Virtual Keyboard Service initialized"),!0):(console.error("Virtual keyboard container not found"),!1)}createKeyboard(){this.container.innerHTML=`
      <div class="virtual-keyboard-header">
        <div class="virtual-keyboard-controls">
          <button class="layout-btn" data-layout="default">ABC</button>
          <button class="layout-btn" data-layout="function">F1-12</button>
        </div>
        <button class="virtual-keyboard-close">×</button>
      </div>
      <div class="virtual-keyboard-body">
        <div class="virtual-keyboard-keys"></div>
      </div>
    `,this.keyboardElement=this.container.querySelector(".virtual-keyboard-keys"),this.renderLayout()}renderLayout(){const t=this.shiftPressed?this.layouts.shift:this.layouts[this.currentLayout];this.keyboardElement.innerHTML="",t.forEach((_,b)=>{const C=document.createElement("div");C.className="keyboard-row",_.forEach((x,L)=>{if(x==="")return;const s=document.createElement("button");s.className="keyboard-key",s.dataset.key=x,["Shift","Ctrl","Alt","CapsLock"].includes(x)&&(s.classList.add("modifier-key"),(x==="Shift"&&this.shiftPressed||x==="Ctrl"&&this.ctrlPressed||x==="Alt"&&this.altPressed)&&s.classList.add("active")),["Backspace","Tab","Enter","Space"].includes(x)&&s.classList.add("special-key"),x.startsWith("Arrow")&&s.classList.add("arrow-key"),x==="Space"?s.classList.add("space-key"):x==="Backspace"||x==="Enter"?s.classList.add("wide-key"):(x==="Tab"||x==="CapsLock")&&s.classList.add("medium-key"),s.textContent=this.keyDisplayNames[x]||x,C.appendChild(s)}),this.keyboardElement.appendChild(C)})}setupEventListeners(){this.keyboardElement.addEventListener("click",t=>{t.target.classList.contains("keyboard-key")&&this.handleKeyPress(t.target.dataset.key)}),this.container.addEventListener("click",t=>{t.target.classList.contains("layout-btn")?this.switchLayout(t.target.dataset.layout):t.target.classList.contains("virtual-keyboard-close")&&this.hide()}),this.container.addEventListener("mousedown",t=>{t.preventDefault()})}handleKeyPress(t){if(console.log("Virtual keyboard key pressed:",t,{shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed}),t==="Shift"){this.shiftPressed=!this.shiftPressed,this.renderLayout(),console.log("Shift toggled:",this.shiftPressed);return}if(t==="Ctrl"){this.ctrlPressed=!this.ctrlPressed,this.renderLayout(),console.log("Ctrl toggled:",this.ctrlPressed);return}if(t==="Alt"){this.altPressed=!this.altPressed,this.renderLayout(),console.log("Alt toggled:",this.altPressed);return}if(t==="CapsLock"){this.shiftPressed=!this.shiftPressed,this.renderLayout(),console.log("CapsLock toggled:",this.shiftPressed);return}let _={key:t,shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed,timestamp:Date.now()};console.log("Sending key data to callback:",_),this.onKeyPress&&this.onKeyPress(_),this.shiftPressed&&t!=="CapsLock"&&(this.shiftPressed=!1,this.renderLayout()),(this.ctrlPressed||this.altPressed)&&(this.ctrlPressed=!1,this.altPressed=!1,this.renderLayout())}switchLayout(t){this.currentLayout=t,this.renderLayout(),this.container.querySelectorAll(".layout-btn").forEach(_=>{_.classList.toggle("active",_.dataset.layout===t)})}show(){this.isVisible=!0,this.container.classList.remove("keyboard-hidden"),this.container.classList.add("keyboard-visible")}hide(){this.isVisible=!1,this.container.classList.remove("keyboard-visible"),this.container.classList.add("keyboard-hidden")}toggle(){this.isVisible?this.hide():this.show()}isKeyboardVisible(){return this.isVisible}typeString(t,_=100){let b=0;const C=()=>{if(b<t.length){const x=t[b];this.handleKeyPress(x),b++,setTimeout(C,_)}};C()}simulateKeyCombo(t){t.includes("Ctrl")&&(this.ctrlPressed=!0,this.renderLayout()),t.includes("Alt")&&(this.altPressed=!0,this.renderLayout()),t.includes("Shift")&&(this.shiftPressed=!0,this.renderLayout());const _=t.find(b=>!["Ctrl","Alt","Shift"].includes(b));_&&this.handleKeyPress(_)}getModifierStates(){return{shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed}}}const $e=new $s;let He=null,ye=0;const Te=document.getElementById("remainingTime"),zs=document.getElementById("extendBtn");function Ws(E){const t=Math.floor(E/3600),_=Math.floor(E%3600/60),b=E%60;return[t,_,b].map(C=>C.toString().padStart(2,"0")).join(":")}function gt(){Te.textContent=Ws(ye),Te.classList.remove("warning","danger"),ye<=300?Te.classList.add("danger"):ye<=1800&&Te.classList.add("warning")}function Ns(E){He&&clearInterval(He),ye=Math.max(0,E),gt(),He=setInterval(()=>{ye--,gt(),ye<=0&&(clearInterval(He),zs.disabled=!0,Te.textContent="Session expired")},1e3)}async function Us(){try{const E=await fetch("/api/status");if(E.ok){const t=await E.json();t.remaining_seconds!==void 0&&Ns(t.remaining_seconds)}}catch(E){console.error("Error initializing session timer:",E)}}let he=null;function js(E){if(!ae||!he||he.readyState!==WebSocket.OPEN){console.log("Terminal or socket not ready for keyboard input");return}let t="";const{key:_,shift:b,ctrl:C,alt:x}=E;switch(_){case"Enter":t="\r";break;case"Tab":t="	";break;case"Backspace":t="";break;case"Delete":t="\x1B[3~";break;case"Esc":t="\x1B";break;case"Space":t=" ";break;case"ArrowLeft":t="\x1B[D";break;case"ArrowUp":t="\x1B[A";break;case"ArrowDown":t="\x1B[B";break;case"ArrowRight":t="\x1B[C";break;case"Home":t="\x1B[H";break;case"End":t="\x1B[F";break;case"PageUp":t="\x1B[5~";break;case"PageDown":t="\x1B[6~";break;case"Insert":t="\x1B[2~";break;default:if(C&&_.length===1){const L=_.toLowerCase().charCodeAt(0);L>=97&&L<=122&&(t=String.fromCharCode(L-96))}else x&&_.length===1?t="\x1B"+_:t=_}t&&(console.log("Sending virtual keyboard input:",t,"from key:",E),he.send(JSON.stringify({type:"input",data:t})))}const Ks=document.getElementById("terminal"),ae=new Ht.Terminal({fontFamily:'Menlo, Monaco, "Courier New", monospace',fontSize:14,lineHeight:1.2,cursorBlink:!0,cursorStyle:"block",theme:{background:"#000000",foreground:"#f0f0f0",cursor:"#f0f0f0",selection:"rgba(255, 255, 255, 0.3)"}}),we=new Ft.FitAddon,qs=new $t.WebLinksAddon;ae.loadAddon(we);ae.loadAddon(qs);ae.open(Ks);we.fit();Us();function Vs(){console.log("Initializing virtual keyboard...");const E=document.getElementById("keyboardBtn");if(console.log("keyboardBtn:",E),!$e.initialize("keyboardContainer",js)){console.error("Failed to initialize virtual keyboard service");return}function _(){console.log("Toggle keyboard clicked, current state:",$e.isKeyboardVisible()),$e.toggle(),we&&we.fit&&setTimeout(()=>we.fit(),100)}E&&E.addEventListener("click",_)}function pt(){Vs(),Ie.initialize(),Bt.initialize()}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",pt):pt();const Xs=window.location.protocol==="https:"?"wss:":"ws:",It=`${Xs}//${window.location.host}/ws`;console.log(`Connecting to WebSocket at: ${It}`);he=new WebSocket(It);he.addEventListener("open",()=>{console.log("WebSocket connection established"),ae.writeln("Connected to terminal server"),ie.initialize(ae,he),window.xtermService=ie,window.aiDialogService=Ie,window.virtualKeyboardService=$e,window.frontendAIAgent=Hs,window.aiAgentConfig=et,window.settingsDialogService=Bt;const E=()=>{we.fit();const t={cols:ae.cols,rows:ae.rows};he.send(JSON.stringify({type:"resize",data:JSON.stringify(t)}))};E(),window.addEventListener("resize",E),ae.onData(t=>{console.log("Sending data to server:",t),he.send(JSON.stringify({type:"input",data:t}))})});he.addEventListener("message",E=>{try{const t=JSON.parse(E.data);t.type==="output"?ae.write(t.data):t.type==="error"&&ae.writeln(`\r
\x1B[31mError: ${t.data}\x1B[0m`)}catch(t){console.error("Failed to parse message:",t),ae.writeln(`\r
\x1B[31mError: Failed to parse server message\x1B[0m`)}});he.addEventListener("close",()=>{console.log("WebSocket connection closed"),ae.writeln(`\r
\x1B[33mConnection closed\x1B[0m`)});he.addEventListener("error",E=>{console.error("WebSocket error:",E),ae.writeln(`\r
\x1B[31mConnection error\x1B[0m`)});
