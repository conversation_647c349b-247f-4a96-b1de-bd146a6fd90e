var Rt=Object.defineProperty;var Bt=(x,o,m)=>o in x?Rt(x,o,{enumerable:!0,configurable:!0,writable:!0,value:m}):x[o]=m;var Y=(x,o,m)=>Bt(x,typeof o!="symbol"?o+"":o,m);(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const k of document.querySelectorAll('link[rel="modulepreload"]'))w(k);new MutationObserver(k=>{for(const M of k)if(M.type==="childList")for(const D of M.addedNodes)D.tagName==="LINK"&&D.rel==="modulepreload"&&w(D)}).observe(document,{childList:!0,subtree:!0});function m(k){const M={};return k.integrity&&(M.integrity=k.integrity),k.referrerPolicy&&(M.referrerPolicy=k.referrerPolicy),k.crossOrigin==="use-credentials"?M.credentials="include":k.crossOrigin==="anonymous"?M.credentials="omit":M.credentials="same-origin",M}function w(k){if(k.ep)return;k.ep=!0;const M=m(k);fetch(k.href,M)}})();var ut={exports:{}};(function(x,o){(function(m,w){x.exports=w()})(self,()=>(()=>{var m={4567:function(D,t,n){var l=this&&this.__decorate||function(r,h,f,v){var b,c=arguments.length,S=c<3?h:v===null?v=Object.getOwnPropertyDescriptor(h,f):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(r,h,f,v);else for(var E=r.length-1;E>=0;E--)(b=r[E])&&(S=(c<3?b(S):c>3?b(h,f,S):b(h,f))||S);return c>3&&S&&Object.defineProperty(h,f,S),S},u=this&&this.__param||function(r,h){return function(f,v){h(f,v,r)}};Object.defineProperty(t,"__esModule",{value:!0}),t.AccessibilityManager=void 0;const a=n(9042),d=n(6114),g=n(9924),p=n(844),_=n(5596),e=n(4725),i=n(3656);let s=t.AccessibilityManager=class extends p.Disposable{constructor(r,h){super(),this._terminal=r,this._renderService=h,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=document.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=document.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let f=0;f<this._terminal.rows;f++)this._rowElements[f]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[f]);if(this._topBoundaryFocusListener=f=>this._handleBoundaryFocus(f,0),this._bottomBoundaryFocusListener=f=>this._handleBoundaryFocus(f,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=document.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new g.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(f=>this._handleResize(f.rows))),this.register(this._terminal.onRender(f=>this._refreshRows(f.start,f.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(f=>this._handleChar(f))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(f=>this._handleTab(f))),this.register(this._terminal.onKey(f=>this._handleKey(f.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this._screenDprMonitor=new _.ScreenDprMonitor(window),this.register(this._screenDprMonitor),this._screenDprMonitor.setListener(()=>this._refreshRowsDimensions()),this.register((0,i.addDisposableDomListener)(window,"resize",()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,p.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(r){for(let h=0;h<r;h++)this._handleChar(" ")}_handleChar(r){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==r&&(this._charsToAnnounce+=r):this._charsToAnnounce+=r,r===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=a.tooMuchOutput)),d.isMac&&this._liveRegion.textContent&&this._liveRegion.textContent.length>0&&!this._liveRegion.parentNode&&setTimeout(()=>{this._accessibilityContainer.appendChild(this._liveRegion)},0))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0,d.isMac&&this._liveRegion.remove()}_handleKey(r){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(r)||this._charsToConsume.push(r)}_refreshRows(r,h){this._liveRegionDebouncer.refresh(r,h,this._terminal.rows)}_renderRows(r,h){const f=this._terminal.buffer,v=f.lines.length.toString();for(let b=r;b<=h;b++){const c=f.translateBufferLineToString(f.ydisp+b,!0),S=(f.ydisp+b+1).toString(),E=this._rowElements[b];E&&(c.length===0?E.innerText=" ":E.textContent=c,E.setAttribute("aria-posinset",S),E.setAttribute("aria-setsize",v))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(r,h){const f=r.target,v=this._rowElements[h===0?1:this._rowElements.length-2];if(f.getAttribute("aria-posinset")===(h===0?"1":`${this._terminal.buffer.lines.length}`)||r.relatedTarget!==v)return;let b,c;if(h===0?(b=f,c=this._rowElements.pop(),this._rowContainer.removeChild(c)):(b=this._rowElements.shift(),c=f,this._rowContainer.removeChild(b)),b.removeEventListener("focus",this._topBoundaryFocusListener),c.removeEventListener("focus",this._bottomBoundaryFocusListener),h===0){const S=this._createAccessibilityTreeNode();this._rowElements.unshift(S),this._rowContainer.insertAdjacentElement("afterbegin",S)}else{const S=this._createAccessibilityTreeNode();this._rowElements.push(S),this._rowContainer.appendChild(S)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(h===0?-1:1),this._rowElements[h===0?1:this._rowElements.length-2].focus(),r.preventDefault(),r.stopImmediatePropagation()}_handleResize(r){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let h=this._rowContainer.children.length;h<this._terminal.rows;h++)this._rowElements[h]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[h]);for(;this._rowElements.length>r;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const r=document.createElement("div");return r.setAttribute("role","listitem"),r.tabIndex=-1,this._refreshRowDimensions(r),r}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let r=0;r<this._terminal.rows;r++)this._refreshRowDimensions(this._rowElements[r])}}_refreshRowDimensions(r){r.style.height=`${this._renderService.dimensions.css.cell.height}px`}};t.AccessibilityManager=s=l([u(1,e.IRenderService)],s)},3614:(D,t)=>{function n(d){return d.replace(/\r?\n/g,"\r")}function l(d,g){return g?"\x1B[200~"+d+"\x1B[201~":d}function u(d,g,p,_){d=l(d=n(d),p.decPrivateModes.bracketedPasteMode&&_.rawOptions.ignoreBracketedPasteMode!==!0),p.triggerDataEvent(d,!0),g.value=""}function a(d,g,p){const _=p.getBoundingClientRect(),e=d.clientX-_.left-10,i=d.clientY-_.top-10;g.style.width="20px",g.style.height="20px",g.style.left=`${e}px`,g.style.top=`${i}px`,g.style.zIndex="1000",g.focus()}Object.defineProperty(t,"__esModule",{value:!0}),t.rightClickHandler=t.moveTextAreaUnderMouseCursor=t.paste=t.handlePasteEvent=t.copyHandler=t.bracketTextForPaste=t.prepareTextForTerminal=void 0,t.prepareTextForTerminal=n,t.bracketTextForPaste=l,t.copyHandler=function(d,g){d.clipboardData&&d.clipboardData.setData("text/plain",g.selectionText),d.preventDefault()},t.handlePasteEvent=function(d,g,p,_){d.stopPropagation(),d.clipboardData&&u(d.clipboardData.getData("text/plain"),g,p,_)},t.paste=u,t.moveTextAreaUnderMouseCursor=a,t.rightClickHandler=function(d,g,p,_,e){a(d,g,p),e&&_.rightClickSelect(d),g.value=_.selectionText,g.select()}},7239:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ColorContrastCache=void 0;const l=n(1505);t.ColorContrastCache=class{constructor(){this._color=new l.TwoKeyMap,this._css=new l.TwoKeyMap}setCss(u,a,d){this._css.set(u,a,d)}getCss(u,a){return this._css.get(u,a)}setColor(u,a,d){this._color.set(u,a,d)}getColor(u,a){return this._color.get(u,a)}clear(){this._color.clear(),this._css.clear()}}},3656:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.addDisposableDomListener=void 0,t.addDisposableDomListener=function(n,l,u,a){n.addEventListener(l,u,a);let d=!1;return{dispose:()=>{d||(d=!0,n.removeEventListener(l,u,a))}}}},6465:function(D,t,n){var l=this&&this.__decorate||function(e,i,s,r){var h,f=arguments.length,v=f<3?i:r===null?r=Object.getOwnPropertyDescriptor(i,s):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,i,s,r);else for(var b=e.length-1;b>=0;b--)(h=e[b])&&(v=(f<3?h(v):f>3?h(i,s,v):h(i,s))||v);return f>3&&v&&Object.defineProperty(i,s,v),v},u=this&&this.__param||function(e,i){return function(s,r){i(s,r,e)}};Object.defineProperty(t,"__esModule",{value:!0}),t.Linkifier2=void 0;const a=n(3656),d=n(8460),g=n(844),p=n(2585);let _=t.Linkifier2=class extends g.Disposable{get currentLink(){return this._currentLink}constructor(e){super(),this._bufferService=e,this._linkProviders=[],this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new d.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new d.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,g.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,g.toDisposable)(()=>{this._lastMouseEvent=void 0})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0}))}registerLinkProvider(e){return this._linkProviders.push(e),{dispose:()=>{const i=this._linkProviders.indexOf(e);i!==-1&&this._linkProviders.splice(i,1)}}}attachToDom(e,i,s){this._element=e,this._mouseService=i,this._renderService=s,this.register((0,a.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,a.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,a.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,a.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(e){if(this._lastMouseEvent=e,!this._element||!this._mouseService)return;const i=this._positionFromMouseEvent(e,this._element,this._mouseService);if(!i)return;this._isMouseOut=!1;const s=e.composedPath();for(let r=0;r<s.length;r++){const h=s[r];if(h.classList.contains("xterm"))break;if(h.classList.contains("xterm-hover"))return}this._lastBufferCell&&i.x===this._lastBufferCell.x&&i.y===this._lastBufferCell.y||(this._handleHover(i),this._lastBufferCell=i)}_handleHover(e){if(this._activeLine!==e.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(e,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,e)||(this._clearCurrentLink(),this._askForLink(e,!0))}_askForLink(e,i){var s,r;this._activeProviderReplies&&i||((s=this._activeProviderReplies)===null||s===void 0||s.forEach(f=>{f==null||f.forEach(v=>{v.link.dispose&&v.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=e.y);let h=!1;for(const[f,v]of this._linkProviders.entries())i?!((r=this._activeProviderReplies)===null||r===void 0)&&r.get(f)&&(h=this._checkLinkProviderResult(f,e,h)):v.provideLinks(e.y,b=>{var c,S;if(this._isMouseOut)return;const E=b==null?void 0:b.map(T=>({link:T}));(c=this._activeProviderReplies)===null||c===void 0||c.set(f,E),h=this._checkLinkProviderResult(f,e,h),((S=this._activeProviderReplies)===null||S===void 0?void 0:S.size)===this._linkProviders.length&&this._removeIntersectingLinks(e.y,this._activeProviderReplies)})}_removeIntersectingLinks(e,i){const s=new Set;for(let r=0;r<i.size;r++){const h=i.get(r);if(h)for(let f=0;f<h.length;f++){const v=h[f],b=v.link.range.start.y<e?0:v.link.range.start.x,c=v.link.range.end.y>e?this._bufferService.cols:v.link.range.end.x;for(let S=b;S<=c;S++){if(s.has(S)){h.splice(f--,1);break}s.add(S)}}}}_checkLinkProviderResult(e,i,s){var r;if(!this._activeProviderReplies)return s;const h=this._activeProviderReplies.get(e);let f=!1;for(let v=0;v<e;v++)this._activeProviderReplies.has(v)&&!this._activeProviderReplies.get(v)||(f=!0);if(!f&&h){const v=h.find(b=>this._linkAtPosition(b.link,i));v&&(s=!0,this._handleNewLink(v))}if(this._activeProviderReplies.size===this._linkProviders.length&&!s)for(let v=0;v<this._activeProviderReplies.size;v++){const b=(r=this._activeProviderReplies.get(v))===null||r===void 0?void 0:r.find(c=>this._linkAtPosition(c.link,i));if(b){s=!0,this._handleNewLink(b);break}}return s}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(e){if(!this._element||!this._mouseService||!this._currentLink)return;const i=this._positionFromMouseEvent(e,this._element,this._mouseService);i&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,i)&&this._currentLink.link.activate(e,this._currentLink.link.text)}_clearCurrentLink(e,i){this._element&&this._currentLink&&this._lastMouseEvent&&(!e||!i||this._currentLink.link.range.start.y>=e&&this._currentLink.link.range.end.y<=i)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,g.disposeArray)(this._linkCacheDisposables))}_handleNewLink(e){if(!this._element||!this._lastMouseEvent||!this._mouseService)return;const i=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);i&&this._linkAtPosition(e.link,i)&&(this._currentLink=e,this._currentLink.state={decorations:{underline:e.link.decorations===void 0||e.link.decorations.underline,pointerCursor:e.link.decorations===void 0||e.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,e.link,this._lastMouseEvent),e.link.decorations={},Object.defineProperties(e.link.decorations,{pointerCursor:{get:()=>{var s,r;return(r=(s=this._currentLink)===null||s===void 0?void 0:s.state)===null||r===void 0?void 0:r.decorations.pointerCursor},set:s=>{var r,h;!((r=this._currentLink)===null||r===void 0)&&r.state&&this._currentLink.state.decorations.pointerCursor!==s&&(this._currentLink.state.decorations.pointerCursor=s,this._currentLink.state.isHovered&&((h=this._element)===null||h===void 0||h.classList.toggle("xterm-cursor-pointer",s)))}},underline:{get:()=>{var s,r;return(r=(s=this._currentLink)===null||s===void 0?void 0:s.state)===null||r===void 0?void 0:r.decorations.underline},set:s=>{var r,h,f;!((r=this._currentLink)===null||r===void 0)&&r.state&&((f=(h=this._currentLink)===null||h===void 0?void 0:h.state)===null||f===void 0?void 0:f.decorations.underline)!==s&&(this._currentLink.state.decorations.underline=s,this._currentLink.state.isHovered&&this._fireUnderlineEvent(e.link,s))}}}),this._renderService&&this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(s=>{if(!this._currentLink)return;const r=s.start===0?0:s.start+1+this._bufferService.buffer.ydisp,h=this._bufferService.buffer.ydisp+1+s.end;if(this._currentLink.link.range.start.y>=r&&this._currentLink.link.range.end.y<=h&&(this._clearCurrentLink(r,h),this._lastMouseEvent&&this._element)){const f=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);f&&this._askForLink(f,!1)}})))}_linkHover(e,i,s){var r;!((r=this._currentLink)===null||r===void 0)&&r.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(i,!0),this._currentLink.state.decorations.pointerCursor&&e.classList.add("xterm-cursor-pointer")),i.hover&&i.hover(s,i.text)}_fireUnderlineEvent(e,i){const s=e.range,r=this._bufferService.buffer.ydisp,h=this._createLinkUnderlineEvent(s.start.x-1,s.start.y-r-1,s.end.x,s.end.y-r-1,void 0);(i?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(h)}_linkLeave(e,i,s){var r;!((r=this._currentLink)===null||r===void 0)&&r.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(i,!1),this._currentLink.state.decorations.pointerCursor&&e.classList.remove("xterm-cursor-pointer")),i.leave&&i.leave(s,i.text)}_linkAtPosition(e,i){const s=e.range.start.y*this._bufferService.cols+e.range.start.x,r=e.range.end.y*this._bufferService.cols+e.range.end.x,h=i.y*this._bufferService.cols+i.x;return s<=h&&h<=r}_positionFromMouseEvent(e,i,s){const r=s.getCoords(e,i,this._bufferService.cols,this._bufferService.rows);if(r)return{x:r[0],y:r[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(e,i,s,r,h){return{x1:e,y1:i,x2:s,y2:r,cols:this._bufferService.cols,fg:h}}};t.Linkifier2=_=l([u(0,p.IBufferService)],_)},9042:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.tooMuchOutput=t.promptLabel=void 0,t.promptLabel="Terminal input",t.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(D,t,n){var l=this&&this.__decorate||function(_,e,i,s){var r,h=arguments.length,f=h<3?e:s===null?s=Object.getOwnPropertyDescriptor(e,i):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")f=Reflect.decorate(_,e,i,s);else for(var v=_.length-1;v>=0;v--)(r=_[v])&&(f=(h<3?r(f):h>3?r(e,i,f):r(e,i))||f);return h>3&&f&&Object.defineProperty(e,i,f),f},u=this&&this.__param||function(_,e){return function(i,s){e(i,s,_)}};Object.defineProperty(t,"__esModule",{value:!0}),t.OscLinkProvider=void 0;const a=n(511),d=n(2585);let g=t.OscLinkProvider=class{constructor(_,e,i){this._bufferService=_,this._optionsService=e,this._oscLinkService=i}provideLinks(_,e){var i;const s=this._bufferService.buffer.lines.get(_-1);if(!s)return void e(void 0);const r=[],h=this._optionsService.rawOptions.linkHandler,f=new a.CellData,v=s.getTrimmedLength();let b=-1,c=-1,S=!1;for(let E=0;E<v;E++)if(c!==-1||s.hasContent(E)){if(s.loadCell(E,f),f.hasExtendedAttrs()&&f.extended.urlId){if(c===-1){c=E,b=f.extended.urlId;continue}S=f.extended.urlId!==b}else c!==-1&&(S=!0);if(S||c!==-1&&E===v-1){const T=(i=this._oscLinkService.getLinkData(b))===null||i===void 0?void 0:i.uri;if(T){const L={start:{x:c+1,y:_},end:{x:E+(S||E!==v-1?0:1),y:_}};let B=!1;if(!(h!=null&&h.allowNonHttpProtocols))try{const O=new URL(T);["http:","https:"].includes(O.protocol)||(B=!0)}catch{B=!0}B||r.push({text:T,range:L,activate:(O,$)=>h?h.activate(O,$,L):p(0,$),hover:(O,$)=>{var W;return(W=h==null?void 0:h.hover)===null||W===void 0?void 0:W.call(h,O,$,L)},leave:(O,$)=>{var W;return(W=h==null?void 0:h.leave)===null||W===void 0?void 0:W.call(h,O,$,L)}})}S=!1,f.hasExtendedAttrs()&&f.extended.urlId?(c=E,b=f.extended.urlId):(c=-1,b=-1)}}e(r)}};function p(_,e){if(confirm(`Do you want to navigate to ${e}?

WARNING: This link could potentially be dangerous`)){const i=window.open();if(i){try{i.opener=null}catch{}i.location.href=e}else console.warn("Opening link blocked as opener could not be cleared")}}t.OscLinkProvider=g=l([u(0,d.IBufferService),u(1,d.IOptionsService),u(2,d.IOscLinkService)],g)},6193:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.RenderDebouncer=void 0,t.RenderDebouncer=class{constructor(n,l){this._parentWindow=n,this._renderCallback=l,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._parentWindow.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(n){return this._refreshCallbacks.push(n),this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(n,l,u){this._rowCount=u,n=n!==void 0?n:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,n):n,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l,this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const n=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(n,l),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const n of this._refreshCallbacks)n(0);this._refreshCallbacks=[]}}},5596:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ScreenDprMonitor=void 0;const l=n(844);class u extends l.Disposable{constructor(d){super(),this._parentWindow=d,this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this.register((0,l.toDisposable)(()=>{this.clearListener()}))}setListener(d){this._listener&&this.clearListener(),this._listener=d,this._outerListener=()=>{this._listener&&(this._listener(this._parentWindow.devicePixelRatio,this._currentDevicePixelRatio),this._updateDpr())},this._updateDpr()}_updateDpr(){var d;this._outerListener&&((d=this._resolutionMediaMatchList)===null||d===void 0||d.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._listener&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._listener=void 0,this._outerListener=void 0)}}t.ScreenDprMonitor=u},3236:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Terminal=void 0;const l=n(3614),u=n(3656),a=n(6465),d=n(9042),g=n(3730),p=n(1680),_=n(3107),e=n(5744),i=n(2950),s=n(1296),r=n(428),h=n(4269),f=n(5114),v=n(8934),b=n(3230),c=n(9312),S=n(4725),E=n(6731),T=n(8055),L=n(8969),B=n(8460),O=n(844),$=n(6114),W=n(8437),z=n(2584),y=n(7399),A=n(5941),R=n(9074),I=n(2585),U=n(5435),N=n(4567),q=typeof window<"u"?window.document:null;class K extends L.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(C={}){super(C),this.browser=$,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new O.MutableDisposable),this._onCursorMove=this.register(new B.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new B.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new B.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new B.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new B.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new B.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new B.EventEmitter),this._onBlur=this.register(new B.EventEmitter),this._onA11yCharEmitter=this.register(new B.EventEmitter),this._onA11yTabEmitter=this.register(new B.EventEmitter),this._onWillOpen=this.register(new B.EventEmitter),this._setup(),this.linkifier2=this.register(this._instantiationService.createInstance(a.Linkifier2)),this.linkifier2.registerLinkProvider(this._instantiationService.createInstance(g.OscLinkProvider)),this._decorationService=this._instantiationService.createInstance(R.DecorationService),this._instantiationService.setService(I.IDecorationService,this._decorationService),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((P,F)=>this.refresh(P,F))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(P=>this._reportWindowsOptions(P))),this.register(this._inputHandler.onColor(P=>this._handleColorEvent(P))),this.register((0,B.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,B.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,B.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,B.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(P=>this._afterResize(P.cols,P.rows))),this.register((0,O.toDisposable)(()=>{var P,F;this._customKeyEventHandler=void 0,(F=(P=this.element)===null||P===void 0?void 0:P.parentNode)===null||F===void 0||F.removeChild(this.element)}))}_handleColorEvent(C){if(this._themeService)for(const P of C){let F,H="";switch(P.index){case 256:F="foreground",H="10";break;case 257:F="background",H="11";break;case 258:F="cursor",H="12";break;default:F="ansi",H="4;"+P.index}switch(P.type){case 0:const V=T.color.toColorRGB(F==="ansi"?this._themeService.colors.ansi[P.index]:this._themeService.colors[F]);this.coreService.triggerDataEvent(`${z.C0.ESC}]${H};${(0,A.toRgbString)(V)}${z.C1_ESCAPED.ST}`);break;case 1:if(F==="ansi")this._themeService.modifyColors(j=>j.ansi[P.index]=T.rgba.toColor(...P.color));else{const j=F;this._themeService.modifyColors(Z=>Z[j]=T.rgba.toColor(...P.color))}break;case 2:this._themeService.restoreColor(P.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(C){C?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(N.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(C){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(z.C0.ESC+"[I"),this.updateCursorStyle(C),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var C;return(C=this.textarea)===null||C===void 0?void 0:C.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(z.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const C=this.buffer.ybase+this.buffer.y,P=this.buffer.lines.get(C);if(!P)return;const F=Math.min(this.buffer.x,this.cols-1),H=this._renderService.dimensions.css.cell.height,V=P.getWidth(F),j=this._renderService.dimensions.css.cell.width*V,Z=this.buffer.y*this._renderService.dimensions.css.cell.height,te=F*this._renderService.dimensions.css.cell.width;this.textarea.style.left=te+"px",this.textarea.style.top=Z+"px",this.textarea.style.width=j+"px",this.textarea.style.height=H+"px",this.textarea.style.lineHeight=H+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,u.addDisposableDomListener)(this.element,"copy",P=>{this.hasSelection()&&(0,l.copyHandler)(P,this._selectionService)}));const C=P=>(0,l.handlePasteEvent)(P,this.textarea,this.coreService,this.optionsService);this.register((0,u.addDisposableDomListener)(this.textarea,"paste",C)),this.register((0,u.addDisposableDomListener)(this.element,"paste",C)),$.isFirefox?this.register((0,u.addDisposableDomListener)(this.element,"mousedown",P=>{P.button===2&&(0,l.rightClickHandler)(P,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,u.addDisposableDomListener)(this.element,"contextmenu",P=>{(0,l.rightClickHandler)(P,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),$.isLinux&&this.register((0,u.addDisposableDomListener)(this.element,"auxclick",P=>{P.button===1&&(0,l.moveTextAreaUnderMouseCursor)(P,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,u.addDisposableDomListener)(this.textarea,"keyup",C=>this._keyUp(C),!0)),this.register((0,u.addDisposableDomListener)(this.textarea,"keydown",C=>this._keyDown(C),!0)),this.register((0,u.addDisposableDomListener)(this.textarea,"keypress",C=>this._keyPress(C),!0)),this.register((0,u.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,u.addDisposableDomListener)(this.textarea,"compositionupdate",C=>this._compositionHelper.compositionupdate(C))),this.register((0,u.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,u.addDisposableDomListener)(this.textarea,"input",C=>this._inputEvent(C),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(C){var P;if(!C)throw new Error("Terminal requires a parent element.");C.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),this._document=C.ownerDocument,this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),C.appendChild(this.element);const F=q.createDocumentFragment();this._viewportElement=q.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),F.appendChild(this._viewportElement),this._viewportScrollArea=q.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=q.createElement("div"),this.screenElement.classList.add("xterm-screen"),this._helperContainer=q.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),F.appendChild(this.screenElement),this.textarea=q.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",d.promptLabel),$.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this._instantiationService.createInstance(f.CoreBrowserService,this.textarea,(P=this._document.defaultView)!==null&&P!==void 0?P:window),this._instantiationService.setService(S.ICoreBrowserService,this._coreBrowserService),this.register((0,u.addDisposableDomListener)(this.textarea,"focus",H=>this._handleTextAreaFocus(H))),this.register((0,u.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(r.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(S.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(E.ThemeService),this._instantiationService.setService(S.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(h.CharacterJoinerService),this._instantiationService.setService(S.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(b.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(S.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(H=>this._onRender.fire(H))),this.onResize(H=>this._renderService.resize(H.cols,H.rows)),this._compositionView=q.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(i.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this.element.appendChild(F);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this._mouseService=this._instantiationService.createInstance(v.MouseService),this._instantiationService.setService(S.IMouseService,this._mouseService),this.viewport=this._instantiationService.createInstance(p.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(H=>this.scrollLines(H.amount,H.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(c.SelectionService,this.element,this.screenElement,this.linkifier2)),this._instantiationService.setService(S.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(H=>this.scrollLines(H.amount,H.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(H=>this._renderService.handleSelectionChanged(H.start,H.end,H.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(H=>{this.textarea.value=H,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(H=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,u.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.linkifier2.attachToDom(this.screenElement,this._mouseService,this._renderService),this.register(this._instantiationService.createInstance(_.BufferDecorationRenderer,this.screenElement)),this.register((0,u.addDisposableDomListener)(this.element,"mousedown",H=>this._selectionService.handleMouseDown(H))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(N.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",H=>this._handleScreenReaderModeOptionChange(H))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",H=>{!this._overviewRulerRenderer&&H&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(e.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(s.DomRenderer,this.element,this.screenElement,this._viewportElement,this.linkifier2)}bindMouse(){const C=this,P=this.element;function F(j){const Z=C._mouseService.getMouseReportCoords(j,C.screenElement);if(!Z)return!1;let te,ie;switch(j.overrideType||j.type){case"mousemove":ie=32,j.buttons===void 0?(te=3,j.button!==void 0&&(te=j.button<3?j.button:3)):te=1&j.buttons?0:4&j.buttons?1:2&j.buttons?2:3;break;case"mouseup":ie=0,te=j.button<3?j.button:3;break;case"mousedown":ie=1,te=j.button<3?j.button:3;break;case"wheel":if(C.viewport.getLinesScrolled(j)===0)return!1;ie=j.deltaY<0?0:1,te=4;break;default:return!1}return!(ie===void 0||te===void 0||te>4)&&C.coreMouseService.triggerMouseEvent({col:Z.col,row:Z.row,x:Z.x,y:Z.y,button:te,action:ie,ctrl:j.ctrlKey,alt:j.altKey,shift:j.shiftKey})}const H={mouseup:null,wheel:null,mousedrag:null,mousemove:null},V={mouseup:j=>(F(j),j.buttons||(this._document.removeEventListener("mouseup",H.mouseup),H.mousedrag&&this._document.removeEventListener("mousemove",H.mousedrag)),this.cancel(j)),wheel:j=>(F(j),this.cancel(j,!0)),mousedrag:j=>{j.buttons&&F(j)},mousemove:j=>{j.buttons||F(j)}};this.register(this.coreMouseService.onProtocolChange(j=>{j?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(j)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&j?H.mousemove||(P.addEventListener("mousemove",V.mousemove),H.mousemove=V.mousemove):(P.removeEventListener("mousemove",H.mousemove),H.mousemove=null),16&j?H.wheel||(P.addEventListener("wheel",V.wheel,{passive:!1}),H.wheel=V.wheel):(P.removeEventListener("wheel",H.wheel),H.wheel=null),2&j?H.mouseup||(P.addEventListener("mouseup",V.mouseup),H.mouseup=V.mouseup):(this._document.removeEventListener("mouseup",H.mouseup),P.removeEventListener("mouseup",H.mouseup),H.mouseup=null),4&j?H.mousedrag||(H.mousedrag=V.mousedrag):(this._document.removeEventListener("mousemove",H.mousedrag),H.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,u.addDisposableDomListener)(P,"mousedown",j=>{if(j.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(j))return F(j),H.mouseup&&this._document.addEventListener("mouseup",H.mouseup),H.mousedrag&&this._document.addEventListener("mousemove",H.mousedrag),this.cancel(j)})),this.register((0,u.addDisposableDomListener)(P,"wheel",j=>{if(!H.wheel){if(!this.buffer.hasScrollback){const Z=this.viewport.getLinesScrolled(j);if(Z===0)return;const te=z.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(j.deltaY<0?"A":"B");let ie="";for(let ve=0;ve<Math.abs(Z);ve++)ie+=te;return this.coreService.triggerDataEvent(ie,!0),this.cancel(j,!0)}return this.viewport.handleWheel(j)?this.cancel(j):void 0}},{passive:!1})),this.register((0,u.addDisposableDomListener)(P,"touchstart",j=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(j),this.cancel(j)},{passive:!0})),this.register((0,u.addDisposableDomListener)(P,"touchmove",j=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(j)?void 0:this.cancel(j)},{passive:!1}))}refresh(C,P){var F;(F=this._renderService)===null||F===void 0||F.refreshRows(C,P)}updateCursorStyle(C){var P;!((P=this._selectionService)===null||P===void 0)&&P.shouldColumnSelect(C)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(C,P,F=0){var H;F===1?(super.scrollLines(C,P,F),this.refresh(0,this.rows-1)):(H=this.viewport)===null||H===void 0||H.scrollLines(C)}paste(C){(0,l.paste)(C,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(C){this._customKeyEventHandler=C}registerLinkProvider(C){return this.linkifier2.registerLinkProvider(C)}registerCharacterJoiner(C){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const P=this._characterJoinerService.register(C);return this.refresh(0,this.rows-1),P}deregisterCharacterJoiner(C){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(C)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(C){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+C)}registerDecoration(C){return this._decorationService.registerDecoration(C)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(C,P,F){this._selectionService.setSelection(C,P,F)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var C;(C=this._selectionService)===null||C===void 0||C.clearSelection()}selectAll(){var C;(C=this._selectionService)===null||C===void 0||C.selectAll()}selectLines(C,P){var F;(F=this._selectionService)===null||F===void 0||F.selectLines(C,P)}_keyDown(C){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(C)===!1)return!1;const P=this.browser.isMac&&this.options.macOptionIsMeta&&C.altKey;if(!P&&!this._compositionHelper.keydown(C))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;P||C.key!=="Dead"&&C.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const F=(0,y.evaluateKeyboardEvent)(C,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(C),F.type===3||F.type===2){const H=this.rows-1;return this.scrollLines(F.type===2?-H:H),this.cancel(C,!0)}return F.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,C)||(F.cancel&&this.cancel(C,!0),!F.key||!!(C.key&&!C.ctrlKey&&!C.altKey&&!C.metaKey&&C.key.length===1&&C.key.charCodeAt(0)>=65&&C.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(F.key!==z.C0.ETX&&F.key!==z.C0.CR||(this.textarea.value=""),this._onKey.fire({key:F.key,domEvent:C}),this._showCursor(),this.coreService.triggerDataEvent(F.key,!0),!this.optionsService.rawOptions.screenReaderMode||C.altKey||C.ctrlKey?this.cancel(C,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(C,P){const F=C.isMac&&!this.options.macOptionIsMeta&&P.altKey&&!P.ctrlKey&&!P.metaKey||C.isWindows&&P.altKey&&P.ctrlKey&&!P.metaKey||C.isWindows&&P.getModifierState("AltGraph");return P.type==="keypress"?F:F&&(!P.keyCode||P.keyCode>47)}_keyUp(C){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(C)===!1||(function(P){return P.keyCode===16||P.keyCode===17||P.keyCode===18}(C)||this.focus(),this.updateCursorStyle(C),this._keyPressHandled=!1)}_keyPress(C){let P;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(C)===!1)return!1;if(this.cancel(C),C.charCode)P=C.charCode;else if(C.which===null||C.which===void 0)P=C.keyCode;else{if(C.which===0||C.charCode===0)return!1;P=C.which}return!(!P||(C.altKey||C.ctrlKey||C.metaKey)&&!this._isThirdLevelShift(this.browser,C)||(P=String.fromCharCode(P),this._onKey.fire({key:P,domEvent:C}),this._showCursor(),this.coreService.triggerDataEvent(P,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(C){if(C.data&&C.inputType==="insertText"&&(!C.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const P=C.data;return this.coreService.triggerDataEvent(P,!0),this.cancel(C),!0}return!1}resize(C,P){C!==this.cols||P!==this.rows?super.resize(C,P):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(C,P){var F,H;(F=this._charSizeService)===null||F===void 0||F.measure(),(H=this.viewport)===null||H===void 0||H.syncScrollArea(!0)}clear(){var C;if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let P=1;P<this.rows;P++)this.buffer.lines.push(this.buffer.getBlankLine(W.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),(C=this.viewport)===null||C===void 0||C.reset(),this.refresh(0,this.rows-1)}}reset(){var C,P;this.options.rows=this.rows,this.options.cols=this.cols;const F=this._customKeyEventHandler;this._setup(),super.reset(),(C=this._selectionService)===null||C===void 0||C.reset(),this._decorationService.reset(),(P=this.viewport)===null||P===void 0||P.reset(),this._customKeyEventHandler=F,this.refresh(0,this.rows-1)}clearTextureAtlas(){var C;(C=this._renderService)===null||C===void 0||C.clearTextureAtlas()}_reportFocus(){var C;!((C=this.element)===null||C===void 0)&&C.classList.contains("focus")?this.coreService.triggerDataEvent(z.C0.ESC+"[I"):this.coreService.triggerDataEvent(z.C0.ESC+"[O")}_reportWindowsOptions(C){if(this._renderService)switch(C){case U.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const P=this._renderService.dimensions.css.canvas.width.toFixed(0),F=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${z.C0.ESC}[4;${F};${P}t`);break;case U.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const H=this._renderService.dimensions.css.cell.width.toFixed(0),V=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${z.C0.ESC}[6;${V};${H}t`)}}cancel(C,P){if(this.options.cancelEvents||P)return C.preventDefault(),C.stopPropagation(),!1}}t.Terminal=K},9924:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TimeBasedDebouncer=void 0,t.TimeBasedDebouncer=class{constructor(n,l=1e3){this._renderCallback=n,this._debounceThresholdMS=l,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(n,l,u){this._rowCount=u,n=n!==void 0?n:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,n):n,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l;const a=Date.now();if(a-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=a,this._innerRefresh();else if(!this._additionalRefreshRequested){const d=a-this._lastRefreshMs,g=this._debounceThresholdMS-d;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},g)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const n=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(n,l)}}},1680:function(D,t,n){var l=this&&this.__decorate||function(i,s,r,h){var f,v=arguments.length,b=v<3?s:h===null?h=Object.getOwnPropertyDescriptor(s,r):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(i,s,r,h);else for(var c=i.length-1;c>=0;c--)(f=i[c])&&(b=(v<3?f(b):v>3?f(s,r,b):f(s,r))||b);return v>3&&b&&Object.defineProperty(s,r,b),b},u=this&&this.__param||function(i,s){return function(r,h){s(r,h,i)}};Object.defineProperty(t,"__esModule",{value:!0}),t.Viewport=void 0;const a=n(3656),d=n(4725),g=n(8460),p=n(844),_=n(2585);let e=t.Viewport=class extends p.Disposable{constructor(i,s,r,h,f,v,b,c){super(),this._viewportElement=i,this._scrollArea=s,this._bufferService=r,this._optionsService=h,this._charSizeService=f,this._renderService=v,this._coreBrowserService=b,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new g.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,a.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(S=>this._activeBuffer=S.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(S=>this._renderDimensions=S)),this._handleThemeChange(c.colors),this.register(c.onChangeColors(S=>this._handleThemeChange(S))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(i){this._viewportElement.style.backgroundColor=i.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(i){if(i)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderService.dimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderService.dimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const s=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderService.dimensions.css.canvas.height);this._lastRecordedBufferHeight!==s&&(this._lastRecordedBufferHeight=s,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const i=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==i&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=i),this._refreshAnimationFrame=null}syncScrollArea(i=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(i);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(i)}_handleScroll(i){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const s=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:s,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const i=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(i*(this._smoothScrollState.target-this._smoothScrollState.origin)),i<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(i,s){const r=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(s<0&&this._viewportElement.scrollTop!==0||s>0&&r<this._lastRecordedBufferHeight)||(i.cancelable&&i.preventDefault(),!1)}handleWheel(i){const s=this._getPixelsScrolled(i);return s!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+s:this._smoothScrollState.target+=s,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=s,this._bubbleScroll(i,s))}scrollLines(i){if(i!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const s=i*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+s,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:i,suppressScrollEvent:!1})}_getPixelsScrolled(i){if(i.deltaY===0||i.shiftKey)return 0;let s=this._applyScrollModifier(i.deltaY,i);return i.deltaMode===WheelEvent.DOM_DELTA_LINE?s*=this._currentRowHeight:i.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(s*=this._currentRowHeight*this._bufferService.rows),s}getBufferElements(i,s){var r;let h,f="";const v=[],b=s??this._bufferService.buffer.lines.length,c=this._bufferService.buffer.lines;for(let S=i;S<b;S++){const E=c.get(S);if(!E)continue;const T=(r=c.get(S+1))===null||r===void 0?void 0:r.isWrapped;if(f+=E.translateToString(!T),!T||S===c.length-1){const L=document.createElement("div");L.textContent=f,v.push(L),f.length>0&&(h=L),f=""}}return{bufferElements:v,cursorElement:h}}getLinesScrolled(i){if(i.deltaY===0||i.shiftKey)return 0;let s=this._applyScrollModifier(i.deltaY,i);return i.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(s/=this._currentRowHeight+0,this._wheelPartialScroll+=s,s=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):i.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(s*=this._bufferService.rows),s}_applyScrollModifier(i,s){const r=this._optionsService.rawOptions.fastScrollModifier;return r==="alt"&&s.altKey||r==="ctrl"&&s.ctrlKey||r==="shift"&&s.shiftKey?i*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:i*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(i){this._lastTouchY=i.touches[0].pageY}handleTouchMove(i){const s=this._lastTouchY-i.touches[0].pageY;return this._lastTouchY=i.touches[0].pageY,s!==0&&(this._viewportElement.scrollTop+=s,this._bubbleScroll(i,s))}};t.Viewport=e=l([u(2,_.IBufferService),u(3,_.IOptionsService),u(4,d.ICharSizeService),u(5,d.IRenderService),u(6,d.ICoreBrowserService),u(7,d.IThemeService)],e)},3107:function(D,t,n){var l=this&&this.__decorate||function(e,i,s,r){var h,f=arguments.length,v=f<3?i:r===null?r=Object.getOwnPropertyDescriptor(i,s):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,i,s,r);else for(var b=e.length-1;b>=0;b--)(h=e[b])&&(v=(f<3?h(v):f>3?h(i,s,v):h(i,s))||v);return f>3&&v&&Object.defineProperty(i,s,v),v},u=this&&this.__param||function(e,i){return function(s,r){i(s,r,e)}};Object.defineProperty(t,"__esModule",{value:!0}),t.BufferDecorationRenderer=void 0;const a=n(3656),d=n(4725),g=n(844),p=n(2585);let _=t.BufferDecorationRenderer=class extends g.Disposable{constructor(e,i,s,r){super(),this._screenElement=e,this._bufferService=i,this._decorationService=s,this._renderService=r,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register((0,a.addDisposableDomListener)(window,"resize",()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(h=>this._removeDecoration(h))),this.register((0,g.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const e of this._decorationService.decorations)this._renderDecoration(e);this._dimensionsChanged=!1}_renderDecoration(e){this._refreshStyle(e),this._dimensionsChanged&&this._refreshXPosition(e)}_createElement(e){var i,s;const r=document.createElement("div");r.classList.add("xterm-decoration"),r.classList.toggle("xterm-decoration-top-layer",((i=e==null?void 0:e.options)===null||i===void 0?void 0:i.layer)==="top"),r.style.width=`${Math.round((e.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,r.style.height=(e.options.height||1)*this._renderService.dimensions.css.cell.height+"px",r.style.top=(e.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",r.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const h=(s=e.options.x)!==null&&s!==void 0?s:0;return h&&h>this._bufferService.cols&&(r.style.display="none"),this._refreshXPosition(e,r),r}_refreshStyle(e){const i=e.marker.line-this._bufferService.buffers.active.ydisp;if(i<0||i>=this._bufferService.rows)e.element&&(e.element.style.display="none",e.onRenderEmitter.fire(e.element));else{let s=this._decorationElements.get(e);s||(s=this._createElement(e),e.element=s,this._decorationElements.set(e,s),this._container.appendChild(s),e.onDispose(()=>{this._decorationElements.delete(e),s.remove()})),s.style.top=i*this._renderService.dimensions.css.cell.height+"px",s.style.display=this._altBufferIsActive?"none":"block",e.onRenderEmitter.fire(s)}}_refreshXPosition(e,i=e.element){var s;if(!i)return;const r=(s=e.options.x)!==null&&s!==void 0?s:0;(e.options.anchor||"left")==="right"?i.style.right=r?r*this._renderService.dimensions.css.cell.width+"px":"":i.style.left=r?r*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(e){var i;(i=this._decorationElements.get(e))===null||i===void 0||i.remove(),this._decorationElements.delete(e),e.dispose()}};t.BufferDecorationRenderer=_=l([u(1,p.IBufferService),u(2,p.IDecorationService),u(3,d.IRenderService)],_)},5871:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ColorZoneStore=void 0,t.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(n){if(n.options.overviewRulerOptions){for(const l of this._zones)if(l.color===n.options.overviewRulerOptions.color&&l.position===n.options.overviewRulerOptions.position){if(this._lineIntersectsZone(l,n.marker.line))return;if(this._lineAdjacentToZone(l,n.marker.line,n.options.overviewRulerOptions.position))return void this._addLineToZone(l,n.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=n.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=n.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=n.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=n.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:n.options.overviewRulerOptions.color,position:n.options.overviewRulerOptions.position,startBufferLine:n.marker.line,endBufferLine:n.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(n){this._linePadding=n}_lineIntersectsZone(n,l){return l>=n.startBufferLine&&l<=n.endBufferLine}_lineAdjacentToZone(n,l,u){return l>=n.startBufferLine-this._linePadding[u||"full"]&&l<=n.endBufferLine+this._linePadding[u||"full"]}_addLineToZone(n,l){n.startBufferLine=Math.min(n.startBufferLine,l),n.endBufferLine=Math.max(n.endBufferLine,l)}}},5744:function(D,t,n){var l=this&&this.__decorate||function(h,f,v,b){var c,S=arguments.length,E=S<3?f:b===null?b=Object.getOwnPropertyDescriptor(f,v):b;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")E=Reflect.decorate(h,f,v,b);else for(var T=h.length-1;T>=0;T--)(c=h[T])&&(E=(S<3?c(E):S>3?c(f,v,E):c(f,v))||E);return S>3&&E&&Object.defineProperty(f,v,E),E},u=this&&this.__param||function(h,f){return function(v,b){f(v,b,h)}};Object.defineProperty(t,"__esModule",{value:!0}),t.OverviewRulerRenderer=void 0;const a=n(5871),d=n(3656),g=n(4725),p=n(844),_=n(2585),e={full:0,left:0,center:0,right:0},i={full:0,left:0,center:0,right:0},s={full:0,left:0,center:0,right:0};let r=t.OverviewRulerRenderer=class extends p.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(h,f,v,b,c,S,E){var T;super(),this._viewportElement=h,this._screenElement=f,this._bufferService=v,this._decorationService=b,this._renderService=c,this._optionsService=S,this._coreBrowseService=E,this._colorZoneStore=new a.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=document.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(T=this._viewportElement.parentElement)===null||T===void 0||T.insertBefore(this._canvas,this._viewportElement);const L=this._canvas.getContext("2d");if(!L)throw new Error("Ctx cannot be null");this._ctx=L,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,p.toDisposable)(()=>{var B;(B=this._canvas)===null||B===void 0||B.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register((0,d.addDisposableDomListener)(this._coreBrowseService.window,"resize",()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const h=Math.floor(this._canvas.width/3),f=Math.ceil(this._canvas.width/3);i.full=this._canvas.width,i.left=h,i.center=f,i.right=h,this._refreshDrawHeightConstants(),s.full=0,s.left=0,s.center=i.left,s.right=i.left+i.center}_refreshDrawHeightConstants(){e.full=Math.round(2*this._coreBrowseService.dpr);const h=this._canvas.height/this._bufferService.buffer.lines.length,f=Math.round(Math.max(Math.min(h,12),6)*this._coreBrowseService.dpr);e.left=f,e.center=f,e.right=f}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*e.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowseService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowseService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const f of this._decorationService.decorations)this._colorZoneStore.addDecoration(f);this._ctx.lineWidth=1;const h=this._colorZoneStore.zones;for(const f of h)f.position!=="full"&&this._renderColorZone(f);for(const f of h)f.position==="full"&&this._renderColorZone(f);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(h){this._ctx.fillStyle=h.color,this._ctx.fillRect(s[h.position||"full"],Math.round((this._canvas.height-1)*(h.startBufferLine/this._bufferService.buffers.active.lines.length)-e[h.position||"full"]/2),i[h.position||"full"],Math.round((this._canvas.height-1)*((h.endBufferLine-h.startBufferLine)/this._bufferService.buffers.active.lines.length)+e[h.position||"full"]))}_queueRefresh(h,f){this._shouldUpdateDimensions=h||this._shouldUpdateDimensions,this._shouldUpdateAnchor=f||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowseService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};t.OverviewRulerRenderer=r=l([u(2,_.IBufferService),u(3,_.IDecorationService),u(4,g.IRenderService),u(5,_.IOptionsService),u(6,g.ICoreBrowserService)],r)},2950:function(D,t,n){var l=this&&this.__decorate||function(_,e,i,s){var r,h=arguments.length,f=h<3?e:s===null?s=Object.getOwnPropertyDescriptor(e,i):s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")f=Reflect.decorate(_,e,i,s);else for(var v=_.length-1;v>=0;v--)(r=_[v])&&(f=(h<3?r(f):h>3?r(e,i,f):r(e,i))||f);return h>3&&f&&Object.defineProperty(e,i,f),f},u=this&&this.__param||function(_,e){return function(i,s){e(i,s,_)}};Object.defineProperty(t,"__esModule",{value:!0}),t.CompositionHelper=void 0;const a=n(4725),d=n(2585),g=n(2584);let p=t.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(_,e,i,s,r,h){this._textarea=_,this._compositionView=e,this._bufferService=i,this._optionsService=s,this._coreService=r,this._renderService=h,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(_){this._compositionView.textContent=_.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(_){if(this._isComposing||this._isSendingComposition){if(_.keyCode===229||_.keyCode===16||_.keyCode===17||_.keyCode===18)return!1;this._finalizeComposition(!1)}return _.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(_){if(this._compositionView.classList.remove("active"),this._isComposing=!1,_){const e={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let i;this._isSendingComposition=!1,e.start+=this._dataAlreadySent.length,i=this._isComposing?this._textarea.value.substring(e.start,e.end):this._textarea.value.substring(e.start),i.length>0&&this._coreService.triggerDataEvent(i,!0)}},0)}else{this._isSendingComposition=!1;const e=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(e,!0)}}_handleAnyTextareaChanges(){const _=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const e=this._textarea.value,i=e.replace(_,"");this._dataAlreadySent=i,e.length>_.length?this._coreService.triggerDataEvent(i,!0):e.length<_.length?this._coreService.triggerDataEvent(`${g.C0.DEL}`,!0):e.length===_.length&&e!==_&&this._coreService.triggerDataEvent(e,!0)}},0)}updateCompositionElements(_){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const e=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),i=this._renderService.dimensions.css.cell.height,s=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,r=e*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=r+"px",this._compositionView.style.top=s+"px",this._compositionView.style.height=i+"px",this._compositionView.style.lineHeight=i+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const h=this._compositionView.getBoundingClientRect();this._textarea.style.left=r+"px",this._textarea.style.top=s+"px",this._textarea.style.width=Math.max(h.width,1)+"px",this._textarea.style.height=Math.max(h.height,1)+"px",this._textarea.style.lineHeight=h.height+"px"}_||setTimeout(()=>this.updateCompositionElements(!0),0)}}};t.CompositionHelper=p=l([u(2,d.IBufferService),u(3,d.IOptionsService),u(4,d.ICoreService),u(5,a.IRenderService)],p)},9806:(D,t)=>{function n(l,u,a){const d=a.getBoundingClientRect(),g=l.getComputedStyle(a),p=parseInt(g.getPropertyValue("padding-left")),_=parseInt(g.getPropertyValue("padding-top"));return[u.clientX-d.left-p,u.clientY-d.top-_]}Object.defineProperty(t,"__esModule",{value:!0}),t.getCoords=t.getCoordsRelativeToElement=void 0,t.getCoordsRelativeToElement=n,t.getCoords=function(l,u,a,d,g,p,_,e,i){if(!p)return;const s=n(l,u,a);return s?(s[0]=Math.ceil((s[0]+(i?_/2:0))/_),s[1]=Math.ceil(s[1]/e),s[0]=Math.min(Math.max(s[0],1),d+(i?1:0)),s[1]=Math.min(Math.max(s[1],1),g),s):void 0}},9504:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.moveToCellSequence=void 0;const l=n(2584);function u(e,i,s,r){const h=e-a(e,s),f=i-a(i,s),v=Math.abs(h-f)-function(b,c,S){let E=0;const T=b-a(b,S),L=c-a(c,S);for(let B=0;B<Math.abs(T-L);B++){const O=d(b,c)==="A"?-1:1,$=S.buffer.lines.get(T+O*B);$!=null&&$.isWrapped&&E++}return E}(e,i,s);return _(v,p(d(e,i),r))}function a(e,i){let s=0,r=i.buffer.lines.get(e),h=r==null?void 0:r.isWrapped;for(;h&&e>=0&&e<i.rows;)s++,r=i.buffer.lines.get(--e),h=r==null?void 0:r.isWrapped;return s}function d(e,i){return e>i?"A":"B"}function g(e,i,s,r,h,f){let v=e,b=i,c="";for(;v!==s||b!==r;)v+=h?1:-1,h&&v>f.cols-1?(c+=f.buffer.translateBufferLineToString(b,!1,e,v),v=0,e=0,b++):!h&&v<0&&(c+=f.buffer.translateBufferLineToString(b,!1,0,e+1),v=f.cols-1,e=v,b--);return c+f.buffer.translateBufferLineToString(b,!1,e,v)}function p(e,i){const s=i?"O":"[";return l.C0.ESC+s+e}function _(e,i){e=Math.floor(e);let s="";for(let r=0;r<e;r++)s+=i;return s}t.moveToCellSequence=function(e,i,s,r){const h=s.buffer.x,f=s.buffer.y;if(!s.buffer.hasScrollback)return function(c,S,E,T,L,B){return u(S,T,L,B).length===0?"":_(g(c,S,c,S-a(S,L),!1,L).length,p("D",B))}(h,f,0,i,s,r)+u(f,i,s,r)+function(c,S,E,T,L,B){let O;O=u(S,T,L,B).length>0?T-a(T,L):S;const $=T,W=function(z,y,A,R,I,U){let N;return N=u(A,R,I,U).length>0?R-a(R,I):y,z<A&&N<=R||z>=A&&N<R?"C":"D"}(c,S,E,T,L,B);return _(g(c,O,E,$,W==="C",L).length,p(W,B))}(h,f,e,i,s,r);let v;if(f===i)return v=h>e?"D":"C",_(Math.abs(h-e),p(v,r));v=f>i?"D":"C";const b=Math.abs(f-i);return _(function(c,S){return S.cols-c}(f>i?e:h,s)+(b-1)*s.cols+1+((f>i?h:e)-1),p(v,r))}},1296:function(D,t,n){var l=this&&this.__decorate||function(L,B,O,$){var W,z=arguments.length,y=z<3?B:$===null?$=Object.getOwnPropertyDescriptor(B,O):$;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(L,B,O,$);else for(var A=L.length-1;A>=0;A--)(W=L[A])&&(y=(z<3?W(y):z>3?W(B,O,y):W(B,O))||y);return z>3&&y&&Object.defineProperty(B,O,y),y},u=this&&this.__param||function(L,B){return function(O,$){B(O,$,L)}};Object.defineProperty(t,"__esModule",{value:!0}),t.DomRenderer=void 0;const a=n(3787),d=n(2550),g=n(2223),p=n(6171),_=n(4725),e=n(8055),i=n(8460),s=n(844),r=n(2585),h="xterm-dom-renderer-owner-",f="xterm-rows",v="xterm-fg-",b="xterm-bg-",c="xterm-focus",S="xterm-selection";let E=1,T=t.DomRenderer=class extends s.Disposable{constructor(L,B,O,$,W,z,y,A,R,I){super(),this._element=L,this._screenElement=B,this._viewportElement=O,this._linkifier2=$,this._charSizeService=z,this._optionsService=y,this._bufferService=A,this._coreBrowserService=R,this._themeService=I,this._terminalClass=E++,this._rowElements=[],this.onRequestRedraw=this.register(new i.EventEmitter).event,this._rowContainer=document.createElement("div"),this._rowContainer.classList.add(f),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=document.createElement("div"),this._selectionContainer.classList.add(S),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,p.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors(U=>this._injectCss(U))),this._injectCss(this._themeService.colors),this._rowFactory=W.createInstance(a.DomRendererRowFactory,document),this._element.classList.add(h+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(U=>this._handleLinkHover(U))),this.register(this._linkifier2.onHideLinkUnderline(U=>this._handleLinkLeave(U))),this.register((0,s.toDisposable)(()=>{this._element.classList.remove(h+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new d.WidthCache(document),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const L=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*L,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*L),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/L),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/L),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const O of this._rowElements)O.style.width=`${this.dimensions.css.canvas.width}px`,O.style.height=`${this.dimensions.css.cell.height}px`,O.style.lineHeight=`${this.dimensions.css.cell.height}px`,O.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const B=`${this._terminalSelector} .${f} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=B,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(L){this._themeStyleElement||(this._themeStyleElement=document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let B=`${this._terminalSelector} .${f} { color: ${L.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;B+=`${this._terminalSelector} .${f} .xterm-dim { color: ${e.color.multiplyOpacity(L.foreground,.5).css};}`,B+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`,B+="@keyframes blink_box_shadow_"+this._terminalClass+" { 50% {  border-bottom-style: hidden; }}",B+="@keyframes blink_block_"+this._terminalClass+` { 0% {  background-color: ${L.cursor.css};  color: ${L.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${L.cursor.css}; }}`,B+=`${this._terminalSelector} .${f}.${c} .xterm-cursor.xterm-cursor-blink:not(.xterm-cursor-block) { animation: blink_box_shadow_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${f}.${c} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: blink_block_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-block { background-color: ${L.cursor.css}; color: ${L.cursorAccent.css};}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${L.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${L.cursor.css} inset;}${this._terminalSelector} .${f} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${L.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,B+=`${this._terminalSelector} .${S} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${S} div { position: absolute; background-color: ${L.selectionBackgroundOpaque.css};}${this._terminalSelector} .${S} div { position: absolute; background-color: ${L.selectionInactiveBackgroundOpaque.css};}`;for(const[O,$]of L.ansi.entries())B+=`${this._terminalSelector} .${v}${O} { color: ${$.css}; }${this._terminalSelector} .${v}${O}.xterm-dim { color: ${e.color.multiplyOpacity($,.5).css}; }${this._terminalSelector} .${b}${O} { background-color: ${$.css}; }`;B+=`${this._terminalSelector} .${v}${g.INVERTED_DEFAULT_COLOR} { color: ${e.color.opaque(L.background).css}; }${this._terminalSelector} .${v}${g.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${e.color.multiplyOpacity(e.color.opaque(L.background),.5).css}; }${this._terminalSelector} .${b}${g.INVERTED_DEFAULT_COLOR} { background-color: ${L.foreground.css}; }`,this._themeStyleElement.textContent=B}_setDefaultSpacing(){const L=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${L}px`,this._rowFactory.defaultSpacing=L}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(L,B){for(let O=this._rowElements.length;O<=B;O++){const $=document.createElement("div");this._rowContainer.appendChild($),this._rowElements.push($)}for(;this._rowElements.length>B;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(L,B){this._refreshRowElements(L,B),this._updateDimensions()}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(c)}handleFocus(){this._rowContainer.classList.add(c),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(L,B,O){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(L,B,O),this.renderRows(0,this._bufferService.rows-1),!L||!B)return;const $=L[1]-this._bufferService.buffer.ydisp,W=B[1]-this._bufferService.buffer.ydisp,z=Math.max($,0),y=Math.min(W,this._bufferService.rows-1);if(z>=this._bufferService.rows||y<0)return;const A=document.createDocumentFragment();if(O){const R=L[0]>B[0];A.appendChild(this._createSelectionElement(z,R?B[0]:L[0],R?L[0]:B[0],y-z+1))}else{const R=$===z?L[0]:0,I=z===W?B[0]:this._bufferService.cols;A.appendChild(this._createSelectionElement(z,R,I));const U=y-z-1;if(A.appendChild(this._createSelectionElement(z+1,0,this._bufferService.cols,U)),z!==y){const N=W===y?B[0]:this._bufferService.cols;A.appendChild(this._createSelectionElement(y,0,N))}}this._selectionContainer.appendChild(A)}_createSelectionElement(L,B,O,$=1){const W=document.createElement("div");return W.style.height=$*this.dimensions.css.cell.height+"px",W.style.top=L*this.dimensions.css.cell.height+"px",W.style.left=B*this.dimensions.css.cell.width+"px",W.style.width=this.dimensions.css.cell.width*(O-B)+"px",W}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const L of this._rowElements)L.replaceChildren()}renderRows(L,B){const O=this._bufferService.buffer,$=O.ybase+O.y,W=Math.min(O.x,this._bufferService.cols-1),z=this._optionsService.rawOptions.cursorBlink,y=this._optionsService.rawOptions.cursorStyle,A=this._optionsService.rawOptions.cursorInactiveStyle;for(let R=L;R<=B;R++){const I=R+O.ydisp,U=this._rowElements[R],N=O.lines.get(I);if(!U||!N)break;U.replaceChildren(...this._rowFactory.createRow(N,I,I===$,y,A,W,z,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${h}${this._terminalClass}`}_handleLinkHover(L){this._setCellUnderline(L.x1,L.x2,L.y1,L.y2,L.cols,!0)}_handleLinkLeave(L){this._setCellUnderline(L.x1,L.x2,L.y1,L.y2,L.cols,!1)}_setCellUnderline(L,B,O,$,W,z){O<0&&(L=0),$<0&&(B=0);const y=this._bufferService.rows-1;O=Math.max(Math.min(O,y),0),$=Math.max(Math.min($,y),0),W=Math.min(W,this._bufferService.cols);const A=this._bufferService.buffer,R=A.ybase+A.y,I=Math.min(A.x,W-1),U=this._optionsService.rawOptions.cursorBlink,N=this._optionsService.rawOptions.cursorStyle,q=this._optionsService.rawOptions.cursorInactiveStyle;for(let K=O;K<=$;++K){const Q=K+A.ydisp,C=this._rowElements[K],P=A.lines.get(Q);if(!C||!P)break;C.replaceChildren(...this._rowFactory.createRow(P,Q,Q===R,N,q,I,U,this.dimensions.css.cell.width,this._widthCache,z?K===O?L:0:-1,z?(K===$?B:W)-1:-1))}}};t.DomRenderer=T=l([u(4,r.IInstantiationService),u(5,_.ICharSizeService),u(6,r.IOptionsService),u(7,r.IBufferService),u(8,_.ICoreBrowserService),u(9,_.IThemeService)],T)},3787:function(D,t,n){var l=this&&this.__decorate||function(v,b,c,S){var E,T=arguments.length,L=T<3?b:S===null?S=Object.getOwnPropertyDescriptor(b,c):S;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")L=Reflect.decorate(v,b,c,S);else for(var B=v.length-1;B>=0;B--)(E=v[B])&&(L=(T<3?E(L):T>3?E(b,c,L):E(b,c))||L);return T>3&&L&&Object.defineProperty(b,c,L),L},u=this&&this.__param||function(v,b){return function(c,S){b(c,S,v)}};Object.defineProperty(t,"__esModule",{value:!0}),t.DomRendererRowFactory=void 0;const a=n(2223),d=n(643),g=n(511),p=n(2585),_=n(8055),e=n(4725),i=n(4269),s=n(6171),r=n(3734);let h=t.DomRendererRowFactory=class{constructor(v,b,c,S,E,T,L){this._document=v,this._characterJoinerService=b,this._optionsService=c,this._coreBrowserService=S,this._coreService=E,this._decorationService=T,this._themeService=L,this._workCell=new g.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(v,b,c){this._selectionStart=v,this._selectionEnd=b,this._columnSelectMode=c}createRow(v,b,c,S,E,T,L,B,O,$,W){const z=[],y=this._characterJoinerService.getJoinedCharacters(b),A=this._themeService.colors;let R,I=v.getNoBgTrimmedLength();c&&I<T+1&&(I=T+1);let U=0,N="",q=0,K=0,Q=0,C=!1,P=0,F=!1,H=0;const V=[],j=$!==-1&&W!==-1;for(let Z=0;Z<I;Z++){v.loadCell(Z,this._workCell);let te=this._workCell.getWidth();if(te===0)continue;let ie=!1,ve=Z,X=this._workCell;if(y.length>0&&Z===y[0][0]){ie=!0;const ee=y.shift();X=new i.JoinedCellData(this._workCell,v.translateToString(!0,ee[0],ee[1]),ee[1]-ee[0]),ve=ee[1]-1,te=X.getWidth()}const be=this._isCellInSelection(Z,b),ze=c&&Z===T,We=j&&Z>=$&&Z<=W;let Ue=!1;this._decorationService.forEachDecorationAtCell(Z,b,void 0,ee=>{Ue=!0});let Re=X.getChars()||d.WHITESPACE_CELL_CHAR;if(Re===" "&&(X.isUnderline()||X.isOverline())&&(Re=" "),H=te*B-O.get(Re,X.isBold(),X.isItalic()),R){if(U&&(be&&F||!be&&!F&&X.bg===q)&&(be&&F&&A.selectionForeground||X.fg===K)&&X.extended.ext===Q&&We===C&&H===P&&!ze&&!ie&&!Ue){N+=Re,U++;continue}U&&(R.textContent=N),R=this._document.createElement("span"),U=0,N=""}else R=this._document.createElement("span");if(q=X.bg,K=X.fg,Q=X.extended.ext,C=We,P=H,F=be,ie&&T>=Z&&T<=ve&&(T=Z),!this._coreService.isCursorHidden&&ze){if(V.push("xterm-cursor"),this._coreBrowserService.isFocused)L&&V.push("xterm-cursor-blink"),V.push(S==="bar"?"xterm-cursor-bar":S==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(E)switch(E){case"outline":V.push("xterm-cursor-outline");break;case"block":V.push("xterm-cursor-block");break;case"bar":V.push("xterm-cursor-bar");break;case"underline":V.push("xterm-cursor-underline")}}if(X.isBold()&&V.push("xterm-bold"),X.isItalic()&&V.push("xterm-italic"),X.isDim()&&V.push("xterm-dim"),N=X.isInvisible()?d.WHITESPACE_CELL_CHAR:X.getChars()||d.WHITESPACE_CELL_CHAR,X.isUnderline()&&(V.push(`xterm-underline-${X.extended.underlineStyle}`),N===" "&&(N=" "),!X.isUnderlineColorDefault()))if(X.isUnderlineColorRGB())R.style.textDecorationColor=`rgb(${r.AttributeData.toColorRGB(X.getUnderlineColor()).join(",")})`;else{let ee=X.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&X.isBold()&&ee<8&&(ee+=8),R.style.textDecorationColor=A.ansi[ee].css}X.isOverline()&&(V.push("xterm-overline"),N===" "&&(N=" ")),X.isStrikethrough()&&V.push("xterm-strikethrough"),We&&(R.style.textDecoration="underline");let ne=X.getFgColor(),Ce=X.getFgColorMode(),le=X.getBgColor(),ye=X.getBgColorMode();const Ne=!!X.isInverse();if(Ne){const ee=ne;ne=le,le=ee;const Dt=Ce;Ce=ye,ye=Dt}let ue,je,fe,we=!1;switch(this._decorationService.forEachDecorationAtCell(Z,b,void 0,ee=>{ee.options.layer!=="top"&&we||(ee.backgroundColorRGB&&(ye=50331648,le=ee.backgroundColorRGB.rgba>>8&16777215,ue=ee.backgroundColorRGB),ee.foregroundColorRGB&&(Ce=50331648,ne=ee.foregroundColorRGB.rgba>>8&16777215,je=ee.foregroundColorRGB),we=ee.options.layer==="top")}),!we&&be&&(ue=this._coreBrowserService.isFocused?A.selectionBackgroundOpaque:A.selectionInactiveBackgroundOpaque,le=ue.rgba>>8&16777215,ye=50331648,we=!0,A.selectionForeground&&(Ce=50331648,ne=A.selectionForeground.rgba>>8&16777215,je=A.selectionForeground)),we&&V.push("xterm-decoration-top"),ye){case 16777216:case 33554432:fe=A.ansi[le],V.push(`xterm-bg-${le}`);break;case 50331648:fe=_.rgba.toColor(le>>16,le>>8&255,255&le),this._addStyle(R,`background-color:#${f((le>>>0).toString(16),"0",6)}`);break;default:Ne?(fe=A.foreground,V.push(`xterm-bg-${a.INVERTED_DEFAULT_COLOR}`)):fe=A.background}switch(ue||X.isDim()&&(ue=_.color.multiplyOpacity(fe,.5)),Ce){case 16777216:case 33554432:X.isBold()&&ne<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(ne+=8),this._applyMinimumContrast(R,fe,A.ansi[ne],X,ue,void 0)||V.push(`xterm-fg-${ne}`);break;case 50331648:const ee=_.rgba.toColor(ne>>16&255,ne>>8&255,255&ne);this._applyMinimumContrast(R,fe,ee,X,ue,je)||this._addStyle(R,`color:#${f(ne.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(R,fe,A.foreground,X,ue,void 0)||Ne&&V.push(`xterm-fg-${a.INVERTED_DEFAULT_COLOR}`)}V.length&&(R.className=V.join(" "),V.length=0),ze||ie||Ue?R.textContent=N:U++,H!==this.defaultSpacing&&(R.style.letterSpacing=`${H}px`),z.push(R),Z=ve}return R&&U&&(R.textContent=N),z}_applyMinimumContrast(v,b,c,S,E,T){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,s.excludeFromContrastRatioDemands)(S.getCode()))return!1;const L=this._getContrastCache(S);let B;if(E||T||(B=L.getColor(b.rgba,c.rgba)),B===void 0){const O=this._optionsService.rawOptions.minimumContrastRatio/(S.isDim()?2:1);B=_.color.ensureContrastRatio(E||b,T||c,O),L.setColor((E||b).rgba,(T||c).rgba,B??null)}return!!B&&(this._addStyle(v,`color:${B.css}`),!0)}_getContrastCache(v){return v.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(v,b){v.setAttribute("style",`${v.getAttribute("style")||""}${b};`)}_isCellInSelection(v,b){const c=this._selectionStart,S=this._selectionEnd;return!(!c||!S)&&(this._columnSelectMode?c[0]<=S[0]?v>=c[0]&&b>=c[1]&&v<S[0]&&b<=S[1]:v<c[0]&&b>=c[1]&&v>=S[0]&&b<=S[1]:b>c[1]&&b<S[1]||c[1]===S[1]&&b===c[1]&&v>=c[0]&&v<S[0]||c[1]<S[1]&&b===S[1]&&v<S[0]||c[1]<S[1]&&b===c[1]&&v>=c[0])}};function f(v,b,c){for(;v.length<c;)v=b+v;return v}t.DomRendererRowFactory=h=l([u(1,e.ICharacterJoinerService),u(2,p.IOptionsService),u(3,e.ICoreBrowserService),u(4,p.ICoreService),u(5,p.IDecorationService),u(6,e.IThemeService)],h)},2550:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WidthCache=void 0,t.WidthCache=class{constructor(n){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=n.createElement("div"),this._container.style.position="absolute",this._container.style.top="-50000px",this._container.style.width="50000px",this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const l=n.createElement("span"),u=n.createElement("span");u.style.fontWeight="bold";const a=n.createElement("span");a.style.fontStyle="italic";const d=n.createElement("span");d.style.fontWeight="bold",d.style.fontStyle="italic",this._measureElements=[l,u,a,d],this._container.appendChild(l),this._container.appendChild(u),this._container.appendChild(a),this._container.appendChild(d),n.body.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(n,l,u,a){n===this._font&&l===this._fontSize&&u===this._weight&&a===this._weightBold||(this._font=n,this._fontSize=l,this._weight=u,this._weightBold=a,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${u}`,this._measureElements[1].style.fontWeight=`${a}`,this._measureElements[2].style.fontWeight=`${u}`,this._measureElements[3].style.fontWeight=`${a}`,this.clear())}get(n,l,u){let a=0;if(!l&&!u&&n.length===1&&(a=n.charCodeAt(0))<256)return this._flat[a]!==-9999?this._flat[a]:this._flat[a]=this._measure(n,0);let d=n;l&&(d+="B"),u&&(d+="I");let g=this._holey.get(d);if(g===void 0){let p=0;l&&(p|=1),u&&(p|=2),g=this._measure(n,p),this._holey.set(d,g)}return g}_measure(n,l){const u=this._measureElements[l];return u.textContent=n.repeat(32),u.offsetWidth/32}}},2223:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TEXT_BASELINE=t.DIM_OPACITY=t.INVERTED_DEFAULT_COLOR=void 0;const l=n(6114);t.INVERTED_DEFAULT_COLOR=257,t.DIM_OPACITY=.5,t.TEXT_BASELINE=l.isFirefox||l.isLegacyEdge?"bottom":"ideographic"},6171:(D,t)=>{function n(l){return 57508<=l&&l<=57558}Object.defineProperty(t,"__esModule",{value:!0}),t.createRenderDimensions=t.excludeFromContrastRatioDemands=t.isRestrictedPowerlineGlyph=t.isPowerlineGlyph=t.throwIfFalsy=void 0,t.throwIfFalsy=function(l){if(!l)throw new Error("value must not be falsy");return l},t.isPowerlineGlyph=n,t.isRestrictedPowerlineGlyph=function(l){return 57520<=l&&l<=57527},t.excludeFromContrastRatioDemands=function(l){return n(l)||function(u){return 9472<=u&&u<=9631}(l)},t.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}}},456:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SelectionModel=void 0,t.SelectionModel=class{constructor(n){this._bufferService=n,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const n=this.selectionStart[0]+this.selectionStartLength;return n>this._bufferService.cols?n%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(n/this._bufferService.cols)-1]:[n%this._bufferService.cols,this.selectionStart[1]+Math.floor(n/this._bufferService.cols)]:[n,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const n=this.selectionStart[0]+this.selectionStartLength;return n>this._bufferService.cols?[n%this._bufferService.cols,this.selectionStart[1]+Math.floor(n/this._bufferService.cols)]:[Math.max(n,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const n=this.selectionStart,l=this.selectionEnd;return!(!n||!l)&&(n[1]>l[1]||n[1]===l[1]&&n[0]>l[0])}handleTrim(n){return this.selectionStart&&(this.selectionStart[1]-=n),this.selectionEnd&&(this.selectionEnd[1]-=n),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(D,t,n){var l=this&&this.__decorate||function(e,i,s,r){var h,f=arguments.length,v=f<3?i:r===null?r=Object.getOwnPropertyDescriptor(i,s):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,i,s,r);else for(var b=e.length-1;b>=0;b--)(h=e[b])&&(v=(f<3?h(v):f>3?h(i,s,v):h(i,s))||v);return f>3&&v&&Object.defineProperty(i,s,v),v},u=this&&this.__param||function(e,i){return function(s,r){i(s,r,e)}};Object.defineProperty(t,"__esModule",{value:!0}),t.CharSizeService=void 0;const a=n(2585),d=n(8460),g=n(844);let p=t.CharSizeService=class extends g.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(e,i,s){super(),this._optionsService=s,this.width=0,this.height=0,this._onCharSizeChange=this.register(new d.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event,this._measureStrategy=new _(e,i,this._optionsService),this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const e=this._measureStrategy.measure();e.width===this.width&&e.height===this.height||(this.width=e.width,this.height=e.height,this._onCharSizeChange.fire())}};t.CharSizeService=p=l([u(2,a.IOptionsService)],p);class _{constructor(i,s,r){this._document=i,this._parentElement=s,this._optionsService=r,this._result={width:0,height:0},this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`;const i={height:Number(this._measureElement.offsetHeight),width:Number(this._measureElement.offsetWidth)};return i.width!==0&&i.height!==0&&(this._result.width=i.width/32,this._result.height=Math.ceil(i.height)),this._result}}},4269:function(D,t,n){var l=this&&this.__decorate||function(i,s,r,h){var f,v=arguments.length,b=v<3?s:h===null?h=Object.getOwnPropertyDescriptor(s,r):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")b=Reflect.decorate(i,s,r,h);else for(var c=i.length-1;c>=0;c--)(f=i[c])&&(b=(v<3?f(b):v>3?f(s,r,b):f(s,r))||b);return v>3&&b&&Object.defineProperty(s,r,b),b},u=this&&this.__param||function(i,s){return function(r,h){s(r,h,i)}};Object.defineProperty(t,"__esModule",{value:!0}),t.CharacterJoinerService=t.JoinedCellData=void 0;const a=n(3734),d=n(643),g=n(511),p=n(2585);class _ extends a.AttributeData{constructor(s,r,h){super(),this.content=0,this.combinedData="",this.fg=s.fg,this.bg=s.bg,this.combinedData=r,this._width=h}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(s){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}t.JoinedCellData=_;let e=t.CharacterJoinerService=class ft{constructor(s){this._bufferService=s,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new g.CellData}register(s){const r={id:this._nextCharacterJoinerId++,handler:s};return this._characterJoiners.push(r),r.id}deregister(s){for(let r=0;r<this._characterJoiners.length;r++)if(this._characterJoiners[r].id===s)return this._characterJoiners.splice(r,1),!0;return!1}getJoinedCharacters(s){if(this._characterJoiners.length===0)return[];const r=this._bufferService.buffer.lines.get(s);if(!r||r.length===0)return[];const h=[],f=r.translateToString(!0);let v=0,b=0,c=0,S=r.getFg(0),E=r.getBg(0);for(let T=0;T<r.getTrimmedLength();T++)if(r.loadCell(T,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==S||this._workCell.bg!==E){if(T-v>1){const L=this._getJoinedRanges(f,c,b,r,v);for(let B=0;B<L.length;B++)h.push(L[B])}v=T,c=b,S=this._workCell.fg,E=this._workCell.bg}b+=this._workCell.getChars().length||d.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-v>1){const T=this._getJoinedRanges(f,c,b,r,v);for(let L=0;L<T.length;L++)h.push(T[L])}return h}_getJoinedRanges(s,r,h,f,v){const b=s.substring(r,h);let c=[];try{c=this._characterJoiners[0].handler(b)}catch(S){console.error(S)}for(let S=1;S<this._characterJoiners.length;S++)try{const E=this._characterJoiners[S].handler(b);for(let T=0;T<E.length;T++)ft._mergeRanges(c,E[T])}catch(E){console.error(E)}return this._stringRangesToCellRanges(c,f,v),c}_stringRangesToCellRanges(s,r,h){let f=0,v=!1,b=0,c=s[f];if(c){for(let S=h;S<this._bufferService.cols;S++){const E=r.getWidth(S),T=r.getString(S).length||d.WHITESPACE_CELL_CHAR.length;if(E!==0){if(!v&&c[0]<=b&&(c[0]=S,v=!0),c[1]<=b){if(c[1]=S,c=s[++f],!c)break;c[0]<=b?(c[0]=S,v=!0):v=!1}b+=T}}c&&(c[1]=this._bufferService.cols)}}static _mergeRanges(s,r){let h=!1;for(let f=0;f<s.length;f++){const v=s[f];if(h){if(r[1]<=v[0])return s[f-1][1]=r[1],s;if(r[1]<=v[1])return s[f-1][1]=Math.max(r[1],v[1]),s.splice(f,1),s;s.splice(f,1),f--}else{if(r[1]<=v[0])return s.splice(f,0,r),s;if(r[1]<=v[1])return v[0]=Math.min(r[0],v[0]),s;r[0]<v[1]&&(v[0]=Math.min(r[0],v[0]),h=!0)}}return h?s[s.length-1][1]=r[1]:s.push(r),s}};t.CharacterJoinerService=e=l([u(0,p.IBufferService)],e)},5114:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CoreBrowserService=void 0,t.CoreBrowserService=class{constructor(n,l){this._textarea=n,this.window=l,this._isFocused=!1,this._cachedIsFocused=void 0,this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}},8934:function(D,t,n){var l=this&&this.__decorate||function(p,_,e,i){var s,r=arguments.length,h=r<3?_:i===null?i=Object.getOwnPropertyDescriptor(_,e):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")h=Reflect.decorate(p,_,e,i);else for(var f=p.length-1;f>=0;f--)(s=p[f])&&(h=(r<3?s(h):r>3?s(_,e,h):s(_,e))||h);return r>3&&h&&Object.defineProperty(_,e,h),h},u=this&&this.__param||function(p,_){return function(e,i){_(e,i,p)}};Object.defineProperty(t,"__esModule",{value:!0}),t.MouseService=void 0;const a=n(4725),d=n(9806);let g=t.MouseService=class{constructor(p,_){this._renderService=p,this._charSizeService=_}getCoords(p,_,e,i,s){return(0,d.getCoords)(window,p,_,e,i,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,s)}getMouseReportCoords(p,_){const e=(0,d.getCoordsRelativeToElement)(window,p,_);if(this._charSizeService.hasValidSize)return e[0]=Math.min(Math.max(e[0],0),this._renderService.dimensions.css.canvas.width-1),e[1]=Math.min(Math.max(e[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(e[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(e[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(e[0]),y:Math.floor(e[1])}}};t.MouseService=g=l([u(0,a.IRenderService),u(1,a.ICharSizeService)],g)},3230:function(D,t,n){var l=this&&this.__decorate||function(h,f,v,b){var c,S=arguments.length,E=S<3?f:b===null?b=Object.getOwnPropertyDescriptor(f,v):b;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")E=Reflect.decorate(h,f,v,b);else for(var T=h.length-1;T>=0;T--)(c=h[T])&&(E=(S<3?c(E):S>3?c(f,v,E):c(f,v))||E);return S>3&&E&&Object.defineProperty(f,v,E),E},u=this&&this.__param||function(h,f){return function(v,b){f(v,b,h)}};Object.defineProperty(t,"__esModule",{value:!0}),t.RenderService=void 0;const a=n(3656),d=n(6193),g=n(5596),p=n(4725),_=n(8460),e=n(844),i=n(7226),s=n(2585);let r=t.RenderService=class extends e.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(h,f,v,b,c,S,E,T){if(super(),this._rowCount=h,this._charSizeService=b,this._renderer=this.register(new e.MutableDisposable),this._pausedResizeTask=new i.DebouncedIdleTask,this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new _.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new _.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new _.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new _.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new d.RenderDebouncer(E.window,(L,B)=>this._renderRows(L,B)),this.register(this._renderDebouncer),this._screenDprMonitor=new g.ScreenDprMonitor(E.window),this._screenDprMonitor.setListener(()=>this.handleDevicePixelRatioChange()),this.register(this._screenDprMonitor),this.register(S.onResize(()=>this._fullRefresh())),this.register(S.buffers.onBufferActivate(()=>{var L;return(L=this._renderer.value)===null||L===void 0?void 0:L.clear()})),this.register(v.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(c.onDecorationRegistered(()=>this._fullRefresh())),this.register(c.onDecorationRemoved(()=>this._fullRefresh())),this.register(v.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio"],()=>{this.clear(),this.handleResize(S.cols,S.rows),this._fullRefresh()})),this.register(v.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(S.buffer.y,S.buffer.y,!0))),this.register((0,a.addDisposableDomListener)(E.window,"resize",()=>this.handleDevicePixelRatioChange())),this.register(T.onChangeColors(()=>this._fullRefresh())),"IntersectionObserver"in E.window){const L=new E.window.IntersectionObserver(B=>this._handleIntersectionChange(B[B.length-1]),{threshold:0});L.observe(f),this.register({dispose:()=>L.disconnect()})}}_handleIntersectionChange(h){this._isPaused=h.isIntersecting===void 0?h.intersectionRatio===0:!h.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(h,f,v=!1){this._isPaused?this._needsFullRefresh=!0:(v||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(h,f,this._rowCount))}_renderRows(h,f){this._renderer.value&&(h=Math.min(h,this._rowCount-1),f=Math.min(f,this._rowCount-1),this._renderer.value.renderRows(h,f),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:h,end:f}),this._onRender.fire({start:h,end:f}),this._isNextRenderRedrawOnly=!0)}resize(h,f){this._rowCount=f,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(h){this._renderer.value=h,this._renderer.value.onRequestRedraw(f=>this.refreshRows(f.start,f.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh()}addRefreshCallback(h){return this._renderDebouncer.addRefreshCallback(h)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var h,f;this._renderer.value&&((f=(h=this._renderer.value).clearTextureAtlas)===null||f===void 0||f.call(h),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(h,f){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>this._renderer.value.handleResize(h,f)):this._renderer.value.handleResize(h,f),this._fullRefresh())}handleCharSizeChanged(){var h;(h=this._renderer.value)===null||h===void 0||h.handleCharSizeChanged()}handleBlur(){var h;(h=this._renderer.value)===null||h===void 0||h.handleBlur()}handleFocus(){var h;(h=this._renderer.value)===null||h===void 0||h.handleFocus()}handleSelectionChanged(h,f,v){var b;this._selectionState.start=h,this._selectionState.end=f,this._selectionState.columnSelectMode=v,(b=this._renderer.value)===null||b===void 0||b.handleSelectionChanged(h,f,v)}handleCursorMove(){var h;(h=this._renderer.value)===null||h===void 0||h.handleCursorMove()}clear(){var h;(h=this._renderer.value)===null||h===void 0||h.clear()}};t.RenderService=r=l([u(2,s.IOptionsService),u(3,p.ICharSizeService),u(4,s.IDecorationService),u(5,s.IBufferService),u(6,p.ICoreBrowserService),u(7,p.IThemeService)],r)},9312:function(D,t,n){var l=this&&this.__decorate||function(c,S,E,T){var L,B=arguments.length,O=B<3?S:T===null?T=Object.getOwnPropertyDescriptor(S,E):T;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")O=Reflect.decorate(c,S,E,T);else for(var $=c.length-1;$>=0;$--)(L=c[$])&&(O=(B<3?L(O):B>3?L(S,E,O):L(S,E))||O);return B>3&&O&&Object.defineProperty(S,E,O),O},u=this&&this.__param||function(c,S){return function(E,T){S(E,T,c)}};Object.defineProperty(t,"__esModule",{value:!0}),t.SelectionService=void 0;const a=n(9806),d=n(9504),g=n(456),p=n(4725),_=n(8460),e=n(844),i=n(6114),s=n(4841),r=n(511),h=n(2585),f=" ",v=new RegExp(f,"g");let b=t.SelectionService=class extends e.Disposable{constructor(c,S,E,T,L,B,O,$,W){super(),this._element=c,this._screenElement=S,this._linkifier=E,this._bufferService=T,this._coreService=L,this._mouseService=B,this._optionsService=O,this._renderService=$,this._coreBrowserService=W,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new r.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new _.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new _.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new _.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new _.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=z=>this._handleMouseMove(z),this._mouseUpListener=z=>this._handleMouseUp(z),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(z=>this._handleTrim(z)),this.register(this._bufferService.buffers.onBufferActivate(z=>this._handleBufferActivate(z))),this.enable(),this._model=new g.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,e.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const c=this._model.finalSelectionStart,S=this._model.finalSelectionEnd;return!(!c||!S||c[0]===S[0]&&c[1]===S[1])}get selectionText(){const c=this._model.finalSelectionStart,S=this._model.finalSelectionEnd;if(!c||!S)return"";const E=this._bufferService.buffer,T=[];if(this._activeSelectionMode===3){if(c[0]===S[0])return"";const L=c[0]<S[0]?c[0]:S[0],B=c[0]<S[0]?S[0]:c[0];for(let O=c[1];O<=S[1];O++){const $=E.translateBufferLineToString(O,!0,L,B);T.push($)}}else{const L=c[1]===S[1]?S[0]:void 0;T.push(E.translateBufferLineToString(c[1],!0,c[0],L));for(let B=c[1]+1;B<=S[1]-1;B++){const O=E.lines.get(B),$=E.translateBufferLineToString(B,!0);O!=null&&O.isWrapped?T[T.length-1]+=$:T.push($)}if(c[1]!==S[1]){const B=E.lines.get(S[1]),O=E.translateBufferLineToString(S[1],!0,0,S[0]);B&&B.isWrapped?T[T.length-1]+=O:T.push(O)}}return T.map(L=>L.replace(v," ")).join(i.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(c){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),i.isLinux&&c&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(c){const S=this._getMouseBufferCoords(c),E=this._model.finalSelectionStart,T=this._model.finalSelectionEnd;return!!(E&&T&&S)&&this._areCoordsInSelection(S,E,T)}isCellInSelection(c,S){const E=this._model.finalSelectionStart,T=this._model.finalSelectionEnd;return!(!E||!T)&&this._areCoordsInSelection([c,S],E,T)}_areCoordsInSelection(c,S,E){return c[1]>S[1]&&c[1]<E[1]||S[1]===E[1]&&c[1]===S[1]&&c[0]>=S[0]&&c[0]<E[0]||S[1]<E[1]&&c[1]===E[1]&&c[0]<E[0]||S[1]<E[1]&&c[1]===S[1]&&c[0]>=S[0]}_selectWordAtCursor(c,S){var E,T;const L=(T=(E=this._linkifier.currentLink)===null||E===void 0?void 0:E.link)===null||T===void 0?void 0:T.range;if(L)return this._model.selectionStart=[L.start.x-1,L.start.y-1],this._model.selectionStartLength=(0,s.getRangeLength)(L,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const B=this._getMouseBufferCoords(c);return!!B&&(this._selectWordAt(B,S),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(c,S){this._model.clearSelection(),c=Math.max(c,0),S=Math.min(S,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,c],this._model.selectionEnd=[this._bufferService.cols,S],this.refresh(),this._onSelectionChange.fire()}_handleTrim(c){this._model.handleTrim(c)&&this.refresh()}_getMouseBufferCoords(c){const S=this._mouseService.getCoords(c,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(S)return S[0]--,S[1]--,S[1]+=this._bufferService.buffer.ydisp,S}_getMouseEventScrollAmount(c){let S=(0,a.getCoordsRelativeToElement)(this._coreBrowserService.window,c,this._screenElement)[1];const E=this._renderService.dimensions.css.canvas.height;return S>=0&&S<=E?0:(S>E&&(S-=E),S=Math.min(Math.max(S,-50),50),S/=50,S/Math.abs(S)+Math.round(14*S))}shouldForceSelection(c){return i.isMac?c.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:c.shiftKey}handleMouseDown(c){if(this._mouseDownTimeStamp=c.timeStamp,(c.button!==2||!this.hasSelection)&&c.button===0){if(!this._enabled){if(!this.shouldForceSelection(c))return;c.stopPropagation()}c.preventDefault(),this._dragScrollAmount=0,this._enabled&&c.shiftKey?this._handleIncrementalClick(c):c.detail===1?this._handleSingleClick(c):c.detail===2?this._handleDoubleClick(c):c.detail===3&&this._handleTripleClick(c),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(c){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(c))}_handleSingleClick(c){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(c)?3:0,this._model.selectionStart=this._getMouseBufferCoords(c),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const S=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);S&&S.length!==this._model.selectionStart[0]&&S.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(c){this._selectWordAtCursor(c,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(c){const S=this._getMouseBufferCoords(c);S&&(this._activeSelectionMode=2,this._selectLineAt(S[1]))}shouldColumnSelect(c){return c.altKey&&!(i.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(c){if(c.stopImmediatePropagation(),!this._model.selectionStart)return;const S=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(c),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(c),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const E=this._bufferService.buffer;if(this._model.selectionEnd[1]<E.lines.length){const T=E.lines.get(this._model.selectionEnd[1]);T&&T.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]++}S&&S[0]===this._model.selectionEnd[0]&&S[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const c=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(c.ydisp+this._bufferService.rows,c.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=c.ydisp),this.refresh()}}_handleMouseUp(c){const S=c.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&S<500&&c.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const E=this._mouseService.getCoords(c,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(E&&E[0]!==void 0&&E[1]!==void 0){const T=(0,d.moveToCellSequence)(E[0]-1,E[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(T,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const c=this._model.finalSelectionStart,S=this._model.finalSelectionEnd,E=!(!c||!S||c[0]===S[0]&&c[1]===S[1]);E?c&&S&&(this._oldSelectionStart&&this._oldSelectionEnd&&c[0]===this._oldSelectionStart[0]&&c[1]===this._oldSelectionStart[1]&&S[0]===this._oldSelectionEnd[0]&&S[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(c,S,E)):this._oldHasSelection&&this._fireOnSelectionChange(c,S,E)}_fireOnSelectionChange(c,S,E){this._oldSelectionStart=c,this._oldSelectionEnd=S,this._oldHasSelection=E,this._onSelectionChange.fire()}_handleBufferActivate(c){this.clearSelection(),this._trimListener.dispose(),this._trimListener=c.activeBuffer.lines.onTrim(S=>this._handleTrim(S))}_convertViewportColToCharacterIndex(c,S){let E=S;for(let T=0;S>=T;T++){const L=c.loadCell(T,this._workCell).getChars().length;this._workCell.getWidth()===0?E--:L>1&&S!==T&&(E+=L-1)}return E}setSelection(c,S,E){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[c,S],this._model.selectionStartLength=E,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(c){this._isClickInSelection(c)||(this._selectWordAtCursor(c,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(c,S,E=!0,T=!0){if(c[0]>=this._bufferService.cols)return;const L=this._bufferService.buffer,B=L.lines.get(c[1]);if(!B)return;const O=L.translateBufferLineToString(c[1],!1);let $=this._convertViewportColToCharacterIndex(B,c[0]),W=$;const z=c[0]-$;let y=0,A=0,R=0,I=0;if(O.charAt($)===" "){for(;$>0&&O.charAt($-1)===" ";)$--;for(;W<O.length&&O.charAt(W+1)===" ";)W++}else{let q=c[0],K=c[0];B.getWidth(q)===0&&(y++,q--),B.getWidth(K)===2&&(A++,K++);const Q=B.getString(K).length;for(Q>1&&(I+=Q-1,W+=Q-1);q>0&&$>0&&!this._isCharWordSeparator(B.loadCell(q-1,this._workCell));){B.loadCell(q-1,this._workCell);const C=this._workCell.getChars().length;this._workCell.getWidth()===0?(y++,q--):C>1&&(R+=C-1,$-=C-1),$--,q--}for(;K<B.length&&W+1<O.length&&!this._isCharWordSeparator(B.loadCell(K+1,this._workCell));){B.loadCell(K+1,this._workCell);const C=this._workCell.getChars().length;this._workCell.getWidth()===2?(A++,K++):C>1&&(I+=C-1,W+=C-1),W++,K++}}W++;let U=$+z-y+R,N=Math.min(this._bufferService.cols,W-$+y+A-R-I);if(S||O.slice($,W).trim()!==""){if(E&&U===0&&B.getCodePoint(0)!==32){const q=L.lines.get(c[1]-1);if(q&&B.isWrapped&&q.getCodePoint(this._bufferService.cols-1)!==32){const K=this._getWordAt([this._bufferService.cols-1,c[1]-1],!1,!0,!1);if(K){const Q=this._bufferService.cols-K.start;U-=Q,N+=Q}}}if(T&&U+N===this._bufferService.cols&&B.getCodePoint(this._bufferService.cols-1)!==32){const q=L.lines.get(c[1]+1);if(q!=null&&q.isWrapped&&q.getCodePoint(0)!==32){const K=this._getWordAt([0,c[1]+1],!1,!1,!0);K&&(N+=K.length)}}return{start:U,length:N}}}_selectWordAt(c,S){const E=this._getWordAt(c,S);if(E){for(;E.start<0;)E.start+=this._bufferService.cols,c[1]--;this._model.selectionStart=[E.start,c[1]],this._model.selectionStartLength=E.length}}_selectToWordAt(c){const S=this._getWordAt(c,!0);if(S){let E=c[1];for(;S.start<0;)S.start+=this._bufferService.cols,E--;if(!this._model.areSelectionValuesReversed())for(;S.start+S.length>this._bufferService.cols;)S.length-=this._bufferService.cols,E++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?S.start:S.start+S.length,E]}}_isCharWordSeparator(c){return c.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(c.getChars())>=0}_selectLineAt(c){const S=this._bufferService.buffer.getWrappedRangeForLine(c),E={start:{x:0,y:S.first},end:{x:this._bufferService.cols-1,y:S.last}};this._model.selectionStart=[0,S.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,s.getRangeLength)(E,this._bufferService.cols)}};t.SelectionService=b=l([u(3,h.IBufferService),u(4,h.ICoreService),u(5,p.IMouseService),u(6,h.IOptionsService),u(7,p.IRenderService),u(8,p.ICoreBrowserService)],b)},4725:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.IThemeService=t.ICharacterJoinerService=t.ISelectionService=t.IRenderService=t.IMouseService=t.ICoreBrowserService=t.ICharSizeService=void 0;const l=n(8343);t.ICharSizeService=(0,l.createDecorator)("CharSizeService"),t.ICoreBrowserService=(0,l.createDecorator)("CoreBrowserService"),t.IMouseService=(0,l.createDecorator)("MouseService"),t.IRenderService=(0,l.createDecorator)("RenderService"),t.ISelectionService=(0,l.createDecorator)("SelectionService"),t.ICharacterJoinerService=(0,l.createDecorator)("CharacterJoinerService"),t.IThemeService=(0,l.createDecorator)("ThemeService")},6731:function(D,t,n){var l=this&&this.__decorate||function(b,c,S,E){var T,L=arguments.length,B=L<3?c:E===null?E=Object.getOwnPropertyDescriptor(c,S):E;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")B=Reflect.decorate(b,c,S,E);else for(var O=b.length-1;O>=0;O--)(T=b[O])&&(B=(L<3?T(B):L>3?T(c,S,B):T(c,S))||B);return L>3&&B&&Object.defineProperty(c,S,B),B},u=this&&this.__param||function(b,c){return function(S,E){c(S,E,b)}};Object.defineProperty(t,"__esModule",{value:!0}),t.ThemeService=t.DEFAULT_ANSI_COLORS=void 0;const a=n(7239),d=n(8055),g=n(8460),p=n(844),_=n(2585),e=d.css.toColor("#ffffff"),i=d.css.toColor("#000000"),s=d.css.toColor("#ffffff"),r=d.css.toColor("#000000"),h={css:"rgba(255, 255, 255, 0.3)",rgba:4294967117};t.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const b=[d.css.toColor("#2e3436"),d.css.toColor("#cc0000"),d.css.toColor("#4e9a06"),d.css.toColor("#c4a000"),d.css.toColor("#3465a4"),d.css.toColor("#75507b"),d.css.toColor("#06989a"),d.css.toColor("#d3d7cf"),d.css.toColor("#555753"),d.css.toColor("#ef2929"),d.css.toColor("#8ae234"),d.css.toColor("#fce94f"),d.css.toColor("#729fcf"),d.css.toColor("#ad7fa8"),d.css.toColor("#34e2e2"),d.css.toColor("#eeeeec")],c=[0,95,135,175,215,255];for(let S=0;S<216;S++){const E=c[S/36%6|0],T=c[S/6%6|0],L=c[S%6];b.push({css:d.channels.toCss(E,T,L),rgba:d.channels.toRgba(E,T,L)})}for(let S=0;S<24;S++){const E=8+10*S;b.push({css:d.channels.toCss(E,E,E),rgba:d.channels.toRgba(E,E,E)})}return b})());let f=t.ThemeService=class extends p.Disposable{get colors(){return this._colors}constructor(b){super(),this._optionsService=b,this._contrastCache=new a.ColorContrastCache,this._halfContrastCache=new a.ColorContrastCache,this._onChangeColors=this.register(new g.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:e,background:i,cursor:s,cursorAccent:r,selectionForeground:void 0,selectionBackgroundTransparent:h,selectionBackgroundOpaque:d.color.blend(i,h),selectionInactiveBackgroundTransparent:h,selectionInactiveBackgroundOpaque:d.color.blend(i,h),ansi:t.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(b={}){const c=this._colors;if(c.foreground=v(b.foreground,e),c.background=v(b.background,i),c.cursor=v(b.cursor,s),c.cursorAccent=v(b.cursorAccent,r),c.selectionBackgroundTransparent=v(b.selectionBackground,h),c.selectionBackgroundOpaque=d.color.blend(c.background,c.selectionBackgroundTransparent),c.selectionInactiveBackgroundTransparent=v(b.selectionInactiveBackground,c.selectionBackgroundTransparent),c.selectionInactiveBackgroundOpaque=d.color.blend(c.background,c.selectionInactiveBackgroundTransparent),c.selectionForeground=b.selectionForeground?v(b.selectionForeground,d.NULL_COLOR):void 0,c.selectionForeground===d.NULL_COLOR&&(c.selectionForeground=void 0),d.color.isOpaque(c.selectionBackgroundTransparent)&&(c.selectionBackgroundTransparent=d.color.opacity(c.selectionBackgroundTransparent,.3)),d.color.isOpaque(c.selectionInactiveBackgroundTransparent)&&(c.selectionInactiveBackgroundTransparent=d.color.opacity(c.selectionInactiveBackgroundTransparent,.3)),c.ansi=t.DEFAULT_ANSI_COLORS.slice(),c.ansi[0]=v(b.black,t.DEFAULT_ANSI_COLORS[0]),c.ansi[1]=v(b.red,t.DEFAULT_ANSI_COLORS[1]),c.ansi[2]=v(b.green,t.DEFAULT_ANSI_COLORS[2]),c.ansi[3]=v(b.yellow,t.DEFAULT_ANSI_COLORS[3]),c.ansi[4]=v(b.blue,t.DEFAULT_ANSI_COLORS[4]),c.ansi[5]=v(b.magenta,t.DEFAULT_ANSI_COLORS[5]),c.ansi[6]=v(b.cyan,t.DEFAULT_ANSI_COLORS[6]),c.ansi[7]=v(b.white,t.DEFAULT_ANSI_COLORS[7]),c.ansi[8]=v(b.brightBlack,t.DEFAULT_ANSI_COLORS[8]),c.ansi[9]=v(b.brightRed,t.DEFAULT_ANSI_COLORS[9]),c.ansi[10]=v(b.brightGreen,t.DEFAULT_ANSI_COLORS[10]),c.ansi[11]=v(b.brightYellow,t.DEFAULT_ANSI_COLORS[11]),c.ansi[12]=v(b.brightBlue,t.DEFAULT_ANSI_COLORS[12]),c.ansi[13]=v(b.brightMagenta,t.DEFAULT_ANSI_COLORS[13]),c.ansi[14]=v(b.brightCyan,t.DEFAULT_ANSI_COLORS[14]),c.ansi[15]=v(b.brightWhite,t.DEFAULT_ANSI_COLORS[15]),b.extendedAnsi){const S=Math.min(c.ansi.length-16,b.extendedAnsi.length);for(let E=0;E<S;E++)c.ansi[E+16]=v(b.extendedAnsi[E],t.DEFAULT_ANSI_COLORS[E+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(b){this._restoreColor(b),this._onChangeColors.fire(this.colors)}_restoreColor(b){if(b!==void 0)switch(b){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[b]=this._restoreColors.ansi[b]}else for(let c=0;c<this._restoreColors.ansi.length;++c)this._colors.ansi[c]=this._restoreColors.ansi[c]}modifyColors(b){b(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function v(b,c){if(b!==void 0)try{return d.css.toColor(b)}catch{}return c}t.ThemeService=f=l([u(0,_.IOptionsService)],f)},6349:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CircularList=void 0;const l=n(8460),u=n(844);class a extends u.Disposable{constructor(g){super(),this._maxLength=g,this.onDeleteEmitter=this.register(new l.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new l.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new l.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(g){if(this._maxLength===g)return;const p=new Array(g);for(let _=0;_<Math.min(g,this.length);_++)p[_]=this._array[this._getCyclicIndex(_)];this._array=p,this._maxLength=g,this._startIndex=0}get length(){return this._length}set length(g){if(g>this._length)for(let p=this._length;p<g;p++)this._array[p]=void 0;this._length=g}get(g){return this._array[this._getCyclicIndex(g)]}set(g,p){this._array[this._getCyclicIndex(g)]=p}push(g){this._array[this._getCyclicIndex(this._length)]=g,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(g,p,..._){if(p){for(let e=g;e<this._length-p;e++)this._array[this._getCyclicIndex(e)]=this._array[this._getCyclicIndex(e+p)];this._length-=p,this.onDeleteEmitter.fire({index:g,amount:p})}for(let e=this._length-1;e>=g;e--)this._array[this._getCyclicIndex(e+_.length)]=this._array[this._getCyclicIndex(e)];for(let e=0;e<_.length;e++)this._array[this._getCyclicIndex(g+e)]=_[e];if(_.length&&this.onInsertEmitter.fire({index:g,amount:_.length}),this._length+_.length>this._maxLength){const e=this._length+_.length-this._maxLength;this._startIndex+=e,this._length=this._maxLength,this.onTrimEmitter.fire(e)}else this._length+=_.length}trimStart(g){g>this._length&&(g=this._length),this._startIndex+=g,this._length-=g,this.onTrimEmitter.fire(g)}shiftElements(g,p,_){if(!(p<=0)){if(g<0||g>=this._length)throw new Error("start argument out of range");if(g+_<0)throw new Error("Cannot shift elements in list beyond index 0");if(_>0){for(let i=p-1;i>=0;i--)this.set(g+i+_,this.get(g+i));const e=g+p+_-this._length;if(e>0)for(this._length+=e;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let e=0;e<p;e++)this.set(g+e+_,this.get(g+e))}}_getCyclicIndex(g){return(this._startIndex+g)%this._maxLength}}t.CircularList=a},1439:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.clone=void 0,t.clone=function n(l,u=5){if(typeof l!="object")return l;const a=Array.isArray(l)?[]:{};for(const d in l)a[d]=u<=1?l[d]:l[d]&&n(l[d],u-1);return a}},8055:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.contrastRatio=t.toPaddedHex=t.rgba=t.rgb=t.css=t.color=t.channels=t.NULL_COLOR=void 0;const l=n(6114);let u=0,a=0,d=0,g=0;var p,_,e,i,s;function r(f){const v=f.toString(16);return v.length<2?"0"+v:v}function h(f,v){return f<v?(v+.05)/(f+.05):(f+.05)/(v+.05)}t.NULL_COLOR={css:"#00000000",rgba:0},function(f){f.toCss=function(v,b,c,S){return S!==void 0?`#${r(v)}${r(b)}${r(c)}${r(S)}`:`#${r(v)}${r(b)}${r(c)}`},f.toRgba=function(v,b,c,S=255){return(v<<24|b<<16|c<<8|S)>>>0}}(p||(t.channels=p={})),function(f){function v(b,c){return g=Math.round(255*c),[u,a,d]=s.toChannels(b.rgba),{css:p.toCss(u,a,d,g),rgba:p.toRgba(u,a,d,g)}}f.blend=function(b,c){if(g=(255&c.rgba)/255,g===1)return{css:c.css,rgba:c.rgba};const S=c.rgba>>24&255,E=c.rgba>>16&255,T=c.rgba>>8&255,L=b.rgba>>24&255,B=b.rgba>>16&255,O=b.rgba>>8&255;return u=L+Math.round((S-L)*g),a=B+Math.round((E-B)*g),d=O+Math.round((T-O)*g),{css:p.toCss(u,a,d),rgba:p.toRgba(u,a,d)}},f.isOpaque=function(b){return(255&b.rgba)==255},f.ensureContrastRatio=function(b,c,S){const E=s.ensureContrastRatio(b.rgba,c.rgba,S);if(E)return s.toColor(E>>24&255,E>>16&255,E>>8&255)},f.opaque=function(b){const c=(255|b.rgba)>>>0;return[u,a,d]=s.toChannels(c),{css:p.toCss(u,a,d),rgba:c}},f.opacity=v,f.multiplyOpacity=function(b,c){return g=255&b.rgba,v(b,g*c/255)},f.toColorRGB=function(b){return[b.rgba>>24&255,b.rgba>>16&255,b.rgba>>8&255]}}(_||(t.color=_={})),function(f){let v,b;if(!l.isNode){const c=document.createElement("canvas");c.width=1,c.height=1;const S=c.getContext("2d",{willReadFrequently:!0});S&&(v=S,v.globalCompositeOperation="copy",b=v.createLinearGradient(0,0,1,1))}f.toColor=function(c){if(c.match(/#[\da-f]{3,8}/i))switch(c.length){case 4:return u=parseInt(c.slice(1,2).repeat(2),16),a=parseInt(c.slice(2,3).repeat(2),16),d=parseInt(c.slice(3,4).repeat(2),16),s.toColor(u,a,d);case 5:return u=parseInt(c.slice(1,2).repeat(2),16),a=parseInt(c.slice(2,3).repeat(2),16),d=parseInt(c.slice(3,4).repeat(2),16),g=parseInt(c.slice(4,5).repeat(2),16),s.toColor(u,a,d,g);case 7:return{css:c,rgba:(parseInt(c.slice(1),16)<<8|255)>>>0};case 9:return{css:c,rgba:parseInt(c.slice(1),16)>>>0}}const S=c.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(S)return u=parseInt(S[1]),a=parseInt(S[2]),d=parseInt(S[3]),g=Math.round(255*(S[5]===void 0?1:parseFloat(S[5]))),s.toColor(u,a,d,g);if(!v||!b)throw new Error("css.toColor: Unsupported css format");if(v.fillStyle=b,v.fillStyle=c,typeof v.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(v.fillRect(0,0,1,1),[u,a,d,g]=v.getImageData(0,0,1,1).data,g!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:p.toRgba(u,a,d,g),css:c}}}(e||(t.css=e={})),function(f){function v(b,c,S){const E=b/255,T=c/255,L=S/255;return .2126*(E<=.03928?E/12.92:Math.pow((E+.055)/1.055,2.4))+.7152*(T<=.03928?T/12.92:Math.pow((T+.055)/1.055,2.4))+.0722*(L<=.03928?L/12.92:Math.pow((L+.055)/1.055,2.4))}f.relativeLuminance=function(b){return v(b>>16&255,b>>8&255,255&b)},f.relativeLuminance2=v}(i||(t.rgb=i={})),function(f){function v(c,S,E){const T=c>>24&255,L=c>>16&255,B=c>>8&255;let O=S>>24&255,$=S>>16&255,W=S>>8&255,z=h(i.relativeLuminance2(O,$,W),i.relativeLuminance2(T,L,B));for(;z<E&&(O>0||$>0||W>0);)O-=Math.max(0,Math.ceil(.1*O)),$-=Math.max(0,Math.ceil(.1*$)),W-=Math.max(0,Math.ceil(.1*W)),z=h(i.relativeLuminance2(O,$,W),i.relativeLuminance2(T,L,B));return(O<<24|$<<16|W<<8|255)>>>0}function b(c,S,E){const T=c>>24&255,L=c>>16&255,B=c>>8&255;let O=S>>24&255,$=S>>16&255,W=S>>8&255,z=h(i.relativeLuminance2(O,$,W),i.relativeLuminance2(T,L,B));for(;z<E&&(O<255||$<255||W<255);)O=Math.min(255,O+Math.ceil(.1*(255-O))),$=Math.min(255,$+Math.ceil(.1*(255-$))),W=Math.min(255,W+Math.ceil(.1*(255-W))),z=h(i.relativeLuminance2(O,$,W),i.relativeLuminance2(T,L,B));return(O<<24|$<<16|W<<8|255)>>>0}f.ensureContrastRatio=function(c,S,E){const T=i.relativeLuminance(c>>8),L=i.relativeLuminance(S>>8);if(h(T,L)<E){if(L<T){const $=v(c,S,E),W=h(T,i.relativeLuminance($>>8));if(W<E){const z=b(c,S,E);return W>h(T,i.relativeLuminance(z>>8))?$:z}return $}const B=b(c,S,E),O=h(T,i.relativeLuminance(B>>8));if(O<E){const $=v(c,S,E);return O>h(T,i.relativeLuminance($>>8))?B:$}return B}},f.reduceLuminance=v,f.increaseLuminance=b,f.toChannels=function(c){return[c>>24&255,c>>16&255,c>>8&255,255&c]},f.toColor=function(c,S,E,T){return{css:p.toCss(c,S,E,T),rgba:p.toRgba(c,S,E,T)}}}(s||(t.rgba=s={})),t.toPaddedHex=r,t.contrastRatio=h},8969:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CoreTerminal=void 0;const l=n(844),u=n(2585),a=n(4348),d=n(7866),g=n(744),p=n(7302),_=n(6975),e=n(8460),i=n(1753),s=n(1480),r=n(7994),h=n(9282),f=n(5435),v=n(5981),b=n(2660);let c=!1;class S extends l.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new e.EventEmitter),this._onScroll.event(T=>{var L;(L=this._onScrollApi)===null||L===void 0||L.fire(T.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(T){for(const L in T)this.optionsService.options[L]=T[L]}constructor(T){super(),this._windowsWrappingHeuristics=this.register(new l.MutableDisposable),this._onBinary=this.register(new e.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new e.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new e.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new e.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new e.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new e.EventEmitter),this._instantiationService=new a.InstantiationService,this.optionsService=this.register(new p.OptionsService(T)),this._instantiationService.setService(u.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(g.BufferService)),this._instantiationService.setService(u.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(d.LogService)),this._instantiationService.setService(u.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(_.CoreService)),this._instantiationService.setService(u.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(i.CoreMouseService)),this._instantiationService.setService(u.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(s.UnicodeService)),this._instantiationService.setService(u.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(r.CharsetService),this._instantiationService.setService(u.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(b.OscLinkService),this._instantiationService.setService(u.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new f.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,e.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,e.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,e.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,e.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(L=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(L=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new v.WriteBuffer((L,B)=>this._inputHandler.parse(L,B))),this.register((0,e.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(T,L){this._writeBuffer.write(T,L)}writeSync(T,L){this._logService.logLevel<=u.LogLevelEnum.WARN&&!c&&(this._logService.warn("writeSync is unreliable and will be removed soon."),c=!0),this._writeBuffer.writeSync(T,L)}resize(T,L){isNaN(T)||isNaN(L)||(T=Math.max(T,g.MINIMUM_COLS),L=Math.max(L,g.MINIMUM_ROWS),this._bufferService.resize(T,L))}scroll(T,L=!1){this._bufferService.scroll(T,L)}scrollLines(T,L,B){this._bufferService.scrollLines(T,L,B)}scrollPages(T){this.scrollLines(T*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(T){const L=T-this._bufferService.buffer.ydisp;L!==0&&this.scrollLines(L)}registerEscHandler(T,L){return this._inputHandler.registerEscHandler(T,L)}registerDcsHandler(T,L){return this._inputHandler.registerDcsHandler(T,L)}registerCsiHandler(T,L){return this._inputHandler.registerCsiHandler(T,L)}registerOscHandler(T,L){return this._inputHandler.registerOscHandler(T,L)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let T=!1;const L=this.optionsService.rawOptions.windowsPty;L&&L.buildNumber!==void 0&&L.buildNumber!==void 0?T=L.backend==="conpty"&&L.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(T=!0),T?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const T=[];T.push(this.onLineFeed(h.updateWindowsModeWrappedState.bind(null,this._bufferService))),T.push(this.registerCsiHandler({final:"H"},()=>((0,h.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,l.toDisposable)(()=>{for(const L of T)L.dispose()})}}}t.CoreTerminal=S},8460:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.forwardEvent=t.EventEmitter=void 0,t.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=n=>(this._listeners.push(n),{dispose:()=>{if(!this._disposed){for(let l=0;l<this._listeners.length;l++)if(this._listeners[l]===n)return void this._listeners.splice(l,1)}}})),this._event}fire(n,l){const u=[];for(let a=0;a<this._listeners.length;a++)u.push(this._listeners[a]);for(let a=0;a<u.length;a++)u[a].call(void 0,n,l)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},t.forwardEvent=function(n,l){return n(u=>l.fire(u))}},5435:function(D,t,n){var l=this&&this.__decorate||function(z,y,A,R){var I,U=arguments.length,N=U<3?y:R===null?R=Object.getOwnPropertyDescriptor(y,A):R;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")N=Reflect.decorate(z,y,A,R);else for(var q=z.length-1;q>=0;q--)(I=z[q])&&(N=(U<3?I(N):U>3?I(y,A,N):I(y,A))||N);return U>3&&N&&Object.defineProperty(y,A,N),N},u=this&&this.__param||function(z,y){return function(A,R){y(A,R,z)}};Object.defineProperty(t,"__esModule",{value:!0}),t.InputHandler=t.WindowsOptionsReportType=void 0;const a=n(2584),d=n(7116),g=n(2015),p=n(844),_=n(482),e=n(8437),i=n(8460),s=n(643),r=n(511),h=n(3734),f=n(2585),v=n(6242),b=n(6351),c=n(5941),S={"(":0,")":1,"*":2,"+":3,"-":1,".":2},E=131072;function T(z,y){if(z>24)return y.setWinLines||!1;switch(z){case 1:return!!y.restoreWin;case 2:return!!y.minimizeWin;case 3:return!!y.setWinPosition;case 4:return!!y.setWinSizePixels;case 5:return!!y.raiseWin;case 6:return!!y.lowerWin;case 7:return!!y.refreshWin;case 8:return!!y.setWinSizeChars;case 9:return!!y.maximizeWin;case 10:return!!y.fullscreenWin;case 11:return!!y.getWinState;case 13:return!!y.getWinPosition;case 14:return!!y.getWinSizePixels;case 15:return!!y.getScreenSizePixels;case 16:return!!y.getCellSizePixels;case 18:return!!y.getWinSizeChars;case 19:return!!y.getScreenSizeChars;case 20:return!!y.getIconTitle;case 21:return!!y.getWinTitle;case 22:return!!y.pushTitle;case 23:return!!y.popTitle;case 24:return!!y.setWinLines}return!1}var L;(function(z){z[z.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",z[z.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(L||(t.WindowsOptionsReportType=L={}));let B=0;class O extends p.Disposable{getAttrData(){return this._curAttrData}constructor(y,A,R,I,U,N,q,K,Q=new g.EscapeSequenceParser){super(),this._bufferService=y,this._charsetService=A,this._coreService=R,this._logService=I,this._optionsService=U,this._oscLinkService=N,this._coreMouseService=q,this._unicodeService=K,this._parser=Q,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new _.StringToUtf32,this._utf8Decoder=new _.Utf8ToUtf32,this._workCell=new r.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new i.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new i.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new i.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new i.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new i.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new i.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new i.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new i.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new i.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new i.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new i.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new i.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new i.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new $(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(C=>this._activeBuffer=C.activeBuffer)),this._parser.setCsiHandlerFallback((C,P)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(C),params:P.toArray()})}),this._parser.setEscHandlerFallback(C=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(C)})}),this._parser.setExecuteHandlerFallback(C=>{this._logService.debug("Unknown EXECUTE code: ",{code:C})}),this._parser.setOscHandlerFallback((C,P,F)=>{this._logService.debug("Unknown OSC code: ",{identifier:C,action:P,data:F})}),this._parser.setDcsHandlerFallback((C,P,F)=>{P==="HOOK"&&(F=F.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(C),action:P,payload:F})}),this._parser.setPrintHandler((C,P,F)=>this.print(C,P,F)),this._parser.registerCsiHandler({final:"@"},C=>this.insertChars(C)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},C=>this.scrollLeft(C)),this._parser.registerCsiHandler({final:"A"},C=>this.cursorUp(C)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},C=>this.scrollRight(C)),this._parser.registerCsiHandler({final:"B"},C=>this.cursorDown(C)),this._parser.registerCsiHandler({final:"C"},C=>this.cursorForward(C)),this._parser.registerCsiHandler({final:"D"},C=>this.cursorBackward(C)),this._parser.registerCsiHandler({final:"E"},C=>this.cursorNextLine(C)),this._parser.registerCsiHandler({final:"F"},C=>this.cursorPrecedingLine(C)),this._parser.registerCsiHandler({final:"G"},C=>this.cursorCharAbsolute(C)),this._parser.registerCsiHandler({final:"H"},C=>this.cursorPosition(C)),this._parser.registerCsiHandler({final:"I"},C=>this.cursorForwardTab(C)),this._parser.registerCsiHandler({final:"J"},C=>this.eraseInDisplay(C,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},C=>this.eraseInDisplay(C,!0)),this._parser.registerCsiHandler({final:"K"},C=>this.eraseInLine(C,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},C=>this.eraseInLine(C,!0)),this._parser.registerCsiHandler({final:"L"},C=>this.insertLines(C)),this._parser.registerCsiHandler({final:"M"},C=>this.deleteLines(C)),this._parser.registerCsiHandler({final:"P"},C=>this.deleteChars(C)),this._parser.registerCsiHandler({final:"S"},C=>this.scrollUp(C)),this._parser.registerCsiHandler({final:"T"},C=>this.scrollDown(C)),this._parser.registerCsiHandler({final:"X"},C=>this.eraseChars(C)),this._parser.registerCsiHandler({final:"Z"},C=>this.cursorBackwardTab(C)),this._parser.registerCsiHandler({final:"`"},C=>this.charPosAbsolute(C)),this._parser.registerCsiHandler({final:"a"},C=>this.hPositionRelative(C)),this._parser.registerCsiHandler({final:"b"},C=>this.repeatPrecedingCharacter(C)),this._parser.registerCsiHandler({final:"c"},C=>this.sendDeviceAttributesPrimary(C)),this._parser.registerCsiHandler({prefix:">",final:"c"},C=>this.sendDeviceAttributesSecondary(C)),this._parser.registerCsiHandler({final:"d"},C=>this.linePosAbsolute(C)),this._parser.registerCsiHandler({final:"e"},C=>this.vPositionRelative(C)),this._parser.registerCsiHandler({final:"f"},C=>this.hVPosition(C)),this._parser.registerCsiHandler({final:"g"},C=>this.tabClear(C)),this._parser.registerCsiHandler({final:"h"},C=>this.setMode(C)),this._parser.registerCsiHandler({prefix:"?",final:"h"},C=>this.setModePrivate(C)),this._parser.registerCsiHandler({final:"l"},C=>this.resetMode(C)),this._parser.registerCsiHandler({prefix:"?",final:"l"},C=>this.resetModePrivate(C)),this._parser.registerCsiHandler({final:"m"},C=>this.charAttributes(C)),this._parser.registerCsiHandler({final:"n"},C=>this.deviceStatus(C)),this._parser.registerCsiHandler({prefix:"?",final:"n"},C=>this.deviceStatusPrivate(C)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},C=>this.softReset(C)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},C=>this.setCursorStyle(C)),this._parser.registerCsiHandler({final:"r"},C=>this.setScrollRegion(C)),this._parser.registerCsiHandler({final:"s"},C=>this.saveCursor(C)),this._parser.registerCsiHandler({final:"t"},C=>this.windowOptions(C)),this._parser.registerCsiHandler({final:"u"},C=>this.restoreCursor(C)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},C=>this.insertColumns(C)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},C=>this.deleteColumns(C)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},C=>this.selectProtected(C)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},C=>this.requestMode(C,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},C=>this.requestMode(C,!1)),this._parser.setExecuteHandler(a.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(a.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(a.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(a.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(a.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(a.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(a.C1.IND,()=>this.index()),this._parser.setExecuteHandler(a.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(a.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new v.OscHandler(C=>(this.setTitle(C),this.setIconName(C),!0))),this._parser.registerOscHandler(1,new v.OscHandler(C=>this.setIconName(C))),this._parser.registerOscHandler(2,new v.OscHandler(C=>this.setTitle(C))),this._parser.registerOscHandler(4,new v.OscHandler(C=>this.setOrReportIndexedColor(C))),this._parser.registerOscHandler(8,new v.OscHandler(C=>this.setHyperlink(C))),this._parser.registerOscHandler(10,new v.OscHandler(C=>this.setOrReportFgColor(C))),this._parser.registerOscHandler(11,new v.OscHandler(C=>this.setOrReportBgColor(C))),this._parser.registerOscHandler(12,new v.OscHandler(C=>this.setOrReportCursorColor(C))),this._parser.registerOscHandler(104,new v.OscHandler(C=>this.restoreIndexedColor(C))),this._parser.registerOscHandler(110,new v.OscHandler(C=>this.restoreFgColor(C))),this._parser.registerOscHandler(111,new v.OscHandler(C=>this.restoreBgColor(C))),this._parser.registerOscHandler(112,new v.OscHandler(C=>this.restoreCursorColor(C))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const C in d.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:C},()=>this.selectCharset("("+C)),this._parser.registerEscHandler({intermediates:")",final:C},()=>this.selectCharset(")"+C)),this._parser.registerEscHandler({intermediates:"*",final:C},()=>this.selectCharset("*"+C)),this._parser.registerEscHandler({intermediates:"+",final:C},()=>this.selectCharset("+"+C)),this._parser.registerEscHandler({intermediates:"-",final:C},()=>this.selectCharset("-"+C)),this._parser.registerEscHandler({intermediates:".",final:C},()=>this.selectCharset("."+C)),this._parser.registerEscHandler({intermediates:"/",final:C},()=>this.selectCharset("/"+C));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(C=>(this._logService.error("Parsing error: ",C),C)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new b.DcsHandler((C,P)=>this.requestStatusString(C,P)))}_preserveStack(y,A,R,I){this._parseStack.paused=!0,this._parseStack.cursorStartX=y,this._parseStack.cursorStartY=A,this._parseStack.decodedLength=R,this._parseStack.position=I}_logSlowResolvingAsync(y){this._logService.logLevel<=f.LogLevelEnum.WARN&&Promise.race([y,new Promise((A,R)=>setTimeout(()=>R("#SLOW_TIMEOUT"),5e3))]).catch(A=>{if(A!=="#SLOW_TIMEOUT")throw A;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(y,A){let R,I=this._activeBuffer.x,U=this._activeBuffer.y,N=0;const q=this._parseStack.paused;if(q){if(R=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,A))return this._logSlowResolvingAsync(R),R;I=this._parseStack.cursorStartX,U=this._parseStack.cursorStartY,this._parseStack.paused=!1,y.length>E&&(N=this._parseStack.position+E)}if(this._logService.logLevel<=f.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof y=="string"?` "${y}"`:` "${Array.prototype.map.call(y,K=>String.fromCharCode(K)).join("")}"`),typeof y=="string"?y.split("").map(K=>K.charCodeAt(0)):y),this._parseBuffer.length<y.length&&this._parseBuffer.length<E&&(this._parseBuffer=new Uint32Array(Math.min(y.length,E))),q||this._dirtyRowTracker.clearRange(),y.length>E)for(let K=N;K<y.length;K+=E){const Q=K+E<y.length?K+E:y.length,C=typeof y=="string"?this._stringDecoder.decode(y.substring(K,Q),this._parseBuffer):this._utf8Decoder.decode(y.subarray(K,Q),this._parseBuffer);if(R=this._parser.parse(this._parseBuffer,C))return this._preserveStack(I,U,C,K),this._logSlowResolvingAsync(R),R}else if(!q){const K=typeof y=="string"?this._stringDecoder.decode(y,this._parseBuffer):this._utf8Decoder.decode(y,this._parseBuffer);if(R=this._parser.parse(this._parseBuffer,K))return this._preserveStack(I,U,K,0),this._logSlowResolvingAsync(R),R}this._activeBuffer.x===I&&this._activeBuffer.y===U||this._onCursorMove.fire(),this._onRequestRefreshRows.fire(this._dirtyRowTracker.start,this._dirtyRowTracker.end)}print(y,A,R){let I,U;const N=this._charsetService.charset,q=this._optionsService.rawOptions.screenReaderMode,K=this._bufferService.cols,Q=this._coreService.decPrivateModes.wraparound,C=this._coreService.modes.insertMode,P=this._curAttrData;let F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&R-A>0&&F.getWidth(this._activeBuffer.x-1)===2&&F.setCellFromCodePoint(this._activeBuffer.x-1,0,1,P.fg,P.bg,P.extended);for(let H=A;H<R;++H){if(I=y[H],U=this._unicodeService.wcwidth(I),I<127&&N){const V=N[String.fromCharCode(I)];V&&(I=V.charCodeAt(0))}if(q&&this._onA11yChar.fire((0,_.stringFromCodePoint)(I)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),U||!this._activeBuffer.x){if(this._activeBuffer.x+U-1>=K){if(Q){for(;this._activeBuffer.x<K;)F.setCellFromCodePoint(this._activeBuffer.x++,0,1,P.fg,P.bg,P.extended);this._activeBuffer.x=0,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)}else if(this._activeBuffer.x=K-1,U===2)continue}if(C&&(F.insertCells(this._activeBuffer.x,U,this._activeBuffer.getNullCell(P),P),F.getWidth(K-1)===2&&F.setCellFromCodePoint(K-1,s.NULL_CELL_CODE,s.NULL_CELL_WIDTH,P.fg,P.bg,P.extended)),F.setCellFromCodePoint(this._activeBuffer.x++,I,U,P.fg,P.bg,P.extended),U>0)for(;--U;)F.setCellFromCodePoint(this._activeBuffer.x++,0,0,P.fg,P.bg,P.extended)}else F.getWidth(this._activeBuffer.x-1)?F.addCodepointToCell(this._activeBuffer.x-1,I):F.addCodepointToCell(this._activeBuffer.x-2,I)}R-A>0&&(F.loadCell(this._activeBuffer.x-1,this._workCell),this._workCell.getWidth()===2||this._workCell.getCode()>65535?this._parser.precedingCodepoint=0:this._workCell.isCombined()?this._parser.precedingCodepoint=this._workCell.getChars().charCodeAt(0):this._parser.precedingCodepoint=this._workCell.content),this._activeBuffer.x<K&&R-A>0&&F.getWidth(this._activeBuffer.x)===0&&!F.hasContent(this._activeBuffer.x)&&F.setCellFromCodePoint(this._activeBuffer.x,0,1,P.fg,P.bg,P.extended),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(y,A){return y.final!=="t"||y.prefix||y.intermediates?this._parser.registerCsiHandler(y,A):this._parser.registerCsiHandler(y,R=>!T(R.params[0],this._optionsService.rawOptions.windowOptions)||A(R))}registerDcsHandler(y,A){return this._parser.registerDcsHandler(y,new b.DcsHandler(A))}registerEscHandler(y,A){return this._parser.registerEscHandler(y,A)}registerOscHandler(y,A){return this._parser.registerOscHandler(y,new v.OscHandler(A))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var y;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&(!((y=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))===null||y===void 0)&&y.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const A=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);A.hasWidth(this._activeBuffer.x)&&!A.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const y=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-y),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(y=this._bufferService.cols-1){this._activeBuffer.x=Math.min(y,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(y,A){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=y,this._activeBuffer.y=this._activeBuffer.scrollTop+A):(this._activeBuffer.x=y,this._activeBuffer.y=A),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(y,A){this._restrictCursor(),this._setCursor(this._activeBuffer.x+y,this._activeBuffer.y+A)}cursorUp(y){const A=this._activeBuffer.y-this._activeBuffer.scrollTop;return A>=0?this._moveCursor(0,-Math.min(A,y.params[0]||1)):this._moveCursor(0,-(y.params[0]||1)),!0}cursorDown(y){const A=this._activeBuffer.scrollBottom-this._activeBuffer.y;return A>=0?this._moveCursor(0,Math.min(A,y.params[0]||1)):this._moveCursor(0,y.params[0]||1),!0}cursorForward(y){return this._moveCursor(y.params[0]||1,0),!0}cursorBackward(y){return this._moveCursor(-(y.params[0]||1),0),!0}cursorNextLine(y){return this.cursorDown(y),this._activeBuffer.x=0,!0}cursorPrecedingLine(y){return this.cursorUp(y),this._activeBuffer.x=0,!0}cursorCharAbsolute(y){return this._setCursor((y.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(y){return this._setCursor(y.length>=2?(y.params[1]||1)-1:0,(y.params[0]||1)-1),!0}charPosAbsolute(y){return this._setCursor((y.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(y){return this._moveCursor(y.params[0]||1,0),!0}linePosAbsolute(y){return this._setCursor(this._activeBuffer.x,(y.params[0]||1)-1),!0}vPositionRelative(y){return this._moveCursor(0,y.params[0]||1),!0}hVPosition(y){return this.cursorPosition(y),!0}tabClear(y){const A=y.params[0];return A===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:A===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(y){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let A=y.params[0]||1;for(;A--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(y){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let A=y.params[0]||1;for(;A--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(y){const A=y.params[0];return A===1&&(this._curAttrData.bg|=536870912),A!==2&&A!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(y,A,R,I=!1,U=!1){const N=this._activeBuffer.lines.get(this._activeBuffer.ybase+y);N.replaceCells(A,R,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData(),U),I&&(N.isWrapped=!1)}_resetBufferLine(y,A=!1){const R=this._activeBuffer.lines.get(this._activeBuffer.ybase+y);R&&(R.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),A),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+y),R.isWrapped=!1)}eraseInDisplay(y,A=!1){let R;switch(this._restrictCursor(this._bufferService.cols),y.params[0]){case 0:for(R=this._activeBuffer.y,this._dirtyRowTracker.markDirty(R),this._eraseInBufferLine(R++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,A);R<this._bufferService.rows;R++)this._resetBufferLine(R,A);this._dirtyRowTracker.markDirty(R);break;case 1:for(R=this._activeBuffer.y,this._dirtyRowTracker.markDirty(R),this._eraseInBufferLine(R,0,this._activeBuffer.x+1,!0,A),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(R+1).isWrapped=!1);R--;)this._resetBufferLine(R,A);this._dirtyRowTracker.markDirty(0);break;case 2:for(R=this._bufferService.rows,this._dirtyRowTracker.markDirty(R-1);R--;)this._resetBufferLine(R,A);this._dirtyRowTracker.markDirty(0);break;case 3:const I=this._activeBuffer.lines.length-this._bufferService.rows;I>0&&(this._activeBuffer.lines.trimStart(I),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-I,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-I,0),this._onScroll.fire(0))}return!0}eraseInLine(y,A=!1){switch(this._restrictCursor(this._bufferService.cols),y.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,A);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,A);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,A)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(y){this._restrictCursor();let A=y.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const R=this._activeBuffer.ybase+this._activeBuffer.y,I=this._bufferService.rows-1-this._activeBuffer.scrollBottom,U=this._bufferService.rows-1+this._activeBuffer.ybase-I+1;for(;A--;)this._activeBuffer.lines.splice(U-1,1),this._activeBuffer.lines.splice(R,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(y){this._restrictCursor();let A=y.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const R=this._activeBuffer.ybase+this._activeBuffer.y;let I;for(I=this._bufferService.rows-1-this._activeBuffer.scrollBottom,I=this._bufferService.rows-1+this._activeBuffer.ybase-I;A--;)this._activeBuffer.lines.splice(R,1),this._activeBuffer.lines.splice(I,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(y){this._restrictCursor();const A=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return A&&(A.insertCells(this._activeBuffer.x,y.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(y){this._restrictCursor();const A=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return A&&(A.deleteCells(this._activeBuffer.x,y.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(y){let A=y.params[0]||1;for(;A--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(y){let A=y.params[0]||1;for(;A--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(e.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(y){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const A=y.params[0]||1;for(let R=this._activeBuffer.scrollTop;R<=this._activeBuffer.scrollBottom;++R){const I=this._activeBuffer.lines.get(this._activeBuffer.ybase+R);I.deleteCells(0,A,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),I.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(y){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const A=y.params[0]||1;for(let R=this._activeBuffer.scrollTop;R<=this._activeBuffer.scrollBottom;++R){const I=this._activeBuffer.lines.get(this._activeBuffer.ybase+R);I.insertCells(0,A,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),I.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(y){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const A=y.params[0]||1;for(let R=this._activeBuffer.scrollTop;R<=this._activeBuffer.scrollBottom;++R){const I=this._activeBuffer.lines.get(this._activeBuffer.ybase+R);I.insertCells(this._activeBuffer.x,A,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),I.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(y){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const A=y.params[0]||1;for(let R=this._activeBuffer.scrollTop;R<=this._activeBuffer.scrollBottom;++R){const I=this._activeBuffer.lines.get(this._activeBuffer.ybase+R);I.deleteCells(this._activeBuffer.x,A,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),I.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(y){this._restrictCursor();const A=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return A&&(A.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(y.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(y){if(!this._parser.precedingCodepoint)return!0;const A=y.params[0]||1,R=new Uint32Array(A);for(let I=0;I<A;++I)R[I]=this._parser.precedingCodepoint;return this.print(R,0,R.length),!0}sendDeviceAttributesPrimary(y){return y.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(a.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(a.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(y){return y.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(a.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(a.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(y.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(a.C0.ESC+"[>83;40003;0c")),!0}_is(y){return(this._optionsService.rawOptions.termName+"").indexOf(y)===0}setMode(y){for(let A=0;A<y.length;A++)switch(y.params[A]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(y){for(let A=0;A<y.length;A++)switch(y.params[A]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,d.DEFAULT_CHARSET),this._charsetService.setgCharset(1,d.DEFAULT_CHARSET),this._charsetService.setgCharset(2,d.DEFAULT_CHARSET),this._charsetService.setgCharset(3,d.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(y){for(let A=0;A<y.length;A++)switch(y.params[A]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(y){for(let A=0;A<y.length;A++)switch(y.params[A]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),y.params[A]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(y,A){const R=this._coreService.decPrivateModes,{activeProtocol:I,activeEncoding:U}=this._coreMouseService,N=this._coreService,{buffers:q,cols:K}=this._bufferService,{active:Q,alt:C}=q,P=this._optionsService.rawOptions,F=Z=>Z?1:2,H=y.params[0];return V=H,j=A?H===2?4:H===4?F(N.modes.insertMode):H===12?3:H===20?F(P.convertEol):0:H===1?F(R.applicationCursorKeys):H===3?P.windowOptions.setWinLines?K===80?2:K===132?1:0:0:H===6?F(R.origin):H===7?F(R.wraparound):H===8?3:H===9?F(I==="X10"):H===12?F(P.cursorBlink):H===25?F(!N.isCursorHidden):H===45?F(R.reverseWraparound):H===66?F(R.applicationKeypad):H===67?4:H===1e3?F(I==="VT200"):H===1002?F(I==="DRAG"):H===1003?F(I==="ANY"):H===1004?F(R.sendFocus):H===1005?4:H===1006?F(U==="SGR"):H===1015?4:H===1016?F(U==="SGR_PIXELS"):H===1048?1:H===47||H===1047||H===1049?F(Q===C):H===2004?F(R.bracketedPasteMode):0,N.triggerDataEvent(`${a.C0.ESC}[${A?"":"?"}${V};${j}$y`),!0;var V,j}_updateAttrColor(y,A,R,I,U){return A===2?(y|=50331648,y&=-16777216,y|=h.AttributeData.fromColorRGB([R,I,U])):A===5&&(y&=-50331904,y|=33554432|255&R),y}_extractColor(y,A,R){const I=[0,0,-1,0,0,0];let U=0,N=0;do{if(I[N+U]=y.params[A+N],y.hasSubParams(A+N)){const q=y.getSubParams(A+N);let K=0;do I[1]===5&&(U=1),I[N+K+1+U]=q[K];while(++K<q.length&&K+N+1+U<I.length);break}if(I[1]===5&&N+U>=2||I[1]===2&&N+U>=5)break;I[1]&&(U=1)}while(++N+A<y.length&&N+U<I.length);for(let q=2;q<I.length;++q)I[q]===-1&&(I[q]=0);switch(I[0]){case 38:R.fg=this._updateAttrColor(R.fg,I[1],I[3],I[4],I[5]);break;case 48:R.bg=this._updateAttrColor(R.bg,I[1],I[3],I[4],I[5]);break;case 58:R.extended=R.extended.clone(),R.extended.underlineColor=this._updateAttrColor(R.extended.underlineColor,I[1],I[3],I[4],I[5])}return N}_processUnderline(y,A){A.extended=A.extended.clone(),(!~y||y>5)&&(y=1),A.extended.underlineStyle=y,A.fg|=268435456,y===0&&(A.fg&=-268435457),A.updateExtended()}_processSGR0(y){y.fg=e.DEFAULT_ATTR_DATA.fg,y.bg=e.DEFAULT_ATTR_DATA.bg,y.extended=y.extended.clone(),y.extended.underlineStyle=0,y.extended.underlineColor&=-67108864,y.updateExtended()}charAttributes(y){if(y.length===1&&y.params[0]===0)return this._processSGR0(this._curAttrData),!0;const A=y.length;let R;const I=this._curAttrData;for(let U=0;U<A;U++)R=y.params[U],R>=30&&R<=37?(I.fg&=-50331904,I.fg|=16777216|R-30):R>=40&&R<=47?(I.bg&=-50331904,I.bg|=16777216|R-40):R>=90&&R<=97?(I.fg&=-50331904,I.fg|=16777224|R-90):R>=100&&R<=107?(I.bg&=-50331904,I.bg|=16777224|R-100):R===0?this._processSGR0(I):R===1?I.fg|=134217728:R===3?I.bg|=67108864:R===4?(I.fg|=268435456,this._processUnderline(y.hasSubParams(U)?y.getSubParams(U)[0]:1,I)):R===5?I.fg|=536870912:R===7?I.fg|=67108864:R===8?I.fg|=1073741824:R===9?I.fg|=2147483648:R===2?I.bg|=134217728:R===21?this._processUnderline(2,I):R===22?(I.fg&=-134217729,I.bg&=-134217729):R===23?I.bg&=-67108865:R===24?(I.fg&=-268435457,this._processUnderline(0,I)):R===25?I.fg&=-536870913:R===27?I.fg&=-67108865:R===28?I.fg&=-1073741825:R===29?I.fg&=2147483647:R===39?(I.fg&=-67108864,I.fg|=16777215&e.DEFAULT_ATTR_DATA.fg):R===49?(I.bg&=-67108864,I.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):R===38||R===48||R===58?U+=this._extractColor(y,U,I):R===53?I.bg|=1073741824:R===55?I.bg&=-1073741825:R===59?(I.extended=I.extended.clone(),I.extended.underlineColor=-1,I.updateExtended()):R===100?(I.fg&=-67108864,I.fg|=16777215&e.DEFAULT_ATTR_DATA.fg,I.bg&=-67108864,I.bg|=16777215&e.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",R);return!0}deviceStatus(y){switch(y.params[0]){case 5:this._coreService.triggerDataEvent(`${a.C0.ESC}[0n`);break;case 6:const A=this._activeBuffer.y+1,R=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${a.C0.ESC}[${A};${R}R`)}return!0}deviceStatusPrivate(y){if(y.params[0]===6){const A=this._activeBuffer.y+1,R=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${a.C0.ESC}[?${A};${R}R`)}return!0}softReset(y){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(y){const A=y.params[0]||1;switch(A){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const R=A%2==1;return this._optionsService.options.cursorBlink=R,!0}setScrollRegion(y){const A=y.params[0]||1;let R;return(y.length<2||(R=y.params[1])>this._bufferService.rows||R===0)&&(R=this._bufferService.rows),R>A&&(this._activeBuffer.scrollTop=A-1,this._activeBuffer.scrollBottom=R-1,this._setCursor(0,0)),!0}windowOptions(y){if(!T(y.params[0],this._optionsService.rawOptions.windowOptions))return!0;const A=y.length>1?y.params[1]:0;switch(y.params[0]){case 14:A!==2&&this._onRequestWindowsOptionsReport.fire(L.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(L.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${a.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:A!==0&&A!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),A!==0&&A!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:A!==0&&A!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),A!==0&&A!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(y){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(y){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(y){return this._windowTitle=y,this._onTitleChange.fire(y),!0}setIconName(y){return this._iconName=y,!0}setOrReportIndexedColor(y){const A=[],R=y.split(";");for(;R.length>1;){const I=R.shift(),U=R.shift();if(/^\d+$/.exec(I)){const N=parseInt(I);if(W(N))if(U==="?")A.push({type:0,index:N});else{const q=(0,c.parseColor)(U);q&&A.push({type:1,index:N,color:q})}}}return A.length&&this._onColor.fire(A),!0}setHyperlink(y){const A=y.split(";");return!(A.length<2)&&(A[1]?this._createHyperlink(A[0],A[1]):!A[0]&&this._finishHyperlink())}_createHyperlink(y,A){this._getCurrentLinkId()&&this._finishHyperlink();const R=y.split(":");let I;const U=R.findIndex(N=>N.startsWith("id="));return U!==-1&&(I=R[U].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:I,uri:A}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(y,A){const R=y.split(";");for(let I=0;I<R.length&&!(A>=this._specialColors.length);++I,++A)if(R[I]==="?")this._onColor.fire([{type:0,index:this._specialColors[A]}]);else{const U=(0,c.parseColor)(R[I]);U&&this._onColor.fire([{type:1,index:this._specialColors[A],color:U}])}return!0}setOrReportFgColor(y){return this._setOrReportSpecialColor(y,0)}setOrReportBgColor(y){return this._setOrReportSpecialColor(y,1)}setOrReportCursorColor(y){return this._setOrReportSpecialColor(y,2)}restoreIndexedColor(y){if(!y)return this._onColor.fire([{type:2}]),!0;const A=[],R=y.split(";");for(let I=0;I<R.length;++I)if(/^\d+$/.exec(R[I])){const U=parseInt(R[I]);W(U)&&A.push({type:2,index:U})}return A.length&&this._onColor.fire(A),!0}restoreFgColor(y){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(y){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(y){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,d.DEFAULT_CHARSET),!0}selectCharset(y){return y.length!==2?(this.selectDefaultCharset(),!0):(y[0]==="/"||this._charsetService.setgCharset(S[y[0]],d.CHARSETS[y[1]]||d.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const y=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,y,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=e.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=e.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(y){return this._charsetService.setgLevel(y),!0}screenAlignmentPattern(){const y=new r.CellData;y.content=4194373,y.fg=this._curAttrData.fg,y.bg=this._curAttrData.bg,this._setCursor(0,0);for(let A=0;A<this._bufferService.rows;++A){const R=this._activeBuffer.ybase+this._activeBuffer.y+A,I=this._activeBuffer.lines.get(R);I&&(I.fill(y),I.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(y,A){const R=this._bufferService.buffer,I=this._optionsService.rawOptions;return(U=>(this._coreService.triggerDataEvent(`${a.C0.ESC}${U}${a.C0.ESC}\\`),!0))(y==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:y==='"p'?'P1$r61;1"p':y==="r"?`P1$r${R.scrollTop+1};${R.scrollBottom+1}r`:y==="m"?"P1$r0m":y===" q"?`P1$r${{block:2,underline:4,bar:6}[I.cursorStyle]-(I.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(y,A){this._dirtyRowTracker.markRangeDirty(y,A)}}t.InputHandler=O;let $=class{constructor(z){this._bufferService=z,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(z){z<this.start?this.start=z:z>this.end&&(this.end=z)}markRangeDirty(z,y){z>y&&(B=z,z=y,y=B),z<this.start&&(this.start=z),y>this.end&&(this.end=y)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function W(z){return 0<=z&&z<256}$=l([u(0,f.IBufferService)],$)},844:(D,t)=>{function n(l){for(const u of l)u.dispose();l.length=0}Object.defineProperty(t,"__esModule",{value:!0}),t.getDisposeArrayDisposable=t.disposeArray=t.toDisposable=t.MutableDisposable=t.Disposable=void 0,t.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const l of this._disposables)l.dispose();this._disposables.length=0}register(l){return this._disposables.push(l),l}unregister(l){const u=this._disposables.indexOf(l);u!==-1&&this._disposables.splice(u,1)}},t.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(l){var u;this._isDisposed||l===this._value||((u=this._value)===null||u===void 0||u.dispose(),this._value=l)}clear(){this.value=void 0}dispose(){var l;this._isDisposed=!0,(l=this._value)===null||l===void 0||l.dispose(),this._value=void 0}},t.toDisposable=function(l){return{dispose:l}},t.disposeArray=n,t.getDisposeArrayDisposable=function(l){return{dispose:()=>n(l)}}},1505:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FourKeyMap=t.TwoKeyMap=void 0;class n{constructor(){this._data={}}set(u,a,d){this._data[u]||(this._data[u]={}),this._data[u][a]=d}get(u,a){return this._data[u]?this._data[u][a]:void 0}clear(){this._data={}}}t.TwoKeyMap=n,t.FourKeyMap=class{constructor(){this._data=new n}set(l,u,a,d,g){this._data.get(l,u)||this._data.set(l,u,new n),this._data.get(l,u).set(a,d,g)}get(l,u,a,d){var g;return(g=this._data.get(l,u))===null||g===void 0?void 0:g.get(a,d)}clear(){this._data.clear()}}},6114:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isChromeOS=t.isLinux=t.isWindows=t.isIphone=t.isIpad=t.isMac=t.getSafariVersion=t.isSafari=t.isLegacyEdge=t.isFirefox=t.isNode=void 0,t.isNode=typeof navigator>"u";const n=t.isNode?"node":navigator.userAgent,l=t.isNode?"node":navigator.platform;t.isFirefox=n.includes("Firefox"),t.isLegacyEdge=n.includes("Edge"),t.isSafari=/^((?!chrome|android).)*safari/i.test(n),t.getSafariVersion=function(){if(!t.isSafari)return 0;const u=n.match(/Version\/(\d+)/);return u===null||u.length<2?0:parseInt(u[1])},t.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(l),t.isIpad=l==="iPad",t.isIphone=l==="iPhone",t.isWindows=["Windows","Win16","Win32","WinCE"].includes(l),t.isLinux=l.indexOf("Linux")>=0,t.isChromeOS=/\bCrOS\b/.test(n)},6106:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SortedList=void 0;let n=0;t.SortedList=class{constructor(l){this._getKey=l,this._array=[]}clear(){this._array.length=0}insert(l){this._array.length!==0?(n=this._search(this._getKey(l)),this._array.splice(n,0,l)):this._array.push(l)}delete(l){if(this._array.length===0)return!1;const u=this._getKey(l);if(u===void 0||(n=this._search(u),n===-1)||this._getKey(this._array[n])!==u)return!1;do if(this._array[n]===l)return this._array.splice(n,1),!0;while(++n<this._array.length&&this._getKey(this._array[n])===u);return!1}*getKeyIterator(l){if(this._array.length!==0&&(n=this._search(l),!(n<0||n>=this._array.length)&&this._getKey(this._array[n])===l))do yield this._array[n];while(++n<this._array.length&&this._getKey(this._array[n])===l)}forEachByKey(l,u){if(this._array.length!==0&&(n=this._search(l),!(n<0||n>=this._array.length)&&this._getKey(this._array[n])===l))do u(this._array[n]);while(++n<this._array.length&&this._getKey(this._array[n])===l)}values(){return[...this._array].values()}_search(l){let u=0,a=this._array.length-1;for(;a>=u;){let d=u+a>>1;const g=this._getKey(this._array[d]);if(g>l)a=d-1;else{if(!(g<l)){for(;d>0&&this._getKey(this._array[d-1])===l;)d--;return d}u=d+1}}return u}}},7226:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DebouncedIdleTask=t.IdleTaskQueue=t.PriorityTaskQueue=void 0;const l=n(6114);class u{constructor(){this._tasks=[],this._i=0}enqueue(g){this._tasks.push(g),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(g){this._idleCallback=void 0;let p=0,_=0,e=g.timeRemaining(),i=0;for(;this._i<this._tasks.length;){if(p=Date.now(),this._tasks[this._i]()||this._i++,p=Math.max(1,Date.now()-p),_=Math.max(p,_),i=g.timeRemaining(),1.5*_>i)return e-p<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(e-p))}ms`),void this._start();e=i}this.clear()}}class a extends u{_requestCallback(g){return setTimeout(()=>g(this._createDeadline(16)))}_cancelCallback(g){clearTimeout(g)}_createDeadline(g){const p=Date.now()+g;return{timeRemaining:()=>Math.max(0,p-Date.now())}}}t.PriorityTaskQueue=a,t.IdleTaskQueue=!l.isNode&&"requestIdleCallback"in window?class extends u{_requestCallback(d){return requestIdleCallback(d)}_cancelCallback(d){cancelIdleCallback(d)}}:a,t.DebouncedIdleTask=class{constructor(){this._queue=new t.IdleTaskQueue}set(d){this._queue.clear(),this._queue.enqueue(d)}flush(){this._queue.flush()}}},9282:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.updateWindowsModeWrappedState=void 0;const l=n(643);t.updateWindowsModeWrappedState=function(u){const a=u.buffer.lines.get(u.buffer.ybase+u.buffer.y-1),d=a==null?void 0:a.get(u.cols-1),g=u.buffer.lines.get(u.buffer.ybase+u.buffer.y);g&&d&&(g.isWrapped=d[l.CHAR_DATA_CODE_INDEX]!==l.NULL_CELL_CODE&&d[l.CHAR_DATA_CODE_INDEX]!==l.WHITESPACE_CELL_CODE)}},3734:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExtendedAttrs=t.AttributeData=void 0;class n{constructor(){this.fg=0,this.bg=0,this.extended=new l}static toColorRGB(a){return[a>>>16&255,a>>>8&255,255&a]}static fromColorRGB(a){return(255&a[0])<<16|(255&a[1])<<8|255&a[2]}clone(){const a=new n;return a.fg=this.fg,a.bg=this.bg,a.extended=this.extended.clone(),a}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}}t.AttributeData=n;class l{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(a){this._ext=a}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(a){this._ext&=-469762049,this._ext|=a<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(a){this._ext&=-67108864,this._ext|=67108863&a}get urlId(){return this._urlId}set urlId(a){this._urlId=a}constructor(a=0,d=0){this._ext=0,this._urlId=0,this._ext=a,this._urlId=d}clone(){return new l(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}t.ExtendedAttrs=l},9092:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Buffer=t.MAX_BUFFER_SIZE=void 0;const l=n(6349),u=n(7226),a=n(3734),d=n(8437),g=n(4634),p=n(511),_=n(643),e=n(4863),i=n(7116);t.MAX_BUFFER_SIZE=4294967295,t.Buffer=class{constructor(s,r,h){this._hasScrollback=s,this._optionsService=r,this._bufferService=h,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=d.DEFAULT_ATTR_DATA.clone(),this.savedCharset=i.DEFAULT_CHARSET,this.markers=[],this._nullCell=p.CellData.fromCharData([0,_.NULL_CELL_CHAR,_.NULL_CELL_WIDTH,_.NULL_CELL_CODE]),this._whitespaceCell=p.CellData.fromCharData([0,_.WHITESPACE_CELL_CHAR,_.WHITESPACE_CELL_WIDTH,_.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new u.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(s){return s?(this._nullCell.fg=s.fg,this._nullCell.bg=s.bg,this._nullCell.extended=s.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new a.ExtendedAttrs),this._nullCell}getWhitespaceCell(s){return s?(this._whitespaceCell.fg=s.fg,this._whitespaceCell.bg=s.bg,this._whitespaceCell.extended=s.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new a.ExtendedAttrs),this._whitespaceCell}getBlankLine(s,r){return new d.BufferLine(this._bufferService.cols,this.getNullCell(s),r)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const s=this.ybase+this.y-this.ydisp;return s>=0&&s<this._rows}_getCorrectBufferLength(s){if(!this._hasScrollback)return s;const r=s+this._optionsService.rawOptions.scrollback;return r>t.MAX_BUFFER_SIZE?t.MAX_BUFFER_SIZE:r}fillViewportRows(s){if(this.lines.length===0){s===void 0&&(s=d.DEFAULT_ATTR_DATA);let r=this._rows;for(;r--;)this.lines.push(this.getBlankLine(s))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(s,r){const h=this.getNullCell(d.DEFAULT_ATTR_DATA);let f=0;const v=this._getCorrectBufferLength(r);if(v>this.lines.maxLength&&(this.lines.maxLength=v),this.lines.length>0){if(this._cols<s)for(let c=0;c<this.lines.length;c++)f+=+this.lines.get(c).resize(s,h);let b=0;if(this._rows<r)for(let c=this._rows;c<r;c++)this.lines.length<r+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new d.BufferLine(s,h)):this.ybase>0&&this.lines.length<=this.ybase+this.y+b+1?(this.ybase--,b++,this.ydisp>0&&this.ydisp--):this.lines.push(new d.BufferLine(s,h)));else for(let c=this._rows;c>r;c--)this.lines.length>r+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(v<this.lines.maxLength){const c=this.lines.length-v;c>0&&(this.lines.trimStart(c),this.ybase=Math.max(this.ybase-c,0),this.ydisp=Math.max(this.ydisp-c,0),this.savedY=Math.max(this.savedY-c,0)),this.lines.maxLength=v}this.x=Math.min(this.x,s-1),this.y=Math.min(this.y,r-1),b&&(this.y+=b),this.savedX=Math.min(this.savedX,s-1),this.scrollTop=0}if(this.scrollBottom=r-1,this._isReflowEnabled&&(this._reflow(s,r),this._cols>s))for(let b=0;b<this.lines.length;b++)f+=+this.lines.get(b).resize(s,h);this._cols=s,this._rows=r,this._memoryCleanupQueue.clear(),f>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let s=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,s=!1);let r=0;for(;this._memoryCleanupPosition<this.lines.length;)if(r+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),r>100)return!0;return s}get _isReflowEnabled(){const s=this._optionsService.rawOptions.windowsPty;return s&&s.buildNumber?this._hasScrollback&&s.backend==="conpty"&&s.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(s,r){this._cols!==s&&(s>this._cols?this._reflowLarger(s,r):this._reflowSmaller(s,r))}_reflowLarger(s,r){const h=(0,g.reflowLargerGetLinesToRemove)(this.lines,this._cols,s,this.ybase+this.y,this.getNullCell(d.DEFAULT_ATTR_DATA));if(h.length>0){const f=(0,g.reflowLargerCreateNewLayout)(this.lines,h);(0,g.reflowLargerApplyNewLayout)(this.lines,f.layout),this._reflowLargerAdjustViewport(s,r,f.countRemoved)}}_reflowLargerAdjustViewport(s,r,h){const f=this.getNullCell(d.DEFAULT_ATTR_DATA);let v=h;for(;v-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<r&&this.lines.push(new d.BufferLine(s,f))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-h,0)}_reflowSmaller(s,r){const h=this.getNullCell(d.DEFAULT_ATTR_DATA),f=[];let v=0;for(let b=this.lines.length-1;b>=0;b--){let c=this.lines.get(b);if(!c||!c.isWrapped&&c.getTrimmedLength()<=s)continue;const S=[c];for(;c.isWrapped&&b>0;)c=this.lines.get(--b),S.unshift(c);const E=this.ybase+this.y;if(E>=b&&E<b+S.length)continue;const T=S[S.length-1].getTrimmedLength(),L=(0,g.reflowSmallerGetNewLineLengths)(S,this._cols,s),B=L.length-S.length;let O;O=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+B):Math.max(0,this.lines.length-this.lines.maxLength+B);const $=[];for(let I=0;I<B;I++){const U=this.getBlankLine(d.DEFAULT_ATTR_DATA,!0);$.push(U)}$.length>0&&(f.push({start:b+S.length+v,newLines:$}),v+=$.length),S.push(...$);let W=L.length-1,z=L[W];z===0&&(W--,z=L[W]);let y=S.length-B-1,A=T;for(;y>=0;){const I=Math.min(A,z);if(S[W]===void 0)break;if(S[W].copyCellsFrom(S[y],A-I,z-I,I,!0),z-=I,z===0&&(W--,z=L[W]),A-=I,A===0){y--;const U=Math.max(y,0);A=(0,g.getWrappedLineTrimmedLength)(S,U,this._cols)}}for(let I=0;I<S.length;I++)L[I]<s&&S[I].setCell(L[I],h);let R=B-O;for(;R-- >0;)this.ybase===0?this.y<r-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+v)-r&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+B,this.ybase+r-1)}if(f.length>0){const b=[],c=[];for(let W=0;W<this.lines.length;W++)c.push(this.lines.get(W));const S=this.lines.length;let E=S-1,T=0,L=f[T];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+v);let B=0;for(let W=Math.min(this.lines.maxLength-1,S+v-1);W>=0;W--)if(L&&L.start>E+B){for(let z=L.newLines.length-1;z>=0;z--)this.lines.set(W--,L.newLines[z]);W++,b.push({index:E+1,amount:L.newLines.length}),B+=L.newLines.length,L=f[++T]}else this.lines.set(W,c[E--]);let O=0;for(let W=b.length-1;W>=0;W--)b[W].index+=O,this.lines.onInsertEmitter.fire(b[W]),O+=b[W].amount;const $=Math.max(0,S+v-this.lines.maxLength);$>0&&this.lines.onTrimEmitter.fire($)}}translateBufferLineToString(s,r,h=0,f){const v=this.lines.get(s);return v?v.translateToString(r,h,f):""}getWrappedRangeForLine(s){let r=s,h=s;for(;r>0&&this.lines.get(r).isWrapped;)r--;for(;h+1<this.lines.length&&this.lines.get(h+1).isWrapped;)h++;return{first:r,last:h}}setupTabStops(s){for(s!=null?this.tabs[s]||(s=this.prevStop(s)):(this.tabs={},s=0);s<this._cols;s+=this._optionsService.rawOptions.tabStopWidth)this.tabs[s]=!0}prevStop(s){for(s==null&&(s=this.x);!this.tabs[--s]&&s>0;);return s>=this._cols?this._cols-1:s<0?0:s}nextStop(s){for(s==null&&(s=this.x);!this.tabs[++s]&&s<this._cols;);return s>=this._cols?this._cols-1:s<0?0:s}clearMarkers(s){this._isClearing=!0;for(let r=0;r<this.markers.length;r++)this.markers[r].line===s&&(this.markers[r].dispose(),this.markers.splice(r--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let s=0;s<this.markers.length;s++)this.markers[s].dispose(),this.markers.splice(s--,1);this._isClearing=!1}addMarker(s){const r=new e.Marker(s);return this.markers.push(r),r.register(this.lines.onTrim(h=>{r.line-=h,r.line<0&&r.dispose()})),r.register(this.lines.onInsert(h=>{r.line>=h.index&&(r.line+=h.amount)})),r.register(this.lines.onDelete(h=>{r.line>=h.index&&r.line<h.index+h.amount&&r.dispose(),r.line>h.index&&(r.line-=h.amount)})),r.register(r.onDispose(()=>this._removeMarker(r))),r}_removeMarker(s){this._isClearing||this.markers.splice(this.markers.indexOf(s),1)}}},8437:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BufferLine=t.DEFAULT_ATTR_DATA=void 0;const l=n(3734),u=n(511),a=n(643),d=n(482);t.DEFAULT_ATTR_DATA=Object.freeze(new l.AttributeData);let g=0;class p{constructor(e,i,s=!1){this.isWrapped=s,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*e);const r=i||u.CellData.fromCharData([0,a.NULL_CELL_CHAR,a.NULL_CELL_WIDTH,a.NULL_CELL_CODE]);for(let h=0;h<e;++h)this.setCell(h,r);this.length=e}get(e){const i=this._data[3*e+0],s=2097151&i;return[this._data[3*e+1],2097152&i?this._combined[e]:s?(0,d.stringFromCodePoint)(s):"",i>>22,2097152&i?this._combined[e].charCodeAt(this._combined[e].length-1):s]}set(e,i){this._data[3*e+1]=i[a.CHAR_DATA_ATTR_INDEX],i[a.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[e]=i[1],this._data[3*e+0]=2097152|e|i[a.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*e+0]=i[a.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|i[a.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(e){return this._data[3*e+0]>>22}hasWidth(e){return 12582912&this._data[3*e+0]}getFg(e){return this._data[3*e+1]}getBg(e){return this._data[3*e+2]}hasContent(e){return 4194303&this._data[3*e+0]}getCodePoint(e){const i=this._data[3*e+0];return 2097152&i?this._combined[e].charCodeAt(this._combined[e].length-1):2097151&i}isCombined(e){return 2097152&this._data[3*e+0]}getString(e){const i=this._data[3*e+0];return 2097152&i?this._combined[e]:2097151&i?(0,d.stringFromCodePoint)(2097151&i):""}isProtected(e){return 536870912&this._data[3*e+2]}loadCell(e,i){return g=3*e,i.content=this._data[g+0],i.fg=this._data[g+1],i.bg=this._data[g+2],2097152&i.content&&(i.combinedData=this._combined[e]),268435456&i.bg&&(i.extended=this._extendedAttrs[e]),i}setCell(e,i){2097152&i.content&&(this._combined[e]=i.combinedData),268435456&i.bg&&(this._extendedAttrs[e]=i.extended),this._data[3*e+0]=i.content,this._data[3*e+1]=i.fg,this._data[3*e+2]=i.bg}setCellFromCodePoint(e,i,s,r,h,f){268435456&h&&(this._extendedAttrs[e]=f),this._data[3*e+0]=i|s<<22,this._data[3*e+1]=r,this._data[3*e+2]=h}addCodepointToCell(e,i){let s=this._data[3*e+0];2097152&s?this._combined[e]+=(0,d.stringFromCodePoint)(i):(2097151&s?(this._combined[e]=(0,d.stringFromCodePoint)(2097151&s)+(0,d.stringFromCodePoint)(i),s&=-2097152,s|=2097152):s=i|4194304,this._data[3*e+0]=s)}insertCells(e,i,s,r){if((e%=this.length)&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),i<this.length-e){const h=new u.CellData;for(let f=this.length-e-i-1;f>=0;--f)this.setCell(e+i+f,this.loadCell(e+f,h));for(let f=0;f<i;++f)this.setCell(e+f,s)}else for(let h=e;h<this.length;++h)this.setCell(h,s);this.getWidth(this.length-1)===2&&this.setCellFromCodePoint(this.length-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs)}deleteCells(e,i,s,r){if(e%=this.length,i<this.length-e){const h=new u.CellData;for(let f=0;f<this.length-e-i;++f)this.setCell(e+f,this.loadCell(e+i+f,h));for(let f=this.length-i;f<this.length;++f)this.setCell(f,s)}else for(let h=e;h<this.length;++h)this.setCell(h,s);e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),this.getWidth(e)!==0||this.hasContent(e)||this.setCellFromCodePoint(e,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs)}replaceCells(e,i,s,r,h=!1){if(h)for(e&&this.getWidth(e-1)===2&&!this.isProtected(e-1)&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),i<this.length&&this.getWidth(i-1)===2&&!this.isProtected(i)&&this.setCellFromCodePoint(i,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs);e<i&&e<this.length;)this.isProtected(e)||this.setCell(e,s),e++;else for(e&&this.getWidth(e-1)===2&&this.setCellFromCodePoint(e-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),i<this.length&&this.getWidth(i-1)===2&&this.setCellFromCodePoint(i,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs);e<i&&e<this.length;)this.setCell(e++,s)}resize(e,i){if(e===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const s=3*e;if(e>this.length){if(this._data.buffer.byteLength>=4*s)this._data=new Uint32Array(this._data.buffer,0,s);else{const r=new Uint32Array(s);r.set(this._data),this._data=r}for(let r=this.length;r<e;++r)this.setCell(r,i)}else{this._data=this._data.subarray(0,s);const r=Object.keys(this._combined);for(let f=0;f<r.length;f++){const v=parseInt(r[f],10);v>=e&&delete this._combined[v]}const h=Object.keys(this._extendedAttrs);for(let f=0;f<h.length;f++){const v=parseInt(h[f],10);v>=e&&delete this._extendedAttrs[v]}}return this.length=e,4*s*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const e=new Uint32Array(this._data.length);return e.set(this._data),this._data=e,1}return 0}fill(e,i=!1){if(i)for(let s=0;s<this.length;++s)this.isProtected(s)||this.setCell(s,e);else{this._combined={},this._extendedAttrs={};for(let s=0;s<this.length;++s)this.setCell(s,e)}}copyFrom(e){this.length!==e.length?this._data=new Uint32Array(e._data):this._data.set(e._data),this.length=e.length,this._combined={};for(const i in e._combined)this._combined[i]=e._combined[i];this._extendedAttrs={};for(const i in e._extendedAttrs)this._extendedAttrs[i]=e._extendedAttrs[i];this.isWrapped=e.isWrapped}clone(){const e=new p(0);e._data=new Uint32Array(this._data),e.length=this.length;for(const i in this._combined)e._combined[i]=this._combined[i];for(const i in this._extendedAttrs)e._extendedAttrs[i]=this._extendedAttrs[i];return e.isWrapped=this.isWrapped,e}getTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0])return e+(this._data[3*e+0]>>22);return 0}getNoBgTrimmedLength(){for(let e=this.length-1;e>=0;--e)if(4194303&this._data[3*e+0]||50331648&this._data[3*e+2])return e+(this._data[3*e+0]>>22);return 0}copyCellsFrom(e,i,s,r,h){const f=e._data;if(h)for(let b=r-1;b>=0;b--){for(let c=0;c<3;c++)this._data[3*(s+b)+c]=f[3*(i+b)+c];268435456&f[3*(i+b)+2]&&(this._extendedAttrs[s+b]=e._extendedAttrs[i+b])}else for(let b=0;b<r;b++){for(let c=0;c<3;c++)this._data[3*(s+b)+c]=f[3*(i+b)+c];268435456&f[3*(i+b)+2]&&(this._extendedAttrs[s+b]=e._extendedAttrs[i+b])}const v=Object.keys(e._combined);for(let b=0;b<v.length;b++){const c=parseInt(v[b],10);c>=i&&(this._combined[c-i+s]=e._combined[c])}}translateToString(e=!1,i=0,s=this.length){e&&(s=Math.min(s,this.getTrimmedLength()));let r="";for(;i<s;){const h=this._data[3*i+0],f=2097151&h;r+=2097152&h?this._combined[i]:f?(0,d.stringFromCodePoint)(f):a.WHITESPACE_CELL_CHAR,i+=h>>22||1}return r}}t.BufferLine=p},4841:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getRangeLength=void 0,t.getRangeLength=function(n,l){if(n.start.y>n.end.y)throw new Error(`Buffer range end (${n.end.x}, ${n.end.y}) cannot be before start (${n.start.x}, ${n.start.y})`);return l*(n.end.y-n.start.y)+(n.end.x-n.start.x+1)}},4634:(D,t)=>{function n(l,u,a){if(u===l.length-1)return l[u].getTrimmedLength();const d=!l[u].hasContent(a-1)&&l[u].getWidth(a-1)===1,g=l[u+1].getWidth(0)===2;return d&&g?a-1:a}Object.defineProperty(t,"__esModule",{value:!0}),t.getWrappedLineTrimmedLength=t.reflowSmallerGetNewLineLengths=t.reflowLargerApplyNewLayout=t.reflowLargerCreateNewLayout=t.reflowLargerGetLinesToRemove=void 0,t.reflowLargerGetLinesToRemove=function(l,u,a,d,g){const p=[];for(let _=0;_<l.length-1;_++){let e=_,i=l.get(++e);if(!i.isWrapped)continue;const s=[l.get(_)];for(;e<l.length&&i.isWrapped;)s.push(i),i=l.get(++e);if(d>=_&&d<e){_+=s.length-1;continue}let r=0,h=n(s,r,u),f=1,v=0;for(;f<s.length;){const c=n(s,f,u),S=c-v,E=a-h,T=Math.min(S,E);s[r].copyCellsFrom(s[f],v,h,T,!1),h+=T,h===a&&(r++,h=0),v+=T,v===c&&(f++,v=0),h===0&&r!==0&&s[r-1].getWidth(a-1)===2&&(s[r].copyCellsFrom(s[r-1],a-1,h++,1,!1),s[r-1].setCell(a-1,g))}s[r].replaceCells(h,a,g);let b=0;for(let c=s.length-1;c>0&&(c>r||s[c].getTrimmedLength()===0);c--)b++;b>0&&(p.push(_+s.length-b),p.push(b)),_+=s.length-1}return p},t.reflowLargerCreateNewLayout=function(l,u){const a=[];let d=0,g=u[d],p=0;for(let _=0;_<l.length;_++)if(g===_){const e=u[++d];l.onDeleteEmitter.fire({index:_-p,amount:e}),_+=e-1,p+=e,g=u[++d]}else a.push(_);return{layout:a,countRemoved:p}},t.reflowLargerApplyNewLayout=function(l,u){const a=[];for(let d=0;d<u.length;d++)a.push(l.get(u[d]));for(let d=0;d<a.length;d++)l.set(d,a[d]);l.length=u.length},t.reflowSmallerGetNewLineLengths=function(l,u,a){const d=[],g=l.map((i,s)=>n(l,s,u)).reduce((i,s)=>i+s);let p=0,_=0,e=0;for(;e<g;){if(g-e<a){d.push(g-e);break}p+=a;const i=n(l,_,u);p>i&&(p-=i,_++);const s=l[_].getWidth(p-1)===2;s&&p--;const r=s?a-1:a;d.push(r),e+=r}return d},t.getWrappedLineTrimmedLength=n},5295:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BufferSet=void 0;const l=n(8460),u=n(844),a=n(9092);class d extends u.Disposable{constructor(p,_){super(),this._optionsService=p,this._bufferService=_,this._onBufferActivate=this.register(new l.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new a.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new a.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(p){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(p),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(p,_){this._normal.resize(p,_),this._alt.resize(p,_),this.setupTabStops(p)}setupTabStops(p){this._normal.setupTabStops(p),this._alt.setupTabStops(p)}}t.BufferSet=d},511:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CellData=void 0;const l=n(482),u=n(643),a=n(3734);class d extends a.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new a.ExtendedAttrs,this.combinedData=""}static fromCharData(p){const _=new d;return _.setFromCharData(p),_}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,l.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(p){this.fg=p[u.CHAR_DATA_ATTR_INDEX],this.bg=0;let _=!1;if(p[u.CHAR_DATA_CHAR_INDEX].length>2)_=!0;else if(p[u.CHAR_DATA_CHAR_INDEX].length===2){const e=p[u.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=e&&e<=56319){const i=p[u.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=i&&i<=57343?this.content=1024*(e-55296)+i-56320+65536|p[u.CHAR_DATA_WIDTH_INDEX]<<22:_=!0}else _=!0}else this.content=p[u.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|p[u.CHAR_DATA_WIDTH_INDEX]<<22;_&&(this.combinedData=p[u.CHAR_DATA_CHAR_INDEX],this.content=2097152|p[u.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}t.CellData=d},643:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WHITESPACE_CELL_CODE=t.WHITESPACE_CELL_WIDTH=t.WHITESPACE_CELL_CHAR=t.NULL_CELL_CODE=t.NULL_CELL_WIDTH=t.NULL_CELL_CHAR=t.CHAR_DATA_CODE_INDEX=t.CHAR_DATA_WIDTH_INDEX=t.CHAR_DATA_CHAR_INDEX=t.CHAR_DATA_ATTR_INDEX=t.DEFAULT_EXT=t.DEFAULT_ATTR=t.DEFAULT_COLOR=void 0,t.DEFAULT_COLOR=0,t.DEFAULT_ATTR=256|t.DEFAULT_COLOR<<9,t.DEFAULT_EXT=0,t.CHAR_DATA_ATTR_INDEX=0,t.CHAR_DATA_CHAR_INDEX=1,t.CHAR_DATA_WIDTH_INDEX=2,t.CHAR_DATA_CODE_INDEX=3,t.NULL_CELL_CHAR="",t.NULL_CELL_WIDTH=1,t.NULL_CELL_CODE=0,t.WHITESPACE_CELL_CHAR=" ",t.WHITESPACE_CELL_WIDTH=1,t.WHITESPACE_CELL_CODE=32},4863:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Marker=void 0;const l=n(8460),u=n(844);class a{get id(){return this._id}constructor(g){this.line=g,this.isDisposed=!1,this._disposables=[],this._id=a._nextId++,this._onDispose=this.register(new l.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,u.disposeArray)(this._disposables),this._disposables.length=0)}register(g){return this._disposables.push(g),g}}t.Marker=a,a._nextId=1},7116:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_CHARSET=t.CHARSETS=void 0,t.CHARSETS={},t.DEFAULT_CHARSET=t.CHARSETS.B,t.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},t.CHARSETS.A={"#":"£"},t.CHARSETS.B=void 0,t.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},t.CHARSETS.C=t.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},t.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},t.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},t.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},t.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},t.CHARSETS.E=t.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},t.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},t.CHARSETS.H=t.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},t.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(D,t)=>{var n,l,u;Object.defineProperty(t,"__esModule",{value:!0}),t.C1_ESCAPED=t.C1=t.C0=void 0,function(a){a.NUL="\0",a.SOH="",a.STX="",a.ETX="",a.EOT="",a.ENQ="",a.ACK="",a.BEL="\x07",a.BS="\b",a.HT="	",a.LF=`
`,a.VT="\v",a.FF="\f",a.CR="\r",a.SO="",a.SI="",a.DLE="",a.DC1="",a.DC2="",a.DC3="",a.DC4="",a.NAK="",a.SYN="",a.ETB="",a.CAN="",a.EM="",a.SUB="",a.ESC="\x1B",a.FS="",a.GS="",a.RS="",a.US="",a.SP=" ",a.DEL=""}(n||(t.C0=n={})),function(a){a.PAD="",a.HOP="",a.BPH="",a.NBH="",a.IND="",a.NEL="",a.SSA="",a.ESA="",a.HTS="",a.HTJ="",a.VTS="",a.PLD="",a.PLU="",a.RI="",a.SS2="",a.SS3="",a.DCS="",a.PU1="",a.PU2="",a.STS="",a.CCH="",a.MW="",a.SPA="",a.EPA="",a.SOS="",a.SGCI="",a.SCI="",a.CSI="",a.ST="",a.OSC="",a.PM="",a.APC=""}(l||(t.C1=l={})),function(a){a.ST=`${n.ESC}\\`}(u||(t.C1_ESCAPED=u={}))},7399:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.evaluateKeyboardEvent=void 0;const l=n(2584),u={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};t.evaluateKeyboardEvent=function(a,d,g,p){const _={type:0,cancel:!1,key:void 0},e=(a.shiftKey?1:0)|(a.altKey?2:0)|(a.ctrlKey?4:0)|(a.metaKey?8:0);switch(a.keyCode){case 0:a.key==="UIKeyInputUpArrow"?_.key=d?l.C0.ESC+"OA":l.C0.ESC+"[A":a.key==="UIKeyInputLeftArrow"?_.key=d?l.C0.ESC+"OD":l.C0.ESC+"[D":a.key==="UIKeyInputRightArrow"?_.key=d?l.C0.ESC+"OC":l.C0.ESC+"[C":a.key==="UIKeyInputDownArrow"&&(_.key=d?l.C0.ESC+"OB":l.C0.ESC+"[B");break;case 8:if(a.altKey){_.key=l.C0.ESC+l.C0.DEL;break}_.key=l.C0.DEL;break;case 9:if(a.shiftKey){_.key=l.C0.ESC+"[Z";break}_.key=l.C0.HT,_.cancel=!0;break;case 13:_.key=a.altKey?l.C0.ESC+l.C0.CR:l.C0.CR,_.cancel=!0;break;case 27:_.key=l.C0.ESC,a.altKey&&(_.key=l.C0.ESC+l.C0.ESC),_.cancel=!0;break;case 37:if(a.metaKey)break;e?(_.key=l.C0.ESC+"[1;"+(e+1)+"D",_.key===l.C0.ESC+"[1;3D"&&(_.key=l.C0.ESC+(g?"b":"[1;5D"))):_.key=d?l.C0.ESC+"OD":l.C0.ESC+"[D";break;case 39:if(a.metaKey)break;e?(_.key=l.C0.ESC+"[1;"+(e+1)+"C",_.key===l.C0.ESC+"[1;3C"&&(_.key=l.C0.ESC+(g?"f":"[1;5C"))):_.key=d?l.C0.ESC+"OC":l.C0.ESC+"[C";break;case 38:if(a.metaKey)break;e?(_.key=l.C0.ESC+"[1;"+(e+1)+"A",g||_.key!==l.C0.ESC+"[1;3A"||(_.key=l.C0.ESC+"[1;5A")):_.key=d?l.C0.ESC+"OA":l.C0.ESC+"[A";break;case 40:if(a.metaKey)break;e?(_.key=l.C0.ESC+"[1;"+(e+1)+"B",g||_.key!==l.C0.ESC+"[1;3B"||(_.key=l.C0.ESC+"[1;5B")):_.key=d?l.C0.ESC+"OB":l.C0.ESC+"[B";break;case 45:a.shiftKey||a.ctrlKey||(_.key=l.C0.ESC+"[2~");break;case 46:_.key=e?l.C0.ESC+"[3;"+(e+1)+"~":l.C0.ESC+"[3~";break;case 36:_.key=e?l.C0.ESC+"[1;"+(e+1)+"H":d?l.C0.ESC+"OH":l.C0.ESC+"[H";break;case 35:_.key=e?l.C0.ESC+"[1;"+(e+1)+"F":d?l.C0.ESC+"OF":l.C0.ESC+"[F";break;case 33:a.shiftKey?_.type=2:a.ctrlKey?_.key=l.C0.ESC+"[5;"+(e+1)+"~":_.key=l.C0.ESC+"[5~";break;case 34:a.shiftKey?_.type=3:a.ctrlKey?_.key=l.C0.ESC+"[6;"+(e+1)+"~":_.key=l.C0.ESC+"[6~";break;case 112:_.key=e?l.C0.ESC+"[1;"+(e+1)+"P":l.C0.ESC+"OP";break;case 113:_.key=e?l.C0.ESC+"[1;"+(e+1)+"Q":l.C0.ESC+"OQ";break;case 114:_.key=e?l.C0.ESC+"[1;"+(e+1)+"R":l.C0.ESC+"OR";break;case 115:_.key=e?l.C0.ESC+"[1;"+(e+1)+"S":l.C0.ESC+"OS";break;case 116:_.key=e?l.C0.ESC+"[15;"+(e+1)+"~":l.C0.ESC+"[15~";break;case 117:_.key=e?l.C0.ESC+"[17;"+(e+1)+"~":l.C0.ESC+"[17~";break;case 118:_.key=e?l.C0.ESC+"[18;"+(e+1)+"~":l.C0.ESC+"[18~";break;case 119:_.key=e?l.C0.ESC+"[19;"+(e+1)+"~":l.C0.ESC+"[19~";break;case 120:_.key=e?l.C0.ESC+"[20;"+(e+1)+"~":l.C0.ESC+"[20~";break;case 121:_.key=e?l.C0.ESC+"[21;"+(e+1)+"~":l.C0.ESC+"[21~";break;case 122:_.key=e?l.C0.ESC+"[23;"+(e+1)+"~":l.C0.ESC+"[23~";break;case 123:_.key=e?l.C0.ESC+"[24;"+(e+1)+"~":l.C0.ESC+"[24~";break;default:if(!a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)if(g&&!p||!a.altKey||a.metaKey)!g||a.altKey||a.ctrlKey||a.shiftKey||!a.metaKey?a.key&&!a.ctrlKey&&!a.altKey&&!a.metaKey&&a.keyCode>=48&&a.key.length===1?_.key=a.key:a.key&&a.ctrlKey&&(a.key==="_"&&(_.key=l.C0.US),a.key==="@"&&(_.key=l.C0.NUL)):a.keyCode===65&&(_.type=1);else{const i=u[a.keyCode],s=i==null?void 0:i[a.shiftKey?1:0];if(s)_.key=l.C0.ESC+s;else if(a.keyCode>=65&&a.keyCode<=90){const r=a.ctrlKey?a.keyCode-64:a.keyCode+32;let h=String.fromCharCode(r);a.shiftKey&&(h=h.toUpperCase()),_.key=l.C0.ESC+h}else if(a.keyCode===32)_.key=l.C0.ESC+(a.ctrlKey?l.C0.NUL:" ");else if(a.key==="Dead"&&a.code.startsWith("Key")){let r=a.code.slice(3,4);a.shiftKey||(r=r.toLowerCase()),_.key=l.C0.ESC+r,_.cancel=!0}}else a.keyCode>=65&&a.keyCode<=90?_.key=String.fromCharCode(a.keyCode-64):a.keyCode===32?_.key=l.C0.NUL:a.keyCode>=51&&a.keyCode<=55?_.key=String.fromCharCode(a.keyCode-51+27):a.keyCode===56?_.key=l.C0.DEL:a.keyCode===219?_.key=l.C0.ESC:a.keyCode===220?_.key=l.C0.FS:a.keyCode===221&&(_.key=l.C0.GS)}return _}},482:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Utf8ToUtf32=t.StringToUtf32=t.utf32ToString=t.stringFromCodePoint=void 0,t.stringFromCodePoint=function(n){return n>65535?(n-=65536,String.fromCharCode(55296+(n>>10))+String.fromCharCode(n%1024+56320)):String.fromCharCode(n)},t.utf32ToString=function(n,l=0,u=n.length){let a="";for(let d=l;d<u;++d){let g=n[d];g>65535?(g-=65536,a+=String.fromCharCode(55296+(g>>10))+String.fromCharCode(g%1024+56320)):a+=String.fromCharCode(g)}return a},t.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(n,l){const u=n.length;if(!u)return 0;let a=0,d=0;if(this._interim){const g=n.charCodeAt(d++);56320<=g&&g<=57343?l[a++]=1024*(this._interim-55296)+g-56320+65536:(l[a++]=this._interim,l[a++]=g),this._interim=0}for(let g=d;g<u;++g){const p=n.charCodeAt(g);if(55296<=p&&p<=56319){if(++g>=u)return this._interim=p,a;const _=n.charCodeAt(g);56320<=_&&_<=57343?l[a++]=1024*(p-55296)+_-56320+65536:(l[a++]=p,l[a++]=_)}else p!==65279&&(l[a++]=p)}return a}},t.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(n,l){const u=n.length;if(!u)return 0;let a,d,g,p,_=0,e=0,i=0;if(this.interim[0]){let h=!1,f=this.interim[0];f&=(224&f)==192?31:(240&f)==224?15:7;let v,b=0;for(;(v=63&this.interim[++b])&&b<4;)f<<=6,f|=v;const c=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,S=c-b;for(;i<S;){if(i>=u)return 0;if(v=n[i++],(192&v)!=128){i--,h=!0;break}this.interim[b++]=v,f<<=6,f|=63&v}h||(c===2?f<128?i--:l[_++]=f:c===3?f<2048||f>=55296&&f<=57343||f===65279||(l[_++]=f):f<65536||f>1114111||(l[_++]=f)),this.interim.fill(0)}const s=u-4;let r=i;for(;r<u;){for(;!(!(r<s)||128&(a=n[r])||128&(d=n[r+1])||128&(g=n[r+2])||128&(p=n[r+3]));)l[_++]=a,l[_++]=d,l[_++]=g,l[_++]=p,r+=4;if(a=n[r++],a<128)l[_++]=a;else if((224&a)==192){if(r>=u)return this.interim[0]=a,_;if(d=n[r++],(192&d)!=128){r--;continue}if(e=(31&a)<<6|63&d,e<128){r--;continue}l[_++]=e}else if((240&a)==224){if(r>=u)return this.interim[0]=a,_;if(d=n[r++],(192&d)!=128){r--;continue}if(r>=u)return this.interim[0]=a,this.interim[1]=d,_;if(g=n[r++],(192&g)!=128){r--;continue}if(e=(15&a)<<12|(63&d)<<6|63&g,e<2048||e>=55296&&e<=57343||e===65279)continue;l[_++]=e}else if((248&a)==240){if(r>=u)return this.interim[0]=a,_;if(d=n[r++],(192&d)!=128){r--;continue}if(r>=u)return this.interim[0]=a,this.interim[1]=d,_;if(g=n[r++],(192&g)!=128){r--;continue}if(r>=u)return this.interim[0]=a,this.interim[1]=d,this.interim[2]=g,_;if(p=n[r++],(192&p)!=128){r--;continue}if(e=(7&a)<<18|(63&d)<<12|(63&g)<<6|63&p,e<65536||e>1114111)continue;l[_++]=e}}return _}}},225:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UnicodeV6=void 0;const n=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],l=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let u;t.UnicodeV6=class{constructor(){if(this.version="6",!u){u=new Uint8Array(65536),u.fill(1),u[0]=0,u.fill(0,1,32),u.fill(0,127,160),u.fill(2,4352,4448),u[9001]=2,u[9002]=2,u.fill(2,11904,42192),u[12351]=1,u.fill(2,44032,55204),u.fill(2,63744,64256),u.fill(2,65040,65050),u.fill(2,65072,65136),u.fill(2,65280,65377),u.fill(2,65504,65511);for(let a=0;a<n.length;++a)u.fill(0,n[a][0],n[a][1]+1)}}wcwidth(a){return a<32?0:a<127?1:a<65536?u[a]:function(d,g){let p,_=0,e=g.length-1;if(d<g[0][0]||d>g[e][1])return!1;for(;e>=_;)if(p=_+e>>1,d>g[p][1])_=p+1;else{if(!(d<g[p][0]))return!0;e=p-1}return!1}(a,l)?0:a>=131072&&a<=196605||a>=196608&&a<=262141?2:1}}},5981:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WriteBuffer=void 0;const l=n(8460),u=n(844);class a extends u.Disposable{constructor(g){super(),this._action=g,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new l.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(g,p){if(p!==void 0&&this._syncCalls>p)return void(this._syncCalls=0);if(this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let _;for(this._isSyncWriting=!0;_=this._writeBuffer.shift();){this._action(_);const e=this._callbacks.shift();e&&e()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(g,p){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(p),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=g.length,this._writeBuffer.push(g),this._callbacks.push(p)}_innerWrite(g=0,p=!0){const _=g||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const e=this._writeBuffer[this._bufferOffset],i=this._action(e,p);if(i){const r=h=>Date.now()-_>=12?setTimeout(()=>this._innerWrite(0,h)):this._innerWrite(_,h);return void i.catch(h=>(queueMicrotask(()=>{throw h}),Promise.resolve(!1))).then(r)}const s=this._callbacks[this._bufferOffset];if(s&&s(),this._bufferOffset++,this._pendingData-=e.length,Date.now()-_>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}t.WriteBuffer=a},5941:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.toRgbString=t.parseColor=void 0;const n=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,l=/^[\da-f]+$/;function u(a,d){const g=a.toString(16),p=g.length<2?"0"+g:g;switch(d){case 4:return g[0];case 8:return p;case 12:return(p+p).slice(0,3);default:return p+p}}t.parseColor=function(a){if(!a)return;let d=a.toLowerCase();if(d.indexOf("rgb:")===0){d=d.slice(4);const g=n.exec(d);if(g){const p=g[1]?15:g[4]?255:g[7]?4095:65535;return[Math.round(parseInt(g[1]||g[4]||g[7]||g[10],16)/p*255),Math.round(parseInt(g[2]||g[5]||g[8]||g[11],16)/p*255),Math.round(parseInt(g[3]||g[6]||g[9]||g[12],16)/p*255)]}}else if(d.indexOf("#")===0&&(d=d.slice(1),l.exec(d)&&[3,6,9,12].includes(d.length))){const g=d.length/3,p=[0,0,0];for(let _=0;_<3;++_){const e=parseInt(d.slice(g*_,g*_+g),16);p[_]=g===1?e<<4:g===2?e:g===3?e>>4:e>>8}return p}},t.toRgbString=function(a,d=16){const[g,p,_]=a;return`rgb:${u(g,d)}/${u(p,d)}/${u(_,d)}`}},5770:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PAYLOAD_LIMIT=void 0,t.PAYLOAD_LIMIT=1e7},6351:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DcsHandler=t.DcsParser=void 0;const l=n(482),u=n(8742),a=n(5770),d=[];t.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=d,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=d}registerHandler(p,_){this._handlers[p]===void 0&&(this._handlers[p]=[]);const e=this._handlers[p];return e.push(_),{dispose:()=>{const i=e.indexOf(_);i!==-1&&e.splice(i,1)}}}clearHandler(p){this._handlers[p]&&delete this._handlers[p]}setHandlerFallback(p){this._handlerFb=p}reset(){if(this._active.length)for(let p=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;p>=0;--p)this._active[p].unhook(!1);this._stack.paused=!1,this._active=d,this._ident=0}hook(p,_){if(this.reset(),this._ident=p,this._active=this._handlers[p]||d,this._active.length)for(let e=this._active.length-1;e>=0;e--)this._active[e].hook(_);else this._handlerFb(this._ident,"HOOK",_)}put(p,_,e){if(this._active.length)for(let i=this._active.length-1;i>=0;i--)this._active[i].put(p,_,e);else this._handlerFb(this._ident,"PUT",(0,l.utf32ToString)(p,_,e))}unhook(p,_=!0){if(this._active.length){let e=!1,i=this._active.length-1,s=!1;if(this._stack.paused&&(i=this._stack.loopPosition-1,e=_,s=this._stack.fallThrough,this._stack.paused=!1),!s&&e===!1){for(;i>=0&&(e=this._active[i].unhook(p),e!==!0);i--)if(e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=i,this._stack.fallThrough=!1,e;i--}for(;i>=0;i--)if(e=this._active[i].unhook(!1),e instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=i,this._stack.fallThrough=!0,e}else this._handlerFb(this._ident,"UNHOOK",p);this._active=d,this._ident=0}};const g=new u.Params;g.addParam(0),t.DcsHandler=class{constructor(p){this._handler=p,this._data="",this._params=g,this._hitLimit=!1}hook(p){this._params=p.length>1||p.params[0]?p.clone():g,this._data="",this._hitLimit=!1}put(p,_,e){this._hitLimit||(this._data+=(0,l.utf32ToString)(p,_,e),this._data.length>a.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(p){let _=!1;if(this._hitLimit)_=!1;else if(p&&(_=this._handler(this._data,this._params),_ instanceof Promise))return _.then(e=>(this._params=g,this._data="",this._hitLimit=!1,e));return this._params=g,this._data="",this._hitLimit=!1,_}}},2015:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EscapeSequenceParser=t.VT500_TRANSITION_TABLE=t.TransitionTable=void 0;const l=n(844),u=n(8742),a=n(6242),d=n(6351);class g{constructor(i){this.table=new Uint8Array(i)}setDefault(i,s){this.table.fill(i<<4|s)}add(i,s,r,h){this.table[s<<8|i]=r<<4|h}addMany(i,s,r,h){for(let f=0;f<i.length;f++)this.table[s<<8|i[f]]=r<<4|h}}t.TransitionTable=g;const p=160;t.VT500_TRANSITION_TABLE=function(){const e=new g(4095),i=Array.apply(null,Array(256)).map((b,c)=>c),s=(b,c)=>i.slice(b,c),r=s(32,127),h=s(0,24);h.push(25),h.push.apply(h,s(28,32));const f=s(0,14);let v;for(v in e.setDefault(1,0),e.addMany(r,0,2,0),f)e.addMany([24,26,153,154],v,3,0),e.addMany(s(128,144),v,3,0),e.addMany(s(144,152),v,3,0),e.add(156,v,0,0),e.add(27,v,11,1),e.add(157,v,4,8),e.addMany([152,158,159],v,0,7),e.add(155,v,11,3),e.add(144,v,11,9);return e.addMany(h,0,3,0),e.addMany(h,1,3,1),e.add(127,1,0,1),e.addMany(h,8,0,8),e.addMany(h,3,3,3),e.add(127,3,0,3),e.addMany(h,4,3,4),e.add(127,4,0,4),e.addMany(h,6,3,6),e.addMany(h,5,3,5),e.add(127,5,0,5),e.addMany(h,2,3,2),e.add(127,2,0,2),e.add(93,1,4,8),e.addMany(r,8,5,8),e.add(127,8,5,8),e.addMany([156,27,24,26,7],8,6,0),e.addMany(s(28,32),8,0,8),e.addMany([88,94,95],1,0,7),e.addMany(r,7,0,7),e.addMany(h,7,0,7),e.add(156,7,0,0),e.add(127,7,0,7),e.add(91,1,11,3),e.addMany(s(64,127),3,7,0),e.addMany(s(48,60),3,8,4),e.addMany([60,61,62,63],3,9,4),e.addMany(s(48,60),4,8,4),e.addMany(s(64,127),4,7,0),e.addMany([60,61,62,63],4,0,6),e.addMany(s(32,64),6,0,6),e.add(127,6,0,6),e.addMany(s(64,127),6,0,0),e.addMany(s(32,48),3,9,5),e.addMany(s(32,48),5,9,5),e.addMany(s(48,64),5,0,6),e.addMany(s(64,127),5,7,0),e.addMany(s(32,48),4,9,5),e.addMany(s(32,48),1,9,2),e.addMany(s(32,48),2,9,2),e.addMany(s(48,127),2,10,0),e.addMany(s(48,80),1,10,0),e.addMany(s(81,88),1,10,0),e.addMany([89,90,92],1,10,0),e.addMany(s(96,127),1,10,0),e.add(80,1,11,9),e.addMany(h,9,0,9),e.add(127,9,0,9),e.addMany(s(28,32),9,0,9),e.addMany(s(32,48),9,9,12),e.addMany(s(48,60),9,8,10),e.addMany([60,61,62,63],9,9,10),e.addMany(h,11,0,11),e.addMany(s(32,128),11,0,11),e.addMany(s(28,32),11,0,11),e.addMany(h,10,0,10),e.add(127,10,0,10),e.addMany(s(28,32),10,0,10),e.addMany(s(48,60),10,8,10),e.addMany([60,61,62,63],10,0,11),e.addMany(s(32,48),10,9,12),e.addMany(h,12,0,12),e.add(127,12,0,12),e.addMany(s(28,32),12,0,12),e.addMany(s(32,48),12,9,12),e.addMany(s(48,64),12,0,11),e.addMany(s(64,127),12,12,13),e.addMany(s(64,127),10,12,13),e.addMany(s(64,127),9,12,13),e.addMany(h,13,13,13),e.addMany(r,13,13,13),e.add(127,13,0,13),e.addMany([27,156,24,26],13,14,0),e.add(p,0,2,0),e.add(p,8,5,8),e.add(p,6,0,6),e.add(p,11,0,11),e.add(p,13,13,13),e}();class _ extends l.Disposable{constructor(i=t.VT500_TRANSITION_TABLE){super(),this._transitions=i,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new u.Params,this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._printHandlerFb=(s,r,h)=>{},this._executeHandlerFb=s=>{},this._csiHandlerFb=(s,r)=>{},this._escHandlerFb=s=>{},this._errorHandlerFb=s=>s,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,l.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new a.OscParser),this._dcsParser=this.register(new d.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(i,s=[64,126]){let r=0;if(i.prefix){if(i.prefix.length>1)throw new Error("only one byte as prefix supported");if(r=i.prefix.charCodeAt(0),r&&60>r||r>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(i.intermediates){if(i.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let f=0;f<i.intermediates.length;++f){const v=i.intermediates.charCodeAt(f);if(32>v||v>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");r<<=8,r|=v}}if(i.final.length!==1)throw new Error("final must be a single byte");const h=i.final.charCodeAt(0);if(s[0]>h||h>s[1])throw new Error(`final must be in range ${s[0]} .. ${s[1]}`);return r<<=8,r|=h,r}identToString(i){const s=[];for(;i;)s.push(String.fromCharCode(255&i)),i>>=8;return s.reverse().join("")}setPrintHandler(i){this._printHandler=i}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(i,s){const r=this._identifier(i,[48,126]);this._escHandlers[r]===void 0&&(this._escHandlers[r]=[]);const h=this._escHandlers[r];return h.push(s),{dispose:()=>{const f=h.indexOf(s);f!==-1&&h.splice(f,1)}}}clearEscHandler(i){this._escHandlers[this._identifier(i,[48,126])]&&delete this._escHandlers[this._identifier(i,[48,126])]}setEscHandlerFallback(i){this._escHandlerFb=i}setExecuteHandler(i,s){this._executeHandlers[i.charCodeAt(0)]=s}clearExecuteHandler(i){this._executeHandlers[i.charCodeAt(0)]&&delete this._executeHandlers[i.charCodeAt(0)]}setExecuteHandlerFallback(i){this._executeHandlerFb=i}registerCsiHandler(i,s){const r=this._identifier(i);this._csiHandlers[r]===void 0&&(this._csiHandlers[r]=[]);const h=this._csiHandlers[r];return h.push(s),{dispose:()=>{const f=h.indexOf(s);f!==-1&&h.splice(f,1)}}}clearCsiHandler(i){this._csiHandlers[this._identifier(i)]&&delete this._csiHandlers[this._identifier(i)]}setCsiHandlerFallback(i){this._csiHandlerFb=i}registerDcsHandler(i,s){return this._dcsParser.registerHandler(this._identifier(i),s)}clearDcsHandler(i){this._dcsParser.clearHandler(this._identifier(i))}setDcsHandlerFallback(i){this._dcsParser.setHandlerFallback(i)}registerOscHandler(i,s){return this._oscParser.registerHandler(i,s)}clearOscHandler(i){this._oscParser.clearHandler(i)}setOscHandlerFallback(i){this._oscParser.setHandlerFallback(i)}setErrorHandler(i){this._errorHandler=i}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(i,s,r,h,f){this._parseStack.state=i,this._parseStack.handlers=s,this._parseStack.handlerPos=r,this._parseStack.transition=h,this._parseStack.chunkPos=f}parse(i,s,r){let h,f=0,v=0,b=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,b=this._parseStack.chunkPos+1;else{if(r===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const c=this._parseStack.handlers;let S=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(r===!1&&S>-1){for(;S>=0&&(h=c[S](this._params),h!==!0);S--)if(h instanceof Promise)return this._parseStack.handlerPos=S,h}this._parseStack.handlers=[];break;case 4:if(r===!1&&S>-1){for(;S>=0&&(h=c[S](),h!==!0);S--)if(h instanceof Promise)return this._parseStack.handlerPos=S,h}this._parseStack.handlers=[];break;case 6:if(f=i[this._parseStack.chunkPos],h=this._dcsParser.unhook(f!==24&&f!==26,r),h)return h;f===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(f=i[this._parseStack.chunkPos],h=this._oscParser.end(f!==24&&f!==26,r),h)return h;f===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,b=this._parseStack.chunkPos+1,this.precedingCodepoint=0,this.currentState=15&this._parseStack.transition}for(let c=b;c<s;++c){switch(f=i[c],v=this._transitions.table[this.currentState<<8|(f<160?f:p)],v>>4){case 2:for(let B=c+1;;++B){if(B>=s||(f=i[B])<32||f>126&&f<p){this._printHandler(i,c,B),c=B-1;break}if(++B>=s||(f=i[B])<32||f>126&&f<p){this._printHandler(i,c,B),c=B-1;break}if(++B>=s||(f=i[B])<32||f>126&&f<p){this._printHandler(i,c,B),c=B-1;break}if(++B>=s||(f=i[B])<32||f>126&&f<p){this._printHandler(i,c,B),c=B-1;break}}break;case 3:this._executeHandlers[f]?this._executeHandlers[f]():this._executeHandlerFb(f),this.precedingCodepoint=0;break;case 0:break;case 1:if(this._errorHandler({position:c,code:f,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const S=this._csiHandlers[this._collect<<8|f];let E=S?S.length-1:-1;for(;E>=0&&(h=S[E](this._params),h!==!0);E--)if(h instanceof Promise)return this._preserveStack(3,S,E,v,c),h;E<0&&this._csiHandlerFb(this._collect<<8|f,this._params),this.precedingCodepoint=0;break;case 8:do switch(f){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(f-48)}while(++c<s&&(f=i[c])>47&&f<60);c--;break;case 9:this._collect<<=8,this._collect|=f;break;case 10:const T=this._escHandlers[this._collect<<8|f];let L=T?T.length-1:-1;for(;L>=0&&(h=T[L](),h!==!0);L--)if(h instanceof Promise)return this._preserveStack(4,T,L,v,c),h;L<0&&this._escHandlerFb(this._collect<<8|f),this.precedingCodepoint=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|f,this._params);break;case 13:for(let B=c+1;;++B)if(B>=s||(f=i[B])===24||f===26||f===27||f>127&&f<p){this._dcsParser.put(i,c,B),c=B-1;break}break;case 14:if(h=this._dcsParser.unhook(f!==24&&f!==26),h)return this._preserveStack(6,[],0,v,c),h;f===27&&(v|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0;break;case 4:this._oscParser.start();break;case 5:for(let B=c+1;;B++)if(B>=s||(f=i[B])<32||f>127&&f<p){this._oscParser.put(i,c,B),c=B-1;break}break;case 6:if(h=this._oscParser.end(f!==24&&f!==26),h)return this._preserveStack(5,[],0,v,c),h;f===27&&(v|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0}this.currentState=15&v}}}t.EscapeSequenceParser=_},6242:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OscHandler=t.OscParser=void 0;const l=n(5770),u=n(482),a=[];t.OscParser=class{constructor(){this._state=0,this._active=a,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(d,g){this._handlers[d]===void 0&&(this._handlers[d]=[]);const p=this._handlers[d];return p.push(g),{dispose:()=>{const _=p.indexOf(g);_!==-1&&p.splice(_,1)}}}clearHandler(d){this._handlers[d]&&delete this._handlers[d]}setHandlerFallback(d){this._handlerFb=d}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=a}reset(){if(this._state===2)for(let d=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;d>=0;--d)this._active[d].end(!1);this._stack.paused=!1,this._active=a,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||a,this._active.length)for(let d=this._active.length-1;d>=0;d--)this._active[d].start();else this._handlerFb(this._id,"START")}_put(d,g,p){if(this._active.length)for(let _=this._active.length-1;_>=0;_--)this._active[_].put(d,g,p);else this._handlerFb(this._id,"PUT",(0,u.utf32ToString)(d,g,p))}start(){this.reset(),this._state=1}put(d,g,p){if(this._state!==3){if(this._state===1)for(;g<p;){const _=d[g++];if(_===59){this._state=2,this._start();break}if(_<48||57<_)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+_-48}this._state===2&&p-g>0&&this._put(d,g,p)}}end(d,g=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let p=!1,_=this._active.length-1,e=!1;if(this._stack.paused&&(_=this._stack.loopPosition-1,p=g,e=this._stack.fallThrough,this._stack.paused=!1),!e&&p===!1){for(;_>=0&&(p=this._active[_].end(d),p!==!0);_--)if(p instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=_,this._stack.fallThrough=!1,p;_--}for(;_>=0;_--)if(p=this._active[_].end(!1),p instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=_,this._stack.fallThrough=!0,p}else this._handlerFb(this._id,"END",d);this._active=a,this._id=-1,this._state=0}}},t.OscHandler=class{constructor(d){this._handler=d,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(d,g,p){this._hitLimit||(this._data+=(0,u.utf32ToString)(d,g,p),this._data.length>l.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(d){let g=!1;if(this._hitLimit)g=!1;else if(d&&(g=this._handler(this._data),g instanceof Promise))return g.then(p=>(this._data="",this._hitLimit=!1,p));return this._data="",this._hitLimit=!1,g}}},8742:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Params=void 0;const n=2147483647;class l{static fromArray(a){const d=new l;if(!a.length)return d;for(let g=Array.isArray(a[0])?1:0;g<a.length;++g){const p=a[g];if(Array.isArray(p))for(let _=0;_<p.length;++_)d.addSubParam(p[_]);else d.addParam(p)}return d}constructor(a=32,d=32){if(this.maxLength=a,this.maxSubParamsLength=d,d>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(a),this.length=0,this._subParams=new Int32Array(d),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(a),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const a=new l(this.maxLength,this.maxSubParamsLength);return a.params.set(this.params),a.length=this.length,a._subParams.set(this._subParams),a._subParamsLength=this._subParamsLength,a._subParamsIdx.set(this._subParamsIdx),a._rejectDigits=this._rejectDigits,a._rejectSubDigits=this._rejectSubDigits,a._digitIsSub=this._digitIsSub,a}toArray(){const a=[];for(let d=0;d<this.length;++d){a.push(this.params[d]);const g=this._subParamsIdx[d]>>8,p=255&this._subParamsIdx[d];p-g>0&&a.push(Array.prototype.slice.call(this._subParams,g,p))}return a}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(a){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(a<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=a>n?n:a}}addSubParam(a){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(a<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=a>n?n:a,this._subParamsIdx[this.length-1]++}}hasSubParams(a){return(255&this._subParamsIdx[a])-(this._subParamsIdx[a]>>8)>0}getSubParams(a){const d=this._subParamsIdx[a]>>8,g=255&this._subParamsIdx[a];return g-d>0?this._subParams.subarray(d,g):null}getSubParamsAll(){const a={};for(let d=0;d<this.length;++d){const g=this._subParamsIdx[d]>>8,p=255&this._subParamsIdx[d];p-g>0&&(a[d]=this._subParams.slice(g,p))}return a}addDigit(a){let d;if(this._rejectDigits||!(d=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const g=this._digitIsSub?this._subParams:this.params,p=g[d-1];g[d-1]=~p?Math.min(10*p+a,n):a}}t.Params=l},5741:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AddonManager=void 0,t.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let n=this._addons.length-1;n>=0;n--)this._addons[n].instance.dispose()}loadAddon(n,l){const u={instance:l,dispose:l.dispose,isDisposed:!1};this._addons.push(u),l.dispose=()=>this._wrappedAddonDispose(u),l.activate(n)}_wrappedAddonDispose(n){if(n.isDisposed)return;let l=-1;for(let u=0;u<this._addons.length;u++)if(this._addons[u]===n){l=u;break}if(l===-1)throw new Error("Could not dispose an addon that has not been loaded");n.isDisposed=!0,n.dispose.apply(n.instance),this._addons.splice(l,1)}}},8771:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BufferApiView=void 0;const l=n(3785),u=n(511);t.BufferApiView=class{constructor(a,d){this._buffer=a,this.type=d}init(a){return this._buffer=a,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(a){const d=this._buffer.lines.get(a);if(d)return new l.BufferLineApiView(d)}getNullCell(){return new u.CellData}}},3785:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BufferLineApiView=void 0;const l=n(511);t.BufferLineApiView=class{constructor(u){this._line=u}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(u,a){if(!(u<0||u>=this._line.length))return a?(this._line.loadCell(u,a),a):this._line.loadCell(u,new l.CellData)}translateToString(u,a,d){return this._line.translateToString(u,a,d)}}},8285:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BufferNamespaceApi=void 0;const l=n(8771),u=n(8460),a=n(844);class d extends a.Disposable{constructor(p){super(),this._core=p,this._onBufferChange=this.register(new u.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new l.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new l.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}t.BufferNamespaceApi=d},7975:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ParserApi=void 0,t.ParserApi=class{constructor(n){this._core=n}registerCsiHandler(n,l){return this._core.registerCsiHandler(n,u=>l(u.toArray()))}addCsiHandler(n,l){return this.registerCsiHandler(n,l)}registerDcsHandler(n,l){return this._core.registerDcsHandler(n,(u,a)=>l(u,a.toArray()))}addDcsHandler(n,l){return this.registerDcsHandler(n,l)}registerEscHandler(n,l){return this._core.registerEscHandler(n,l)}addEscHandler(n,l){return this.registerEscHandler(n,l)}registerOscHandler(n,l){return this._core.registerOscHandler(n,l)}addOscHandler(n,l){return this.registerOscHandler(n,l)}}},7090:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UnicodeApi=void 0,t.UnicodeApi=class{constructor(n){this._core=n}register(n){this._core.unicodeService.register(n)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(n){this._core.unicodeService.activeVersion=n}}},744:function(D,t,n){var l=this&&this.__decorate||function(e,i,s,r){var h,f=arguments.length,v=f<3?i:r===null?r=Object.getOwnPropertyDescriptor(i,s):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,i,s,r);else for(var b=e.length-1;b>=0;b--)(h=e[b])&&(v=(f<3?h(v):f>3?h(i,s,v):h(i,s))||v);return f>3&&v&&Object.defineProperty(i,s,v),v},u=this&&this.__param||function(e,i){return function(s,r){i(s,r,e)}};Object.defineProperty(t,"__esModule",{value:!0}),t.BufferService=t.MINIMUM_ROWS=t.MINIMUM_COLS=void 0;const a=n(8460),d=n(844),g=n(5295),p=n(2585);t.MINIMUM_COLS=2,t.MINIMUM_ROWS=1;let _=t.BufferService=class extends d.Disposable{get buffer(){return this.buffers.active}constructor(e){super(),this.isUserScrolling=!1,this._onResize=this.register(new a.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new a.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(e.rawOptions.cols||0,t.MINIMUM_COLS),this.rows=Math.max(e.rawOptions.rows||0,t.MINIMUM_ROWS),this.buffers=this.register(new g.BufferSet(e,this))}resize(e,i){this.cols=e,this.rows=i,this.buffers.resize(e,i),this._onResize.fire({cols:e,rows:i})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(e,i=!1){const s=this.buffer;let r;r=this._cachedBlankLine,r&&r.length===this.cols&&r.getFg(0)===e.fg&&r.getBg(0)===e.bg||(r=s.getBlankLine(e,i),this._cachedBlankLine=r),r.isWrapped=i;const h=s.ybase+s.scrollTop,f=s.ybase+s.scrollBottom;if(s.scrollTop===0){const v=s.lines.isFull;f===s.lines.length-1?v?s.lines.recycle().copyFrom(r):s.lines.push(r.clone()):s.lines.splice(f+1,0,r.clone()),v?this.isUserScrolling&&(s.ydisp=Math.max(s.ydisp-1,0)):(s.ybase++,this.isUserScrolling||s.ydisp++)}else{const v=f-h+1;s.lines.shiftElements(h+1,v-1,-1),s.lines.set(f,r.clone())}this.isUserScrolling||(s.ydisp=s.ybase),this._onScroll.fire(s.ydisp)}scrollLines(e,i,s){const r=this.buffer;if(e<0){if(r.ydisp===0)return;this.isUserScrolling=!0}else e+r.ydisp>=r.ybase&&(this.isUserScrolling=!1);const h=r.ydisp;r.ydisp=Math.max(Math.min(r.ydisp+e,r.ybase),0),h!==r.ydisp&&(i||this._onScroll.fire(r.ydisp))}};t.BufferService=_=l([u(0,p.IOptionsService)],_)},7994:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CharsetService=void 0,t.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(n){this.glevel=n,this.charset=this._charsets[n]}setgCharset(n,l){this._charsets[n]=l,this.glevel===n&&(this.charset=l)}}},1753:function(D,t,n){var l=this&&this.__decorate||function(r,h,f,v){var b,c=arguments.length,S=c<3?h:v===null?v=Object.getOwnPropertyDescriptor(h,f):v;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(r,h,f,v);else for(var E=r.length-1;E>=0;E--)(b=r[E])&&(S=(c<3?b(S):c>3?b(h,f,S):b(h,f))||S);return c>3&&S&&Object.defineProperty(h,f,S),S},u=this&&this.__param||function(r,h){return function(f,v){h(f,v,r)}};Object.defineProperty(t,"__esModule",{value:!0}),t.CoreMouseService=void 0;const a=n(2585),d=n(8460),g=n(844),p={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:r=>r.button!==4&&r.action===1&&(r.ctrl=!1,r.alt=!1,r.shift=!1,!0)},VT200:{events:19,restrict:r=>r.action!==32},DRAG:{events:23,restrict:r=>r.action!==32||r.button!==3},ANY:{events:31,restrict:r=>!0}};function _(r,h){let f=(r.ctrl?16:0)|(r.shift?4:0)|(r.alt?8:0);return r.button===4?(f|=64,f|=r.action):(f|=3&r.button,4&r.button&&(f|=64),8&r.button&&(f|=128),r.action===32?f|=32:r.action!==0||h||(f|=3)),f}const e=String.fromCharCode,i={DEFAULT:r=>{const h=[_(r,!1)+32,r.col+32,r.row+32];return h[0]>255||h[1]>255||h[2]>255?"":`\x1B[M${e(h[0])}${e(h[1])}${e(h[2])}`},SGR:r=>{const h=r.action===0&&r.button!==4?"m":"M";return`\x1B[<${_(r,!0)};${r.col};${r.row}${h}`},SGR_PIXELS:r=>{const h=r.action===0&&r.button!==4?"m":"M";return`\x1B[<${_(r,!0)};${r.x};${r.y}${h}`}};let s=t.CoreMouseService=class extends g.Disposable{constructor(r,h){super(),this._bufferService=r,this._coreService=h,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new d.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const f of Object.keys(p))this.addProtocol(f,p[f]);for(const f of Object.keys(i))this.addEncoding(f,i[f]);this.reset()}addProtocol(r,h){this._protocols[r]=h}addEncoding(r,h){this._encodings[r]=h}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(r){if(!this._protocols[r])throw new Error(`unknown protocol "${r}"`);this._activeProtocol=r,this._onProtocolChange.fire(this._protocols[r].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(r){if(!this._encodings[r])throw new Error(`unknown encoding "${r}"`);this._activeEncoding=r}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(r){if(r.col<0||r.col>=this._bufferService.cols||r.row<0||r.row>=this._bufferService.rows||r.button===4&&r.action===32||r.button===3&&r.action!==32||r.button!==4&&(r.action===2||r.action===3)||(r.col++,r.row++,r.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,r,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(r))return!1;const h=this._encodings[this._activeEncoding](r);return h&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(h):this._coreService.triggerDataEvent(h,!0)),this._lastEvent=r,!0}explainEvents(r){return{down:!!(1&r),up:!!(2&r),drag:!!(4&r),move:!!(8&r),wheel:!!(16&r)}}_equalEvents(r,h,f){if(f){if(r.x!==h.x||r.y!==h.y)return!1}else if(r.col!==h.col||r.row!==h.row)return!1;return r.button===h.button&&r.action===h.action&&r.ctrl===h.ctrl&&r.alt===h.alt&&r.shift===h.shift}};t.CoreMouseService=s=l([u(0,a.IBufferService),u(1,a.ICoreService)],s)},6975:function(D,t,n){var l=this&&this.__decorate||function(s,r,h,f){var v,b=arguments.length,c=b<3?r:f===null?f=Object.getOwnPropertyDescriptor(r,h):f;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")c=Reflect.decorate(s,r,h,f);else for(var S=s.length-1;S>=0;S--)(v=s[S])&&(c=(b<3?v(c):b>3?v(r,h,c):v(r,h))||c);return b>3&&c&&Object.defineProperty(r,h,c),c},u=this&&this.__param||function(s,r){return function(h,f){r(h,f,s)}};Object.defineProperty(t,"__esModule",{value:!0}),t.CoreService=void 0;const a=n(1439),d=n(8460),g=n(844),p=n(2585),_=Object.freeze({insertMode:!1}),e=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let i=t.CoreService=class extends g.Disposable{constructor(s,r,h){super(),this._bufferService=s,this._logService=r,this._optionsService=h,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new d.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new d.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new d.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new d.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,a.clone)(_),this.decPrivateModes=(0,a.clone)(e)}reset(){this.modes=(0,a.clone)(_),this.decPrivateModes=(0,a.clone)(e)}triggerDataEvent(s,r=!1){if(this._optionsService.rawOptions.disableStdin)return;const h=this._bufferService.buffer;r&&this._optionsService.rawOptions.scrollOnUserInput&&h.ybase!==h.ydisp&&this._onRequestScrollToBottom.fire(),r&&this._onUserInput.fire(),this._logService.debug(`sending data "${s}"`,()=>s.split("").map(f=>f.charCodeAt(0))),this._onData.fire(s)}triggerBinaryEvent(s){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${s}"`,()=>s.split("").map(r=>r.charCodeAt(0))),this._onBinary.fire(s))}};t.CoreService=i=l([u(0,p.IBufferService),u(1,p.ILogService),u(2,p.IOptionsService)],i)},9074:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DecorationService=void 0;const l=n(8055),u=n(8460),a=n(844),d=n(6106);let g=0,p=0;class _ extends a.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new d.SortedList(s=>s==null?void 0:s.marker.line),this._onDecorationRegistered=this.register(new u.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new u.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,a.toDisposable)(()=>this.reset()))}registerDecoration(s){if(s.marker.isDisposed)return;const r=new e(s);if(r){const h=r.marker.onDispose(()=>r.dispose());r.onDispose(()=>{r&&(this._decorations.delete(r)&&this._onDecorationRemoved.fire(r),h.dispose())}),this._decorations.insert(r),this._onDecorationRegistered.fire(r)}return r}reset(){for(const s of this._decorations.values())s.dispose();this._decorations.clear()}*getDecorationsAtCell(s,r,h){var f,v,b;let c=0,S=0;for(const E of this._decorations.getKeyIterator(r))c=(f=E.options.x)!==null&&f!==void 0?f:0,S=c+((v=E.options.width)!==null&&v!==void 0?v:1),s>=c&&s<S&&(!h||((b=E.options.layer)!==null&&b!==void 0?b:"bottom")===h)&&(yield E)}forEachDecorationAtCell(s,r,h,f){this._decorations.forEachByKey(r,v=>{var b,c,S;g=(b=v.options.x)!==null&&b!==void 0?b:0,p=g+((c=v.options.width)!==null&&c!==void 0?c:1),s>=g&&s<p&&(!h||((S=v.options.layer)!==null&&S!==void 0?S:"bottom")===h)&&f(v)})}}t.DecorationService=_;class e extends a.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=l.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=l.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(s){super(),this.options=s,this.onRenderEmitter=this.register(new u.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new u.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=s.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.InstantiationService=t.ServiceCollection=void 0;const l=n(2585),u=n(8343);class a{constructor(...g){this._entries=new Map;for(const[p,_]of g)this.set(p,_)}set(g,p){const _=this._entries.get(g);return this._entries.set(g,p),_}forEach(g){for(const[p,_]of this._entries.entries())g(p,_)}has(g){return this._entries.has(g)}get(g){return this._entries.get(g)}}t.ServiceCollection=a,t.InstantiationService=class{constructor(){this._services=new a,this._services.set(l.IInstantiationService,this)}setService(d,g){this._services.set(d,g)}getService(d){return this._services.get(d)}createInstance(d,...g){const p=(0,u.getServiceDependencies)(d).sort((i,s)=>i.index-s.index),_=[];for(const i of p){const s=this._services.get(i.id);if(!s)throw new Error(`[createInstance] ${d.name} depends on UNKNOWN service ${i.id}.`);_.push(s)}const e=p.length>0?p[0].index:g.length;if(g.length!==e)throw new Error(`[createInstance] First service dependency of ${d.name} at position ${e+1} conflicts with ${g.length} static arguments`);return new d(...g,..._)}}},7866:function(D,t,n){var l=this&&this.__decorate||function(e,i,s,r){var h,f=arguments.length,v=f<3?i:r===null?r=Object.getOwnPropertyDescriptor(i,s):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")v=Reflect.decorate(e,i,s,r);else for(var b=e.length-1;b>=0;b--)(h=e[b])&&(v=(f<3?h(v):f>3?h(i,s,v):h(i,s))||v);return f>3&&v&&Object.defineProperty(i,s,v),v},u=this&&this.__param||function(e,i){return function(s,r){i(s,r,e)}};Object.defineProperty(t,"__esModule",{value:!0}),t.traceCall=t.setTraceLogger=t.LogService=void 0;const a=n(844),d=n(2585),g={trace:d.LogLevelEnum.TRACE,debug:d.LogLevelEnum.DEBUG,info:d.LogLevelEnum.INFO,warn:d.LogLevelEnum.WARN,error:d.LogLevelEnum.ERROR,off:d.LogLevelEnum.OFF};let p,_=t.LogService=class extends a.Disposable{get logLevel(){return this._logLevel}constructor(e){super(),this._optionsService=e,this._logLevel=d.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),p=this}_updateLogLevel(){this._logLevel=g[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(e){for(let i=0;i<e.length;i++)typeof e[i]=="function"&&(e[i]=e[i]())}_log(e,i,s){this._evalLazyOptionalParams(s),e.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+i,...s)}trace(e,...i){var s,r;this._logLevel<=d.LogLevelEnum.TRACE&&this._log((r=(s=this._optionsService.options.logger)===null||s===void 0?void 0:s.trace.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.log,e,i)}debug(e,...i){var s,r;this._logLevel<=d.LogLevelEnum.DEBUG&&this._log((r=(s=this._optionsService.options.logger)===null||s===void 0?void 0:s.debug.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.log,e,i)}info(e,...i){var s,r;this._logLevel<=d.LogLevelEnum.INFO&&this._log((r=(s=this._optionsService.options.logger)===null||s===void 0?void 0:s.info.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.info,e,i)}warn(e,...i){var s,r;this._logLevel<=d.LogLevelEnum.WARN&&this._log((r=(s=this._optionsService.options.logger)===null||s===void 0?void 0:s.warn.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.warn,e,i)}error(e,...i){var s,r;this._logLevel<=d.LogLevelEnum.ERROR&&this._log((r=(s=this._optionsService.options.logger)===null||s===void 0?void 0:s.error.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.error,e,i)}};t.LogService=_=l([u(0,d.IOptionsService)],_),t.setTraceLogger=function(e){p=e},t.traceCall=function(e,i,s){if(typeof s.value!="function")throw new Error("not supported");const r=s.value;s.value=function(...h){if(p.logLevel!==d.LogLevelEnum.TRACE)return r.apply(this,h);p.trace(`GlyphRenderer#${r.name}(${h.map(v=>JSON.stringify(v)).join(", ")})`);const f=r.apply(this,h);return p.trace(`GlyphRenderer#${r.name} return`,f),f}}},7302:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OptionsService=t.DEFAULT_OPTIONS=void 0;const l=n(8460),u=n(844),a=n(6114);t.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rightClickSelectsWord:a.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const d=["normal","bold","100","200","300","400","500","600","700","800","900"];class g extends u.Disposable{constructor(_){super(),this._onOptionChange=this.register(new l.EventEmitter),this.onOptionChange=this._onOptionChange.event;const e=Object.assign({},t.DEFAULT_OPTIONS);for(const i in _)if(i in e)try{const s=_[i];e[i]=this._sanitizeAndValidateOption(i,s)}catch(s){console.error(s)}this.rawOptions=e,this.options=Object.assign({},e),this._setupOptions()}onSpecificOptionChange(_,e){return this.onOptionChange(i=>{i===_&&e(this.rawOptions[_])})}onMultipleOptionChange(_,e){return this.onOptionChange(i=>{_.indexOf(i)!==-1&&e()})}_setupOptions(){const _=i=>{if(!(i in t.DEFAULT_OPTIONS))throw new Error(`No option with key "${i}"`);return this.rawOptions[i]},e=(i,s)=>{if(!(i in t.DEFAULT_OPTIONS))throw new Error(`No option with key "${i}"`);s=this._sanitizeAndValidateOption(i,s),this.rawOptions[i]!==s&&(this.rawOptions[i]=s,this._onOptionChange.fire(i))};for(const i in this.rawOptions){const s={get:_.bind(this,i),set:e.bind(this,i)};Object.defineProperty(this.options,i,s)}}_sanitizeAndValidateOption(_,e){switch(_){case"cursorStyle":if(e||(e=t.DEFAULT_OPTIONS[_]),!function(i){return i==="block"||i==="underline"||i==="bar"}(e))throw new Error(`"${e}" is not a valid value for ${_}`);break;case"wordSeparator":e||(e=t.DEFAULT_OPTIONS[_]);break;case"fontWeight":case"fontWeightBold":if(typeof e=="number"&&1<=e&&e<=1e3)break;e=d.includes(e)?e:t.DEFAULT_OPTIONS[_];break;case"cursorWidth":e=Math.floor(e);case"lineHeight":case"tabStopWidth":if(e<1)throw new Error(`${_} cannot be less than 1, value: ${e}`);break;case"minimumContrastRatio":e=Math.max(1,Math.min(21,Math.round(10*e)/10));break;case"scrollback":if((e=Math.min(e,4294967295))<0)throw new Error(`${_} cannot be less than 0, value: ${e}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(e<=0)throw new Error(`${_} cannot be less than or equal to 0, value: ${e}`);break;case"rows":case"cols":if(!e&&e!==0)throw new Error(`${_} must be numeric, value: ${e}`);break;case"windowsPty":e=e??{}}return e}}t.OptionsService=g},2660:function(D,t,n){var l=this&&this.__decorate||function(g,p,_,e){var i,s=arguments.length,r=s<3?p:e===null?e=Object.getOwnPropertyDescriptor(p,_):e;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(g,p,_,e);else for(var h=g.length-1;h>=0;h--)(i=g[h])&&(r=(s<3?i(r):s>3?i(p,_,r):i(p,_))||r);return s>3&&r&&Object.defineProperty(p,_,r),r},u=this&&this.__param||function(g,p){return function(_,e){p(_,e,g)}};Object.defineProperty(t,"__esModule",{value:!0}),t.OscLinkService=void 0;const a=n(2585);let d=t.OscLinkService=class{constructor(g){this._bufferService=g,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(g){const p=this._bufferService.buffer;if(g.id===void 0){const h=p.addMarker(p.ybase+p.y),f={data:g,id:this._nextId++,lines:[h]};return h.onDispose(()=>this._removeMarkerFromLink(f,h)),this._dataByLinkId.set(f.id,f),f.id}const _=g,e=this._getEntryIdKey(_),i=this._entriesWithId.get(e);if(i)return this.addLineToLink(i.id,p.ybase+p.y),i.id;const s=p.addMarker(p.ybase+p.y),r={id:this._nextId++,key:this._getEntryIdKey(_),data:_,lines:[s]};return s.onDispose(()=>this._removeMarkerFromLink(r,s)),this._entriesWithId.set(r.key,r),this._dataByLinkId.set(r.id,r),r.id}addLineToLink(g,p){const _=this._dataByLinkId.get(g);if(_&&_.lines.every(e=>e.line!==p)){const e=this._bufferService.buffer.addMarker(p);_.lines.push(e),e.onDispose(()=>this._removeMarkerFromLink(_,e))}}getLinkData(g){var p;return(p=this._dataByLinkId.get(g))===null||p===void 0?void 0:p.data}_getEntryIdKey(g){return`${g.id};;${g.uri}`}_removeMarkerFromLink(g,p){const _=g.lines.indexOf(p);_!==-1&&(g.lines.splice(_,1),g.lines.length===0&&(g.data.id!==void 0&&this._entriesWithId.delete(g.key),this._dataByLinkId.delete(g.id)))}};t.OscLinkService=d=l([u(0,a.IBufferService)],d)},8343:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createDecorator=t.getServiceDependencies=t.serviceRegistry=void 0;const n="di$target",l="di$dependencies";t.serviceRegistry=new Map,t.getServiceDependencies=function(u){return u[l]||[]},t.createDecorator=function(u){if(t.serviceRegistry.has(u))return t.serviceRegistry.get(u);const a=function(d,g,p){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(_,e,i){e[n]===e?e[l].push({id:_,index:i}):(e[l]=[{id:_,index:i}],e[n]=e)})(a,d,p)};return a.toString=()=>u,t.serviceRegistry.set(u,a),a}},2585:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.IDecorationService=t.IUnicodeService=t.IOscLinkService=t.IOptionsService=t.ILogService=t.LogLevelEnum=t.IInstantiationService=t.ICharsetService=t.ICoreService=t.ICoreMouseService=t.IBufferService=void 0;const l=n(8343);var u;t.IBufferService=(0,l.createDecorator)("BufferService"),t.ICoreMouseService=(0,l.createDecorator)("CoreMouseService"),t.ICoreService=(0,l.createDecorator)("CoreService"),t.ICharsetService=(0,l.createDecorator)("CharsetService"),t.IInstantiationService=(0,l.createDecorator)("InstantiationService"),function(a){a[a.TRACE=0]="TRACE",a[a.DEBUG=1]="DEBUG",a[a.INFO=2]="INFO",a[a.WARN=3]="WARN",a[a.ERROR=4]="ERROR",a[a.OFF=5]="OFF"}(u||(t.LogLevelEnum=u={})),t.ILogService=(0,l.createDecorator)("LogService"),t.IOptionsService=(0,l.createDecorator)("OptionsService"),t.IOscLinkService=(0,l.createDecorator)("OscLinkService"),t.IUnicodeService=(0,l.createDecorator)("UnicodeService"),t.IDecorationService=(0,l.createDecorator)("DecorationService")},1480:(D,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.UnicodeService=void 0;const l=n(8460),u=n(225);t.UnicodeService=class{constructor(){this._providers=Object.create(null),this._active="",this._onChange=new l.EventEmitter,this.onChange=this._onChange.event;const a=new u.UnicodeV6;this.register(a),this._active=a.version,this._activeProvider=a}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(a){if(!this._providers[a])throw new Error(`unknown Unicode version "${a}"`);this._active=a,this._activeProvider=this._providers[a],this._onChange.fire(a)}register(a){this._providers[a.version]=a}wcwidth(a){return this._activeProvider.wcwidth(a)}getStringCellWidth(a){let d=0;const g=a.length;for(let p=0;p<g;++p){let _=a.charCodeAt(p);if(55296<=_&&_<=56319){if(++p>=g)return d+this.wcwidth(_);const e=a.charCodeAt(p);56320<=e&&e<=57343?_=1024*(_-55296)+e-56320+65536:d+=this.wcwidth(e)}d+=this.wcwidth(_)}return d}}}},w={};function k(D){var t=w[D];if(t!==void 0)return t.exports;var n=w[D]={exports:{}};return m[D].call(n.exports,n,n.exports,k),n.exports}var M={};return(()=>{var D=M;Object.defineProperty(D,"__esModule",{value:!0}),D.Terminal=void 0;const t=k(9042),n=k(3236),l=k(844),u=k(5741),a=k(8285),d=k(7975),g=k(7090),p=["cols","rows"];class _ extends l.Disposable{constructor(i){super(),this._core=this.register(new n.Terminal(i)),this._addonManager=this.register(new u.AddonManager),this._publicOptions=Object.assign({},this._core.options);const s=h=>this._core.options[h],r=(h,f)=>{this._checkReadonlyOptions(h),this._core.options[h]=f};for(const h in this._core.options){const f={get:s.bind(this,h),set:r.bind(this,h)};Object.defineProperty(this._publicOptions,h,f)}}_checkReadonlyOptions(i){if(p.includes(i))throw new Error(`Option "${i}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new d.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new g.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new a.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const i=this._core.coreService.decPrivateModes;let s="none";switch(this._core.coreMouseService.activeProtocol){case"X10":s="x10";break;case"VT200":s="vt200";break;case"DRAG":s="drag";break;case"ANY":s="any"}return{applicationCursorKeysMode:i.applicationCursorKeys,applicationKeypadMode:i.applicationKeypad,bracketedPasteMode:i.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:s,originMode:i.origin,reverseWraparoundMode:i.reverseWraparound,sendFocusMode:i.sendFocus,wraparoundMode:i.wraparound}}get options(){return this._publicOptions}set options(i){for(const s in i)this._publicOptions[s]=i[s]}blur(){this._core.blur()}focus(){this._core.focus()}resize(i,s){this._verifyIntegers(i,s),this._core.resize(i,s)}open(i){this._core.open(i)}attachCustomKeyEventHandler(i){this._core.attachCustomKeyEventHandler(i)}registerLinkProvider(i){return this._core.registerLinkProvider(i)}registerCharacterJoiner(i){return this._checkProposedApi(),this._core.registerCharacterJoiner(i)}deregisterCharacterJoiner(i){this._checkProposedApi(),this._core.deregisterCharacterJoiner(i)}registerMarker(i=0){return this._verifyIntegers(i),this._core.registerMarker(i)}registerDecoration(i){var s,r,h;return this._checkProposedApi(),this._verifyPositiveIntegers((s=i.x)!==null&&s!==void 0?s:0,(r=i.width)!==null&&r!==void 0?r:0,(h=i.height)!==null&&h!==void 0?h:0),this._core.registerDecoration(i)}hasSelection(){return this._core.hasSelection()}select(i,s,r){this._verifyIntegers(i,s,r),this._core.select(i,s,r)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(i,s){this._verifyIntegers(i,s),this._core.selectLines(i,s)}dispose(){super.dispose()}scrollLines(i){this._verifyIntegers(i),this._core.scrollLines(i)}scrollPages(i){this._verifyIntegers(i),this._core.scrollPages(i)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(i){this._verifyIntegers(i),this._core.scrollToLine(i)}clear(){this._core.clear()}write(i,s){this._core.write(i,s)}writeln(i,s){this._core.write(i),this._core.write(`\r
`,s)}paste(i){this._core.paste(i)}refresh(i,s){this._verifyIntegers(i,s),this._core.refresh(i,s)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(i){this._addonManager.loadAddon(this,i)}static get strings(){return t}_verifyIntegers(...i){for(const s of i)if(s===1/0||isNaN(s)||s%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...i){for(const s of i)if(s&&(s===1/0||isNaN(s)||s%1!=0||s<0))throw new Error("This API only accepts positive integers")}}D.Terminal=_})(),M})())})(ut);var Tt=ut.exports,_t={exports:{}};(function(x,o){(function(m,w){x.exports=w()})(self,()=>(()=>{var m={};return(()=>{var w=m;Object.defineProperty(w,"__esModule",{value:!0}),w.FitAddon=void 0,w.FitAddon=class{activate(k){this._terminal=k}dispose(){}fit(){const k=this.proposeDimensions();if(!k||!this._terminal||isNaN(k.cols)||isNaN(k.rows))return;const M=this._terminal._core;this._terminal.rows===k.rows&&this._terminal.cols===k.cols||(M._renderService.clear(),this._terminal.resize(k.cols,k.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const k=this._terminal._core,M=k._renderService.dimensions;if(M.css.cell.width===0||M.css.cell.height===0)return;const D=this._terminal.options.scrollback===0?0:k.viewport.scrollBarWidth,t=window.getComputedStyle(this._terminal.element.parentElement),n=parseInt(t.getPropertyValue("height")),l=Math.max(0,parseInt(t.getPropertyValue("width"))),u=window.getComputedStyle(this._terminal.element),a=n-(parseInt(u.getPropertyValue("padding-top"))+parseInt(u.getPropertyValue("padding-bottom"))),d=l-(parseInt(u.getPropertyValue("padding-right"))+parseInt(u.getPropertyValue("padding-left")))-D;return{cols:Math.max(2,Math.floor(d/M.css.cell.width)),rows:Math.max(1,Math.floor(a/M.css.cell.height))}}}})(),m})())})(_t);var Mt=_t.exports,gt={exports:{}};(function(x,o){(function(m,w){x.exports=w()})(self,()=>(()=>{var m={6:(D,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.LinkComputer=t.WebLinkProvider=void 0,t.WebLinkProvider=class{constructor(l,u,a,d={}){this._terminal=l,this._regex=u,this._handler=a,this._options=d}provideLinks(l,u){const a=n.computeLink(l,this._regex,this._terminal,this._handler);u(this._addCallbacks(a))}_addCallbacks(l){return l.map(u=>(u.leave=this._options.leave,u.hover=(a,d)=>{if(this._options.hover){const{range:g}=u;this._options.hover(a,d,g)}},u))}};class n{static computeLink(u,a,d,g){const p=new RegExp(a.source,(a.flags||"")+"g"),[_,e]=n._getWindowedLineStrings(u-1,d),i=_.join("");let s;const r=[];for(;s=p.exec(i);){const h=s[0];try{const E=new URL(h),T=decodeURI(E.toString());if(h!==T&&h+"/"!==T)continue}catch{continue}const[f,v]=n._mapStrIdx(d,e,0,s.index),[b,c]=n._mapStrIdx(d,f,v,h.length);if(f===-1||v===-1||b===-1||c===-1)continue;const S={start:{x:v+1,y:f+1},end:{x:c,y:b+1}};r.push({range:S,text:h,activate:g})}return r}static _getWindowedLineStrings(u,a){let d,g=u,p=u,_=0,e="";const i=[];if(d=a.buffer.active.getLine(u)){const s=d.translateToString(!0);if(d.isWrapped&&s[0]!==" "){for(_=0;(d=a.buffer.active.getLine(--g))&&_<2048&&(e=d.translateToString(!0),_+=e.length,i.push(e),d.isWrapped&&e.indexOf(" ")===-1););i.reverse()}for(i.push(s),_=0;(d=a.buffer.active.getLine(++p))&&d.isWrapped&&_<2048&&(e=d.translateToString(!0),_+=e.length,i.push(e),e.indexOf(" ")===-1););}return[i,g]}static _mapStrIdx(u,a,d,g){const p=u.buffer.active,_=p.getNullCell();let e=d;for(;g;){const i=p.getLine(a);if(!i)return[-1,-1];for(let s=e;s<i.length;++s){i.getCell(s,_);const r=_.getChars();if(_.getWidth()&&(g-=r.length||1,s===i.length-1&&r==="")){const h=p.getLine(a+1);h&&h.isWrapped&&(h.getCell(0,_),_.getWidth()===2&&(g+=1))}if(g<0)return[a,s]}a++,e=0}return[a,e]}}t.LinkComputer=n}},w={};function k(D){var t=w[D];if(t!==void 0)return t.exports;var n=w[D]={exports:{}};return m[D](n,n.exports,k),n.exports}var M={};return(()=>{var D=M;Object.defineProperty(D,"__esModule",{value:!0}),D.WebLinksAddon=void 0;const t=k(6),n=/https?:[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;function l(u,a){const d=window.open();if(d){try{d.opener=null}catch{}d.location.href=a}else console.warn("Opening link blocked as opener could not be cleared")}D.WebLinksAddon=class{constructor(u=l,a={}){this._handler=u,this._options=a}activate(u){this._terminal=u;const a=this._options,d=a.urlRegex||n;this._linkProvider=this._terminal.registerLinkProvider(new t.WebLinkProvider(this._terminal,d,this._handler,a))}dispose(){var u;(u=this._linkProvider)===null||u===void 0||u.dispose()}}})(),M})())})(gt);var It=gt.exports;class Pt{constructor(){this.terminal=null,this.socket=null,this.buffer=[],this.isReady=!1}initialize(o,m){this.terminal=o,this.socket=m,this.isReady=!0,this.terminal.onData(w=>{this.buffer.push(w)}),console.log("XTermService initialized")}isServiceReady(){return this.isReady&&this.terminal&&this.socket&&this.socket.readyState===WebSocket.OPEN}async sendRawData(o){if(!this.isServiceReady())return console.error("XTermService not ready"),!1;try{return this.socket.send(JSON.stringify({type:"input",data:o})),!0}catch(m){return console.error("Failed to send data:",m),!1}}async sendKey(o){const w={Enter:"\r",Tab:"	",Backspace:"",Delete:"\x1B[3~",Escape:"\x1B",ArrowUp:"\x1B[A",ArrowDown:"\x1B[B",ArrowRight:"\x1B[C",ArrowLeft:"\x1B[D",Home:"\x1B[H",End:"\x1B[F",PageUp:"\x1B[5~",PageDown:"\x1B[6~",F1:"\x1BOP",F2:"\x1BOQ",F3:"\x1BOR",F4:"\x1BOS",F5:"\x1B[15~",F6:"\x1B[17~",F7:"\x1B[18~",F8:"\x1B[19~",F9:"\x1B[20~",F10:"\x1B[21~",F11:"\x1B[23~",F12:"\x1B[24~"}[o]||o;return await this.sendRawData(w)}async sendKeyCombo(o){const m=o.split("+").map(k=>k.trim());let w="";if(m.length===2){const[k,M]=m,D=M.toLowerCase();switch(k.toLowerCase()){case"ctrl":D>="a"&&D<="z"?w=String.fromCharCode(D.charCodeAt(0)-96):w={c:"",d:"",z:"",l:"\f",r:"",u:"",k:"\v",w:"",y:"",p:"",n:"",f:"",b:"",a:"",e:""}[D]||"";break;case"alt":w="\x1B"+M;break;case"shift":w=M.toUpperCase();break}}return w?await this.sendRawData(w):(console.error("Unsupported key combination:",o),!1)}async sendCommand(o,m=!0){let w=await this.sendRawData(o);return w&&m&&(w=await this.sendKey("Enter")),w}async sendCommands(o,m=100){for(const w of o){if(!await this.sendCommand(w))return console.error("Failed to send command:",w),!1;m>0&&await new Promise(M=>setTimeout(M,m))}return!0}getCurrentLineContent(){if(!this.terminal)return"";const o=this.terminal.buffer.active,m=o.cursorY,w=o.getLine(m);return w?w.translateToString(!0):""}getLineContent(o){if(!this.terminal)return"";const w=this.terminal.buffer.active.getLine(o);return w?w.translateToString(!0):""}getLineRangeContent(o,m){if(!this.terminal)return[];const w=this.terminal.buffer.active,k=[];for(let M=o;M<=m&&M<w.length;M++){const D=w.getLine(M);k.push(D?D.translateToString(!0):"")}return k}getAllContent(){if(!this.terminal)return[];const o=this.terminal.buffer.active,m=[];for(let w=0;w<o.length;w++){const k=o.getLine(w);m.push(k?k.translateToString(!0):"")}return m}getAllContentAsString(o=`
`){return this.getAllContent().join(o)}async clearScreen(){return await this.sendKeyCombo("Ctrl+L")}getTerminalDimensions(){return this.terminal?{cols:this.terminal.cols,rows:this.terminal.rows}:{cols:0,rows:0}}getCursorPosition(){if(!this.terminal)return{x:0,y:0};const o=this.terminal.buffer.active;return{x:o.cursorX,y:o.cursorY}}}const rt=new Pt;function Ge(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var pe=Ge();function pt(x){pe=x}var Le={exec:()=>null};function J(x,o=""){let m=typeof x=="string"?x:x.source,w={replace:(k,M)=>{let D=typeof M=="string"?M:M.source;return D=D.replace(se.caret,"$1"),m=m.replace(k,D),w},getRegex:()=>new RegExp(m,o)};return w}var se={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:x=>new RegExp(`^( {0,3}${x})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:x=>new RegExp(`^ {0,${Math.min(3,x-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:x=>new RegExp(`^ {0,${Math.min(3,x-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:x=>new RegExp(`^ {0,${Math.min(3,x-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:x=>new RegExp(`^ {0,${Math.min(3,x-1)}}#`),htmlBeginRegex:x=>new RegExp(`^ {0,${Math.min(3,x-1)}}<(?:[a-z].*>|!--)`,"i")},Ot=/^(?:[ \t]*(?:\n|$))+/,Ht=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Ft=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,De=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,$t=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,Je=/(?:[*+-]|\d{1,9}[.)])/,vt=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,mt=J(vt).replace(/bull/g,Je).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),zt=J(vt).replace(/bull/g,Je).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),Ze=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Wt=/^[^\n]+/,Ye=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Ut=J(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",Ye).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Nt=J(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Je).getRegex(),Fe="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Qe=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,jt=J("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",Qe).replace("tag",Fe).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),St=J(Ze).replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fe).getRegex(),Kt=J(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",St).getRegex(),et={blockquote:Kt,code:Ht,def:Ut,fences:Ft,heading:$t,hr:De,html:jt,lheading:mt,list:Nt,newline:Ot,paragraph:St,table:Le,text:Wt},nt=J("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fe).getRegex(),qt={...et,lheading:zt,table:nt,paragraph:J(Ze).replace("hr",De).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",nt).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Fe).getRegex()},Vt={...et,html:J(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Qe).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Le,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:J(Ze).replace("hr",De).replace("heading",` *#{1,6} *[^
]`).replace("lheading",mt).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Xt=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,Gt=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,bt=/^( {2,}|\\)\n(?!\s*$)/,Jt=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,$e=/[\p{P}\p{S}]/u,tt=/[\s\p{P}\p{S}]/u,Ct=/[^\s\p{P}\p{S}]/u,Zt=J(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,tt).getRegex(),yt=/(?!~)[\p{P}\p{S}]/u,Yt=/(?!~)[\s\p{P}\p{S}]/u,Qt=/(?:[^\s\p{P}\p{S}]|~)/u,es=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,wt=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,ts=J(wt,"u").replace(/punct/g,$e).getRegex(),ss=J(wt,"u").replace(/punct/g,yt).getRegex(),kt="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",is=J(kt,"gu").replace(/notPunctSpace/g,Ct).replace(/punctSpace/g,tt).replace(/punct/g,$e).getRegex(),rs=J(kt,"gu").replace(/notPunctSpace/g,Qt).replace(/punctSpace/g,Yt).replace(/punct/g,yt).getRegex(),ns=J("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Ct).replace(/punctSpace/g,tt).replace(/punct/g,$e).getRegex(),os=J(/\\(punct)/,"gu").replace(/punct/g,$e).getRegex(),as=J(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),ls=J(Qe).replace("(?:-->|$)","-->").getRegex(),hs=J("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",ls).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),Pe=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,cs=J(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",Pe).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),xt=J(/^!?\[(label)\]\[(ref)\]/).replace("label",Pe).replace("ref",Ye).getRegex(),Et=J(/^!?\[(ref)\](?:\[\])?/).replace("ref",Ye).getRegex(),ds=J("reflink|nolink(?!\\()","g").replace("reflink",xt).replace("nolink",Et).getRegex(),st={_backpedal:Le,anyPunctuation:os,autolink:as,blockSkip:es,br:bt,code:Gt,del:Le,emStrongLDelim:ts,emStrongRDelimAst:is,emStrongRDelimUnd:ns,escape:Xt,link:cs,nolink:Et,punctuation:Zt,reflink:xt,reflinkSearch:ds,tag:hs,text:Jt,url:Le},us={...st,link:J(/^!?\[(label)\]\((.*?)\)/).replace("label",Pe).getRegex(),reflink:J(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",Pe).getRegex()},qe={...st,emStrongRDelimAst:rs,emStrongLDelim:ss,url:J(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},fs={...qe,br:J(bt).replace("{2,}","*").getRegex(),text:J(qe.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Be={normal:et,gfm:qt,pedantic:Vt},ke={normal:st,gfm:qe,breaks:fs,pedantic:us},_s={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ot=x=>_s[x];function he(x,o){if(o){if(se.escapeTest.test(x))return x.replace(se.escapeReplace,ot)}else if(se.escapeTestNoEncode.test(x))return x.replace(se.escapeReplaceNoEncode,ot);return x}function at(x){try{x=encodeURI(x).replace(se.percentDecode,"%")}catch{return null}return x}function lt(x,o){var M;let m=x.replace(se.findPipe,(D,t,n)=>{let l=!1,u=t;for(;--u>=0&&n[u]==="\\";)l=!l;return l?"|":" |"}),w=m.split(se.splitPipe),k=0;if(w[0].trim()||w.shift(),w.length>0&&!((M=w.at(-1))!=null&&M.trim())&&w.pop(),o)if(w.length>o)w.splice(o);else for(;w.length<o;)w.push("");for(;k<w.length;k++)w[k]=w[k].trim().replace(se.slashPipe,"|");return w}function xe(x,o,m){let w=x.length;if(w===0)return"";let k=0;for(;k<w&&x.charAt(w-k-1)===o;)k++;return x.slice(0,w-k)}function gs(x,o){if(x.indexOf(o[1])===-1)return-1;let m=0;for(let w=0;w<x.length;w++)if(x[w]==="\\")w++;else if(x[w]===o[0])m++;else if(x[w]===o[1]&&(m--,m<0))return w;return m>0?-2:-1}function ht(x,o,m,w,k){let M=o.href,D=o.title||null,t=x[1].replace(k.other.outputLinkReplace,"$1");w.state.inLink=!0;let n={type:x[0].charAt(0)==="!"?"image":"link",raw:m,href:M,title:D,text:t,tokens:w.inlineTokens(t)};return w.state.inLink=!1,n}function ps(x,o,m){let w=x.match(m.other.indentCodeCompensation);if(w===null)return o;let k=w[1];return o.split(`
`).map(M=>{let D=M.match(m.other.beginningSpace);if(D===null)return M;let[t]=D;return t.length>=k.length?M.slice(k.length):M}).join(`
`)}var Oe=class{constructor(x){Y(this,"options");Y(this,"rules");Y(this,"lexer");this.options=x||pe}space(x){let o=this.rules.block.newline.exec(x);if(o&&o[0].length>0)return{type:"space",raw:o[0]}}code(x){let o=this.rules.block.code.exec(x);if(o){let m=o[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:o[0],codeBlockStyle:"indented",text:this.options.pedantic?m:xe(m,`
`)}}}fences(x){let o=this.rules.block.fences.exec(x);if(o){let m=o[0],w=ps(m,o[3]||"",this.rules);return{type:"code",raw:m,lang:o[2]?o[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):o[2],text:w}}}heading(x){let o=this.rules.block.heading.exec(x);if(o){let m=o[2].trim();if(this.rules.other.endingHash.test(m)){let w=xe(m,"#");(this.options.pedantic||!w||this.rules.other.endingSpaceChar.test(w))&&(m=w.trim())}return{type:"heading",raw:o[0],depth:o[1].length,text:m,tokens:this.lexer.inline(m)}}}hr(x){let o=this.rules.block.hr.exec(x);if(o)return{type:"hr",raw:xe(o[0],`
`)}}blockquote(x){let o=this.rules.block.blockquote.exec(x);if(o){let m=xe(o[0],`
`).split(`
`),w="",k="",M=[];for(;m.length>0;){let D=!1,t=[],n;for(n=0;n<m.length;n++)if(this.rules.other.blockquoteStart.test(m[n]))t.push(m[n]),D=!0;else if(!D)t.push(m[n]);else break;m=m.slice(n);let l=t.join(`
`),u=l.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");w=w?`${w}
${l}`:l,k=k?`${k}
${u}`:u;let a=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,M,!0),this.lexer.state.top=a,m.length===0)break;let d=M.at(-1);if((d==null?void 0:d.type)==="code")break;if((d==null?void 0:d.type)==="blockquote"){let g=d,p=g.raw+`
`+m.join(`
`),_=this.blockquote(p);M[M.length-1]=_,w=w.substring(0,w.length-g.raw.length)+_.raw,k=k.substring(0,k.length-g.text.length)+_.text;break}else if((d==null?void 0:d.type)==="list"){let g=d,p=g.raw+`
`+m.join(`
`),_=this.list(p);M[M.length-1]=_,w=w.substring(0,w.length-d.raw.length)+_.raw,k=k.substring(0,k.length-g.raw.length)+_.raw,m=p.substring(M.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:w,tokens:M,text:k}}}list(x){let o=this.rules.block.list.exec(x);if(o){let m=o[1].trim(),w=m.length>1,k={type:"list",raw:"",ordered:w,start:w?+m.slice(0,-1):"",loose:!1,items:[]};m=w?`\\d{1,9}\\${m.slice(-1)}`:`\\${m}`,this.options.pedantic&&(m=w?m:"[*+-]");let M=this.rules.other.listItemRegex(m),D=!1;for(;x;){let n=!1,l="",u="";if(!(o=M.exec(x))||this.rules.block.hr.test(x))break;l=o[0],x=x.substring(l.length);let a=o[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,i=>" ".repeat(3*i.length)),d=x.split(`
`,1)[0],g=!a.trim(),p=0;if(this.options.pedantic?(p=2,u=a.trimStart()):g?p=o[1].length+1:(p=o[2].search(this.rules.other.nonSpaceChar),p=p>4?1:p,u=a.slice(p),p+=o[1].length),g&&this.rules.other.blankLine.test(d)&&(l+=d+`
`,x=x.substring(d.length+1),n=!0),!n){let i=this.rules.other.nextBulletRegex(p),s=this.rules.other.hrRegex(p),r=this.rules.other.fencesBeginRegex(p),h=this.rules.other.headingBeginRegex(p),f=this.rules.other.htmlBeginRegex(p);for(;x;){let v=x.split(`
`,1)[0],b;if(d=v,this.options.pedantic?(d=d.replace(this.rules.other.listReplaceNesting,"  "),b=d):b=d.replace(this.rules.other.tabCharGlobal,"    "),r.test(d)||h.test(d)||f.test(d)||i.test(d)||s.test(d))break;if(b.search(this.rules.other.nonSpaceChar)>=p||!d.trim())u+=`
`+b.slice(p);else{if(g||a.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||r.test(a)||h.test(a)||s.test(a))break;u+=`
`+d}!g&&!d.trim()&&(g=!0),l+=v+`
`,x=x.substring(v.length+1),a=b.slice(p)}}k.loose||(D?k.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(D=!0));let _=null,e;this.options.gfm&&(_=this.rules.other.listIsTask.exec(u),_&&(e=_[0]!=="[ ] ",u=u.replace(this.rules.other.listReplaceTask,""))),k.items.push({type:"list_item",raw:l,task:!!_,checked:e,loose:!1,text:u,tokens:[]}),k.raw+=l}let t=k.items.at(-1);if(t)t.raw=t.raw.trimEnd(),t.text=t.text.trimEnd();else return;k.raw=k.raw.trimEnd();for(let n=0;n<k.items.length;n++)if(this.lexer.state.top=!1,k.items[n].tokens=this.lexer.blockTokens(k.items[n].text,[]),!k.loose){let l=k.items[n].tokens.filter(a=>a.type==="space"),u=l.length>0&&l.some(a=>this.rules.other.anyLine.test(a.raw));k.loose=u}if(k.loose)for(let n=0;n<k.items.length;n++)k.items[n].loose=!0;return k}}html(x){let o=this.rules.block.html.exec(x);if(o)return{type:"html",block:!0,raw:o[0],pre:o[1]==="pre"||o[1]==="script"||o[1]==="style",text:o[0]}}def(x){let o=this.rules.block.def.exec(x);if(o){let m=o[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),w=o[2]?o[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",k=o[3]?o[3].substring(1,o[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):o[3];return{type:"def",tag:m,raw:o[0],href:w,title:k}}}table(x){var D;let o=this.rules.block.table.exec(x);if(!o||!this.rules.other.tableDelimiter.test(o[2]))return;let m=lt(o[1]),w=o[2].replace(this.rules.other.tableAlignChars,"").split("|"),k=(D=o[3])!=null&&D.trim()?o[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],M={type:"table",raw:o[0],header:[],align:[],rows:[]};if(m.length===w.length){for(let t of w)this.rules.other.tableAlignRight.test(t)?M.align.push("right"):this.rules.other.tableAlignCenter.test(t)?M.align.push("center"):this.rules.other.tableAlignLeft.test(t)?M.align.push("left"):M.align.push(null);for(let t=0;t<m.length;t++)M.header.push({text:m[t],tokens:this.lexer.inline(m[t]),header:!0,align:M.align[t]});for(let t of k)M.rows.push(lt(t,M.header.length).map((n,l)=>({text:n,tokens:this.lexer.inline(n),header:!1,align:M.align[l]})));return M}}lheading(x){let o=this.rules.block.lheading.exec(x);if(o)return{type:"heading",raw:o[0],depth:o[2].charAt(0)==="="?1:2,text:o[1],tokens:this.lexer.inline(o[1])}}paragraph(x){let o=this.rules.block.paragraph.exec(x);if(o){let m=o[1].charAt(o[1].length-1)===`
`?o[1].slice(0,-1):o[1];return{type:"paragraph",raw:o[0],text:m,tokens:this.lexer.inline(m)}}}text(x){let o=this.rules.block.text.exec(x);if(o)return{type:"text",raw:o[0],text:o[0],tokens:this.lexer.inline(o[0])}}escape(x){let o=this.rules.inline.escape.exec(x);if(o)return{type:"escape",raw:o[0],text:o[1]}}tag(x){let o=this.rules.inline.tag.exec(x);if(o)return!this.lexer.state.inLink&&this.rules.other.startATag.test(o[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(o[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(o[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(o[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:o[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:o[0]}}link(x){let o=this.rules.inline.link.exec(x);if(o){let m=o[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(m)){if(!this.rules.other.endAngleBracket.test(m))return;let M=xe(m.slice(0,-1),"\\");if((m.length-M.length)%2===0)return}else{let M=gs(o[2],"()");if(M===-2)return;if(M>-1){let D=(o[0].indexOf("!")===0?5:4)+o[1].length+M;o[2]=o[2].substring(0,M),o[0]=o[0].substring(0,D).trim(),o[3]=""}}let w=o[2],k="";if(this.options.pedantic){let M=this.rules.other.pedanticHrefTitle.exec(w);M&&(w=M[1],k=M[3])}else k=o[3]?o[3].slice(1,-1):"";return w=w.trim(),this.rules.other.startAngleBracket.test(w)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(m)?w=w.slice(1):w=w.slice(1,-1)),ht(o,{href:w&&w.replace(this.rules.inline.anyPunctuation,"$1"),title:k&&k.replace(this.rules.inline.anyPunctuation,"$1")},o[0],this.lexer,this.rules)}}reflink(x,o){let m;if((m=this.rules.inline.reflink.exec(x))||(m=this.rules.inline.nolink.exec(x))){let w=(m[2]||m[1]).replace(this.rules.other.multipleSpaceGlobal," "),k=o[w.toLowerCase()];if(!k){let M=m[0].charAt(0);return{type:"text",raw:M,text:M}}return ht(m,k,m[0],this.lexer,this.rules)}}emStrong(x,o,m=""){let w=this.rules.inline.emStrongLDelim.exec(x);if(!(!w||w[3]&&m.match(this.rules.other.unicodeAlphaNumeric))&&(!(w[1]||w[2])||!m||this.rules.inline.punctuation.exec(m))){let k=[...w[0]].length-1,M,D,t=k,n=0,l=w[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,o=o.slice(-1*x.length+k);(w=l.exec(o))!=null;){if(M=w[1]||w[2]||w[3]||w[4]||w[5]||w[6],!M)continue;if(D=[...M].length,w[3]||w[4]){t+=D;continue}else if((w[5]||w[6])&&k%3&&!((k+D)%3)){n+=D;continue}if(t-=D,t>0)continue;D=Math.min(D,D+t+n);let u=[...w[0]][0].length,a=x.slice(0,k+w.index+u+D);if(Math.min(k,D)%2){let g=a.slice(1,-1);return{type:"em",raw:a,text:g,tokens:this.lexer.inlineTokens(g)}}let d=a.slice(2,-2);return{type:"strong",raw:a,text:d,tokens:this.lexer.inlineTokens(d)}}}}codespan(x){let o=this.rules.inline.code.exec(x);if(o){let m=o[2].replace(this.rules.other.newLineCharGlobal," "),w=this.rules.other.nonSpaceChar.test(m),k=this.rules.other.startingSpaceChar.test(m)&&this.rules.other.endingSpaceChar.test(m);return w&&k&&(m=m.substring(1,m.length-1)),{type:"codespan",raw:o[0],text:m}}}br(x){let o=this.rules.inline.br.exec(x);if(o)return{type:"br",raw:o[0]}}del(x){let o=this.rules.inline.del.exec(x);if(o)return{type:"del",raw:o[0],text:o[2],tokens:this.lexer.inlineTokens(o[2])}}autolink(x){let o=this.rules.inline.autolink.exec(x);if(o){let m,w;return o[2]==="@"?(m=o[1],w="mailto:"+m):(m=o[1],w=m),{type:"link",raw:o[0],text:m,href:w,tokens:[{type:"text",raw:m,text:m}]}}}url(x){var m;let o;if(o=this.rules.inline.url.exec(x)){let w,k;if(o[2]==="@")w=o[0],k="mailto:"+w;else{let M;do M=o[0],o[0]=((m=this.rules.inline._backpedal.exec(o[0]))==null?void 0:m[0])??"";while(M!==o[0]);w=o[0],o[1]==="www."?k="http://"+o[0]:k=o[0]}return{type:"link",raw:o[0],text:w,href:k,tokens:[{type:"text",raw:w,text:w}]}}}inlineText(x){let o=this.rules.inline.text.exec(x);if(o){let m=this.lexer.state.inRawBlock;return{type:"text",raw:o[0],text:o[0],escaped:m}}}},ce=class Ve{constructor(o){Y(this,"tokens");Y(this,"options");Y(this,"state");Y(this,"tokenizer");Y(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=o||pe,this.options.tokenizer=this.options.tokenizer||new Oe,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let m={other:se,block:Be.normal,inline:ke.normal};this.options.pedantic?(m.block=Be.pedantic,m.inline=ke.pedantic):this.options.gfm&&(m.block=Be.gfm,this.options.breaks?m.inline=ke.breaks:m.inline=ke.gfm),this.tokenizer.rules=m}static get rules(){return{block:Be,inline:ke}}static lex(o,m){return new Ve(m).lex(o)}static lexInline(o,m){return new Ve(m).inlineTokens(o)}lex(o){o=o.replace(se.carriageReturn,`
`),this.blockTokens(o,this.tokens);for(let m=0;m<this.inlineQueue.length;m++){let w=this.inlineQueue[m];this.inlineTokens(w.src,w.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(o,m=[],w=!1){var k,M,D;for(this.options.pedantic&&(o=o.replace(se.tabCharGlobal,"    ").replace(se.spaceLine,""));o;){let t;if((M=(k=this.options.extensions)==null?void 0:k.block)!=null&&M.some(l=>(t=l.call({lexer:this},o,m))?(o=o.substring(t.raw.length),m.push(t),!0):!1))continue;if(t=this.tokenizer.space(o)){o=o.substring(t.raw.length);let l=m.at(-1);t.raw.length===1&&l!==void 0?l.raw+=`
`:m.push(t);continue}if(t=this.tokenizer.code(o)){o=o.substring(t.raw.length);let l=m.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+t.raw,l.text+=`
`+t.text,this.inlineQueue.at(-1).src=l.text):m.push(t);continue}if(t=this.tokenizer.fences(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.heading(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.hr(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.blockquote(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.list(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.html(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.def(o)){o=o.substring(t.raw.length);let l=m.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+t.raw,l.text+=`
`+t.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[t.tag]||(this.tokens.links[t.tag]={href:t.href,title:t.title});continue}if(t=this.tokenizer.table(o)){o=o.substring(t.raw.length),m.push(t);continue}if(t=this.tokenizer.lheading(o)){o=o.substring(t.raw.length),m.push(t);continue}let n=o;if((D=this.options.extensions)!=null&&D.startBlock){let l=1/0,u=o.slice(1),a;this.options.extensions.startBlock.forEach(d=>{a=d.call({lexer:this},u),typeof a=="number"&&a>=0&&(l=Math.min(l,a))}),l<1/0&&l>=0&&(n=o.substring(0,l+1))}if(this.state.top&&(t=this.tokenizer.paragraph(n))){let l=m.at(-1);w&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+t.raw,l.text+=`
`+t.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):m.push(t),w=n.length!==o.length,o=o.substring(t.raw.length);continue}if(t=this.tokenizer.text(o)){o=o.substring(t.raw.length);let l=m.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+t.raw,l.text+=`
`+t.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):m.push(t);continue}if(o){let l="Infinite loop on byte: "+o.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,m}inline(o,m=[]){return this.inlineQueue.push({src:o,tokens:m}),m}inlineTokens(o,m=[]){var t,n,l;let w=o,k=null;if(this.tokens.links){let u=Object.keys(this.tokens.links);if(u.length>0)for(;(k=this.tokenizer.rules.inline.reflinkSearch.exec(w))!=null;)u.includes(k[0].slice(k[0].lastIndexOf("[")+1,-1))&&(w=w.slice(0,k.index)+"["+"a".repeat(k[0].length-2)+"]"+w.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(k=this.tokenizer.rules.inline.anyPunctuation.exec(w))!=null;)w=w.slice(0,k.index)+"++"+w.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(k=this.tokenizer.rules.inline.blockSkip.exec(w))!=null;)w=w.slice(0,k.index)+"["+"a".repeat(k[0].length-2)+"]"+w.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let M=!1,D="";for(;o;){M||(D=""),M=!1;let u;if((n=(t=this.options.extensions)==null?void 0:t.inline)!=null&&n.some(d=>(u=d.call({lexer:this},o,m))?(o=o.substring(u.raw.length),m.push(u),!0):!1))continue;if(u=this.tokenizer.escape(o)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.tag(o)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.link(o)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.reflink(o,this.tokens.links)){o=o.substring(u.raw.length);let d=m.at(-1);u.type==="text"&&(d==null?void 0:d.type)==="text"?(d.raw+=u.raw,d.text+=u.text):m.push(u);continue}if(u=this.tokenizer.emStrong(o,w,D)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.codespan(o)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.br(o)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.del(o)){o=o.substring(u.raw.length),m.push(u);continue}if(u=this.tokenizer.autolink(o)){o=o.substring(u.raw.length),m.push(u);continue}if(!this.state.inLink&&(u=this.tokenizer.url(o))){o=o.substring(u.raw.length),m.push(u);continue}let a=o;if((l=this.options.extensions)!=null&&l.startInline){let d=1/0,g=o.slice(1),p;this.options.extensions.startInline.forEach(_=>{p=_.call({lexer:this},g),typeof p=="number"&&p>=0&&(d=Math.min(d,p))}),d<1/0&&d>=0&&(a=o.substring(0,d+1))}if(u=this.tokenizer.inlineText(a)){o=o.substring(u.raw.length),u.raw.slice(-1)!=="_"&&(D=u.raw.slice(-1)),M=!0;let d=m.at(-1);(d==null?void 0:d.type)==="text"?(d.raw+=u.raw,d.text+=u.text):m.push(u);continue}if(o){let d="Infinite loop on byte: "+o.charCodeAt(0);if(this.options.silent){console.error(d);break}else throw new Error(d)}}return m}},He=class{constructor(x){Y(this,"options");Y(this,"parser");this.options=x||pe}space(x){return""}code({text:x,lang:o,escaped:m}){var M;let w=(M=(o||"").match(se.notSpaceStart))==null?void 0:M[0],k=x.replace(se.endingNewline,"")+`
`;return w?'<pre><code class="language-'+he(w)+'">'+(m?k:he(k,!0))+`</code></pre>
`:"<pre><code>"+(m?k:he(k,!0))+`</code></pre>
`}blockquote({tokens:x}){return`<blockquote>
${this.parser.parse(x)}</blockquote>
`}html({text:x}){return x}heading({tokens:x,depth:o}){return`<h${o}>${this.parser.parseInline(x)}</h${o}>
`}hr(x){return`<hr>
`}list(x){let o=x.ordered,m=x.start,w="";for(let D=0;D<x.items.length;D++){let t=x.items[D];w+=this.listitem(t)}let k=o?"ol":"ul",M=o&&m!==1?' start="'+m+'"':"";return"<"+k+M+`>
`+w+"</"+k+`>
`}listitem(x){var m;let o="";if(x.task){let w=this.checkbox({checked:!!x.checked});x.loose?((m=x.tokens[0])==null?void 0:m.type)==="paragraph"?(x.tokens[0].text=w+" "+x.tokens[0].text,x.tokens[0].tokens&&x.tokens[0].tokens.length>0&&x.tokens[0].tokens[0].type==="text"&&(x.tokens[0].tokens[0].text=w+" "+he(x.tokens[0].tokens[0].text),x.tokens[0].tokens[0].escaped=!0)):x.tokens.unshift({type:"text",raw:w+" ",text:w+" ",escaped:!0}):o+=w+" "}return o+=this.parser.parse(x.tokens,!!x.loose),`<li>${o}</li>
`}checkbox({checked:x}){return"<input "+(x?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:x}){return`<p>${this.parser.parseInline(x)}</p>
`}table(x){let o="",m="";for(let k=0;k<x.header.length;k++)m+=this.tablecell(x.header[k]);o+=this.tablerow({text:m});let w="";for(let k=0;k<x.rows.length;k++){let M=x.rows[k];m="";for(let D=0;D<M.length;D++)m+=this.tablecell(M[D]);w+=this.tablerow({text:m})}return w&&(w=`<tbody>${w}</tbody>`),`<table>
<thead>
`+o+`</thead>
`+w+`</table>
`}tablerow({text:x}){return`<tr>
${x}</tr>
`}tablecell(x){let o=this.parser.parseInline(x.tokens),m=x.header?"th":"td";return(x.align?`<${m} align="${x.align}">`:`<${m}>`)+o+`</${m}>
`}strong({tokens:x}){return`<strong>${this.parser.parseInline(x)}</strong>`}em({tokens:x}){return`<em>${this.parser.parseInline(x)}</em>`}codespan({text:x}){return`<code>${he(x,!0)}</code>`}br(x){return"<br>"}del({tokens:x}){return`<del>${this.parser.parseInline(x)}</del>`}link({href:x,title:o,tokens:m}){let w=this.parser.parseInline(m),k=at(x);if(k===null)return w;x=k;let M='<a href="'+x+'"';return o&&(M+=' title="'+he(o)+'"'),M+=">"+w+"</a>",M}image({href:x,title:o,text:m,tokens:w}){w&&(m=this.parser.parseInline(w,this.parser.textRenderer));let k=at(x);if(k===null)return he(m);x=k;let M=`<img src="${x}" alt="${m}"`;return o&&(M+=` title="${he(o)}"`),M+=">",M}text(x){return"tokens"in x&&x.tokens?this.parser.parseInline(x.tokens):"escaped"in x&&x.escaped?x.text:he(x.text)}},it=class{strong({text:x}){return x}em({text:x}){return x}codespan({text:x}){return x}del({text:x}){return x}html({text:x}){return x}text({text:x}){return x}link({text:x}){return""+x}image({text:x}){return""+x}br(){return""}},de=class Xe{constructor(o){Y(this,"options");Y(this,"renderer");Y(this,"textRenderer");this.options=o||pe,this.options.renderer=this.options.renderer||new He,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new it}static parse(o,m){return new Xe(m).parse(o)}static parseInline(o,m){return new Xe(m).parseInline(o)}parse(o,m=!0){var k,M;let w="";for(let D=0;D<o.length;D++){let t=o[D];if((M=(k=this.options.extensions)==null?void 0:k.renderers)!=null&&M[t.type]){let l=t,u=this.options.extensions.renderers[l.type].call({parser:this},l);if(u!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){w+=u||"";continue}}let n=t;switch(n.type){case"space":{w+=this.renderer.space(n);continue}case"hr":{w+=this.renderer.hr(n);continue}case"heading":{w+=this.renderer.heading(n);continue}case"code":{w+=this.renderer.code(n);continue}case"table":{w+=this.renderer.table(n);continue}case"blockquote":{w+=this.renderer.blockquote(n);continue}case"list":{w+=this.renderer.list(n);continue}case"html":{w+=this.renderer.html(n);continue}case"paragraph":{w+=this.renderer.paragraph(n);continue}case"text":{let l=n,u=this.renderer.text(l);for(;D+1<o.length&&o[D+1].type==="text";)l=o[++D],u+=`
`+this.renderer.text(l);m?w+=this.renderer.paragraph({type:"paragraph",raw:u,text:u,tokens:[{type:"text",raw:u,text:u,escaped:!0}]}):w+=u;continue}default:{let l='Token with "'+n.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return w}parseInline(o,m=this.renderer){var k,M;let w="";for(let D=0;D<o.length;D++){let t=o[D];if((M=(k=this.options.extensions)==null?void 0:k.renderers)!=null&&M[t.type]){let l=this.options.extensions.renderers[t.type].call({parser:this},t);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(t.type)){w+=l||"";continue}}let n=t;switch(n.type){case"escape":{w+=m.text(n);break}case"html":{w+=m.html(n);break}case"link":{w+=m.link(n);break}case"image":{w+=m.image(n);break}case"strong":{w+=m.strong(n);break}case"em":{w+=m.em(n);break}case"codespan":{w+=m.codespan(n);break}case"br":{w+=m.br(n);break}case"del":{w+=m.del(n);break}case"text":{w+=m.text(n);break}default:{let l='Token with "'+n.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return w}},Ke,Me=(Ke=class{constructor(x){Y(this,"options");Y(this,"block");this.options=x||pe}preprocess(x){return x}postprocess(x){return x}processAllTokens(x){return x}provideLexer(){return this.block?ce.lex:ce.lexInline}provideParser(){return this.block?de.parse:de.parseInline}},Y(Ke,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Ke),vs=class{constructor(...x){Y(this,"defaults",Ge());Y(this,"options",this.setOptions);Y(this,"parse",this.parseMarkdown(!0));Y(this,"parseInline",this.parseMarkdown(!1));Y(this,"Parser",de);Y(this,"Renderer",He);Y(this,"TextRenderer",it);Y(this,"Lexer",ce);Y(this,"Tokenizer",Oe);Y(this,"Hooks",Me);this.use(...x)}walkTokens(x,o){var w,k;let m=[];for(let M of x)switch(m=m.concat(o.call(this,M)),M.type){case"table":{let D=M;for(let t of D.header)m=m.concat(this.walkTokens(t.tokens,o));for(let t of D.rows)for(let n of t)m=m.concat(this.walkTokens(n.tokens,o));break}case"list":{let D=M;m=m.concat(this.walkTokens(D.items,o));break}default:{let D=M;(k=(w=this.defaults.extensions)==null?void 0:w.childTokens)!=null&&k[D.type]?this.defaults.extensions.childTokens[D.type].forEach(t=>{let n=D[t].flat(1/0);m=m.concat(this.walkTokens(n,o))}):D.tokens&&(m=m.concat(this.walkTokens(D.tokens,o)))}}return m}use(...x){let o=this.defaults.extensions||{renderers:{},childTokens:{}};return x.forEach(m=>{let w={...m};if(w.async=this.defaults.async||w.async||!1,m.extensions&&(m.extensions.forEach(k=>{if(!k.name)throw new Error("extension name required");if("renderer"in k){let M=o.renderers[k.name];M?o.renderers[k.name]=function(...D){let t=k.renderer.apply(this,D);return t===!1&&(t=M.apply(this,D)),t}:o.renderers[k.name]=k.renderer}if("tokenizer"in k){if(!k.level||k.level!=="block"&&k.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let M=o[k.level];M?M.unshift(k.tokenizer):o[k.level]=[k.tokenizer],k.start&&(k.level==="block"?o.startBlock?o.startBlock.push(k.start):o.startBlock=[k.start]:k.level==="inline"&&(o.startInline?o.startInline.push(k.start):o.startInline=[k.start]))}"childTokens"in k&&k.childTokens&&(o.childTokens[k.name]=k.childTokens)}),w.extensions=o),m.renderer){let k=this.defaults.renderer||new He(this.defaults);for(let M in m.renderer){if(!(M in k))throw new Error(`renderer '${M}' does not exist`);if(["options","parser"].includes(M))continue;let D=M,t=m.renderer[D],n=k[D];k[D]=(...l)=>{let u=t.apply(k,l);return u===!1&&(u=n.apply(k,l)),u||""}}w.renderer=k}if(m.tokenizer){let k=this.defaults.tokenizer||new Oe(this.defaults);for(let M in m.tokenizer){if(!(M in k))throw new Error(`tokenizer '${M}' does not exist`);if(["options","rules","lexer"].includes(M))continue;let D=M,t=m.tokenizer[D],n=k[D];k[D]=(...l)=>{let u=t.apply(k,l);return u===!1&&(u=n.apply(k,l)),u}}w.tokenizer=k}if(m.hooks){let k=this.defaults.hooks||new Me;for(let M in m.hooks){if(!(M in k))throw new Error(`hook '${M}' does not exist`);if(["options","block"].includes(M))continue;let D=M,t=m.hooks[D],n=k[D];Me.passThroughHooks.has(M)?k[D]=l=>{if(this.defaults.async)return Promise.resolve(t.call(k,l)).then(a=>n.call(k,a));let u=t.call(k,l);return n.call(k,u)}:k[D]=(...l)=>{let u=t.apply(k,l);return u===!1&&(u=n.apply(k,l)),u}}w.hooks=k}if(m.walkTokens){let k=this.defaults.walkTokens,M=m.walkTokens;w.walkTokens=function(D){let t=[];return t.push(M.call(this,D)),k&&(t=t.concat(k.call(this,D))),t}}this.defaults={...this.defaults,...w}}),this}setOptions(x){return this.defaults={...this.defaults,...x},this}lexer(x,o){return ce.lex(x,o??this.defaults)}parser(x,o){return de.parse(x,o??this.defaults)}parseMarkdown(x){return(o,m)=>{let w={...m},k={...this.defaults,...w},M=this.onError(!!k.silent,!!k.async);if(this.defaults.async===!0&&w.async===!1)return M(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof o>"u"||o===null)return M(new Error("marked(): input parameter is undefined or null"));if(typeof o!="string")return M(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(o)+", string expected"));k.hooks&&(k.hooks.options=k,k.hooks.block=x);let D=k.hooks?k.hooks.provideLexer():x?ce.lex:ce.lexInline,t=k.hooks?k.hooks.provideParser():x?de.parse:de.parseInline;if(k.async)return Promise.resolve(k.hooks?k.hooks.preprocess(o):o).then(n=>D(n,k)).then(n=>k.hooks?k.hooks.processAllTokens(n):n).then(n=>k.walkTokens?Promise.all(this.walkTokens(n,k.walkTokens)).then(()=>n):n).then(n=>t(n,k)).then(n=>k.hooks?k.hooks.postprocess(n):n).catch(M);try{k.hooks&&(o=k.hooks.preprocess(o));let n=D(o,k);k.hooks&&(n=k.hooks.processAllTokens(n)),k.walkTokens&&this.walkTokens(n,k.walkTokens);let l=t(n,k);return k.hooks&&(l=k.hooks.postprocess(l)),l}catch(n){return M(n)}}}onError(x,o){return m=>{if(m.message+=`
Please report this to https://github.com/markedjs/marked.`,x){let w="<p>An error occurred:</p><pre>"+he(m.message+"",!0)+"</pre>";return o?Promise.resolve(w):w}if(o)return Promise.reject(m);throw m}}},ge=new vs;function G(x,o){return ge.parse(x,o)}G.options=G.setOptions=function(x){return ge.setOptions(x),G.defaults=ge.defaults,pt(G.defaults),G};G.getDefaults=Ge;G.defaults=pe;G.use=function(...x){return ge.use(...x),G.defaults=ge.defaults,pt(G.defaults),G};G.walkTokens=function(x,o){return ge.walkTokens(x,o)};G.parseInline=ge.parseInline;G.Parser=de;G.parser=de.parse;G.Renderer=He;G.TextRenderer=it;G.Lexer=ce;G.lexer=ce.lex;G.Tokenizer=Oe;G.Hooks=Me;G.parse=G;G.options;G.setOptions;G.use;G.walkTokens;G.parseInline;de.parse;ce.lex;class ms{constructor(){this.config={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7},this.sessions=new Map,this.isReady=!1,this.systemPrompt=`You are a helpful AI assistant for a web terminal application. You can help users with:

1. **Terminal Commands**: Explain, suggest, and help execute terminal commands
2. **File Operations**: Help with file management, navigation, and manipulation  
3. **System Administration**: Provide guidance on system tasks and troubleshooting
4. **Programming**: Help with coding tasks, debugging, and development workflows

You have access to a web terminal where you can suggest commands for the user to execute.
When suggesting commands, always:
- Explain what the command does
- Mention any potential risks or side effects
- Provide context about when and why to use it

You can suggest commands by mentioning them in your response. The system may automatically execute safe commands you suggest.

Current context: Web terminal environment with xterm.js frontend.`}async initialize(o={}){if(this.config={...this.config,...o},!this.config.apiKey)return console.warn("AI Agent: No API key provided. Please set API key before using."),!1;try{return await this.testConnection(),this.isReady=!0,console.log("Frontend AI Agent initialized successfully"),!0}catch(m){return console.error("Failed to initialize AI Agent:",m),this.isReady=!1,!1}}async testConnection(){const o=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model,messages:[{role:"user",content:"Hello"}],max_tokens:10})});if(!o.ok)throw new Error(`API test failed: ${o.status} ${o.statusText}`);return!0}updateConfig(o){this.config={...this.config,...o},console.log("AI Agent configuration updated:",this.config)}getConfig(){const{apiKey:o,...m}=this.config;return{...m,apiKey:o?"***":""}}isAgentReady(){return this.isReady&&this.config.apiKey}async processMessage(o,m={}){var D;if(!this.isAgentReady())throw new Error("AI Agent not ready. Please check configuration.");const w=m.sessionId||"default",k=m.context||{},M=this.getSession(w);M.messages.push({role:"user",content:o,timestamp:new Date().toISOString()});try{const t=[{role:"system",content:this.systemPrompt},...M.messages.map(d=>({role:d.role,content:d.content}))];Object.keys(k).length>0&&t.push({role:"system",content:`Current context: ${JSON.stringify(k,null,2)}`});const n=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model,messages:t,max_tokens:this.config.maxTokens,temperature:this.config.temperature})});if(!n.ok)throw new Error(`API request failed: ${n.status} ${n.statusText}`);const l=await n.json(),u=l.choices[0].message.content;M.messages.push({role:"assistant",content:u,timestamp:new Date().toISOString()});const a=this.extractCommandsFromResponse(u);return{content:u,commands:a,sessionId:w,timestamp:new Date().toISOString(),tokensUsed:((D=l.usage)==null?void 0:D.total_tokens)||0}}catch(t){throw console.error("Error processing message:",t),t}}extractCommandsFromResponse(o){const m=[],w=[/```(?:bash|shell|sh)?\s*\n([^`]+)\n```/gi,/`([a-z]+(?:\s+[-\w\.\/]+)*)`/gi];for(const M of w){let D;for(;(D=M.exec(o))!==null;){const t=D[1].trim();this.isValidCommand(t)&&m.push({type:"command",command:t,execute:!0})}}return m.filter((M,D,t)=>D===t.findIndex(n=>n.command===M.command)).slice(0,3)}isValidCommand(o){const m=o.split(" ")[0];return["ls","pwd","whoami","date","uptime","uname","which","cat","head","tail","grep","find","wc","sort","uniq","ps","top","df","du","free","echo","history","clear"].includes(m)&&o.length<100&&!o.includes("&&")&&!o.includes("||")&&!o.includes(";")}getSession(o){this.sessions.has(o)||this.sessions.set(o,{id:o,messages:[],createdAt:new Date().toISOString(),lastActivity:new Date().toISOString()});const m=this.sessions.get(o);return m.lastActivity=new Date().toISOString(),m}getSessionHistory(o){const m=this.sessions.get(o);return m?m.messages:[]}clearSession(o){this.sessions.delete(o)}getAllSessions(){return Array.from(this.sessions.keys())}getStats(){return{ready:this.isReady,model:this.config.model,endpoint:this.config.endpoint,activeSessions:this.sessions.size,totalMessages:Array.from(this.sessions.values()).reduce((o,m)=>o+m.messages.length,0)}}}class Ss{constructor(){this.storageKey="ai-agent-config",this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7}}load(){try{const o=localStorage.getItem(this.storageKey);if(o)return{...this.defaultConfig,...JSON.parse(o)}}catch(o){console.warn("Failed to load AI agent config from localStorage:",o)}return{...this.defaultConfig}}save(o){try{return localStorage.setItem(this.storageKey,JSON.stringify(o)),!0}catch(m){return console.error("Failed to save AI agent config to localStorage:",m),!1}}reset(){try{return localStorage.removeItem(this.storageKey),{...this.defaultConfig}}catch(o){return console.error("Failed to reset AI agent config:",o),{...this.defaultConfig}}}validate(o){const m=[];return(!o.endpoint||!o.endpoint.startsWith("http"))&&m.push("Invalid endpoint URL"),(!o.model||o.model.trim()==="")&&m.push("Model ID is required"),(!o.apiKey||o.apiKey.trim()==="")&&m.push("API Key is required"),o.maxTokens&&(o.maxTokens<1||o.maxTokens>4e3)&&m.push("Max tokens must be between 1 and 4000"),o.temperature&&(o.temperature<0||o.temperature>2)&&m.push("Temperature must be between 0 and 2"),{valid:m.length===0,errors:m}}}const _e=new Ss,oe=new ms;class bs{constructor(){this.isVisible=!1,this.messages=[],this.dialog=null,this.messagesContainer=null,this.input=null,this.sendBtn=null,this.closeBtn=null,this.minimizeBtn=null,this.maximizeBtn=null,this.headerElement=null,this.aiBtn=null,this.sessionId=this.generateSessionId(),this.useFrontendAgent=!0,this.isMinimized=!1,this.isMaximized=!1,this.isDragging=!1,this.dragOffset={x:0,y:0},this.originalPosition={top:20,right:20},this.originalSize={width:400,height:500},G.setOptions({breaks:!0,gfm:!0,sanitize:!1})}initialize(){return this.dialog=document.getElementById("aiDialog"),this.messagesContainer=document.getElementById("aiMessages"),this.input=document.getElementById("aiInput"),this.sendBtn=document.getElementById("aiSend"),this.closeBtn=document.getElementById("aiDialogClose"),this.minimizeBtn=document.getElementById("aiDialogMinimize"),this.maximizeBtn=document.getElementById("aiDialogMaximize"),this.headerElement=document.getElementById("aiDialogHeader"),this.aiBtn=document.getElementById("aiBtn"),!this.dialog||!this.messagesContainer||!this.input||!this.sendBtn||!this.closeBtn||!this.minimizeBtn||!this.maximizeBtn||!this.headerElement||!this.aiBtn?(console.error("AI Dialog elements not found"),!1):(this.setupEventListeners(),this.setupAutoResize(),console.log("AI Dialog Service initialized"),!0)}setupEventListeners(){this.aiBtn.addEventListener("click",()=>this.toggleDialog()),this.closeBtn.addEventListener("click",()=>this.hideDialog()),this.minimizeBtn.addEventListener("click",()=>this.toggleMinimize()),this.maximizeBtn.addEventListener("click",()=>this.toggleMaximize()),this.setupDragging(),this.sendBtn.addEventListener("click",()=>this.sendMessage()),this.input.addEventListener("keydown",o=>{o.key==="Enter"&&!o.shiftKey&&(o.preventDefault(),this.sendMessage())}),document.addEventListener("keydown",o=>{o.key==="Escape"&&this.isVisible&&this.hideDialog()})}setupAutoResize(){this.input.addEventListener("input",()=>{this.input.style.height="auto",this.input.style.height=Math.min(this.input.scrollHeight,120)+"px"})}toggleDialog(){this.isVisible?this.hideDialog():this.showDialog()}showDialog(){this.isVisible=!0,this.dialog.classList.remove("ai-dialog-hidden"),this.input.focus(),console.log("AI Dialog shown")}hideDialog(){this.isVisible=!1,this.dialog.classList.add("ai-dialog-hidden"),console.log("AI Dialog hidden")}toggleMinimize(){this.isMinimized=!this.isMinimized,this.isMinimized?(this.dialog.classList.add("minimized"),this.minimizeBtn.textContent="+",this.minimizeBtn.title="Restore"):(this.dialog.classList.remove("minimized"),this.minimizeBtn.textContent="−",this.minimizeBtn.title="Minimize"),console.log("AI Dialog minimized:",this.isMinimized)}toggleMaximize(){this.isMaximized=!this.isMaximized,this.isMaximized?(this.dialog.classList.add("maximized"),this.maximizeBtn.textContent="❐",this.maximizeBtn.title="Restore"):(this.dialog.classList.remove("maximized"),this.maximizeBtn.textContent="□",this.maximizeBtn.title="Maximize"),console.log("AI Dialog maximized:",this.isMaximized)}setupDragging(){this.headerElement.addEventListener("mousedown",o=>{(o.target===this.headerElement||o.target.tagName==="H3")&&this.startDragging(o)}),document.addEventListener("mousemove",o=>{this.isDragging&&this.drag(o)}),document.addEventListener("mouseup",()=>{this.isDragging&&this.stopDragging()})}startDragging(o){if(this.isMaximized)return;this.isDragging=!0,this.dialog.classList.add("dragging");const m=this.dialog.getBoundingClientRect();this.dragOffset.x=o.clientX-m.left,this.dragOffset.y=o.clientY-m.top,o.preventDefault()}drag(o){if(!this.isDragging)return;const m=o.clientX-this.dragOffset.x,w=o.clientY-this.dragOffset.y,k=window.innerWidth-this.dialog.offsetWidth,M=window.innerHeight-this.dialog.offsetHeight,D=Math.max(0,Math.min(m,k)),t=Math.max(0,Math.min(w,M));this.dialog.style.left=D+"px",this.dialog.style.top=t+"px",this.dialog.style.right="auto",this.dialog.style.bottom="auto"}stopDragging(){this.isDragging=!1,this.dialog.classList.remove("dragging")}sendMessage(){debugger;const o=this.input.value.trim();o&&(this.addMessage(o,"user"),this.input.value="",this.input.style.height="auto",this.sendToAIAgent(o))}addMessage(o,m){const w={content:o,sender:m,timestamp:new Date};this.messages.push(w),this.renderMessage(w),this.scrollToBottom()}renderMessage(o){const m=document.createElement("div");m.className=`ai-message ai-message-${o.sender}`;const w=document.createElement("div");w.className="ai-message-content",o.sender==="assistant"?w.innerHTML=G.parse(o.content):w.innerHTML=this.formatUserMessage(o.content),m.appendChild(w),this.messagesContainer.appendChild(m)}formatUserMessage(o){return o.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"<br>")}scrollToBottom(){this.messagesContainer.scrollTop=this.messagesContainer.scrollHeight}async sendToAIAgent(o){this.sendBtn.disabled=!0;try{this.useFrontendAgent?await this.sendToFrontendAgent(o):await this.sendToBackendAgent(o)}catch(m){console.error("Error communicating with AI agent:",m);let w="";m.message.includes("AI Agent not ready")?w=`The AI agent is not configured. Please check the AI settings and ensure you have:

1. **API Key**: A valid OpenAI API key
2. **Endpoint**: The correct API endpoint URL
3. **Model**: A valid model ID

You can configure these in the AI settings. Would you like help with the configuration?`:m.message.includes("API request failed")?w=`I'm having trouble connecting to the AI service. This could be due to:

1. **Invalid API Key**: Please check your API key
2. **Network Issues**: Check your internet connection
3. **API Limits**: You may have exceeded your API quota

Please check your configuration and try again.`:w="Sorry, I encountered an error processing your request. Please try again.",this.addMessage(w,"assistant")}finally{this.sendBtn.disabled=!1}}async sendToFrontendAgent(o){if(!oe.isAgentReady()){const k=_e.load();if(await oe.initialize(k),!oe.isAgentReady())throw new Error("AI Agent not ready. Please configure the AI settings.")}const m=this.getTerminalContext(),w=await oe.processMessage(o,{sessionId:this.sessionId,context:m});if(w.commands&&w.commands.length>0){const k=await this.executeCommandsInTerminal(w.commands);k&&k.length>0&&await this.provideExecutionFeedback(k)}this.addMessage(w.content,"assistant")}async sendToBackendAgent(o){const m=this.getTerminalContext(),w=await fetch(`${this.aiAgentUrl}/chat`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({message:o,sessionId:this.sessionId,context:m,terminalIntegration:!0})});if(!w.ok)throw new Error(`HTTP error! status: ${w.status}`);const k=await w.json();k.success?(k.commands&&k.commands.length>0&&await this.executeCommandsInTerminal(k.commands),this.addMessage(k.response,"assistant")):this.addMessage("Sorry, I encountered an error processing your request.","assistant")}async executeCommandsInTerminal(o){if(!window.xtermService||!window.xtermService.isServiceReady()){console.warn("XTerm service not available for command execution");return}const m=[];for(const w of o)try{console.log("Executing AI-requested command:",w);const k=window.xtermService.getCursorPosition();w.type==="single_key"?await window.xtermService.sendKey(w.key):w.type==="key_combo"?await window.xtermService.sendKeyCombo(w.combination):w.type==="command"?await window.xtermService.sendCommand(w.command,w.execute!==!1):w.type==="commands"&&await window.xtermService.sendCommands(w.commands,w.delay||100),await new Promise(t=>setTimeout(t,500));const M=window.xtermService.getCursorPosition();let D="";M.y>k.y&&(D=window.xtermService.getLineRangeContent(k.y,M.y).join(`
`).trim()),m.push({command:w,output:D,success:!0}),o.length>1&&await new Promise(t=>setTimeout(t,200))}catch(k){console.error("Error executing command in terminal:",k),m.push({command:w,error:k.message,success:!1})}return console.log("AI command execution results:",m),m}async provideExecutionFeedback(o){try{const w=`Command execution results:

${o.map(k=>k.success?`Command executed: ${JSON.stringify(k.command)}
Output: ${k.output||"(no output)"}`:`Command failed: ${JSON.stringify(k.command)}
Error: ${k.error}`).join(`

`)}`;console.log("Providing execution feedback to AI:",w)}catch(m){console.error("Error providing execution feedback:",m)}}getTerminalContext(){const o={};if(window.xtermService&&window.xtermService.isServiceReady())try{o.currentLine=window.xtermService.getCurrentLineContent(),o.terminalDimensions=window.xtermService.getTerminalDimensions(),o.cursorPosition=window.xtermService.getCursorPosition();const m=window.xtermService.getLineRangeContent(Math.max(0,o.cursorPosition.y-5),o.cursorPosition.y);o.recentOutput=m.join(`
`)}catch(m){console.warn("Error getting terminal context:",m)}return o}generateSessionId(){return"session_"+Date.now()+"_"+Math.random().toString(36).substr(2,9)}clearMessages(){this.messages=[],this.messagesContainer.innerHTML=`
      <div class="ai-message ai-message-assistant">
        <div class="ai-message-content">
          <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
        </div>
      </div>
    `}getMessages(){return[...this.messages]}setAIAgentUrl(o){this.aiAgentUrl=o}async checkAIAgentHealth(){try{const o=await fetch(`${this.aiAgentUrl}/health`);return o.ok?await o.json():null}catch(o){return console.error("AI agent health check failed:",o),null}}async getSessionHistory(){try{const o=await fetch(`${this.aiAgentUrl}/sessions/${this.sessionId}/history`);return o.ok?(await o.json()).history:[]}catch(o){return console.error("Error getting session history:",o),[]}}async clearSessionOnAgent(){try{return(await fetch(`${this.aiAgentUrl}/sessions/${this.sessionId}`,{method:"DELETE"})).ok}catch(o){return console.error("Error clearing session on agent:",o),!1}}setAIResponseHandler(o){this.aiResponseHandler=o}getSessionId(){return this.sessionId}resetSession(){this.sessionId=this.generateSessionId(),this.clearMessages()}async configureAIAgent(o){try{const m=_e.validate(o);if(!m.valid)throw new Error(`Invalid configuration: ${m.errors.join(", ")}`);if(_e.save(o),!await oe.initialize(o))throw new Error("Failed to initialize AI agent with new configuration");return{success:!0,message:"AI agent configured successfully"}}catch(m){return console.error("Error configuring AI agent:",m),{success:!1,error:m.message}}}getAIAgentConfig(){return _e.load()}async testAIAgentConnection(){try{if(!oe.isAgentReady()){const m=_e.load();await oe.initialize(m)}const o=await oe.processMessage("Hello, this is a test message.",{sessionId:"test-session"});return oe.clearSession("test-session"),{success:!0,message:"Connection test successful",response:o.content}}catch(o){return console.error("AI agent connection test failed:",o),{success:!1,error:o.message}}}getAIAgentStats(){return oe.getStats()}setAIAgentMode(o=!0){this.useFrontendAgent=o,console.log("AI agent mode set to:",o?"frontend":"backend")}async executeCommand(o){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for command execution"),!1;try{return await window.xtermService.sendCommand(o,!0),console.log("Manual command executed:",o),!0}catch(m){return console.error("Error executing manual command:",m),!1}}async sendTextToTerminal(o){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for text input"),!1;try{return await window.xtermService.sendRawData(o),console.log("Text sent to terminal:",o),!0}catch(m){return console.error("Error sending text to terminal:",m),!1}}getTerminalOutput(o=10){if(!window.xtermService||!window.xtermService.isServiceReady())return"";try{const m=window.xtermService.getCursorPosition(),w=Math.max(0,m.y-o);return window.xtermService.getLineRangeContent(w,m.y).join(`
`).trim()}catch(m){return console.error("Error getting terminal output:",m),""}}}const Ae=new bs;class Cs{constructor(){this.isVisible=!1,this.dialog=null,this.form=null,this.statusElement=null,this.settingsBtn=null,this.closeBtn=null}initialize(){return this.dialog=document.getElementById("settingsDialog"),this.form=document.getElementById("aiSettingsForm"),this.statusElement=document.getElementById("settingsStatus"),this.settingsBtn=document.getElementById("settingsBtn"),this.closeBtn=document.getElementById("settingsDialogClose"),!this.dialog||!this.form||!this.statusElement||!this.settingsBtn||!this.closeBtn?(console.error("Settings dialog elements not found"),!1):(this.setupEventListeners(),this.loadCurrentSettings(),console.log("Settings Dialog Service initialized"),!0)}setupEventListeners(){this.settingsBtn.addEventListener("click",()=>this.show()),this.closeBtn.addEventListener("click",()=>this.hide()),this.dialog.addEventListener("click",o=>{o.target===this.dialog&&this.hide()}),this.form.addEventListener("submit",o=>{o.preventDefault(),this.saveSettings()}),document.getElementById("testConnection").addEventListener("click",()=>{this.testConnection()}),document.getElementById("resetSettings").addEventListener("click",()=>{this.resetSettings()}),document.addEventListener("keydown",o=>{o.key==="Escape"&&this.isVisible&&this.hide()})}loadCurrentSettings(){const o=_e.load();document.getElementById("apiKey").value=o.apiKey||"",document.getElementById("endpoint").value=o.endpoint||"",document.getElementById("model").value=o.model||"",document.getElementById("maxTokens").value=o.maxTokens||"",document.getElementById("temperature").value=o.temperature||""}show(){this.isVisible=!0,this.dialog.classList.remove("settings-dialog-hidden"),this.loadCurrentSettings(),this.clearStatus(),document.getElementById("apiKey").focus()}hide(){this.isVisible=!1,this.dialog.classList.add("settings-dialog-hidden"),this.clearStatus()}async saveSettings(){try{const o=new FormData(this.form),m={apiKey:o.get("apiKey").trim(),endpoint:o.get("endpoint").trim(),model:o.get("model").trim(),maxTokens:parseInt(o.get("maxTokens"))||1e3,temperature:parseFloat(o.get("temperature"))||.7},w=await Ae.configureAIAgent(m);w.success?(this.showStatus("Settings saved successfully!","success"),setTimeout(()=>{this.hide()},1500)):this.showStatus(`Error: ${w.error}`,"error")}catch(o){console.error("Error saving settings:",o),this.showStatus(`Error saving settings: ${o.message}`,"error")}}async testConnection(){try{this.showStatus("Testing connection...","info");const o=new FormData(this.form),m={apiKey:o.get("apiKey").trim(),endpoint:o.get("endpoint").trim(),model:o.get("model").trim(),maxTokens:parseInt(o.get("maxTokens"))||1e3,temperature:parseFloat(o.get("temperature"))||.7};await Ae.configureAIAgent(m);const w=await Ae.testAIAgentConnection();w.success?this.showStatus("Connection test successful!","success"):this.showStatus(`Connection test failed: ${w.error}`,"error")}catch(o){console.error("Error testing connection:",o),this.showStatus(`Connection test failed: ${o.message}`,"error")}}resetSettings(){if(confirm("Are you sure you want to reset all settings to defaults?")){const o=_e.reset();document.getElementById("apiKey").value=o.apiKey,document.getElementById("endpoint").value=o.endpoint,document.getElementById("model").value=o.model,document.getElementById("maxTokens").value=o.maxTokens,document.getElementById("temperature").value=o.temperature,this.showStatus("Settings reset to defaults","success")}}showStatus(o,m="info"){this.statusElement.textContent=o,this.statusElement.className=`settings-status ${m}`}clearStatus(){this.statusElement.textContent="",this.statusElement.className="settings-status"}isDialogVisible(){return this.isVisible}}const Lt=new Cs;class ys{constructor(){this.container=null,this.keyboardElement=null,this.isVisible=!1,this.currentLayout="default",this.shiftPressed=!1,this.ctrlPressed=!1,this.altPressed=!1,this.onKeyPress=null,this.layouts={default:[["`","1","2","3","4","5","6","7","8","9","0","-","=","Backspace"],["Tab","q","w","e","r","t","y","u","i","o","p","[","]","\\"],["CapsLock","a","s","d","f","g","h","j","k","l",";","'","Enter"],["Shift","z","x","c","v","b","n","m",",",".","/","Shift"],["Ctrl","Alt","Space","Alt","Ctrl","ArrowLeft","ArrowUp","ArrowDown","ArrowRight"]],shift:[["~","!","@","#","$","%","^","&","*","(",")","_","+","Backspace"],["Tab","Q","W","E","R","T","Y","U","I","O","P","{","}","|"],["CapsLock","A","S","D","F","G","H","J","K","L",":",'"',"Enter"],["Shift","Z","X","C","V","B","N","M","<",">","?","Shift"],["Ctrl","Alt","Space","Alt","Ctrl","ArrowLeft","ArrowUp","ArrowDown","ArrowRight"]],function:[["Esc","F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12","Delete"],["Insert","Home","PageUp","","","","","","","","","","",""],["Delete","End","PageDown","","","","","","","","","","",""],["","","","","","","","","","","","","",""],["","","","","","","","","","","","","",""]]},this.keyDisplayNames={Backspace:"⌫",Tab:"⇥",CapsLock:"⇪",Enter:"⏎",Shift:"⇧",Ctrl:"Ctrl",Alt:"Alt",Space:"␣",ArrowLeft:"←",ArrowUp:"↑",ArrowDown:"↓",ArrowRight:"→",Esc:"Esc",Delete:"Del",Insert:"Ins",Home:"Home",End:"End",PageUp:"PgUp",PageDown:"PgDn"}}initialize(o,m){return this.container=document.getElementById(o),this.onKeyPress=m,this.container?(this.createKeyboard(),this.setupEventListeners(),console.log("Virtual Keyboard Service initialized"),!0):(console.error("Virtual keyboard container not found"),!1)}createKeyboard(){this.container.innerHTML=`
      <div class="virtual-keyboard-header">
        <div class="virtual-keyboard-controls">
          <button class="layout-btn" data-layout="default">ABC</button>
          <button class="layout-btn" data-layout="function">F1-12</button>
        </div>
        <button class="virtual-keyboard-close">×</button>
      </div>
      <div class="virtual-keyboard-body">
        <div class="virtual-keyboard-keys"></div>
      </div>
    `,this.keyboardElement=this.container.querySelector(".virtual-keyboard-keys"),this.renderLayout()}renderLayout(){const o=this.shiftPressed?this.layouts.shift:this.layouts[this.currentLayout];this.keyboardElement.innerHTML="",o.forEach((m,w)=>{const k=document.createElement("div");k.className="keyboard-row",m.forEach((M,D)=>{if(M==="")return;const t=document.createElement("button");t.className="keyboard-key",t.dataset.key=M,["Shift","Ctrl","Alt","CapsLock"].includes(M)&&(t.classList.add("modifier-key"),(M==="Shift"&&this.shiftPressed||M==="Ctrl"&&this.ctrlPressed||M==="Alt"&&this.altPressed)&&t.classList.add("active")),["Backspace","Tab","Enter","Space"].includes(M)&&t.classList.add("special-key"),M.startsWith("Arrow")&&t.classList.add("arrow-key"),M==="Space"?t.classList.add("space-key"):M==="Backspace"||M==="Enter"?t.classList.add("wide-key"):(M==="Tab"||M==="CapsLock")&&t.classList.add("medium-key"),t.textContent=this.keyDisplayNames[M]||M,k.appendChild(t)}),this.keyboardElement.appendChild(k)})}setupEventListeners(){this.keyboardElement.addEventListener("click",o=>{o.target.classList.contains("keyboard-key")&&this.handleKeyPress(o.target.dataset.key)}),this.container.addEventListener("click",o=>{o.target.classList.contains("layout-btn")?this.switchLayout(o.target.dataset.layout):o.target.classList.contains("virtual-keyboard-close")&&this.hide()}),this.container.addEventListener("mousedown",o=>{o.preventDefault()})}handleKeyPress(o){if(console.log("Virtual keyboard key pressed:",o,{shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed}),o==="Shift"){this.shiftPressed=!this.shiftPressed,this.renderLayout(),console.log("Shift toggled:",this.shiftPressed);return}if(o==="Ctrl"){this.ctrlPressed=!this.ctrlPressed,this.renderLayout(),console.log("Ctrl toggled:",this.ctrlPressed);return}if(o==="Alt"){this.altPressed=!this.altPressed,this.renderLayout(),console.log("Alt toggled:",this.altPressed);return}if(o==="CapsLock"){this.shiftPressed=!this.shiftPressed,this.renderLayout(),console.log("CapsLock toggled:",this.shiftPressed);return}let m={key:o,shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed,timestamp:Date.now()};console.log("Sending key data to callback:",m),this.onKeyPress&&this.onKeyPress(m),this.shiftPressed&&o!=="CapsLock"&&(this.shiftPressed=!1,this.renderLayout()),(this.ctrlPressed||this.altPressed)&&(this.ctrlPressed=!1,this.altPressed=!1,this.renderLayout())}switchLayout(o){this.currentLayout=o,this.renderLayout(),this.container.querySelectorAll(".layout-btn").forEach(m=>{m.classList.toggle("active",m.dataset.layout===o)})}show(){this.isVisible=!0,this.container.classList.remove("keyboard-hidden"),this.container.classList.add("keyboard-visible")}hide(){this.isVisible=!1,this.container.classList.remove("keyboard-visible"),this.container.classList.add("keyboard-hidden")}toggle(){this.isVisible?this.hide():this.show()}isKeyboardVisible(){return this.isVisible}typeString(o,m=100){let w=0;const k=()=>{if(w<o.length){const M=o[w];this.handleKeyPress(M),w++,setTimeout(k,m)}};k()}simulateKeyCombo(o){o.includes("Ctrl")&&(this.ctrlPressed=!0,this.renderLayout()),o.includes("Alt")&&(this.altPressed=!0,this.renderLayout()),o.includes("Shift")&&(this.shiftPressed=!0,this.renderLayout());const m=o.find(w=>!["Ctrl","Alt","Shift"].includes(w));m&&this.handleKeyPress(m)}getModifierStates(){return{shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed}}}const Ie=new ys;let Te=null,me=0;const Ee=document.getElementById("remainingTime"),ws=document.getElementById("extendBtn");function ks(x){const o=Math.floor(x/3600),m=Math.floor(x%3600/60),w=x%60;return[o,m,w].map(k=>k.toString().padStart(2,"0")).join(":")}function ct(){Ee.textContent=ks(me),Ee.classList.remove("warning","danger"),me<=300?Ee.classList.add("danger"):me<=1800&&Ee.classList.add("warning")}function xs(x){Te&&clearInterval(Te),me=Math.max(0,x),ct(),Te=setInterval(()=>{me--,ct(),me<=0&&(clearInterval(Te),ws.disabled=!0,Ee.textContent="Session expired")},1e3)}async function Es(){try{const x=await fetch("/api/status");if(x.ok){const o=await x.json();o.remaining_seconds!==void 0&&xs(o.remaining_seconds)}}catch(x){console.error("Error initializing session timer:",x)}}let ae=null;function Ls(x){if(!re||!ae||ae.readyState!==WebSocket.OPEN){console.log("Terminal or socket not ready for keyboard input");return}let o="";const{key:m,shift:w,ctrl:k,alt:M}=x;switch(m){case"Enter":o="\r";break;case"Tab":o="	";break;case"Backspace":o="";break;case"Delete":o="\x1B[3~";break;case"Esc":o="\x1B";break;case"Space":o=" ";break;case"ArrowLeft":o="\x1B[D";break;case"ArrowUp":o="\x1B[A";break;case"ArrowDown":o="\x1B[B";break;case"ArrowRight":o="\x1B[C";break;case"Home":o="\x1B[H";break;case"End":o="\x1B[F";break;case"PageUp":o="\x1B[5~";break;case"PageDown":o="\x1B[6~";break;case"Insert":o="\x1B[2~";break;default:if(k&&m.length===1){const D=m.toLowerCase().charCodeAt(0);D>=97&&D<=122&&(o=String.fromCharCode(D-96))}else M&&m.length===1?o="\x1B"+m:o=m}o&&(console.log("Sending virtual keyboard input:",o,"from key:",x),ae.send(JSON.stringify({type:"input",data:o})))}const As=document.getElementById("terminal"),re=new Tt.Terminal({fontFamily:'Menlo, Monaco, "Courier New", monospace',fontSize:14,lineHeight:1.2,cursorBlink:!0,cursorStyle:"block",theme:{background:"#000000",foreground:"#f0f0f0",cursor:"#f0f0f0",selection:"rgba(255, 255, 255, 0.3)"}}),Se=new Mt.FitAddon,Ds=new It.WebLinksAddon;re.loadAddon(Se);re.loadAddon(Ds);re.open(As);Se.fit();Es();function Rs(){console.log("Initializing virtual keyboard...");const x=document.getElementById("keyboardBtn");if(console.log("keyboardBtn:",x),!Ie.initialize("keyboardContainer",Ls)){console.error("Failed to initialize virtual keyboard service");return}function m(){console.log("Toggle keyboard clicked, current state:",Ie.isKeyboardVisible()),Ie.toggle(),Se&&Se.fit&&setTimeout(()=>Se.fit(),100)}x&&x.addEventListener("click",m)}function dt(){Rs(),Ae.initialize(),Lt.initialize()}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",dt):dt();const Bs=window.location.protocol==="https:"?"wss:":"ws:",At=`${Bs}//${window.location.host}/ws`;console.log(`Connecting to WebSocket at: ${At}`);ae=new WebSocket(At);ae.addEventListener("open",()=>{console.log("WebSocket connection established"),re.writeln("Connected to terminal server"),rt.initialize(re,ae),window.xtermService=rt,window.aiDialogService=Ae,window.virtualKeyboardService=Ie,window.frontendAIAgent=oe,window.aiAgentConfig=_e,window.settingsDialogService=Lt;const x=()=>{Se.fit();const o={cols:re.cols,rows:re.rows};ae.send(JSON.stringify({type:"resize",data:JSON.stringify(o)}))};x(),window.addEventListener("resize",x),re.onData(o=>{console.log("Sending data to server:",o),ae.send(JSON.stringify({type:"input",data:o}))})});ae.addEventListener("message",x=>{try{const o=JSON.parse(x.data);o.type==="output"?re.write(o.data):o.type==="error"&&re.writeln(`\r
\x1B[31mError: ${o.data}\x1B[0m`)}catch(o){console.error("Failed to parse message:",o),re.writeln(`\r
\x1B[31mError: Failed to parse server message\x1B[0m`)}});ae.addEventListener("close",()=>{console.log("WebSocket connection closed"),re.writeln(`\r
\x1B[33mConnection closed\x1B[0m`)});ae.addEventListener("error",x=>{console.error("WebSocket error:",x),re.writeln(`\r
\x1B[31mConnection error\x1B[0m`)});
