<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Web Terminal</title>
  <script type="module" crossorigin src="/assets/index-Cz7ll_uK.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-D-b1eURa.css">
</head>
<body>
  <div class="container">
    <header>
      <div class="header-content">
        <h1>Web Terminal</h1>
        <div class="header-buttons">
          <button id="getAllContentBtn" class="get-all-content-btn" title="获取全屏内容">🖥️</button>
          <button id="keyboardBtn" class="keyboard-btn" title="Toggle Virtual Keyboard">⌨️</button>
          <button id="aiBtn" class="ai-btn" title="Open AI Chat">🤖</button>
          <button id="settingsBtn" class="settings-btn" title="AI Settings">⚙️</button>
        </div>

        <div class="status-bar" id="statusBar">
          <span id="remainingTime">Checking status...</span>
          <div class="status-buttons">
            <button id="extendBtn" class="extend-btn" title="Extend session by 1 hour">+1h</button>
          </div>
        </div>
      </div>
    </header>
    <main>
      <div id="terminal"></div>
    </main>
    <footer>
      <p>Powered by xterm.js and Go WebSocket</p>
    </footer>
  </div>
  <!-- Virtual Keyboard Container -->
  <div id="keyboardContainer" class="keyboard-container keyboard-hidden">
    <!-- Keyboard content will be generated by virtualKeyboardService -->
  </div>

  <!-- AI Chat Dialog -->
  <div id="aiDialog" class="ai-dialog ai-dialog-hidden">
    <div class="ai-dialog-content">
      <div class="ai-dialog-header" id="aiDialogHeader">
        <h3>AI Assistant</h3>
        <div class="ai-dialog-controls">
          <button class="ai-dialog-minimize" id="aiDialogMinimize" title="Minimize">−</button>
          <button class="ai-dialog-maximize" id="aiDialogMaximize" title="Maximize">□</button>
          <button class="ai-dialog-close" id="aiDialogClose" title="Close">×</button>
        </div>
      </div>
      <div class="ai-dialog-messages" id="aiMessages">
        <div class="ai-message ai-message-assistant">
          <div class="ai-message-content">
            <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
          </div>
        </div>
      </div>
      <div class="ai-dialog-input">
        <textarea id="aiInput" class="ai-input" placeholder="Type your message here..." rows="1"></textarea>
        <button id="aiPauseResume" class="ai-pause-resume-btn" title="Pause/Resume Agent">
          <svg id="pauseIcon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="6" y="4" width="4" height="16"></rect>
            <rect x="14" y="4" width="4" height="16"></rect>
          </svg>
          <svg id="playIcon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
            <polygon points="5,3 19,12 5,21"></polygon>
          </svg>
        </button>
        <button id="aiSend" class="ai-send-btn" title="Send message">
          <svg id="sendIcon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="22" y1="2" x2="11" y2="13"></line>
            <polygon points="22,2 15,22 11,13 2,9"></polygon>
          </svg>
          <svg id="stopIcon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- AI Settings Dialog -->
  <div id="settingsDialog" class="settings-dialog settings-dialog-hidden">
    <div class="settings-dialog-content">
      <div class="settings-dialog-header">
        <h3>AI Settings</h3>
        <button class="settings-dialog-close" id="settingsDialogClose">×</button>
      </div>
      <div class="settings-dialog-body">
        <form id="aiSettingsForm">
          <div class="form-group">
            <label for="apiKey">API Key:</label>
            <input type="password" id="apiKey" name="apiKey" placeholder="Enter your OpenAI API key">
          </div>
          <div class="form-group">
            <label for="endpoint">Endpoint:</label>
            <input type="url" id="endpoint" name="endpoint" placeholder="https://api.openai.com/v1/chat/completions">
          </div>
          <div class="form-group">
            <label for="model">Model:</label>
            <input type="text" id="model" name="model" placeholder="gpt-3.5-turbo">
          </div>
          <div class="form-group">
            <label for="maxTokens">Max Tokens:</label>
            <input type="number" id="maxTokens" name="maxTokens" min="1" max="40000" placeholder="40000">
          </div>
          <div class="form-group">
            <label for="temperature">Temperature:</label>
            <input type="number" id="temperature" name="temperature" min="0" max="2" step="0.1" placeholder="0.7">
          </div>
          <div class="form-actions">
            <button type="button" id="testConnection" class="test-btn">Test Connection</button>
            <button type="button" id="resetSettings" class="reset-btn">Reset</button>
            <button type="submit" class="save-btn">Save</button>
          </div>
        </form>
        <div id="settingsStatus" class="settings-status"></div>
      </div>
    </div>
  </div>

</body>
</html>
