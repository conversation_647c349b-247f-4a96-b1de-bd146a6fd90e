var $t=Object.defineProperty;var zt=(E,e,d)=>e in E?$t(E,e,{enumerable:!0,configurable:!0,writable:!0,value:d}):E[e]=d;var Z=(E,e,d)=>zt(E,typeof e!="symbol"?e+"":e,d);(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const b of document.querySelectorAll('link[rel="modulepreload"]'))m(b);new MutationObserver(b=>{for(const A of b)if(A.type==="childList")for(const x of A.addedNodes)x.tagName==="LINK"&&x.rel==="modulepreload"&&m(x)}).observe(document,{childList:!0,subtree:!0});function d(b){const A={};return b.integrity&&(A.integrity=b.integrity),b.referrerPolicy&&(A.referrerPolicy=b.referrerPolicy),b.crossOrigin==="use-credentials"?A.credentials="include":b.crossOrigin==="anonymous"?A.credentials="omit":A.credentials="same-origin",A}function m(b){if(b.ep)return;b.ep=!0;const A=d(b);fetch(b.href,A)}})();var Ct={exports:{}};(function(E,e){(function(d,m){E.exports=m()})(self,()=>(()=>{var d={4567:function(x,s,o){var l=this&&this.__decorate||function(r,h,_,S){var y,u=arguments.length,C=u<3?h:S===null?S=Object.getOwnPropertyDescriptor(h,_):S;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")C=Reflect.decorate(r,h,_,S);else for(var L=r.length-1;L>=0;L--)(y=r[L])&&(C=(u<3?y(C):u>3?y(h,_,C):y(h,_))||C);return u>3&&C&&Object.defineProperty(h,_,C),C},c=this&&this.__param||function(r,h){return function(_,S){h(_,S,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.AccessibilityManager=void 0;const a=o(9042),f=o(6114),p=o(9924),v=o(844),g=o(5596),t=o(4725),n=o(3656);let i=s.AccessibilityManager=class extends v.Disposable{constructor(r,h){super(),this._terminal=r,this._renderService=h,this._liveRegionLineCount=0,this._charsToConsume=[],this._charsToAnnounce="",this._accessibilityContainer=document.createElement("div"),this._accessibilityContainer.classList.add("xterm-accessibility"),this._rowContainer=document.createElement("div"),this._rowContainer.setAttribute("role","list"),this._rowContainer.classList.add("xterm-accessibility-tree"),this._rowElements=[];for(let _=0;_<this._terminal.rows;_++)this._rowElements[_]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[_]);if(this._topBoundaryFocusListener=_=>this._handleBoundaryFocus(_,0),this._bottomBoundaryFocusListener=_=>this._handleBoundaryFocus(_,1),this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions(),this._accessibilityContainer.appendChild(this._rowContainer),this._liveRegion=document.createElement("div"),this._liveRegion.classList.add("live-region"),this._liveRegion.setAttribute("aria-live","assertive"),this._accessibilityContainer.appendChild(this._liveRegion),this._liveRegionDebouncer=this.register(new p.TimeBasedDebouncer(this._renderRows.bind(this))),!this._terminal.element)throw new Error("Cannot enable accessibility before Terminal.open");this._terminal.element.insertAdjacentElement("afterbegin",this._accessibilityContainer),this.register(this._terminal.onResize(_=>this._handleResize(_.rows))),this.register(this._terminal.onRender(_=>this._refreshRows(_.start,_.end))),this.register(this._terminal.onScroll(()=>this._refreshRows())),this.register(this._terminal.onA11yChar(_=>this._handleChar(_))),this.register(this._terminal.onLineFeed(()=>this._handleChar(`
`))),this.register(this._terminal.onA11yTab(_=>this._handleTab(_))),this.register(this._terminal.onKey(_=>this._handleKey(_.key))),this.register(this._terminal.onBlur(()=>this._clearLiveRegion())),this.register(this._renderService.onDimensionsChange(()=>this._refreshRowsDimensions())),this._screenDprMonitor=new g.ScreenDprMonitor(window),this.register(this._screenDprMonitor),this._screenDprMonitor.setListener(()=>this._refreshRowsDimensions()),this.register((0,n.addDisposableDomListener)(window,"resize",()=>this._refreshRowsDimensions())),this._refreshRows(),this.register((0,v.toDisposable)(()=>{this._accessibilityContainer.remove(),this._rowElements.length=0}))}_handleTab(r){for(let h=0;h<r;h++)this._handleChar(" ")}_handleChar(r){this._liveRegionLineCount<21&&(this._charsToConsume.length>0?this._charsToConsume.shift()!==r&&(this._charsToAnnounce+=r):this._charsToAnnounce+=r,r===`
`&&(this._liveRegionLineCount++,this._liveRegionLineCount===21&&(this._liveRegion.textContent+=a.tooMuchOutput)),f.isMac&&this._liveRegion.textContent&&this._liveRegion.textContent.length>0&&!this._liveRegion.parentNode&&setTimeout(()=>{this._accessibilityContainer.appendChild(this._liveRegion)},0))}_clearLiveRegion(){this._liveRegion.textContent="",this._liveRegionLineCount=0,f.isMac&&this._liveRegion.remove()}_handleKey(r){this._clearLiveRegion(),new RegExp("\\p{Control}","u").test(r)||this._charsToConsume.push(r)}_refreshRows(r,h){this._liveRegionDebouncer.refresh(r,h,this._terminal.rows)}_renderRows(r,h){const _=this._terminal.buffer,S=_.lines.length.toString();for(let y=r;y<=h;y++){const u=_.translateBufferLineToString(_.ydisp+y,!0),C=(_.ydisp+y+1).toString(),L=this._rowElements[y];L&&(u.length===0?L.innerText=" ":L.textContent=u,L.setAttribute("aria-posinset",C),L.setAttribute("aria-setsize",S))}this._announceCharacters()}_announceCharacters(){this._charsToAnnounce.length!==0&&(this._liveRegion.textContent+=this._charsToAnnounce,this._charsToAnnounce="")}_handleBoundaryFocus(r,h){const _=r.target,S=this._rowElements[h===0?1:this._rowElements.length-2];if(_.getAttribute("aria-posinset")===(h===0?"1":`${this._terminal.buffer.lines.length}`)||r.relatedTarget!==S)return;let y,u;if(h===0?(y=_,u=this._rowElements.pop(),this._rowContainer.removeChild(u)):(y=this._rowElements.shift(),u=_,this._rowContainer.removeChild(y)),y.removeEventListener("focus",this._topBoundaryFocusListener),u.removeEventListener("focus",this._bottomBoundaryFocusListener),h===0){const C=this._createAccessibilityTreeNode();this._rowElements.unshift(C),this._rowContainer.insertAdjacentElement("afterbegin",C)}else{const C=this._createAccessibilityTreeNode();this._rowElements.push(C),this._rowContainer.appendChild(C)}this._rowElements[0].addEventListener("focus",this._topBoundaryFocusListener),this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._terminal.scrollLines(h===0?-1:1),this._rowElements[h===0?1:this._rowElements.length-2].focus(),r.preventDefault(),r.stopImmediatePropagation()}_handleResize(r){this._rowElements[this._rowElements.length-1].removeEventListener("focus",this._bottomBoundaryFocusListener);for(let h=this._rowContainer.children.length;h<this._terminal.rows;h++)this._rowElements[h]=this._createAccessibilityTreeNode(),this._rowContainer.appendChild(this._rowElements[h]);for(;this._rowElements.length>r;)this._rowContainer.removeChild(this._rowElements.pop());this._rowElements[this._rowElements.length-1].addEventListener("focus",this._bottomBoundaryFocusListener),this._refreshRowsDimensions()}_createAccessibilityTreeNode(){const r=document.createElement("div");return r.setAttribute("role","listitem"),r.tabIndex=-1,this._refreshRowDimensions(r),r}_refreshRowsDimensions(){if(this._renderService.dimensions.css.cell.height){this._accessibilityContainer.style.width=`${this._renderService.dimensions.css.canvas.width}px`,this._rowElements.length!==this._terminal.rows&&this._handleResize(this._terminal.rows);for(let r=0;r<this._terminal.rows;r++)this._refreshRowDimensions(this._rowElements[r])}}_refreshRowDimensions(r){r.style.height=`${this._renderService.dimensions.css.cell.height}px`}};s.AccessibilityManager=i=l([c(1,t.IRenderService)],i)},3614:(x,s)=>{function o(f){return f.replace(/\r?\n/g,"\r")}function l(f,p){return p?"\x1B[200~"+f+"\x1B[201~":f}function c(f,p,v,g){f=l(f=o(f),v.decPrivateModes.bracketedPasteMode&&g.rawOptions.ignoreBracketedPasteMode!==!0),v.triggerDataEvent(f,!0),p.value=""}function a(f,p,v){const g=v.getBoundingClientRect(),t=f.clientX-g.left-10,n=f.clientY-g.top-10;p.style.width="20px",p.style.height="20px",p.style.left=`${t}px`,p.style.top=`${n}px`,p.style.zIndex="1000",p.focus()}Object.defineProperty(s,"__esModule",{value:!0}),s.rightClickHandler=s.moveTextAreaUnderMouseCursor=s.paste=s.handlePasteEvent=s.copyHandler=s.bracketTextForPaste=s.prepareTextForTerminal=void 0,s.prepareTextForTerminal=o,s.bracketTextForPaste=l,s.copyHandler=function(f,p){f.clipboardData&&f.clipboardData.setData("text/plain",p.selectionText),f.preventDefault()},s.handlePasteEvent=function(f,p,v,g){f.stopPropagation(),f.clipboardData&&c(f.clipboardData.getData("text/plain"),p,v,g)},s.paste=c,s.moveTextAreaUnderMouseCursor=a,s.rightClickHandler=function(f,p,v,g,t){a(f,p,v),t&&g.rightClickSelect(f),p.value=g.selectionText,p.select()}},7239:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ColorContrastCache=void 0;const l=o(1505);s.ColorContrastCache=class{constructor(){this._color=new l.TwoKeyMap,this._css=new l.TwoKeyMap}setCss(c,a,f){this._css.set(c,a,f)}getCss(c,a){return this._css.get(c,a)}setColor(c,a,f){this._color.set(c,a,f)}getColor(c,a){return this._color.get(c,a)}clear(){this._color.clear(),this._css.clear()}}},3656:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.addDisposableDomListener=void 0,s.addDisposableDomListener=function(o,l,c,a){o.addEventListener(l,c,a);let f=!1;return{dispose:()=>{f||(f=!0,o.removeEventListener(l,c,a))}}}},6465:function(x,s,o){var l=this&&this.__decorate||function(t,n,i,r){var h,_=arguments.length,S=_<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(t,n,i,r);else for(var y=t.length-1;y>=0;y--)(h=t[y])&&(S=(_<3?h(S):_>3?h(n,i,S):h(n,i))||S);return _>3&&S&&Object.defineProperty(n,i,S),S},c=this&&this.__param||function(t,n){return function(i,r){n(i,r,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.Linkifier2=void 0;const a=o(3656),f=o(8460),p=o(844),v=o(2585);let g=s.Linkifier2=class extends p.Disposable{get currentLink(){return this._currentLink}constructor(t){super(),this._bufferService=t,this._linkProviders=[],this._linkCacheDisposables=[],this._isMouseOut=!0,this._wasResized=!1,this._activeLine=-1,this._onShowLinkUnderline=this.register(new f.EventEmitter),this.onShowLinkUnderline=this._onShowLinkUnderline.event,this._onHideLinkUnderline=this.register(new f.EventEmitter),this.onHideLinkUnderline=this._onHideLinkUnderline.event,this.register((0,p.getDisposeArrayDisposable)(this._linkCacheDisposables)),this.register((0,p.toDisposable)(()=>{this._lastMouseEvent=void 0})),this.register(this._bufferService.onResize(()=>{this._clearCurrentLink(),this._wasResized=!0}))}registerLinkProvider(t){return this._linkProviders.push(t),{dispose:()=>{const n=this._linkProviders.indexOf(t);n!==-1&&this._linkProviders.splice(n,1)}}}attachToDom(t,n,i){this._element=t,this._mouseService=n,this._renderService=i,this.register((0,a.addDisposableDomListener)(this._element,"mouseleave",()=>{this._isMouseOut=!0,this._clearCurrentLink()})),this.register((0,a.addDisposableDomListener)(this._element,"mousemove",this._handleMouseMove.bind(this))),this.register((0,a.addDisposableDomListener)(this._element,"mousedown",this._handleMouseDown.bind(this))),this.register((0,a.addDisposableDomListener)(this._element,"mouseup",this._handleMouseUp.bind(this)))}_handleMouseMove(t){if(this._lastMouseEvent=t,!this._element||!this._mouseService)return;const n=this._positionFromMouseEvent(t,this._element,this._mouseService);if(!n)return;this._isMouseOut=!1;const i=t.composedPath();for(let r=0;r<i.length;r++){const h=i[r];if(h.classList.contains("xterm"))break;if(h.classList.contains("xterm-hover"))return}this._lastBufferCell&&n.x===this._lastBufferCell.x&&n.y===this._lastBufferCell.y||(this._handleHover(n),this._lastBufferCell=n)}_handleHover(t){if(this._activeLine!==t.y||this._wasResized)return this._clearCurrentLink(),this._askForLink(t,!1),void(this._wasResized=!1);this._currentLink&&this._linkAtPosition(this._currentLink.link,t)||(this._clearCurrentLink(),this._askForLink(t,!0))}_askForLink(t,n){var i,r;this._activeProviderReplies&&n||((i=this._activeProviderReplies)===null||i===void 0||i.forEach(_=>{_==null||_.forEach(S=>{S.link.dispose&&S.link.dispose()})}),this._activeProviderReplies=new Map,this._activeLine=t.y);let h=!1;for(const[_,S]of this._linkProviders.entries())n?!((r=this._activeProviderReplies)===null||r===void 0)&&r.get(_)&&(h=this._checkLinkProviderResult(_,t,h)):S.provideLinks(t.y,y=>{var u,C;if(this._isMouseOut)return;const L=y==null?void 0:y.map(I=>({link:I}));(u=this._activeProviderReplies)===null||u===void 0||u.set(_,L),h=this._checkLinkProviderResult(_,t,h),((C=this._activeProviderReplies)===null||C===void 0?void 0:C.size)===this._linkProviders.length&&this._removeIntersectingLinks(t.y,this._activeProviderReplies)})}_removeIntersectingLinks(t,n){const i=new Set;for(let r=0;r<n.size;r++){const h=n.get(r);if(h)for(let _=0;_<h.length;_++){const S=h[_],y=S.link.range.start.y<t?0:S.link.range.start.x,u=S.link.range.end.y>t?this._bufferService.cols:S.link.range.end.x;for(let C=y;C<=u;C++){if(i.has(C)){h.splice(_--,1);break}i.add(C)}}}}_checkLinkProviderResult(t,n,i){var r;if(!this._activeProviderReplies)return i;const h=this._activeProviderReplies.get(t);let _=!1;for(let S=0;S<t;S++)this._activeProviderReplies.has(S)&&!this._activeProviderReplies.get(S)||(_=!0);if(!_&&h){const S=h.find(y=>this._linkAtPosition(y.link,n));S&&(i=!0,this._handleNewLink(S))}if(this._activeProviderReplies.size===this._linkProviders.length&&!i)for(let S=0;S<this._activeProviderReplies.size;S++){const y=(r=this._activeProviderReplies.get(S))===null||r===void 0?void 0:r.find(u=>this._linkAtPosition(u.link,n));if(y){i=!0,this._handleNewLink(y);break}}return i}_handleMouseDown(){this._mouseDownLink=this._currentLink}_handleMouseUp(t){if(!this._element||!this._mouseService||!this._currentLink)return;const n=this._positionFromMouseEvent(t,this._element,this._mouseService);n&&this._mouseDownLink===this._currentLink&&this._linkAtPosition(this._currentLink.link,n)&&this._currentLink.link.activate(t,this._currentLink.link.text)}_clearCurrentLink(t,n){this._element&&this._currentLink&&this._lastMouseEvent&&(!t||!n||this._currentLink.link.range.start.y>=t&&this._currentLink.link.range.end.y<=n)&&(this._linkLeave(this._element,this._currentLink.link,this._lastMouseEvent),this._currentLink=void 0,(0,p.disposeArray)(this._linkCacheDisposables))}_handleNewLink(t){if(!this._element||!this._lastMouseEvent||!this._mouseService)return;const n=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);n&&this._linkAtPosition(t.link,n)&&(this._currentLink=t,this._currentLink.state={decorations:{underline:t.link.decorations===void 0||t.link.decorations.underline,pointerCursor:t.link.decorations===void 0||t.link.decorations.pointerCursor},isHovered:!0},this._linkHover(this._element,t.link,this._lastMouseEvent),t.link.decorations={},Object.defineProperties(t.link.decorations,{pointerCursor:{get:()=>{var i,r;return(r=(i=this._currentLink)===null||i===void 0?void 0:i.state)===null||r===void 0?void 0:r.decorations.pointerCursor},set:i=>{var r,h;!((r=this._currentLink)===null||r===void 0)&&r.state&&this._currentLink.state.decorations.pointerCursor!==i&&(this._currentLink.state.decorations.pointerCursor=i,this._currentLink.state.isHovered&&((h=this._element)===null||h===void 0||h.classList.toggle("xterm-cursor-pointer",i)))}},underline:{get:()=>{var i,r;return(r=(i=this._currentLink)===null||i===void 0?void 0:i.state)===null||r===void 0?void 0:r.decorations.underline},set:i=>{var r,h,_;!((r=this._currentLink)===null||r===void 0)&&r.state&&((_=(h=this._currentLink)===null||h===void 0?void 0:h.state)===null||_===void 0?void 0:_.decorations.underline)!==i&&(this._currentLink.state.decorations.underline=i,this._currentLink.state.isHovered&&this._fireUnderlineEvent(t.link,i))}}}),this._renderService&&this._linkCacheDisposables.push(this._renderService.onRenderedViewportChange(i=>{if(!this._currentLink)return;const r=i.start===0?0:i.start+1+this._bufferService.buffer.ydisp,h=this._bufferService.buffer.ydisp+1+i.end;if(this._currentLink.link.range.start.y>=r&&this._currentLink.link.range.end.y<=h&&(this._clearCurrentLink(r,h),this._lastMouseEvent&&this._element)){const _=this._positionFromMouseEvent(this._lastMouseEvent,this._element,this._mouseService);_&&this._askForLink(_,!1)}})))}_linkHover(t,n,i){var r;!((r=this._currentLink)===null||r===void 0)&&r.state&&(this._currentLink.state.isHovered=!0,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(n,!0),this._currentLink.state.decorations.pointerCursor&&t.classList.add("xterm-cursor-pointer")),n.hover&&n.hover(i,n.text)}_fireUnderlineEvent(t,n){const i=t.range,r=this._bufferService.buffer.ydisp,h=this._createLinkUnderlineEvent(i.start.x-1,i.start.y-r-1,i.end.x,i.end.y-r-1,void 0);(n?this._onShowLinkUnderline:this._onHideLinkUnderline).fire(h)}_linkLeave(t,n,i){var r;!((r=this._currentLink)===null||r===void 0)&&r.state&&(this._currentLink.state.isHovered=!1,this._currentLink.state.decorations.underline&&this._fireUnderlineEvent(n,!1),this._currentLink.state.decorations.pointerCursor&&t.classList.remove("xterm-cursor-pointer")),n.leave&&n.leave(i,n.text)}_linkAtPosition(t,n){const i=t.range.start.y*this._bufferService.cols+t.range.start.x,r=t.range.end.y*this._bufferService.cols+t.range.end.x,h=n.y*this._bufferService.cols+n.x;return i<=h&&h<=r}_positionFromMouseEvent(t,n,i){const r=i.getCoords(t,n,this._bufferService.cols,this._bufferService.rows);if(r)return{x:r[0],y:r[1]+this._bufferService.buffer.ydisp}}_createLinkUnderlineEvent(t,n,i,r,h){return{x1:t,y1:n,x2:i,y2:r,cols:this._bufferService.cols,fg:h}}};s.Linkifier2=g=l([c(0,v.IBufferService)],g)},9042:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.tooMuchOutput=s.promptLabel=void 0,s.promptLabel="Terminal input",s.tooMuchOutput="Too much output to announce, navigate to rows manually to read"},3730:function(x,s,o){var l=this&&this.__decorate||function(g,t,n,i){var r,h=arguments.length,_=h<3?t:i===null?i=Object.getOwnPropertyDescriptor(t,n):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")_=Reflect.decorate(g,t,n,i);else for(var S=g.length-1;S>=0;S--)(r=g[S])&&(_=(h<3?r(_):h>3?r(t,n,_):r(t,n))||_);return h>3&&_&&Object.defineProperty(t,n,_),_},c=this&&this.__param||function(g,t){return function(n,i){t(n,i,g)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OscLinkProvider=void 0;const a=o(511),f=o(2585);let p=s.OscLinkProvider=class{constructor(g,t,n){this._bufferService=g,this._optionsService=t,this._oscLinkService=n}provideLinks(g,t){var n;const i=this._bufferService.buffer.lines.get(g-1);if(!i)return void t(void 0);const r=[],h=this._optionsService.rawOptions.linkHandler,_=new a.CellData,S=i.getTrimmedLength();let y=-1,u=-1,C=!1;for(let L=0;L<S;L++)if(u!==-1||i.hasContent(L)){if(i.loadCell(L,_),_.hasExtendedAttrs()&&_.extended.urlId){if(u===-1){u=L,y=_.extended.urlId;continue}C=_.extended.urlId!==y}else u!==-1&&(C=!0);if(C||u!==-1&&L===S-1){const I=(n=this._oscLinkService.getLinkData(y))===null||n===void 0?void 0:n.uri;if(I){const R={start:{x:u+1,y:g},end:{x:L+(C||L!==S-1?0:1),y:g}};let B=!1;if(!(h!=null&&h.allowNonHttpProtocols))try{const O=new URL(I);["http:","https:"].includes(O.protocol)||(B=!0)}catch{B=!0}B||r.push({text:I,range:R,activate:(O,$)=>h?h.activate(O,$,R):v(0,$),hover:(O,$)=>{var W;return(W=h==null?void 0:h.hover)===null||W===void 0?void 0:W.call(h,O,$,R)},leave:(O,$)=>{var W;return(W=h==null?void 0:h.leave)===null||W===void 0?void 0:W.call(h,O,$,R)}})}C=!1,_.hasExtendedAttrs()&&_.extended.urlId?(u=L,y=_.extended.urlId):(u=-1,y=-1)}}t(r)}};function v(g,t){if(confirm(`Do you want to navigate to ${t}?

WARNING: This link could potentially be dangerous`)){const n=window.open();if(n){try{n.opener=null}catch{}n.location.href=t}else console.warn("Opening link blocked as opener could not be cleared")}}s.OscLinkProvider=p=l([c(0,f.IBufferService),c(1,f.IOptionsService),c(2,f.IOscLinkService)],p)},6193:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.RenderDebouncer=void 0,s.RenderDebouncer=class{constructor(o,l){this._parentWindow=o,this._renderCallback=l,this._refreshCallbacks=[]}dispose(){this._animationFrame&&(this._parentWindow.cancelAnimationFrame(this._animationFrame),this._animationFrame=void 0)}addRefreshCallback(o){return this._refreshCallbacks.push(o),this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh())),this._animationFrame}refresh(o,l,c){this._rowCount=c,o=o!==void 0?o:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,o):o,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l,this._animationFrame||(this._animationFrame=this._parentWindow.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._animationFrame=void 0,this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return void this._runRefreshCallbacks();const o=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(o,l),this._runRefreshCallbacks()}_runRefreshCallbacks(){for(const o of this._refreshCallbacks)o(0);this._refreshCallbacks=[]}}},5596:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ScreenDprMonitor=void 0;const l=o(844);class c extends l.Disposable{constructor(f){super(),this._parentWindow=f,this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this.register((0,l.toDisposable)(()=>{this.clearListener()}))}setListener(f){this._listener&&this.clearListener(),this._listener=f,this._outerListener=()=>{this._listener&&(this._listener(this._parentWindow.devicePixelRatio,this._currentDevicePixelRatio),this._updateDpr())},this._updateDpr()}_updateDpr(){var f;this._outerListener&&((f=this._resolutionMediaMatchList)===null||f===void 0||f.removeListener(this._outerListener),this._currentDevicePixelRatio=this._parentWindow.devicePixelRatio,this._resolutionMediaMatchList=this._parentWindow.matchMedia(`screen and (resolution: ${this._parentWindow.devicePixelRatio}dppx)`),this._resolutionMediaMatchList.addListener(this._outerListener))}clearListener(){this._resolutionMediaMatchList&&this._listener&&this._outerListener&&(this._resolutionMediaMatchList.removeListener(this._outerListener),this._resolutionMediaMatchList=void 0,this._listener=void 0,this._outerListener=void 0)}}s.ScreenDprMonitor=c},3236:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Terminal=void 0;const l=o(3614),c=o(3656),a=o(6465),f=o(9042),p=o(3730),v=o(1680),g=o(3107),t=o(5744),n=o(2950),i=o(1296),r=o(428),h=o(4269),_=o(5114),S=o(8934),y=o(3230),u=o(9312),C=o(4725),L=o(6731),I=o(8055),R=o(8969),B=o(8460),O=o(844),$=o(6114),W=o(8437),z=o(2584),k=o(7399),D=o(5941),T=o(9074),M=o(2585),N=o(5435),U=o(4567),q=typeof window<"u"?window.document:null;class K extends R.CoreTerminal{get onFocus(){return this._onFocus.event}get onBlur(){return this._onBlur.event}get onA11yChar(){return this._onA11yCharEmitter.event}get onA11yTab(){return this._onA11yTabEmitter.event}get onWillOpen(){return this._onWillOpen.event}constructor(w={}){super(w),this.browser=$,this._keyDownHandled=!1,this._keyDownSeen=!1,this._keyPressHandled=!1,this._unprocessedDeadKey=!1,this._accessibilityManager=this.register(new O.MutableDisposable),this._onCursorMove=this.register(new B.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onKey=this.register(new B.EventEmitter),this.onKey=this._onKey.event,this._onRender=this.register(new B.EventEmitter),this.onRender=this._onRender.event,this._onSelectionChange=this.register(new B.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onTitleChange=this.register(new B.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onBell=this.register(new B.EventEmitter),this.onBell=this._onBell.event,this._onFocus=this.register(new B.EventEmitter),this._onBlur=this.register(new B.EventEmitter),this._onA11yCharEmitter=this.register(new B.EventEmitter),this._onA11yTabEmitter=this.register(new B.EventEmitter),this._onWillOpen=this.register(new B.EventEmitter),this._setup(),this.linkifier2=this.register(this._instantiationService.createInstance(a.Linkifier2)),this.linkifier2.registerLinkProvider(this._instantiationService.createInstance(p.OscLinkProvider)),this._decorationService=this._instantiationService.createInstance(T.DecorationService),this._instantiationService.setService(M.IDecorationService,this._decorationService),this.register(this._inputHandler.onRequestBell(()=>this._onBell.fire())),this.register(this._inputHandler.onRequestRefreshRows((P,F)=>this.refresh(P,F))),this.register(this._inputHandler.onRequestSendFocus(()=>this._reportFocus())),this.register(this._inputHandler.onRequestReset(()=>this.reset())),this.register(this._inputHandler.onRequestWindowsOptionsReport(P=>this._reportWindowsOptions(P))),this.register(this._inputHandler.onColor(P=>this._handleColorEvent(P))),this.register((0,B.forwardEvent)(this._inputHandler.onCursorMove,this._onCursorMove)),this.register((0,B.forwardEvent)(this._inputHandler.onTitleChange,this._onTitleChange)),this.register((0,B.forwardEvent)(this._inputHandler.onA11yChar,this._onA11yCharEmitter)),this.register((0,B.forwardEvent)(this._inputHandler.onA11yTab,this._onA11yTabEmitter)),this.register(this._bufferService.onResize(P=>this._afterResize(P.cols,P.rows))),this.register((0,O.toDisposable)(()=>{var P,F;this._customKeyEventHandler=void 0,(F=(P=this.element)===null||P===void 0?void 0:P.parentNode)===null||F===void 0||F.removeChild(this.element)}))}_handleColorEvent(w){if(this._themeService)for(const P of w){let F,H="";switch(P.index){case 256:F="foreground",H="10";break;case 257:F="background",H="11";break;case 258:F="cursor",H="12";break;default:F="ansi",H="4;"+P.index}switch(P.type){case 0:const V=I.color.toColorRGB(F==="ansi"?this._themeService.colors.ansi[P.index]:this._themeService.colors[F]);this.coreService.triggerDataEvent(`${z.C0.ESC}]${H};${(0,D.toRgbString)(V)}${z.C1_ESCAPED.ST}`);break;case 1:if(F==="ansi")this._themeService.modifyColors(j=>j.ansi[P.index]=I.rgba.toColor(...P.color));else{const j=F;this._themeService.modifyColors(Y=>Y[j]=I.rgba.toColor(...P.color))}break;case 2:this._themeService.restoreColor(P.index)}}}_setup(){super._setup(),this._customKeyEventHandler=void 0}get buffer(){return this.buffers.active}focus(){this.textarea&&this.textarea.focus({preventScroll:!0})}_handleScreenReaderModeOptionChange(w){w?!this._accessibilityManager.value&&this._renderService&&(this._accessibilityManager.value=this._instantiationService.createInstance(U.AccessibilityManager,this)):this._accessibilityManager.clear()}_handleTextAreaFocus(w){this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(z.C0.ESC+"[I"),this.updateCursorStyle(w),this.element.classList.add("focus"),this._showCursor(),this._onFocus.fire()}blur(){var w;return(w=this.textarea)===null||w===void 0?void 0:w.blur()}_handleTextAreaBlur(){this.textarea.value="",this.refresh(this.buffer.y,this.buffer.y),this.coreService.decPrivateModes.sendFocus&&this.coreService.triggerDataEvent(z.C0.ESC+"[O"),this.element.classList.remove("focus"),this._onBlur.fire()}_syncTextArea(){if(!this.textarea||!this.buffer.isCursorInViewport||this._compositionHelper.isComposing||!this._renderService)return;const w=this.buffer.ybase+this.buffer.y,P=this.buffer.lines.get(w);if(!P)return;const F=Math.min(this.buffer.x,this.cols-1),H=this._renderService.dimensions.css.cell.height,V=P.getWidth(F),j=this._renderService.dimensions.css.cell.width*V,Y=this.buffer.y*this._renderService.dimensions.css.cell.height,ie=F*this._renderService.dimensions.css.cell.width;this.textarea.style.left=ie+"px",this.textarea.style.top=Y+"px",this.textarea.style.width=j+"px",this.textarea.style.height=H+"px",this.textarea.style.lineHeight=H+"px",this.textarea.style.zIndex="-5"}_initGlobal(){this._bindKeys(),this.register((0,c.addDisposableDomListener)(this.element,"copy",P=>{this.hasSelection()&&(0,l.copyHandler)(P,this._selectionService)}));const w=P=>(0,l.handlePasteEvent)(P,this.textarea,this.coreService,this.optionsService);this.register((0,c.addDisposableDomListener)(this.textarea,"paste",w)),this.register((0,c.addDisposableDomListener)(this.element,"paste",w)),$.isFirefox?this.register((0,c.addDisposableDomListener)(this.element,"mousedown",P=>{P.button===2&&(0,l.rightClickHandler)(P,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})):this.register((0,c.addDisposableDomListener)(this.element,"contextmenu",P=>{(0,l.rightClickHandler)(P,this.textarea,this.screenElement,this._selectionService,this.options.rightClickSelectsWord)})),$.isLinux&&this.register((0,c.addDisposableDomListener)(this.element,"auxclick",P=>{P.button===1&&(0,l.moveTextAreaUnderMouseCursor)(P,this.textarea,this.screenElement)}))}_bindKeys(){this.register((0,c.addDisposableDomListener)(this.textarea,"keyup",w=>this._keyUp(w),!0)),this.register((0,c.addDisposableDomListener)(this.textarea,"keydown",w=>this._keyDown(w),!0)),this.register((0,c.addDisposableDomListener)(this.textarea,"keypress",w=>this._keyPress(w),!0)),this.register((0,c.addDisposableDomListener)(this.textarea,"compositionstart",()=>this._compositionHelper.compositionstart())),this.register((0,c.addDisposableDomListener)(this.textarea,"compositionupdate",w=>this._compositionHelper.compositionupdate(w))),this.register((0,c.addDisposableDomListener)(this.textarea,"compositionend",()=>this._compositionHelper.compositionend())),this.register((0,c.addDisposableDomListener)(this.textarea,"input",w=>this._inputEvent(w),!0)),this.register(this.onRender(()=>this._compositionHelper.updateCompositionElements()))}open(w){var P;if(!w)throw new Error("Terminal requires a parent element.");w.isConnected||this._logService.debug("Terminal.open was called on an element that was not attached to the DOM"),this._document=w.ownerDocument,this.element=this._document.createElement("div"),this.element.dir="ltr",this.element.classList.add("terminal"),this.element.classList.add("xterm"),w.appendChild(this.element);const F=q.createDocumentFragment();this._viewportElement=q.createElement("div"),this._viewportElement.classList.add("xterm-viewport"),F.appendChild(this._viewportElement),this._viewportScrollArea=q.createElement("div"),this._viewportScrollArea.classList.add("xterm-scroll-area"),this._viewportElement.appendChild(this._viewportScrollArea),this.screenElement=q.createElement("div"),this.screenElement.classList.add("xterm-screen"),this._helperContainer=q.createElement("div"),this._helperContainer.classList.add("xterm-helpers"),this.screenElement.appendChild(this._helperContainer),F.appendChild(this.screenElement),this.textarea=q.createElement("textarea"),this.textarea.classList.add("xterm-helper-textarea"),this.textarea.setAttribute("aria-label",f.promptLabel),$.isChromeOS||this.textarea.setAttribute("aria-multiline","false"),this.textarea.setAttribute("autocorrect","off"),this.textarea.setAttribute("autocapitalize","off"),this.textarea.setAttribute("spellcheck","false"),this.textarea.tabIndex=0,this._coreBrowserService=this._instantiationService.createInstance(_.CoreBrowserService,this.textarea,(P=this._document.defaultView)!==null&&P!==void 0?P:window),this._instantiationService.setService(C.ICoreBrowserService,this._coreBrowserService),this.register((0,c.addDisposableDomListener)(this.textarea,"focus",H=>this._handleTextAreaFocus(H))),this.register((0,c.addDisposableDomListener)(this.textarea,"blur",()=>this._handleTextAreaBlur())),this._helperContainer.appendChild(this.textarea),this._charSizeService=this._instantiationService.createInstance(r.CharSizeService,this._document,this._helperContainer),this._instantiationService.setService(C.ICharSizeService,this._charSizeService),this._themeService=this._instantiationService.createInstance(L.ThemeService),this._instantiationService.setService(C.IThemeService,this._themeService),this._characterJoinerService=this._instantiationService.createInstance(h.CharacterJoinerService),this._instantiationService.setService(C.ICharacterJoinerService,this._characterJoinerService),this._renderService=this.register(this._instantiationService.createInstance(y.RenderService,this.rows,this.screenElement)),this._instantiationService.setService(C.IRenderService,this._renderService),this.register(this._renderService.onRenderedViewportChange(H=>this._onRender.fire(H))),this.onResize(H=>this._renderService.resize(H.cols,H.rows)),this._compositionView=q.createElement("div"),this._compositionView.classList.add("composition-view"),this._compositionHelper=this._instantiationService.createInstance(n.CompositionHelper,this.textarea,this._compositionView),this._helperContainer.appendChild(this._compositionView),this.element.appendChild(F);try{this._onWillOpen.fire(this.element)}catch{}this._renderService.hasRenderer()||this._renderService.setRenderer(this._createRenderer()),this._mouseService=this._instantiationService.createInstance(S.MouseService),this._instantiationService.setService(C.IMouseService,this._mouseService),this.viewport=this._instantiationService.createInstance(v.Viewport,this._viewportElement,this._viewportScrollArea),this.viewport.onRequestScrollLines(H=>this.scrollLines(H.amount,H.suppressScrollEvent,1)),this.register(this._inputHandler.onRequestSyncScrollBar(()=>this.viewport.syncScrollArea())),this.register(this.viewport),this.register(this.onCursorMove(()=>{this._renderService.handleCursorMove(),this._syncTextArea()})),this.register(this.onResize(()=>this._renderService.handleResize(this.cols,this.rows))),this.register(this.onBlur(()=>this._renderService.handleBlur())),this.register(this.onFocus(()=>this._renderService.handleFocus())),this.register(this._renderService.onDimensionsChange(()=>this.viewport.syncScrollArea())),this._selectionService=this.register(this._instantiationService.createInstance(u.SelectionService,this.element,this.screenElement,this.linkifier2)),this._instantiationService.setService(C.ISelectionService,this._selectionService),this.register(this._selectionService.onRequestScrollLines(H=>this.scrollLines(H.amount,H.suppressScrollEvent))),this.register(this._selectionService.onSelectionChange(()=>this._onSelectionChange.fire())),this.register(this._selectionService.onRequestRedraw(H=>this._renderService.handleSelectionChanged(H.start,H.end,H.columnSelectMode))),this.register(this._selectionService.onLinuxMouseSelection(H=>{this.textarea.value=H,this.textarea.focus(),this.textarea.select()})),this.register(this._onScroll.event(H=>{this.viewport.syncScrollArea(),this._selectionService.refresh()})),this.register((0,c.addDisposableDomListener)(this._viewportElement,"scroll",()=>this._selectionService.refresh())),this.linkifier2.attachToDom(this.screenElement,this._mouseService,this._renderService),this.register(this._instantiationService.createInstance(g.BufferDecorationRenderer,this.screenElement)),this.register((0,c.addDisposableDomListener)(this.element,"mousedown",H=>this._selectionService.handleMouseDown(H))),this.coreMouseService.areMouseEventsActive?(this._selectionService.disable(),this.element.classList.add("enable-mouse-events")):this._selectionService.enable(),this.options.screenReaderMode&&(this._accessibilityManager.value=this._instantiationService.createInstance(U.AccessibilityManager,this)),this.register(this.optionsService.onSpecificOptionChange("screenReaderMode",H=>this._handleScreenReaderModeOptionChange(H))),this.options.overviewRulerWidth&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(t.OverviewRulerRenderer,this._viewportElement,this.screenElement))),this.optionsService.onSpecificOptionChange("overviewRulerWidth",H=>{!this._overviewRulerRenderer&&H&&this._viewportElement&&this.screenElement&&(this._overviewRulerRenderer=this.register(this._instantiationService.createInstance(t.OverviewRulerRenderer,this._viewportElement,this.screenElement)))}),this._charSizeService.measure(),this.refresh(0,this.rows-1),this._initGlobal(),this.bindMouse()}_createRenderer(){return this._instantiationService.createInstance(i.DomRenderer,this.element,this.screenElement,this._viewportElement,this.linkifier2)}bindMouse(){const w=this,P=this.element;function F(j){const Y=w._mouseService.getMouseReportCoords(j,w.screenElement);if(!Y)return!1;let ie,ae;switch(j.overrideType||j.type){case"mousemove":ae=32,j.buttons===void 0?(ie=3,j.button!==void 0&&(ie=j.button<3?j.button:3)):ie=1&j.buttons?0:4&j.buttons?1:2&j.buttons?2:3;break;case"mouseup":ae=0,ie=j.button<3?j.button:3;break;case"mousedown":ae=1,ie=j.button<3?j.button:3;break;case"wheel":if(w.viewport.getLinesScrolled(j)===0)return!1;ae=j.deltaY<0?0:1,ie=4;break;default:return!1}return!(ae===void 0||ie===void 0||ie>4)&&w.coreMouseService.triggerMouseEvent({col:Y.col,row:Y.row,x:Y.x,y:Y.y,button:ie,action:ae,ctrl:j.ctrlKey,alt:j.altKey,shift:j.shiftKey})}const H={mouseup:null,wheel:null,mousedrag:null,mousemove:null},V={mouseup:j=>(F(j),j.buttons||(this._document.removeEventListener("mouseup",H.mouseup),H.mousedrag&&this._document.removeEventListener("mousemove",H.mousedrag)),this.cancel(j)),wheel:j=>(F(j),this.cancel(j,!0)),mousedrag:j=>{j.buttons&&F(j)},mousemove:j=>{j.buttons||F(j)}};this.register(this.coreMouseService.onProtocolChange(j=>{j?(this.optionsService.rawOptions.logLevel==="debug"&&this._logService.debug("Binding to mouse events:",this.coreMouseService.explainEvents(j)),this.element.classList.add("enable-mouse-events"),this._selectionService.disable()):(this._logService.debug("Unbinding from mouse events."),this.element.classList.remove("enable-mouse-events"),this._selectionService.enable()),8&j?H.mousemove||(P.addEventListener("mousemove",V.mousemove),H.mousemove=V.mousemove):(P.removeEventListener("mousemove",H.mousemove),H.mousemove=null),16&j?H.wheel||(P.addEventListener("wheel",V.wheel,{passive:!1}),H.wheel=V.wheel):(P.removeEventListener("wheel",H.wheel),H.wheel=null),2&j?H.mouseup||(P.addEventListener("mouseup",V.mouseup),H.mouseup=V.mouseup):(this._document.removeEventListener("mouseup",H.mouseup),P.removeEventListener("mouseup",H.mouseup),H.mouseup=null),4&j?H.mousedrag||(H.mousedrag=V.mousedrag):(this._document.removeEventListener("mousemove",H.mousedrag),H.mousedrag=null)})),this.coreMouseService.activeProtocol=this.coreMouseService.activeProtocol,this.register((0,c.addDisposableDomListener)(P,"mousedown",j=>{if(j.preventDefault(),this.focus(),this.coreMouseService.areMouseEventsActive&&!this._selectionService.shouldForceSelection(j))return F(j),H.mouseup&&this._document.addEventListener("mouseup",H.mouseup),H.mousedrag&&this._document.addEventListener("mousemove",H.mousedrag),this.cancel(j)})),this.register((0,c.addDisposableDomListener)(P,"wheel",j=>{if(!H.wheel){if(!this.buffer.hasScrollback){const Y=this.viewport.getLinesScrolled(j);if(Y===0)return;const ie=z.C0.ESC+(this.coreService.decPrivateModes.applicationCursorKeys?"O":"[")+(j.deltaY<0?"A":"B");let ae="";for(let be=0;be<Math.abs(Y);be++)ae+=ie;return this.coreService.triggerDataEvent(ae,!0),this.cancel(j,!0)}return this.viewport.handleWheel(j)?this.cancel(j):void 0}},{passive:!1})),this.register((0,c.addDisposableDomListener)(P,"touchstart",j=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchStart(j),this.cancel(j)},{passive:!0})),this.register((0,c.addDisposableDomListener)(P,"touchmove",j=>{if(!this.coreMouseService.areMouseEventsActive)return this.viewport.handleTouchMove(j)?void 0:this.cancel(j)},{passive:!1}))}refresh(w,P){var F;(F=this._renderService)===null||F===void 0||F.refreshRows(w,P)}updateCursorStyle(w){var P;!((P=this._selectionService)===null||P===void 0)&&P.shouldColumnSelect(w)?this.element.classList.add("column-select"):this.element.classList.remove("column-select")}_showCursor(){this.coreService.isCursorInitialized||(this.coreService.isCursorInitialized=!0,this.refresh(this.buffer.y,this.buffer.y))}scrollLines(w,P,F=0){var H;F===1?(super.scrollLines(w,P,F),this.refresh(0,this.rows-1)):(H=this.viewport)===null||H===void 0||H.scrollLines(w)}paste(w){(0,l.paste)(w,this.textarea,this.coreService,this.optionsService)}attachCustomKeyEventHandler(w){this._customKeyEventHandler=w}registerLinkProvider(w){return this.linkifier2.registerLinkProvider(w)}registerCharacterJoiner(w){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");const P=this._characterJoinerService.register(w);return this.refresh(0,this.rows-1),P}deregisterCharacterJoiner(w){if(!this._characterJoinerService)throw new Error("Terminal must be opened first");this._characterJoinerService.deregister(w)&&this.refresh(0,this.rows-1)}get markers(){return this.buffer.markers}registerMarker(w){return this.buffer.addMarker(this.buffer.ybase+this.buffer.y+w)}registerDecoration(w){return this._decorationService.registerDecoration(w)}hasSelection(){return!!this._selectionService&&this._selectionService.hasSelection}select(w,P,F){this._selectionService.setSelection(w,P,F)}getSelection(){return this._selectionService?this._selectionService.selectionText:""}getSelectionPosition(){if(this._selectionService&&this._selectionService.hasSelection)return{start:{x:this._selectionService.selectionStart[0],y:this._selectionService.selectionStart[1]},end:{x:this._selectionService.selectionEnd[0],y:this._selectionService.selectionEnd[1]}}}clearSelection(){var w;(w=this._selectionService)===null||w===void 0||w.clearSelection()}selectAll(){var w;(w=this._selectionService)===null||w===void 0||w.selectAll()}selectLines(w,P){var F;(F=this._selectionService)===null||F===void 0||F.selectLines(w,P)}_keyDown(w){if(this._keyDownHandled=!1,this._keyDownSeen=!0,this._customKeyEventHandler&&this._customKeyEventHandler(w)===!1)return!1;const P=this.browser.isMac&&this.options.macOptionIsMeta&&w.altKey;if(!P&&!this._compositionHelper.keydown(w))return this.options.scrollOnUserInput&&this.buffer.ybase!==this.buffer.ydisp&&this.scrollToBottom(),!1;P||w.key!=="Dead"&&w.key!=="AltGraph"||(this._unprocessedDeadKey=!0);const F=(0,k.evaluateKeyboardEvent)(w,this.coreService.decPrivateModes.applicationCursorKeys,this.browser.isMac,this.options.macOptionIsMeta);if(this.updateCursorStyle(w),F.type===3||F.type===2){const H=this.rows-1;return this.scrollLines(F.type===2?-H:H),this.cancel(w,!0)}return F.type===1&&this.selectAll(),!!this._isThirdLevelShift(this.browser,w)||(F.cancel&&this.cancel(w,!0),!F.key||!!(w.key&&!w.ctrlKey&&!w.altKey&&!w.metaKey&&w.key.length===1&&w.key.charCodeAt(0)>=65&&w.key.charCodeAt(0)<=90)||(this._unprocessedDeadKey?(this._unprocessedDeadKey=!1,!0):(F.key!==z.C0.ETX&&F.key!==z.C0.CR||(this.textarea.value=""),this._onKey.fire({key:F.key,domEvent:w}),this._showCursor(),this.coreService.triggerDataEvent(F.key,!0),!this.optionsService.rawOptions.screenReaderMode||w.altKey||w.ctrlKey?this.cancel(w,!0):void(this._keyDownHandled=!0))))}_isThirdLevelShift(w,P){const F=w.isMac&&!this.options.macOptionIsMeta&&P.altKey&&!P.ctrlKey&&!P.metaKey||w.isWindows&&P.altKey&&P.ctrlKey&&!P.metaKey||w.isWindows&&P.getModifierState("AltGraph");return P.type==="keypress"?F:F&&(!P.keyCode||P.keyCode>47)}_keyUp(w){this._keyDownSeen=!1,this._customKeyEventHandler&&this._customKeyEventHandler(w)===!1||(function(P){return P.keyCode===16||P.keyCode===17||P.keyCode===18}(w)||this.focus(),this.updateCursorStyle(w),this._keyPressHandled=!1)}_keyPress(w){let P;if(this._keyPressHandled=!1,this._keyDownHandled||this._customKeyEventHandler&&this._customKeyEventHandler(w)===!1)return!1;if(this.cancel(w),w.charCode)P=w.charCode;else if(w.which===null||w.which===void 0)P=w.keyCode;else{if(w.which===0||w.charCode===0)return!1;P=w.which}return!(!P||(w.altKey||w.ctrlKey||w.metaKey)&&!this._isThirdLevelShift(this.browser,w)||(P=String.fromCharCode(P),this._onKey.fire({key:P,domEvent:w}),this._showCursor(),this.coreService.triggerDataEvent(P,!0),this._keyPressHandled=!0,this._unprocessedDeadKey=!1,0))}_inputEvent(w){if(w.data&&w.inputType==="insertText"&&(!w.composed||!this._keyDownSeen)&&!this.optionsService.rawOptions.screenReaderMode){if(this._keyPressHandled)return!1;this._unprocessedDeadKey=!1;const P=w.data;return this.coreService.triggerDataEvent(P,!0),this.cancel(w),!0}return!1}resize(w,P){w!==this.cols||P!==this.rows?super.resize(w,P):this._charSizeService&&!this._charSizeService.hasValidSize&&this._charSizeService.measure()}_afterResize(w,P){var F,H;(F=this._charSizeService)===null||F===void 0||F.measure(),(H=this.viewport)===null||H===void 0||H.syncScrollArea(!0)}clear(){var w;if(this.buffer.ybase!==0||this.buffer.y!==0){this.buffer.clearAllMarkers(),this.buffer.lines.set(0,this.buffer.lines.get(this.buffer.ybase+this.buffer.y)),this.buffer.lines.length=1,this.buffer.ydisp=0,this.buffer.ybase=0,this.buffer.y=0;for(let P=1;P<this.rows;P++)this.buffer.lines.push(this.buffer.getBlankLine(W.DEFAULT_ATTR_DATA));this._onScroll.fire({position:this.buffer.ydisp,source:0}),(w=this.viewport)===null||w===void 0||w.reset(),this.refresh(0,this.rows-1)}}reset(){var w,P;this.options.rows=this.rows,this.options.cols=this.cols;const F=this._customKeyEventHandler;this._setup(),super.reset(),(w=this._selectionService)===null||w===void 0||w.reset(),this._decorationService.reset(),(P=this.viewport)===null||P===void 0||P.reset(),this._customKeyEventHandler=F,this.refresh(0,this.rows-1)}clearTextureAtlas(){var w;(w=this._renderService)===null||w===void 0||w.clearTextureAtlas()}_reportFocus(){var w;!((w=this.element)===null||w===void 0)&&w.classList.contains("focus")?this.coreService.triggerDataEvent(z.C0.ESC+"[I"):this.coreService.triggerDataEvent(z.C0.ESC+"[O")}_reportWindowsOptions(w){if(this._renderService)switch(w){case N.WindowsOptionsReportType.GET_WIN_SIZE_PIXELS:const P=this._renderService.dimensions.css.canvas.width.toFixed(0),F=this._renderService.dimensions.css.canvas.height.toFixed(0);this.coreService.triggerDataEvent(`${z.C0.ESC}[4;${F};${P}t`);break;case N.WindowsOptionsReportType.GET_CELL_SIZE_PIXELS:const H=this._renderService.dimensions.css.cell.width.toFixed(0),V=this._renderService.dimensions.css.cell.height.toFixed(0);this.coreService.triggerDataEvent(`${z.C0.ESC}[6;${V};${H}t`)}}cancel(w,P){if(this.options.cancelEvents||P)return w.preventDefault(),w.stopPropagation(),!1}}s.Terminal=K},9924:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TimeBasedDebouncer=void 0,s.TimeBasedDebouncer=class{constructor(o,l=1e3){this._renderCallback=o,this._debounceThresholdMS=l,this._lastRefreshMs=0,this._additionalRefreshRequested=!1}dispose(){this._refreshTimeoutID&&clearTimeout(this._refreshTimeoutID)}refresh(o,l,c){this._rowCount=c,o=o!==void 0?o:0,l=l!==void 0?l:this._rowCount-1,this._rowStart=this._rowStart!==void 0?Math.min(this._rowStart,o):o,this._rowEnd=this._rowEnd!==void 0?Math.max(this._rowEnd,l):l;const a=Date.now();if(a-this._lastRefreshMs>=this._debounceThresholdMS)this._lastRefreshMs=a,this._innerRefresh();else if(!this._additionalRefreshRequested){const f=a-this._lastRefreshMs,p=this._debounceThresholdMS-f;this._additionalRefreshRequested=!0,this._refreshTimeoutID=window.setTimeout(()=>{this._lastRefreshMs=Date.now(),this._innerRefresh(),this._additionalRefreshRequested=!1,this._refreshTimeoutID=void 0},p)}}_innerRefresh(){if(this._rowStart===void 0||this._rowEnd===void 0||this._rowCount===void 0)return;const o=Math.max(this._rowStart,0),l=Math.min(this._rowEnd,this._rowCount-1);this._rowStart=void 0,this._rowEnd=void 0,this._renderCallback(o,l)}}},1680:function(x,s,o){var l=this&&this.__decorate||function(n,i,r,h){var _,S=arguments.length,y=S<3?i:h===null?h=Object.getOwnPropertyDescriptor(i,r):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(n,i,r,h);else for(var u=n.length-1;u>=0;u--)(_=n[u])&&(y=(S<3?_(y):S>3?_(i,r,y):_(i,r))||y);return S>3&&y&&Object.defineProperty(i,r,y),y},c=this&&this.__param||function(n,i){return function(r,h){i(r,h,n)}};Object.defineProperty(s,"__esModule",{value:!0}),s.Viewport=void 0;const a=o(3656),f=o(4725),p=o(8460),v=o(844),g=o(2585);let t=s.Viewport=class extends v.Disposable{constructor(n,i,r,h,_,S,y,u){super(),this._viewportElement=n,this._scrollArea=i,this._bufferService=r,this._optionsService=h,this._charSizeService=_,this._renderService=S,this._coreBrowserService=y,this.scrollBarWidth=0,this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._wheelPartialScroll=0,this._refreshAnimationFrame=null,this._ignoreNextScrollEvent=!1,this._smoothScrollState={startTime:0,origin:-1,target:-1},this._onRequestScrollLines=this.register(new p.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this.scrollBarWidth=this._viewportElement.offsetWidth-this._scrollArea.offsetWidth||15,this.register((0,a.addDisposableDomListener)(this._viewportElement,"scroll",this._handleScroll.bind(this))),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(C=>this._activeBuffer=C.activeBuffer)),this._renderDimensions=this._renderService.dimensions,this.register(this._renderService.onDimensionsChange(C=>this._renderDimensions=C)),this._handleThemeChange(u.colors),this.register(u.onChangeColors(C=>this._handleThemeChange(C))),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.syncScrollArea())),setTimeout(()=>this.syncScrollArea())}_handleThemeChange(n){this._viewportElement.style.backgroundColor=n.background.css}reset(){this._currentRowHeight=0,this._currentDeviceCellHeight=0,this._lastRecordedBufferLength=0,this._lastRecordedViewportHeight=0,this._lastRecordedBufferHeight=0,this._lastTouchY=0,this._lastScrollTop=0,this._coreBrowserService.window.requestAnimationFrame(()=>this.syncScrollArea())}_refresh(n){if(n)return this._innerRefresh(),void(this._refreshAnimationFrame!==null&&this._coreBrowserService.window.cancelAnimationFrame(this._refreshAnimationFrame));this._refreshAnimationFrame===null&&(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._innerRefresh()))}_innerRefresh(){if(this._charSizeService.height>0){this._currentRowHeight=this._renderService.dimensions.device.cell.height/this._coreBrowserService.dpr,this._currentDeviceCellHeight=this._renderService.dimensions.device.cell.height,this._lastRecordedViewportHeight=this._viewportElement.offsetHeight;const i=Math.round(this._currentRowHeight*this._lastRecordedBufferLength)+(this._lastRecordedViewportHeight-this._renderService.dimensions.css.canvas.height);this._lastRecordedBufferHeight!==i&&(this._lastRecordedBufferHeight=i,this._scrollArea.style.height=this._lastRecordedBufferHeight+"px")}const n=this._bufferService.buffer.ydisp*this._currentRowHeight;this._viewportElement.scrollTop!==n&&(this._ignoreNextScrollEvent=!0,this._viewportElement.scrollTop=n),this._refreshAnimationFrame=null}syncScrollArea(n=!1){if(this._lastRecordedBufferLength!==this._bufferService.buffer.lines.length)return this._lastRecordedBufferLength=this._bufferService.buffer.lines.length,void this._refresh(n);this._lastRecordedViewportHeight===this._renderService.dimensions.css.canvas.height&&this._lastScrollTop===this._activeBuffer.ydisp*this._currentRowHeight&&this._renderDimensions.device.cell.height===this._currentDeviceCellHeight||this._refresh(n)}_handleScroll(n){if(this._lastScrollTop=this._viewportElement.scrollTop,!this._viewportElement.offsetParent)return;if(this._ignoreNextScrollEvent)return this._ignoreNextScrollEvent=!1,void this._onRequestScrollLines.fire({amount:0,suppressScrollEvent:!0});const i=Math.round(this._lastScrollTop/this._currentRowHeight)-this._bufferService.buffer.ydisp;this._onRequestScrollLines.fire({amount:i,suppressScrollEvent:!0})}_smoothScroll(){if(this._isDisposed||this._smoothScrollState.origin===-1||this._smoothScrollState.target===-1)return;const n=this._smoothScrollPercent();this._viewportElement.scrollTop=this._smoothScrollState.origin+Math.round(n*(this._smoothScrollState.target-this._smoothScrollState.origin)),n<1?this._coreBrowserService.window.requestAnimationFrame(()=>this._smoothScroll()):this._clearSmoothScrollState()}_smoothScrollPercent(){return this._optionsService.rawOptions.smoothScrollDuration&&this._smoothScrollState.startTime?Math.max(Math.min((Date.now()-this._smoothScrollState.startTime)/this._optionsService.rawOptions.smoothScrollDuration,1),0):1}_clearSmoothScrollState(){this._smoothScrollState.startTime=0,this._smoothScrollState.origin=-1,this._smoothScrollState.target=-1}_bubbleScroll(n,i){const r=this._viewportElement.scrollTop+this._lastRecordedViewportHeight;return!(i<0&&this._viewportElement.scrollTop!==0||i>0&&r<this._lastRecordedBufferHeight)||(n.cancelable&&n.preventDefault(),!1)}handleWheel(n){const i=this._getPixelsScrolled(n);return i!==0&&(this._optionsService.rawOptions.smoothScrollDuration?(this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target===-1?this._smoothScrollState.target=this._viewportElement.scrollTop+i:this._smoothScrollState.target+=i,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()):this._viewportElement.scrollTop+=i,this._bubbleScroll(n,i))}scrollLines(n){if(n!==0)if(this._optionsService.rawOptions.smoothScrollDuration){const i=n*this._currentRowHeight;this._smoothScrollState.startTime=Date.now(),this._smoothScrollPercent()<1?(this._smoothScrollState.origin=this._viewportElement.scrollTop,this._smoothScrollState.target=this._smoothScrollState.origin+i,this._smoothScrollState.target=Math.max(Math.min(this._smoothScrollState.target,this._viewportElement.scrollHeight),0),this._smoothScroll()):this._clearSmoothScrollState()}else this._onRequestScrollLines.fire({amount:n,suppressScrollEvent:!1})}_getPixelsScrolled(n){if(n.deltaY===0||n.shiftKey)return 0;let i=this._applyScrollModifier(n.deltaY,n);return n.deltaMode===WheelEvent.DOM_DELTA_LINE?i*=this._currentRowHeight:n.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(i*=this._currentRowHeight*this._bufferService.rows),i}getBufferElements(n,i){var r;let h,_="";const S=[],y=i??this._bufferService.buffer.lines.length,u=this._bufferService.buffer.lines;for(let C=n;C<y;C++){const L=u.get(C);if(!L)continue;const I=(r=u.get(C+1))===null||r===void 0?void 0:r.isWrapped;if(_+=L.translateToString(!I),!I||C===u.length-1){const R=document.createElement("div");R.textContent=_,S.push(R),_.length>0&&(h=R),_=""}}return{bufferElements:S,cursorElement:h}}getLinesScrolled(n){if(n.deltaY===0||n.shiftKey)return 0;let i=this._applyScrollModifier(n.deltaY,n);return n.deltaMode===WheelEvent.DOM_DELTA_PIXEL?(i/=this._currentRowHeight+0,this._wheelPartialScroll+=i,i=Math.floor(Math.abs(this._wheelPartialScroll))*(this._wheelPartialScroll>0?1:-1),this._wheelPartialScroll%=1):n.deltaMode===WheelEvent.DOM_DELTA_PAGE&&(i*=this._bufferService.rows),i}_applyScrollModifier(n,i){const r=this._optionsService.rawOptions.fastScrollModifier;return r==="alt"&&i.altKey||r==="ctrl"&&i.ctrlKey||r==="shift"&&i.shiftKey?n*this._optionsService.rawOptions.fastScrollSensitivity*this._optionsService.rawOptions.scrollSensitivity:n*this._optionsService.rawOptions.scrollSensitivity}handleTouchStart(n){this._lastTouchY=n.touches[0].pageY}handleTouchMove(n){const i=this._lastTouchY-n.touches[0].pageY;return this._lastTouchY=n.touches[0].pageY,i!==0&&(this._viewportElement.scrollTop+=i,this._bubbleScroll(n,i))}};s.Viewport=t=l([c(2,g.IBufferService),c(3,g.IOptionsService),c(4,f.ICharSizeService),c(5,f.IRenderService),c(6,f.ICoreBrowserService),c(7,f.IThemeService)],t)},3107:function(x,s,o){var l=this&&this.__decorate||function(t,n,i,r){var h,_=arguments.length,S=_<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(t,n,i,r);else for(var y=t.length-1;y>=0;y--)(h=t[y])&&(S=(_<3?h(S):_>3?h(n,i,S):h(n,i))||S);return _>3&&S&&Object.defineProperty(n,i,S),S},c=this&&this.__param||function(t,n){return function(i,r){n(i,r,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.BufferDecorationRenderer=void 0;const a=o(3656),f=o(4725),p=o(844),v=o(2585);let g=s.BufferDecorationRenderer=class extends p.Disposable{constructor(t,n,i,r){super(),this._screenElement=t,this._bufferService=n,this._decorationService=i,this._renderService=r,this._decorationElements=new Map,this._altBufferIsActive=!1,this._dimensionsChanged=!1,this._container=document.createElement("div"),this._container.classList.add("xterm-decoration-container"),this._screenElement.appendChild(this._container),this.register(this._renderService.onRenderedViewportChange(()=>this._doRefreshDecorations())),this.register(this._renderService.onDimensionsChange(()=>{this._dimensionsChanged=!0,this._queueRefresh()})),this.register((0,a.addDisposableDomListener)(window,"resize",()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._altBufferIsActive=this._bufferService.buffer===this._bufferService.buffers.alt})),this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh())),this.register(this._decorationService.onDecorationRemoved(h=>this._removeDecoration(h))),this.register((0,p.toDisposable)(()=>{this._container.remove(),this._decorationElements.clear()}))}_queueRefresh(){this._animationFrame===void 0&&(this._animationFrame=this._renderService.addRefreshCallback(()=>{this._doRefreshDecorations(),this._animationFrame=void 0}))}_doRefreshDecorations(){for(const t of this._decorationService.decorations)this._renderDecoration(t);this._dimensionsChanged=!1}_renderDecoration(t){this._refreshStyle(t),this._dimensionsChanged&&this._refreshXPosition(t)}_createElement(t){var n,i;const r=document.createElement("div");r.classList.add("xterm-decoration"),r.classList.toggle("xterm-decoration-top-layer",((n=t==null?void 0:t.options)===null||n===void 0?void 0:n.layer)==="top"),r.style.width=`${Math.round((t.options.width||1)*this._renderService.dimensions.css.cell.width)}px`,r.style.height=(t.options.height||1)*this._renderService.dimensions.css.cell.height+"px",r.style.top=(t.marker.line-this._bufferService.buffers.active.ydisp)*this._renderService.dimensions.css.cell.height+"px",r.style.lineHeight=`${this._renderService.dimensions.css.cell.height}px`;const h=(i=t.options.x)!==null&&i!==void 0?i:0;return h&&h>this._bufferService.cols&&(r.style.display="none"),this._refreshXPosition(t,r),r}_refreshStyle(t){const n=t.marker.line-this._bufferService.buffers.active.ydisp;if(n<0||n>=this._bufferService.rows)t.element&&(t.element.style.display="none",t.onRenderEmitter.fire(t.element));else{let i=this._decorationElements.get(t);i||(i=this._createElement(t),t.element=i,this._decorationElements.set(t,i),this._container.appendChild(i),t.onDispose(()=>{this._decorationElements.delete(t),i.remove()})),i.style.top=n*this._renderService.dimensions.css.cell.height+"px",i.style.display=this._altBufferIsActive?"none":"block",t.onRenderEmitter.fire(i)}}_refreshXPosition(t,n=t.element){var i;if(!n)return;const r=(i=t.options.x)!==null&&i!==void 0?i:0;(t.options.anchor||"left")==="right"?n.style.right=r?r*this._renderService.dimensions.css.cell.width+"px":"":n.style.left=r?r*this._renderService.dimensions.css.cell.width+"px":""}_removeDecoration(t){var n;(n=this._decorationElements.get(t))===null||n===void 0||n.remove(),this._decorationElements.delete(t),t.dispose()}};s.BufferDecorationRenderer=g=l([c(1,v.IBufferService),c(2,v.IDecorationService),c(3,f.IRenderService)],g)},5871:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ColorZoneStore=void 0,s.ColorZoneStore=class{constructor(){this._zones=[],this._zonePool=[],this._zonePoolIndex=0,this._linePadding={full:0,left:0,center:0,right:0}}get zones(){return this._zonePool.length=Math.min(this._zonePool.length,this._zones.length),this._zones}clear(){this._zones.length=0,this._zonePoolIndex=0}addDecoration(o){if(o.options.overviewRulerOptions){for(const l of this._zones)if(l.color===o.options.overviewRulerOptions.color&&l.position===o.options.overviewRulerOptions.position){if(this._lineIntersectsZone(l,o.marker.line))return;if(this._lineAdjacentToZone(l,o.marker.line,o.options.overviewRulerOptions.position))return void this._addLineToZone(l,o.marker.line)}if(this._zonePoolIndex<this._zonePool.length)return this._zonePool[this._zonePoolIndex].color=o.options.overviewRulerOptions.color,this._zonePool[this._zonePoolIndex].position=o.options.overviewRulerOptions.position,this._zonePool[this._zonePoolIndex].startBufferLine=o.marker.line,this._zonePool[this._zonePoolIndex].endBufferLine=o.marker.line,void this._zones.push(this._zonePool[this._zonePoolIndex++]);this._zones.push({color:o.options.overviewRulerOptions.color,position:o.options.overviewRulerOptions.position,startBufferLine:o.marker.line,endBufferLine:o.marker.line}),this._zonePool.push(this._zones[this._zones.length-1]),this._zonePoolIndex++}}setPadding(o){this._linePadding=o}_lineIntersectsZone(o,l){return l>=o.startBufferLine&&l<=o.endBufferLine}_lineAdjacentToZone(o,l,c){return l>=o.startBufferLine-this._linePadding[c||"full"]&&l<=o.endBufferLine+this._linePadding[c||"full"]}_addLineToZone(o,l){o.startBufferLine=Math.min(o.startBufferLine,l),o.endBufferLine=Math.max(o.endBufferLine,l)}}},5744:function(x,s,o){var l=this&&this.__decorate||function(h,_,S,y){var u,C=arguments.length,L=C<3?_:y===null?y=Object.getOwnPropertyDescriptor(_,S):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")L=Reflect.decorate(h,_,S,y);else for(var I=h.length-1;I>=0;I--)(u=h[I])&&(L=(C<3?u(L):C>3?u(_,S,L):u(_,S))||L);return C>3&&L&&Object.defineProperty(_,S,L),L},c=this&&this.__param||function(h,_){return function(S,y){_(S,y,h)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OverviewRulerRenderer=void 0;const a=o(5871),f=o(3656),p=o(4725),v=o(844),g=o(2585),t={full:0,left:0,center:0,right:0},n={full:0,left:0,center:0,right:0},i={full:0,left:0,center:0,right:0};let r=s.OverviewRulerRenderer=class extends v.Disposable{get _width(){return this._optionsService.options.overviewRulerWidth||0}constructor(h,_,S,y,u,C,L){var I;super(),this._viewportElement=h,this._screenElement=_,this._bufferService=S,this._decorationService=y,this._renderService=u,this._optionsService=C,this._coreBrowseService=L,this._colorZoneStore=new a.ColorZoneStore,this._shouldUpdateDimensions=!0,this._shouldUpdateAnchor=!0,this._lastKnownBufferLength=0,this._canvas=document.createElement("canvas"),this._canvas.classList.add("xterm-decoration-overview-ruler"),this._refreshCanvasDimensions(),(I=this._viewportElement.parentElement)===null||I===void 0||I.insertBefore(this._canvas,this._viewportElement);const R=this._canvas.getContext("2d");if(!R)throw new Error("Ctx cannot be null");this._ctx=R,this._registerDecorationListeners(),this._registerBufferChangeListeners(),this._registerDimensionChangeListeners(),this.register((0,v.toDisposable)(()=>{var B;(B=this._canvas)===null||B===void 0||B.remove()}))}_registerDecorationListeners(){this.register(this._decorationService.onDecorationRegistered(()=>this._queueRefresh(void 0,!0))),this.register(this._decorationService.onDecorationRemoved(()=>this._queueRefresh(void 0,!0)))}_registerBufferChangeListeners(){this.register(this._renderService.onRenderedViewportChange(()=>this._queueRefresh())),this.register(this._bufferService.buffers.onBufferActivate(()=>{this._canvas.style.display=this._bufferService.buffer===this._bufferService.buffers.alt?"none":"block"})),this.register(this._bufferService.onScroll(()=>{this._lastKnownBufferLength!==this._bufferService.buffers.normal.lines.length&&(this._refreshDrawHeightConstants(),this._refreshColorZonePadding())}))}_registerDimensionChangeListeners(){this.register(this._renderService.onRender(()=>{this._containerHeight&&this._containerHeight===this._screenElement.clientHeight||(this._queueRefresh(!0),this._containerHeight=this._screenElement.clientHeight)})),this.register(this._optionsService.onSpecificOptionChange("overviewRulerWidth",()=>this._queueRefresh(!0))),this.register((0,f.addDisposableDomListener)(this._coreBrowseService.window,"resize",()=>this._queueRefresh(!0))),this._queueRefresh(!0)}_refreshDrawConstants(){const h=Math.floor(this._canvas.width/3),_=Math.ceil(this._canvas.width/3);n.full=this._canvas.width,n.left=h,n.center=_,n.right=h,this._refreshDrawHeightConstants(),i.full=0,i.left=0,i.center=n.left,i.right=n.left+n.center}_refreshDrawHeightConstants(){t.full=Math.round(2*this._coreBrowseService.dpr);const h=this._canvas.height/this._bufferService.buffer.lines.length,_=Math.round(Math.max(Math.min(h,12),6)*this._coreBrowseService.dpr);t.left=_,t.center=_,t.right=_}_refreshColorZonePadding(){this._colorZoneStore.setPadding({full:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*t.full),left:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*t.left),center:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*t.center),right:Math.floor(this._bufferService.buffers.active.lines.length/(this._canvas.height-1)*t.right)}),this._lastKnownBufferLength=this._bufferService.buffers.normal.lines.length}_refreshCanvasDimensions(){this._canvas.style.width=`${this._width}px`,this._canvas.width=Math.round(this._width*this._coreBrowseService.dpr),this._canvas.style.height=`${this._screenElement.clientHeight}px`,this._canvas.height=Math.round(this._screenElement.clientHeight*this._coreBrowseService.dpr),this._refreshDrawConstants(),this._refreshColorZonePadding()}_refreshDecorations(){this._shouldUpdateDimensions&&this._refreshCanvasDimensions(),this._ctx.clearRect(0,0,this._canvas.width,this._canvas.height),this._colorZoneStore.clear();for(const _ of this._decorationService.decorations)this._colorZoneStore.addDecoration(_);this._ctx.lineWidth=1;const h=this._colorZoneStore.zones;for(const _ of h)_.position!=="full"&&this._renderColorZone(_);for(const _ of h)_.position==="full"&&this._renderColorZone(_);this._shouldUpdateDimensions=!1,this._shouldUpdateAnchor=!1}_renderColorZone(h){this._ctx.fillStyle=h.color,this._ctx.fillRect(i[h.position||"full"],Math.round((this._canvas.height-1)*(h.startBufferLine/this._bufferService.buffers.active.lines.length)-t[h.position||"full"]/2),n[h.position||"full"],Math.round((this._canvas.height-1)*((h.endBufferLine-h.startBufferLine)/this._bufferService.buffers.active.lines.length)+t[h.position||"full"]))}_queueRefresh(h,_){this._shouldUpdateDimensions=h||this._shouldUpdateDimensions,this._shouldUpdateAnchor=_||this._shouldUpdateAnchor,this._animationFrame===void 0&&(this._animationFrame=this._coreBrowseService.window.requestAnimationFrame(()=>{this._refreshDecorations(),this._animationFrame=void 0}))}};s.OverviewRulerRenderer=r=l([c(2,g.IBufferService),c(3,g.IDecorationService),c(4,p.IRenderService),c(5,g.IOptionsService),c(6,p.ICoreBrowserService)],r)},2950:function(x,s,o){var l=this&&this.__decorate||function(g,t,n,i){var r,h=arguments.length,_=h<3?t:i===null?i=Object.getOwnPropertyDescriptor(t,n):i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")_=Reflect.decorate(g,t,n,i);else for(var S=g.length-1;S>=0;S--)(r=g[S])&&(_=(h<3?r(_):h>3?r(t,n,_):r(t,n))||_);return h>3&&_&&Object.defineProperty(t,n,_),_},c=this&&this.__param||function(g,t){return function(n,i){t(n,i,g)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CompositionHelper=void 0;const a=o(4725),f=o(2585),p=o(2584);let v=s.CompositionHelper=class{get isComposing(){return this._isComposing}constructor(g,t,n,i,r,h){this._textarea=g,this._compositionView=t,this._bufferService=n,this._optionsService=i,this._coreService=r,this._renderService=h,this._isComposing=!1,this._isSendingComposition=!1,this._compositionPosition={start:0,end:0},this._dataAlreadySent=""}compositionstart(){this._isComposing=!0,this._compositionPosition.start=this._textarea.value.length,this._compositionView.textContent="",this._dataAlreadySent="",this._compositionView.classList.add("active")}compositionupdate(g){this._compositionView.textContent=g.data,this.updateCompositionElements(),setTimeout(()=>{this._compositionPosition.end=this._textarea.value.length},0)}compositionend(){this._finalizeComposition(!0)}keydown(g){if(this._isComposing||this._isSendingComposition){if(g.keyCode===229||g.keyCode===16||g.keyCode===17||g.keyCode===18)return!1;this._finalizeComposition(!1)}return g.keyCode!==229||(this._handleAnyTextareaChanges(),!1)}_finalizeComposition(g){if(this._compositionView.classList.remove("active"),this._isComposing=!1,g){const t={start:this._compositionPosition.start,end:this._compositionPosition.end};this._isSendingComposition=!0,setTimeout(()=>{if(this._isSendingComposition){let n;this._isSendingComposition=!1,t.start+=this._dataAlreadySent.length,n=this._isComposing?this._textarea.value.substring(t.start,t.end):this._textarea.value.substring(t.start),n.length>0&&this._coreService.triggerDataEvent(n,!0)}},0)}else{this._isSendingComposition=!1;const t=this._textarea.value.substring(this._compositionPosition.start,this._compositionPosition.end);this._coreService.triggerDataEvent(t,!0)}}_handleAnyTextareaChanges(){const g=this._textarea.value;setTimeout(()=>{if(!this._isComposing){const t=this._textarea.value,n=t.replace(g,"");this._dataAlreadySent=n,t.length>g.length?this._coreService.triggerDataEvent(n,!0):t.length<g.length?this._coreService.triggerDataEvent(`${p.C0.DEL}`,!0):t.length===g.length&&t!==g&&this._coreService.triggerDataEvent(t,!0)}},0)}updateCompositionElements(g){if(this._isComposing){if(this._bufferService.buffer.isCursorInViewport){const t=Math.min(this._bufferService.buffer.x,this._bufferService.cols-1),n=this._renderService.dimensions.css.cell.height,i=this._bufferService.buffer.y*this._renderService.dimensions.css.cell.height,r=t*this._renderService.dimensions.css.cell.width;this._compositionView.style.left=r+"px",this._compositionView.style.top=i+"px",this._compositionView.style.height=n+"px",this._compositionView.style.lineHeight=n+"px",this._compositionView.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._compositionView.style.fontSize=this._optionsService.rawOptions.fontSize+"px";const h=this._compositionView.getBoundingClientRect();this._textarea.style.left=r+"px",this._textarea.style.top=i+"px",this._textarea.style.width=Math.max(h.width,1)+"px",this._textarea.style.height=Math.max(h.height,1)+"px",this._textarea.style.lineHeight=h.height+"px"}g||setTimeout(()=>this.updateCompositionElements(!0),0)}}};s.CompositionHelper=v=l([c(2,f.IBufferService),c(3,f.IOptionsService),c(4,f.ICoreService),c(5,a.IRenderService)],v)},9806:(x,s)=>{function o(l,c,a){const f=a.getBoundingClientRect(),p=l.getComputedStyle(a),v=parseInt(p.getPropertyValue("padding-left")),g=parseInt(p.getPropertyValue("padding-top"));return[c.clientX-f.left-v,c.clientY-f.top-g]}Object.defineProperty(s,"__esModule",{value:!0}),s.getCoords=s.getCoordsRelativeToElement=void 0,s.getCoordsRelativeToElement=o,s.getCoords=function(l,c,a,f,p,v,g,t,n){if(!v)return;const i=o(l,c,a);return i?(i[0]=Math.ceil((i[0]+(n?g/2:0))/g),i[1]=Math.ceil(i[1]/t),i[0]=Math.min(Math.max(i[0],1),f+(n?1:0)),i[1]=Math.min(Math.max(i[1],1),p),i):void 0}},9504:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.moveToCellSequence=void 0;const l=o(2584);function c(t,n,i,r){const h=t-a(t,i),_=n-a(n,i),S=Math.abs(h-_)-function(y,u,C){let L=0;const I=y-a(y,C),R=u-a(u,C);for(let B=0;B<Math.abs(I-R);B++){const O=f(y,u)==="A"?-1:1,$=C.buffer.lines.get(I+O*B);$!=null&&$.isWrapped&&L++}return L}(t,n,i);return g(S,v(f(t,n),r))}function a(t,n){let i=0,r=n.buffer.lines.get(t),h=r==null?void 0:r.isWrapped;for(;h&&t>=0&&t<n.rows;)i++,r=n.buffer.lines.get(--t),h=r==null?void 0:r.isWrapped;return i}function f(t,n){return t>n?"A":"B"}function p(t,n,i,r,h,_){let S=t,y=n,u="";for(;S!==i||y!==r;)S+=h?1:-1,h&&S>_.cols-1?(u+=_.buffer.translateBufferLineToString(y,!1,t,S),S=0,t=0,y++):!h&&S<0&&(u+=_.buffer.translateBufferLineToString(y,!1,0,t+1),S=_.cols-1,t=S,y--);return u+_.buffer.translateBufferLineToString(y,!1,t,S)}function v(t,n){const i=n?"O":"[";return l.C0.ESC+i+t}function g(t,n){t=Math.floor(t);let i="";for(let r=0;r<t;r++)i+=n;return i}s.moveToCellSequence=function(t,n,i,r){const h=i.buffer.x,_=i.buffer.y;if(!i.buffer.hasScrollback)return function(u,C,L,I,R,B){return c(C,I,R,B).length===0?"":g(p(u,C,u,C-a(C,R),!1,R).length,v("D",B))}(h,_,0,n,i,r)+c(_,n,i,r)+function(u,C,L,I,R,B){let O;O=c(C,I,R,B).length>0?I-a(I,R):C;const $=I,W=function(z,k,D,T,M,N){let U;return U=c(D,T,M,N).length>0?T-a(T,M):k,z<D&&U<=T||z>=D&&U<T?"C":"D"}(u,C,L,I,R,B);return g(p(u,O,L,$,W==="C",R).length,v(W,B))}(h,_,t,n,i,r);let S;if(_===n)return S=h>t?"D":"C",g(Math.abs(h-t),v(S,r));S=_>n?"D":"C";const y=Math.abs(_-n);return g(function(u,C){return C.cols-u}(_>n?t:h,i)+(y-1)*i.cols+1+((_>n?h:t)-1),v(S,r))}},1296:function(x,s,o){var l=this&&this.__decorate||function(R,B,O,$){var W,z=arguments.length,k=z<3?B:$===null?$=Object.getOwnPropertyDescriptor(B,O):$;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")k=Reflect.decorate(R,B,O,$);else for(var D=R.length-1;D>=0;D--)(W=R[D])&&(k=(z<3?W(k):z>3?W(B,O,k):W(B,O))||k);return z>3&&k&&Object.defineProperty(B,O,k),k},c=this&&this.__param||function(R,B){return function(O,$){B(O,$,R)}};Object.defineProperty(s,"__esModule",{value:!0}),s.DomRenderer=void 0;const a=o(3787),f=o(2550),p=o(2223),v=o(6171),g=o(4725),t=o(8055),n=o(8460),i=o(844),r=o(2585),h="xterm-dom-renderer-owner-",_="xterm-rows",S="xterm-fg-",y="xterm-bg-",u="xterm-focus",C="xterm-selection";let L=1,I=s.DomRenderer=class extends i.Disposable{constructor(R,B,O,$,W,z,k,D,T,M){super(),this._element=R,this._screenElement=B,this._viewportElement=O,this._linkifier2=$,this._charSizeService=z,this._optionsService=k,this._bufferService=D,this._coreBrowserService=T,this._themeService=M,this._terminalClass=L++,this._rowElements=[],this.onRequestRedraw=this.register(new n.EventEmitter).event,this._rowContainer=document.createElement("div"),this._rowContainer.classList.add(_),this._rowContainer.style.lineHeight="normal",this._rowContainer.setAttribute("aria-hidden","true"),this._refreshRowElements(this._bufferService.cols,this._bufferService.rows),this._selectionContainer=document.createElement("div"),this._selectionContainer.classList.add(C),this._selectionContainer.setAttribute("aria-hidden","true"),this.dimensions=(0,v.createRenderDimensions)(),this._updateDimensions(),this.register(this._optionsService.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._themeService.onChangeColors(N=>this._injectCss(N))),this._injectCss(this._themeService.colors),this._rowFactory=W.createInstance(a.DomRendererRowFactory,document),this._element.classList.add(h+this._terminalClass),this._screenElement.appendChild(this._rowContainer),this._screenElement.appendChild(this._selectionContainer),this.register(this._linkifier2.onShowLinkUnderline(N=>this._handleLinkHover(N))),this.register(this._linkifier2.onHideLinkUnderline(N=>this._handleLinkLeave(N))),this.register((0,i.toDisposable)(()=>{this._element.classList.remove(h+this._terminalClass),this._rowContainer.remove(),this._selectionContainer.remove(),this._widthCache.dispose(),this._themeStyleElement.remove(),this._dimensionsStyleElement.remove()})),this._widthCache=new f.WidthCache(document),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}_updateDimensions(){const R=this._coreBrowserService.dpr;this.dimensions.device.char.width=this._charSizeService.width*R,this.dimensions.device.char.height=Math.ceil(this._charSizeService.height*R),this.dimensions.device.cell.width=this.dimensions.device.char.width+Math.round(this._optionsService.rawOptions.letterSpacing),this.dimensions.device.cell.height=Math.floor(this.dimensions.device.char.height*this._optionsService.rawOptions.lineHeight),this.dimensions.device.char.left=0,this.dimensions.device.char.top=0,this.dimensions.device.canvas.width=this.dimensions.device.cell.width*this._bufferService.cols,this.dimensions.device.canvas.height=this.dimensions.device.cell.height*this._bufferService.rows,this.dimensions.css.canvas.width=Math.round(this.dimensions.device.canvas.width/R),this.dimensions.css.canvas.height=Math.round(this.dimensions.device.canvas.height/R),this.dimensions.css.cell.width=this.dimensions.css.canvas.width/this._bufferService.cols,this.dimensions.css.cell.height=this.dimensions.css.canvas.height/this._bufferService.rows;for(const O of this._rowElements)O.style.width=`${this.dimensions.css.canvas.width}px`,O.style.height=`${this.dimensions.css.cell.height}px`,O.style.lineHeight=`${this.dimensions.css.cell.height}px`,O.style.overflow="hidden";this._dimensionsStyleElement||(this._dimensionsStyleElement=document.createElement("style"),this._screenElement.appendChild(this._dimensionsStyleElement));const B=`${this._terminalSelector} .${_} span { display: inline-block; height: 100%; vertical-align: top;}`;this._dimensionsStyleElement.textContent=B,this._selectionContainer.style.height=this._viewportElement.style.height,this._screenElement.style.width=`${this.dimensions.css.canvas.width}px`,this._screenElement.style.height=`${this.dimensions.css.canvas.height}px`}_injectCss(R){this._themeStyleElement||(this._themeStyleElement=document.createElement("style"),this._screenElement.appendChild(this._themeStyleElement));let B=`${this._terminalSelector} .${_} { color: ${R.foreground.css}; font-family: ${this._optionsService.rawOptions.fontFamily}; font-size: ${this._optionsService.rawOptions.fontSize}px; font-kerning: none; white-space: pre}`;B+=`${this._terminalSelector} .${_} .xterm-dim { color: ${t.color.multiplyOpacity(R.foreground,.5).css};}`,B+=`${this._terminalSelector} span:not(.xterm-bold) { font-weight: ${this._optionsService.rawOptions.fontWeight};}${this._terminalSelector} span.xterm-bold { font-weight: ${this._optionsService.rawOptions.fontWeightBold};}${this._terminalSelector} span.xterm-italic { font-style: italic;}`,B+="@keyframes blink_box_shadow_"+this._terminalClass+" { 50% {  border-bottom-style: hidden; }}",B+="@keyframes blink_block_"+this._terminalClass+` { 0% {  background-color: ${R.cursor.css};  color: ${R.cursorAccent.css}; } 50% {  background-color: inherit;  color: ${R.cursor.css}; }}`,B+=`${this._terminalSelector} .${_}.${u} .xterm-cursor.xterm-cursor-blink:not(.xterm-cursor-block) { animation: blink_box_shadow_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${_}.${u} .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: blink_block_`+this._terminalClass+` 1s step-end infinite;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-block { background-color: ${R.cursor.css}; color: ${R.cursorAccent.css};}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-outline { outline: 1px solid ${R.cursor.css}; outline-offset: -1px;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-bar { box-shadow: ${this._optionsService.rawOptions.cursorWidth}px 0 0 ${R.cursor.css} inset;}${this._terminalSelector} .${_} .xterm-cursor.xterm-cursor-underline { border-bottom: 1px ${R.cursor.css}; border-bottom-style: solid; height: calc(100% - 1px);}`,B+=`${this._terminalSelector} .${C} { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}${this._terminalSelector}.focus .${C} div { position: absolute; background-color: ${R.selectionBackgroundOpaque.css};}${this._terminalSelector} .${C} div { position: absolute; background-color: ${R.selectionInactiveBackgroundOpaque.css};}`;for(const[O,$]of R.ansi.entries())B+=`${this._terminalSelector} .${S}${O} { color: ${$.css}; }${this._terminalSelector} .${S}${O}.xterm-dim { color: ${t.color.multiplyOpacity($,.5).css}; }${this._terminalSelector} .${y}${O} { background-color: ${$.css}; }`;B+=`${this._terminalSelector} .${S}${p.INVERTED_DEFAULT_COLOR} { color: ${t.color.opaque(R.background).css}; }${this._terminalSelector} .${S}${p.INVERTED_DEFAULT_COLOR}.xterm-dim { color: ${t.color.multiplyOpacity(t.color.opaque(R.background),.5).css}; }${this._terminalSelector} .${y}${p.INVERTED_DEFAULT_COLOR} { background-color: ${R.foreground.css}; }`,this._themeStyleElement.textContent=B}_setDefaultSpacing(){const R=this.dimensions.css.cell.width-this._widthCache.get("W",!1,!1);this._rowContainer.style.letterSpacing=`${R}px`,this._rowFactory.defaultSpacing=R}handleDevicePixelRatioChange(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}_refreshRowElements(R,B){for(let O=this._rowElements.length;O<=B;O++){const $=document.createElement("div");this._rowContainer.appendChild($),this._rowElements.push($)}for(;this._rowElements.length>B;)this._rowContainer.removeChild(this._rowElements.pop())}handleResize(R,B){this._refreshRowElements(R,B),this._updateDimensions()}handleCharSizeChanged(){this._updateDimensions(),this._widthCache.clear(),this._setDefaultSpacing()}handleBlur(){this._rowContainer.classList.remove(u)}handleFocus(){this._rowContainer.classList.add(u),this.renderRows(this._bufferService.buffer.y,this._bufferService.buffer.y)}handleSelectionChanged(R,B,O){if(this._selectionContainer.replaceChildren(),this._rowFactory.handleSelectionChanged(R,B,O),this.renderRows(0,this._bufferService.rows-1),!R||!B)return;const $=R[1]-this._bufferService.buffer.ydisp,W=B[1]-this._bufferService.buffer.ydisp,z=Math.max($,0),k=Math.min(W,this._bufferService.rows-1);if(z>=this._bufferService.rows||k<0)return;const D=document.createDocumentFragment();if(O){const T=R[0]>B[0];D.appendChild(this._createSelectionElement(z,T?B[0]:R[0],T?R[0]:B[0],k-z+1))}else{const T=$===z?R[0]:0,M=z===W?B[0]:this._bufferService.cols;D.appendChild(this._createSelectionElement(z,T,M));const N=k-z-1;if(D.appendChild(this._createSelectionElement(z+1,0,this._bufferService.cols,N)),z!==k){const U=W===k?B[0]:this._bufferService.cols;D.appendChild(this._createSelectionElement(k,0,U))}}this._selectionContainer.appendChild(D)}_createSelectionElement(R,B,O,$=1){const W=document.createElement("div");return W.style.height=$*this.dimensions.css.cell.height+"px",W.style.top=R*this.dimensions.css.cell.height+"px",W.style.left=B*this.dimensions.css.cell.width+"px",W.style.width=this.dimensions.css.cell.width*(O-B)+"px",W}handleCursorMove(){}_handleOptionsChanged(){this._updateDimensions(),this._injectCss(this._themeService.colors),this._widthCache.setFont(this._optionsService.rawOptions.fontFamily,this._optionsService.rawOptions.fontSize,this._optionsService.rawOptions.fontWeight,this._optionsService.rawOptions.fontWeightBold),this._setDefaultSpacing()}clear(){for(const R of this._rowElements)R.replaceChildren()}renderRows(R,B){const O=this._bufferService.buffer,$=O.ybase+O.y,W=Math.min(O.x,this._bufferService.cols-1),z=this._optionsService.rawOptions.cursorBlink,k=this._optionsService.rawOptions.cursorStyle,D=this._optionsService.rawOptions.cursorInactiveStyle;for(let T=R;T<=B;T++){const M=T+O.ydisp,N=this._rowElements[T],U=O.lines.get(M);if(!N||!U)break;N.replaceChildren(...this._rowFactory.createRow(U,M,M===$,k,D,W,z,this.dimensions.css.cell.width,this._widthCache,-1,-1))}}get _terminalSelector(){return`.${h}${this._terminalClass}`}_handleLinkHover(R){this._setCellUnderline(R.x1,R.x2,R.y1,R.y2,R.cols,!0)}_handleLinkLeave(R){this._setCellUnderline(R.x1,R.x2,R.y1,R.y2,R.cols,!1)}_setCellUnderline(R,B,O,$,W,z){O<0&&(R=0),$<0&&(B=0);const k=this._bufferService.rows-1;O=Math.max(Math.min(O,k),0),$=Math.max(Math.min($,k),0),W=Math.min(W,this._bufferService.cols);const D=this._bufferService.buffer,T=D.ybase+D.y,M=Math.min(D.x,W-1),N=this._optionsService.rawOptions.cursorBlink,U=this._optionsService.rawOptions.cursorStyle,q=this._optionsService.rawOptions.cursorInactiveStyle;for(let K=O;K<=$;++K){const Q=K+D.ydisp,w=this._rowElements[K],P=D.lines.get(Q);if(!w||!P)break;w.replaceChildren(...this._rowFactory.createRow(P,Q,Q===T,U,q,M,N,this.dimensions.css.cell.width,this._widthCache,z?K===O?R:0:-1,z?(K===$?B:W)-1:-1))}}};s.DomRenderer=I=l([c(4,r.IInstantiationService),c(5,g.ICharSizeService),c(6,r.IOptionsService),c(7,r.IBufferService),c(8,g.ICoreBrowserService),c(9,g.IThemeService)],I)},3787:function(x,s,o){var l=this&&this.__decorate||function(S,y,u,C){var L,I=arguments.length,R=I<3?y:C===null?C=Object.getOwnPropertyDescriptor(y,u):C;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")R=Reflect.decorate(S,y,u,C);else for(var B=S.length-1;B>=0;B--)(L=S[B])&&(R=(I<3?L(R):I>3?L(y,u,R):L(y,u))||R);return I>3&&R&&Object.defineProperty(y,u,R),R},c=this&&this.__param||function(S,y){return function(u,C){y(u,C,S)}};Object.defineProperty(s,"__esModule",{value:!0}),s.DomRendererRowFactory=void 0;const a=o(2223),f=o(643),p=o(511),v=o(2585),g=o(8055),t=o(4725),n=o(4269),i=o(6171),r=o(3734);let h=s.DomRendererRowFactory=class{constructor(S,y,u,C,L,I,R){this._document=S,this._characterJoinerService=y,this._optionsService=u,this._coreBrowserService=C,this._coreService=L,this._decorationService=I,this._themeService=R,this._workCell=new p.CellData,this._columnSelectMode=!1,this.defaultSpacing=0}handleSelectionChanged(S,y,u){this._selectionStart=S,this._selectionEnd=y,this._columnSelectMode=u}createRow(S,y,u,C,L,I,R,B,O,$,W){const z=[],k=this._characterJoinerService.getJoinedCharacters(y),D=this._themeService.colors;let T,M=S.getNoBgTrimmedLength();u&&M<I+1&&(M=I+1);let N=0,U="",q=0,K=0,Q=0,w=!1,P=0,F=!1,H=0;const V=[],j=$!==-1&&W!==-1;for(let Y=0;Y<M;Y++){S.loadCell(Y,this._workCell);let ie=this._workCell.getWidth();if(ie===0)continue;let ae=!1,be=Y,X=this._workCell;if(k.length>0&&Y===k[0][0]){ae=!0;const ee=k.shift();X=new n.JoinedCellData(this._workCell,S.translateToString(!0,ee[0],ee[1]),ee[1]-ee[0]),be=ee[1]-1,ie=X.getWidth()}const Ee=this._isCellInSelection(Y,y),Ve=u&&Y===I,Xe=j&&Y>=$&&Y<=W;let Ge=!1;this._decorationService.forEachDecorationAtCell(Y,y,void 0,ee=>{Ge=!0});let Oe=X.getChars()||f.WHITESPACE_CELL_CHAR;if(Oe===" "&&(X.isUnderline()||X.isOverline())&&(Oe=" "),H=ie*B-O.get(Oe,X.isBold(),X.isItalic()),T){if(N&&(Ee&&F||!Ee&&!F&&X.bg===q)&&(Ee&&F&&D.selectionForeground||X.fg===K)&&X.extended.ext===Q&&Xe===w&&H===P&&!Ve&&!ae&&!Ge){U+=Oe,N++;continue}N&&(T.textContent=U),T=this._document.createElement("span"),N=0,U=""}else T=this._document.createElement("span");if(q=X.bg,K=X.fg,Q=X.extended.ext,w=Xe,P=H,F=Ee,ae&&I>=Y&&I<=be&&(I=Y),!this._coreService.isCursorHidden&&Ve){if(V.push("xterm-cursor"),this._coreBrowserService.isFocused)R&&V.push("xterm-cursor-blink"),V.push(C==="bar"?"xterm-cursor-bar":C==="underline"?"xterm-cursor-underline":"xterm-cursor-block");else if(L)switch(L){case"outline":V.push("xterm-cursor-outline");break;case"block":V.push("xterm-cursor-block");break;case"bar":V.push("xterm-cursor-bar");break;case"underline":V.push("xterm-cursor-underline")}}if(X.isBold()&&V.push("xterm-bold"),X.isItalic()&&V.push("xterm-italic"),X.isDim()&&V.push("xterm-dim"),U=X.isInvisible()?f.WHITESPACE_CELL_CHAR:X.getChars()||f.WHITESPACE_CELL_CHAR,X.isUnderline()&&(V.push(`xterm-underline-${X.extended.underlineStyle}`),U===" "&&(U=" "),!X.isUnderlineColorDefault()))if(X.isUnderlineColorRGB())T.style.textDecorationColor=`rgb(${r.AttributeData.toColorRGB(X.getUnderlineColor()).join(",")})`;else{let ee=X.getUnderlineColor();this._optionsService.rawOptions.drawBoldTextInBrightColors&&X.isBold()&&ee<8&&(ee+=8),T.style.textDecorationColor=D.ansi[ee].css}X.isOverline()&&(V.push("xterm-overline"),U===" "&&(U=" ")),X.isStrikethrough()&&V.push("xterm-strikethrough"),Xe&&(T.style.textDecoration="underline");let le=X.getFgColor(),xe=X.getFgColorMode(),he=X.getBgColor(),Ae=X.getBgColorMode();const Je=!!X.isInverse();if(Je){const ee=le;le=he,he=ee;const Ft=xe;xe=Ae,Ae=Ft}let _e,Ye,ge,Le=!1;switch(this._decorationService.forEachDecorationAtCell(Y,y,void 0,ee=>{ee.options.layer!=="top"&&Le||(ee.backgroundColorRGB&&(Ae=50331648,he=ee.backgroundColorRGB.rgba>>8&16777215,_e=ee.backgroundColorRGB),ee.foregroundColorRGB&&(xe=50331648,le=ee.foregroundColorRGB.rgba>>8&16777215,Ye=ee.foregroundColorRGB),Le=ee.options.layer==="top")}),!Le&&Ee&&(_e=this._coreBrowserService.isFocused?D.selectionBackgroundOpaque:D.selectionInactiveBackgroundOpaque,he=_e.rgba>>8&16777215,Ae=50331648,Le=!0,D.selectionForeground&&(xe=50331648,le=D.selectionForeground.rgba>>8&16777215,Ye=D.selectionForeground)),Le&&V.push("xterm-decoration-top"),Ae){case 16777216:case 33554432:ge=D.ansi[he],V.push(`xterm-bg-${he}`);break;case 50331648:ge=g.rgba.toColor(he>>16,he>>8&255,255&he),this._addStyle(T,`background-color:#${_((he>>>0).toString(16),"0",6)}`);break;default:Je?(ge=D.foreground,V.push(`xterm-bg-${a.INVERTED_DEFAULT_COLOR}`)):ge=D.background}switch(_e||X.isDim()&&(_e=g.color.multiplyOpacity(ge,.5)),xe){case 16777216:case 33554432:X.isBold()&&le<8&&this._optionsService.rawOptions.drawBoldTextInBrightColors&&(le+=8),this._applyMinimumContrast(T,ge,D.ansi[le],X,_e,void 0)||V.push(`xterm-fg-${le}`);break;case 50331648:const ee=g.rgba.toColor(le>>16&255,le>>8&255,255&le);this._applyMinimumContrast(T,ge,ee,X,_e,Ye)||this._addStyle(T,`color:#${_(le.toString(16),"0",6)}`);break;default:this._applyMinimumContrast(T,ge,D.foreground,X,_e,void 0)||Je&&V.push(`xterm-fg-${a.INVERTED_DEFAULT_COLOR}`)}V.length&&(T.className=V.join(" "),V.length=0),Ve||ae||Ge?T.textContent=U:N++,H!==this.defaultSpacing&&(T.style.letterSpacing=`${H}px`),z.push(T),Y=be}return T&&N&&(T.textContent=U),z}_applyMinimumContrast(S,y,u,C,L,I){if(this._optionsService.rawOptions.minimumContrastRatio===1||(0,i.excludeFromContrastRatioDemands)(C.getCode()))return!1;const R=this._getContrastCache(C);let B;if(L||I||(B=R.getColor(y.rgba,u.rgba)),B===void 0){const O=this._optionsService.rawOptions.minimumContrastRatio/(C.isDim()?2:1);B=g.color.ensureContrastRatio(L||y,I||u,O),R.setColor((L||y).rgba,(I||u).rgba,B??null)}return!!B&&(this._addStyle(S,`color:${B.css}`),!0)}_getContrastCache(S){return S.isDim()?this._themeService.colors.halfContrastCache:this._themeService.colors.contrastCache}_addStyle(S,y){S.setAttribute("style",`${S.getAttribute("style")||""}${y};`)}_isCellInSelection(S,y){const u=this._selectionStart,C=this._selectionEnd;return!(!u||!C)&&(this._columnSelectMode?u[0]<=C[0]?S>=u[0]&&y>=u[1]&&S<C[0]&&y<=C[1]:S<u[0]&&y>=u[1]&&S>=C[0]&&y<=C[1]:y>u[1]&&y<C[1]||u[1]===C[1]&&y===u[1]&&S>=u[0]&&S<C[0]||u[1]<C[1]&&y===C[1]&&S<C[0]||u[1]<C[1]&&y===u[1]&&S>=u[0])}};function _(S,y,u){for(;S.length<u;)S=y+S;return S}s.DomRendererRowFactory=h=l([c(1,t.ICharacterJoinerService),c(2,v.IOptionsService),c(3,t.ICoreBrowserService),c(4,v.ICoreService),c(5,v.IDecorationService),c(6,t.IThemeService)],h)},2550:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WidthCache=void 0,s.WidthCache=class{constructor(o){this._flat=new Float32Array(256),this._font="",this._fontSize=0,this._weight="normal",this._weightBold="bold",this._measureElements=[],this._container=o.createElement("div"),this._container.style.position="absolute",this._container.style.top="-50000px",this._container.style.width="50000px",this._container.style.whiteSpace="pre",this._container.style.fontKerning="none";const l=o.createElement("span"),c=o.createElement("span");c.style.fontWeight="bold";const a=o.createElement("span");a.style.fontStyle="italic";const f=o.createElement("span");f.style.fontWeight="bold",f.style.fontStyle="italic",this._measureElements=[l,c,a,f],this._container.appendChild(l),this._container.appendChild(c),this._container.appendChild(a),this._container.appendChild(f),o.body.appendChild(this._container),this.clear()}dispose(){this._container.remove(),this._measureElements.length=0,this._holey=void 0}clear(){this._flat.fill(-9999),this._holey=new Map}setFont(o,l,c,a){o===this._font&&l===this._fontSize&&c===this._weight&&a===this._weightBold||(this._font=o,this._fontSize=l,this._weight=c,this._weightBold=a,this._container.style.fontFamily=this._font,this._container.style.fontSize=`${this._fontSize}px`,this._measureElements[0].style.fontWeight=`${c}`,this._measureElements[1].style.fontWeight=`${a}`,this._measureElements[2].style.fontWeight=`${c}`,this._measureElements[3].style.fontWeight=`${a}`,this.clear())}get(o,l,c){let a=0;if(!l&&!c&&o.length===1&&(a=o.charCodeAt(0))<256)return this._flat[a]!==-9999?this._flat[a]:this._flat[a]=this._measure(o,0);let f=o;l&&(f+="B"),c&&(f+="I");let p=this._holey.get(f);if(p===void 0){let v=0;l&&(v|=1),c&&(v|=2),p=this._measure(o,v),this._holey.set(f,p)}return p}_measure(o,l){const c=this._measureElements[l];return c.textContent=o.repeat(32),c.offsetWidth/32}}},2223:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.TEXT_BASELINE=s.DIM_OPACITY=s.INVERTED_DEFAULT_COLOR=void 0;const l=o(6114);s.INVERTED_DEFAULT_COLOR=257,s.DIM_OPACITY=.5,s.TEXT_BASELINE=l.isFirefox||l.isLegacyEdge?"bottom":"ideographic"},6171:(x,s)=>{function o(l){return 57508<=l&&l<=57558}Object.defineProperty(s,"__esModule",{value:!0}),s.createRenderDimensions=s.excludeFromContrastRatioDemands=s.isRestrictedPowerlineGlyph=s.isPowerlineGlyph=s.throwIfFalsy=void 0,s.throwIfFalsy=function(l){if(!l)throw new Error("value must not be falsy");return l},s.isPowerlineGlyph=o,s.isRestrictedPowerlineGlyph=function(l){return 57520<=l&&l<=57527},s.excludeFromContrastRatioDemands=function(l){return o(l)||function(c){return 9472<=c&&c<=9631}(l)},s.createRenderDimensions=function(){return{css:{canvas:{width:0,height:0},cell:{width:0,height:0}},device:{canvas:{width:0,height:0},cell:{width:0,height:0},char:{width:0,height:0,left:0,top:0}}}}},456:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionModel=void 0,s.SelectionModel=class{constructor(o){this._bufferService=o,this.isSelectAllActive=!1,this.selectionStartLength=0}clearSelection(){this.selectionStart=void 0,this.selectionEnd=void 0,this.isSelectAllActive=!1,this.selectionStartLength=0}get finalSelectionStart(){return this.isSelectAllActive?[0,0]:this.selectionEnd&&this.selectionStart&&this.areSelectionValuesReversed()?this.selectionEnd:this.selectionStart}get finalSelectionEnd(){if(this.isSelectAllActive)return[this._bufferService.cols,this._bufferService.buffer.ybase+this._bufferService.rows-1];if(this.selectionStart){if(!this.selectionEnd||this.areSelectionValuesReversed()){const o=this.selectionStart[0]+this.selectionStartLength;return o>this._bufferService.cols?o%this._bufferService.cols==0?[this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)-1]:[o%this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)]:[o,this.selectionStart[1]]}if(this.selectionStartLength&&this.selectionEnd[1]===this.selectionStart[1]){const o=this.selectionStart[0]+this.selectionStartLength;return o>this._bufferService.cols?[o%this._bufferService.cols,this.selectionStart[1]+Math.floor(o/this._bufferService.cols)]:[Math.max(o,this.selectionEnd[0]),this.selectionEnd[1]]}return this.selectionEnd}}areSelectionValuesReversed(){const o=this.selectionStart,l=this.selectionEnd;return!(!o||!l)&&(o[1]>l[1]||o[1]===l[1]&&o[0]>l[0])}handleTrim(o){return this.selectionStart&&(this.selectionStart[1]-=o),this.selectionEnd&&(this.selectionEnd[1]-=o),this.selectionEnd&&this.selectionEnd[1]<0?(this.clearSelection(),!0):(this.selectionStart&&this.selectionStart[1]<0&&(this.selectionStart[1]=0),!1)}}},428:function(x,s,o){var l=this&&this.__decorate||function(t,n,i,r){var h,_=arguments.length,S=_<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(t,n,i,r);else for(var y=t.length-1;y>=0;y--)(h=t[y])&&(S=(_<3?h(S):_>3?h(n,i,S):h(n,i))||S);return _>3&&S&&Object.defineProperty(n,i,S),S},c=this&&this.__param||function(t,n){return function(i,r){n(i,r,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharSizeService=void 0;const a=o(2585),f=o(8460),p=o(844);let v=s.CharSizeService=class extends p.Disposable{get hasValidSize(){return this.width>0&&this.height>0}constructor(t,n,i){super(),this._optionsService=i,this.width=0,this.height=0,this._onCharSizeChange=this.register(new f.EventEmitter),this.onCharSizeChange=this._onCharSizeChange.event,this._measureStrategy=new g(t,n,this._optionsService),this.register(this._optionsService.onMultipleOptionChange(["fontFamily","fontSize"],()=>this.measure()))}measure(){const t=this._measureStrategy.measure();t.width===this.width&&t.height===this.height||(this.width=t.width,this.height=t.height,this._onCharSizeChange.fire())}};s.CharSizeService=v=l([c(2,a.IOptionsService)],v);class g{constructor(n,i,r){this._document=n,this._parentElement=i,this._optionsService=r,this._result={width:0,height:0},this._measureElement=this._document.createElement("span"),this._measureElement.classList.add("xterm-char-measure-element"),this._measureElement.textContent="W".repeat(32),this._measureElement.setAttribute("aria-hidden","true"),this._measureElement.style.whiteSpace="pre",this._measureElement.style.fontKerning="none",this._parentElement.appendChild(this._measureElement)}measure(){this._measureElement.style.fontFamily=this._optionsService.rawOptions.fontFamily,this._measureElement.style.fontSize=`${this._optionsService.rawOptions.fontSize}px`;const n={height:Number(this._measureElement.offsetHeight),width:Number(this._measureElement.offsetWidth)};return n.width!==0&&n.height!==0&&(this._result.width=n.width/32,this._result.height=Math.ceil(n.height)),this._result}}},4269:function(x,s,o){var l=this&&this.__decorate||function(n,i,r,h){var _,S=arguments.length,y=S<3?i:h===null?h=Object.getOwnPropertyDescriptor(i,r):h;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")y=Reflect.decorate(n,i,r,h);else for(var u=n.length-1;u>=0;u--)(_=n[u])&&(y=(S<3?_(y):S>3?_(i,r,y):_(i,r))||y);return S>3&&y&&Object.defineProperty(i,r,y),y},c=this&&this.__param||function(n,i){return function(r,h){i(r,h,n)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CharacterJoinerService=s.JoinedCellData=void 0;const a=o(3734),f=o(643),p=o(511),v=o(2585);class g extends a.AttributeData{constructor(i,r,h){super(),this.content=0,this.combinedData="",this.fg=i.fg,this.bg=i.bg,this.combinedData=r,this._width=h}isCombined(){return 2097152}getWidth(){return this._width}getChars(){return this.combinedData}getCode(){return 2097151}setFromCharData(i){throw new Error("not implemented")}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.JoinedCellData=g;let t=s.CharacterJoinerService=class bt{constructor(i){this._bufferService=i,this._characterJoiners=[],this._nextCharacterJoinerId=0,this._workCell=new p.CellData}register(i){const r={id:this._nextCharacterJoinerId++,handler:i};return this._characterJoiners.push(r),r.id}deregister(i){for(let r=0;r<this._characterJoiners.length;r++)if(this._characterJoiners[r].id===i)return this._characterJoiners.splice(r,1),!0;return!1}getJoinedCharacters(i){if(this._characterJoiners.length===0)return[];const r=this._bufferService.buffer.lines.get(i);if(!r||r.length===0)return[];const h=[],_=r.translateToString(!0);let S=0,y=0,u=0,C=r.getFg(0),L=r.getBg(0);for(let I=0;I<r.getTrimmedLength();I++)if(r.loadCell(I,this._workCell),this._workCell.getWidth()!==0){if(this._workCell.fg!==C||this._workCell.bg!==L){if(I-S>1){const R=this._getJoinedRanges(_,u,y,r,S);for(let B=0;B<R.length;B++)h.push(R[B])}S=I,u=y,C=this._workCell.fg,L=this._workCell.bg}y+=this._workCell.getChars().length||f.WHITESPACE_CELL_CHAR.length}if(this._bufferService.cols-S>1){const I=this._getJoinedRanges(_,u,y,r,S);for(let R=0;R<I.length;R++)h.push(I[R])}return h}_getJoinedRanges(i,r,h,_,S){const y=i.substring(r,h);let u=[];try{u=this._characterJoiners[0].handler(y)}catch(C){console.error(C)}for(let C=1;C<this._characterJoiners.length;C++)try{const L=this._characterJoiners[C].handler(y);for(let I=0;I<L.length;I++)bt._mergeRanges(u,L[I])}catch(L){console.error(L)}return this._stringRangesToCellRanges(u,_,S),u}_stringRangesToCellRanges(i,r,h){let _=0,S=!1,y=0,u=i[_];if(u){for(let C=h;C<this._bufferService.cols;C++){const L=r.getWidth(C),I=r.getString(C).length||f.WHITESPACE_CELL_CHAR.length;if(L!==0){if(!S&&u[0]<=y&&(u[0]=C,S=!0),u[1]<=y){if(u[1]=C,u=i[++_],!u)break;u[0]<=y?(u[0]=C,S=!0):S=!1}y+=I}}u&&(u[1]=this._bufferService.cols)}}static _mergeRanges(i,r){let h=!1;for(let _=0;_<i.length;_++){const S=i[_];if(h){if(r[1]<=S[0])return i[_-1][1]=r[1],i;if(r[1]<=S[1])return i[_-1][1]=Math.max(r[1],S[1]),i.splice(_,1),i;i.splice(_,1),_--}else{if(r[1]<=S[0])return i.splice(_,0,r),i;if(r[1]<=S[1])return S[0]=Math.min(r[0],S[0]),i;r[0]<S[1]&&(S[0]=Math.min(r[0],S[0]),h=!0)}}return h?i[i.length-1][1]=r[1]:i.push(r),i}};s.CharacterJoinerService=t=l([c(0,v.IBufferService)],t)},5114:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CoreBrowserService=void 0,s.CoreBrowserService=class{constructor(o,l){this._textarea=o,this.window=l,this._isFocused=!1,this._cachedIsFocused=void 0,this._textarea.addEventListener("focus",()=>this._isFocused=!0),this._textarea.addEventListener("blur",()=>this._isFocused=!1)}get dpr(){return this.window.devicePixelRatio}get isFocused(){return this._cachedIsFocused===void 0&&(this._cachedIsFocused=this._isFocused&&this._textarea.ownerDocument.hasFocus(),queueMicrotask(()=>this._cachedIsFocused=void 0)),this._cachedIsFocused}}},8934:function(x,s,o){var l=this&&this.__decorate||function(v,g,t,n){var i,r=arguments.length,h=r<3?g:n===null?n=Object.getOwnPropertyDescriptor(g,t):n;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")h=Reflect.decorate(v,g,t,n);else for(var _=v.length-1;_>=0;_--)(i=v[_])&&(h=(r<3?i(h):r>3?i(g,t,h):i(g,t))||h);return r>3&&h&&Object.defineProperty(g,t,h),h},c=this&&this.__param||function(v,g){return function(t,n){g(t,n,v)}};Object.defineProperty(s,"__esModule",{value:!0}),s.MouseService=void 0;const a=o(4725),f=o(9806);let p=s.MouseService=class{constructor(v,g){this._renderService=v,this._charSizeService=g}getCoords(v,g,t,n,i){return(0,f.getCoords)(window,v,g,t,n,this._charSizeService.hasValidSize,this._renderService.dimensions.css.cell.width,this._renderService.dimensions.css.cell.height,i)}getMouseReportCoords(v,g){const t=(0,f.getCoordsRelativeToElement)(window,v,g);if(this._charSizeService.hasValidSize)return t[0]=Math.min(Math.max(t[0],0),this._renderService.dimensions.css.canvas.width-1),t[1]=Math.min(Math.max(t[1],0),this._renderService.dimensions.css.canvas.height-1),{col:Math.floor(t[0]/this._renderService.dimensions.css.cell.width),row:Math.floor(t[1]/this._renderService.dimensions.css.cell.height),x:Math.floor(t[0]),y:Math.floor(t[1])}}};s.MouseService=p=l([c(0,a.IRenderService),c(1,a.ICharSizeService)],p)},3230:function(x,s,o){var l=this&&this.__decorate||function(h,_,S,y){var u,C=arguments.length,L=C<3?_:y===null?y=Object.getOwnPropertyDescriptor(_,S):y;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")L=Reflect.decorate(h,_,S,y);else for(var I=h.length-1;I>=0;I--)(u=h[I])&&(L=(C<3?u(L):C>3?u(_,S,L):u(_,S))||L);return C>3&&L&&Object.defineProperty(_,S,L),L},c=this&&this.__param||function(h,_){return function(S,y){_(S,y,h)}};Object.defineProperty(s,"__esModule",{value:!0}),s.RenderService=void 0;const a=o(3656),f=o(6193),p=o(5596),v=o(4725),g=o(8460),t=o(844),n=o(7226),i=o(2585);let r=s.RenderService=class extends t.Disposable{get dimensions(){return this._renderer.value.dimensions}constructor(h,_,S,y,u,C,L,I){if(super(),this._rowCount=h,this._charSizeService=y,this._renderer=this.register(new t.MutableDisposable),this._pausedResizeTask=new n.DebouncedIdleTask,this._isPaused=!1,this._needsFullRefresh=!1,this._isNextRenderRedrawOnly=!0,this._needsSelectionRefresh=!1,this._canvasWidth=0,this._canvasHeight=0,this._selectionState={start:void 0,end:void 0,columnSelectMode:!1},this._onDimensionsChange=this.register(new g.EventEmitter),this.onDimensionsChange=this._onDimensionsChange.event,this._onRenderedViewportChange=this.register(new g.EventEmitter),this.onRenderedViewportChange=this._onRenderedViewportChange.event,this._onRender=this.register(new g.EventEmitter),this.onRender=this._onRender.event,this._onRefreshRequest=this.register(new g.EventEmitter),this.onRefreshRequest=this._onRefreshRequest.event,this._renderDebouncer=new f.RenderDebouncer(L.window,(R,B)=>this._renderRows(R,B)),this.register(this._renderDebouncer),this._screenDprMonitor=new p.ScreenDprMonitor(L.window),this._screenDprMonitor.setListener(()=>this.handleDevicePixelRatioChange()),this.register(this._screenDprMonitor),this.register(C.onResize(()=>this._fullRefresh())),this.register(C.buffers.onBufferActivate(()=>{var R;return(R=this._renderer.value)===null||R===void 0?void 0:R.clear()})),this.register(S.onOptionChange(()=>this._handleOptionsChanged())),this.register(this._charSizeService.onCharSizeChange(()=>this.handleCharSizeChanged())),this.register(u.onDecorationRegistered(()=>this._fullRefresh())),this.register(u.onDecorationRemoved(()=>this._fullRefresh())),this.register(S.onMultipleOptionChange(["customGlyphs","drawBoldTextInBrightColors","letterSpacing","lineHeight","fontFamily","fontSize","fontWeight","fontWeightBold","minimumContrastRatio"],()=>{this.clear(),this.handleResize(C.cols,C.rows),this._fullRefresh()})),this.register(S.onMultipleOptionChange(["cursorBlink","cursorStyle"],()=>this.refreshRows(C.buffer.y,C.buffer.y,!0))),this.register((0,a.addDisposableDomListener)(L.window,"resize",()=>this.handleDevicePixelRatioChange())),this.register(I.onChangeColors(()=>this._fullRefresh())),"IntersectionObserver"in L.window){const R=new L.window.IntersectionObserver(B=>this._handleIntersectionChange(B[B.length-1]),{threshold:0});R.observe(_),this.register({dispose:()=>R.disconnect()})}}_handleIntersectionChange(h){this._isPaused=h.isIntersecting===void 0?h.intersectionRatio===0:!h.isIntersecting,this._isPaused||this._charSizeService.hasValidSize||this._charSizeService.measure(),!this._isPaused&&this._needsFullRefresh&&(this._pausedResizeTask.flush(),this.refreshRows(0,this._rowCount-1),this._needsFullRefresh=!1)}refreshRows(h,_,S=!1){this._isPaused?this._needsFullRefresh=!0:(S||(this._isNextRenderRedrawOnly=!1),this._renderDebouncer.refresh(h,_,this._rowCount))}_renderRows(h,_){this._renderer.value&&(h=Math.min(h,this._rowCount-1),_=Math.min(_,this._rowCount-1),this._renderer.value.renderRows(h,_),this._needsSelectionRefresh&&(this._renderer.value.handleSelectionChanged(this._selectionState.start,this._selectionState.end,this._selectionState.columnSelectMode),this._needsSelectionRefresh=!1),this._isNextRenderRedrawOnly||this._onRenderedViewportChange.fire({start:h,end:_}),this._onRender.fire({start:h,end:_}),this._isNextRenderRedrawOnly=!0)}resize(h,_){this._rowCount=_,this._fireOnCanvasResize()}_handleOptionsChanged(){this._renderer.value&&(this.refreshRows(0,this._rowCount-1),this._fireOnCanvasResize())}_fireOnCanvasResize(){this._renderer.value&&(this._renderer.value.dimensions.css.canvas.width===this._canvasWidth&&this._renderer.value.dimensions.css.canvas.height===this._canvasHeight||this._onDimensionsChange.fire(this._renderer.value.dimensions))}hasRenderer(){return!!this._renderer.value}setRenderer(h){this._renderer.value=h,this._renderer.value.onRequestRedraw(_=>this.refreshRows(_.start,_.end,!0)),this._needsSelectionRefresh=!0,this._fullRefresh()}addRefreshCallback(h){return this._renderDebouncer.addRefreshCallback(h)}_fullRefresh(){this._isPaused?this._needsFullRefresh=!0:this.refreshRows(0,this._rowCount-1)}clearTextureAtlas(){var h,_;this._renderer.value&&((_=(h=this._renderer.value).clearTextureAtlas)===null||_===void 0||_.call(h),this._fullRefresh())}handleDevicePixelRatioChange(){this._charSizeService.measure(),this._renderer.value&&(this._renderer.value.handleDevicePixelRatioChange(),this.refreshRows(0,this._rowCount-1))}handleResize(h,_){this._renderer.value&&(this._isPaused?this._pausedResizeTask.set(()=>this._renderer.value.handleResize(h,_)):this._renderer.value.handleResize(h,_),this._fullRefresh())}handleCharSizeChanged(){var h;(h=this._renderer.value)===null||h===void 0||h.handleCharSizeChanged()}handleBlur(){var h;(h=this._renderer.value)===null||h===void 0||h.handleBlur()}handleFocus(){var h;(h=this._renderer.value)===null||h===void 0||h.handleFocus()}handleSelectionChanged(h,_,S){var y;this._selectionState.start=h,this._selectionState.end=_,this._selectionState.columnSelectMode=S,(y=this._renderer.value)===null||y===void 0||y.handleSelectionChanged(h,_,S)}handleCursorMove(){var h;(h=this._renderer.value)===null||h===void 0||h.handleCursorMove()}clear(){var h;(h=this._renderer.value)===null||h===void 0||h.clear()}};s.RenderService=r=l([c(2,i.IOptionsService),c(3,v.ICharSizeService),c(4,i.IDecorationService),c(5,i.IBufferService),c(6,v.ICoreBrowserService),c(7,v.IThemeService)],r)},9312:function(x,s,o){var l=this&&this.__decorate||function(u,C,L,I){var R,B=arguments.length,O=B<3?C:I===null?I=Object.getOwnPropertyDescriptor(C,L):I;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")O=Reflect.decorate(u,C,L,I);else for(var $=u.length-1;$>=0;$--)(R=u[$])&&(O=(B<3?R(O):B>3?R(C,L,O):R(C,L))||O);return B>3&&O&&Object.defineProperty(C,L,O),O},c=this&&this.__param||function(u,C){return function(L,I){C(L,I,u)}};Object.defineProperty(s,"__esModule",{value:!0}),s.SelectionService=void 0;const a=o(9806),f=o(9504),p=o(456),v=o(4725),g=o(8460),t=o(844),n=o(6114),i=o(4841),r=o(511),h=o(2585),_=" ",S=new RegExp(_,"g");let y=s.SelectionService=class extends t.Disposable{constructor(u,C,L,I,R,B,O,$,W){super(),this._element=u,this._screenElement=C,this._linkifier=L,this._bufferService=I,this._coreService=R,this._mouseService=B,this._optionsService=O,this._renderService=$,this._coreBrowserService=W,this._dragScrollAmount=0,this._enabled=!0,this._workCell=new r.CellData,this._mouseDownTimeStamp=0,this._oldHasSelection=!1,this._oldSelectionStart=void 0,this._oldSelectionEnd=void 0,this._onLinuxMouseSelection=this.register(new g.EventEmitter),this.onLinuxMouseSelection=this._onLinuxMouseSelection.event,this._onRedrawRequest=this.register(new g.EventEmitter),this.onRequestRedraw=this._onRedrawRequest.event,this._onSelectionChange=this.register(new g.EventEmitter),this.onSelectionChange=this._onSelectionChange.event,this._onRequestScrollLines=this.register(new g.EventEmitter),this.onRequestScrollLines=this._onRequestScrollLines.event,this._mouseMoveListener=z=>this._handleMouseMove(z),this._mouseUpListener=z=>this._handleMouseUp(z),this._coreService.onUserInput(()=>{this.hasSelection&&this.clearSelection()}),this._trimListener=this._bufferService.buffer.lines.onTrim(z=>this._handleTrim(z)),this.register(this._bufferService.buffers.onBufferActivate(z=>this._handleBufferActivate(z))),this.enable(),this._model=new p.SelectionModel(this._bufferService),this._activeSelectionMode=0,this.register((0,t.toDisposable)(()=>{this._removeMouseDownListeners()}))}reset(){this.clearSelection()}disable(){this.clearSelection(),this._enabled=!1}enable(){this._enabled=!0}get selectionStart(){return this._model.finalSelectionStart}get selectionEnd(){return this._model.finalSelectionEnd}get hasSelection(){const u=this._model.finalSelectionStart,C=this._model.finalSelectionEnd;return!(!u||!C||u[0]===C[0]&&u[1]===C[1])}get selectionText(){const u=this._model.finalSelectionStart,C=this._model.finalSelectionEnd;if(!u||!C)return"";const L=this._bufferService.buffer,I=[];if(this._activeSelectionMode===3){if(u[0]===C[0])return"";const R=u[0]<C[0]?u[0]:C[0],B=u[0]<C[0]?C[0]:u[0];for(let O=u[1];O<=C[1];O++){const $=L.translateBufferLineToString(O,!0,R,B);I.push($)}}else{const R=u[1]===C[1]?C[0]:void 0;I.push(L.translateBufferLineToString(u[1],!0,u[0],R));for(let B=u[1]+1;B<=C[1]-1;B++){const O=L.lines.get(B),$=L.translateBufferLineToString(B,!0);O!=null&&O.isWrapped?I[I.length-1]+=$:I.push($)}if(u[1]!==C[1]){const B=L.lines.get(C[1]),O=L.translateBufferLineToString(C[1],!0,0,C[0]);B&&B.isWrapped?I[I.length-1]+=O:I.push(O)}}return I.map(R=>R.replace(S," ")).join(n.isWindows?`\r
`:`
`)}clearSelection(){this._model.clearSelection(),this._removeMouseDownListeners(),this.refresh(),this._onSelectionChange.fire()}refresh(u){this._refreshAnimationFrame||(this._refreshAnimationFrame=this._coreBrowserService.window.requestAnimationFrame(()=>this._refresh())),n.isLinux&&u&&this.selectionText.length&&this._onLinuxMouseSelection.fire(this.selectionText)}_refresh(){this._refreshAnimationFrame=void 0,this._onRedrawRequest.fire({start:this._model.finalSelectionStart,end:this._model.finalSelectionEnd,columnSelectMode:this._activeSelectionMode===3})}_isClickInSelection(u){const C=this._getMouseBufferCoords(u),L=this._model.finalSelectionStart,I=this._model.finalSelectionEnd;return!!(L&&I&&C)&&this._areCoordsInSelection(C,L,I)}isCellInSelection(u,C){const L=this._model.finalSelectionStart,I=this._model.finalSelectionEnd;return!(!L||!I)&&this._areCoordsInSelection([u,C],L,I)}_areCoordsInSelection(u,C,L){return u[1]>C[1]&&u[1]<L[1]||C[1]===L[1]&&u[1]===C[1]&&u[0]>=C[0]&&u[0]<L[0]||C[1]<L[1]&&u[1]===L[1]&&u[0]<L[0]||C[1]<L[1]&&u[1]===C[1]&&u[0]>=C[0]}_selectWordAtCursor(u,C){var L,I;const R=(I=(L=this._linkifier.currentLink)===null||L===void 0?void 0:L.link)===null||I===void 0?void 0:I.range;if(R)return this._model.selectionStart=[R.start.x-1,R.start.y-1],this._model.selectionStartLength=(0,i.getRangeLength)(R,this._bufferService.cols),this._model.selectionEnd=void 0,!0;const B=this._getMouseBufferCoords(u);return!!B&&(this._selectWordAt(B,C),this._model.selectionEnd=void 0,!0)}selectAll(){this._model.isSelectAllActive=!0,this.refresh(),this._onSelectionChange.fire()}selectLines(u,C){this._model.clearSelection(),u=Math.max(u,0),C=Math.min(C,this._bufferService.buffer.lines.length-1),this._model.selectionStart=[0,u],this._model.selectionEnd=[this._bufferService.cols,C],this.refresh(),this._onSelectionChange.fire()}_handleTrim(u){this._model.handleTrim(u)&&this.refresh()}_getMouseBufferCoords(u){const C=this._mouseService.getCoords(u,this._screenElement,this._bufferService.cols,this._bufferService.rows,!0);if(C)return C[0]--,C[1]--,C[1]+=this._bufferService.buffer.ydisp,C}_getMouseEventScrollAmount(u){let C=(0,a.getCoordsRelativeToElement)(this._coreBrowserService.window,u,this._screenElement)[1];const L=this._renderService.dimensions.css.canvas.height;return C>=0&&C<=L?0:(C>L&&(C-=L),C=Math.min(Math.max(C,-50),50),C/=50,C/Math.abs(C)+Math.round(14*C))}shouldForceSelection(u){return n.isMac?u.altKey&&this._optionsService.rawOptions.macOptionClickForcesSelection:u.shiftKey}handleMouseDown(u){if(this._mouseDownTimeStamp=u.timeStamp,(u.button!==2||!this.hasSelection)&&u.button===0){if(!this._enabled){if(!this.shouldForceSelection(u))return;u.stopPropagation()}u.preventDefault(),this._dragScrollAmount=0,this._enabled&&u.shiftKey?this._handleIncrementalClick(u):u.detail===1?this._handleSingleClick(u):u.detail===2?this._handleDoubleClick(u):u.detail===3&&this._handleTripleClick(u),this._addMouseDownListeners(),this.refresh(!0)}}_addMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.addEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.addEventListener("mouseup",this._mouseUpListener)),this._dragScrollIntervalTimer=this._coreBrowserService.window.setInterval(()=>this._dragScroll(),50)}_removeMouseDownListeners(){this._screenElement.ownerDocument&&(this._screenElement.ownerDocument.removeEventListener("mousemove",this._mouseMoveListener),this._screenElement.ownerDocument.removeEventListener("mouseup",this._mouseUpListener)),this._coreBrowserService.window.clearInterval(this._dragScrollIntervalTimer),this._dragScrollIntervalTimer=void 0}_handleIncrementalClick(u){this._model.selectionStart&&(this._model.selectionEnd=this._getMouseBufferCoords(u))}_handleSingleClick(u){if(this._model.selectionStartLength=0,this._model.isSelectAllActive=!1,this._activeSelectionMode=this.shouldColumnSelect(u)?3:0,this._model.selectionStart=this._getMouseBufferCoords(u),!this._model.selectionStart)return;this._model.selectionEnd=void 0;const C=this._bufferService.buffer.lines.get(this._model.selectionStart[1]);C&&C.length!==this._model.selectionStart[0]&&C.hasWidth(this._model.selectionStart[0])===0&&this._model.selectionStart[0]++}_handleDoubleClick(u){this._selectWordAtCursor(u,!0)&&(this._activeSelectionMode=1)}_handleTripleClick(u){const C=this._getMouseBufferCoords(u);C&&(this._activeSelectionMode=2,this._selectLineAt(C[1]))}shouldColumnSelect(u){return u.altKey&&!(n.isMac&&this._optionsService.rawOptions.macOptionClickForcesSelection)}_handleMouseMove(u){if(u.stopImmediatePropagation(),!this._model.selectionStart)return;const C=this._model.selectionEnd?[this._model.selectionEnd[0],this._model.selectionEnd[1]]:null;if(this._model.selectionEnd=this._getMouseBufferCoords(u),!this._model.selectionEnd)return void this.refresh(!0);this._activeSelectionMode===2?this._model.selectionEnd[1]<this._model.selectionStart[1]?this._model.selectionEnd[0]=0:this._model.selectionEnd[0]=this._bufferService.cols:this._activeSelectionMode===1&&this._selectToWordAt(this._model.selectionEnd),this._dragScrollAmount=this._getMouseEventScrollAmount(u),this._activeSelectionMode!==3&&(this._dragScrollAmount>0?this._model.selectionEnd[0]=this._bufferService.cols:this._dragScrollAmount<0&&(this._model.selectionEnd[0]=0));const L=this._bufferService.buffer;if(this._model.selectionEnd[1]<L.lines.length){const I=L.lines.get(this._model.selectionEnd[1]);I&&I.hasWidth(this._model.selectionEnd[0])===0&&this._model.selectionEnd[0]++}C&&C[0]===this._model.selectionEnd[0]&&C[1]===this._model.selectionEnd[1]||this.refresh(!0)}_dragScroll(){if(this._model.selectionEnd&&this._model.selectionStart&&this._dragScrollAmount){this._onRequestScrollLines.fire({amount:this._dragScrollAmount,suppressScrollEvent:!1});const u=this._bufferService.buffer;this._dragScrollAmount>0?(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=this._bufferService.cols),this._model.selectionEnd[1]=Math.min(u.ydisp+this._bufferService.rows,u.lines.length-1)):(this._activeSelectionMode!==3&&(this._model.selectionEnd[0]=0),this._model.selectionEnd[1]=u.ydisp),this.refresh()}}_handleMouseUp(u){const C=u.timeStamp-this._mouseDownTimeStamp;if(this._removeMouseDownListeners(),this.selectionText.length<=1&&C<500&&u.altKey&&this._optionsService.rawOptions.altClickMovesCursor){if(this._bufferService.buffer.ybase===this._bufferService.buffer.ydisp){const L=this._mouseService.getCoords(u,this._element,this._bufferService.cols,this._bufferService.rows,!1);if(L&&L[0]!==void 0&&L[1]!==void 0){const I=(0,f.moveToCellSequence)(L[0]-1,L[1]-1,this._bufferService,this._coreService.decPrivateModes.applicationCursorKeys);this._coreService.triggerDataEvent(I,!0)}}}else this._fireEventIfSelectionChanged()}_fireEventIfSelectionChanged(){const u=this._model.finalSelectionStart,C=this._model.finalSelectionEnd,L=!(!u||!C||u[0]===C[0]&&u[1]===C[1]);L?u&&C&&(this._oldSelectionStart&&this._oldSelectionEnd&&u[0]===this._oldSelectionStart[0]&&u[1]===this._oldSelectionStart[1]&&C[0]===this._oldSelectionEnd[0]&&C[1]===this._oldSelectionEnd[1]||this._fireOnSelectionChange(u,C,L)):this._oldHasSelection&&this._fireOnSelectionChange(u,C,L)}_fireOnSelectionChange(u,C,L){this._oldSelectionStart=u,this._oldSelectionEnd=C,this._oldHasSelection=L,this._onSelectionChange.fire()}_handleBufferActivate(u){this.clearSelection(),this._trimListener.dispose(),this._trimListener=u.activeBuffer.lines.onTrim(C=>this._handleTrim(C))}_convertViewportColToCharacterIndex(u,C){let L=C;for(let I=0;C>=I;I++){const R=u.loadCell(I,this._workCell).getChars().length;this._workCell.getWidth()===0?L--:R>1&&C!==I&&(L+=R-1)}return L}setSelection(u,C,L){this._model.clearSelection(),this._removeMouseDownListeners(),this._model.selectionStart=[u,C],this._model.selectionStartLength=L,this.refresh(),this._fireEventIfSelectionChanged()}rightClickSelect(u){this._isClickInSelection(u)||(this._selectWordAtCursor(u,!1)&&this.refresh(!0),this._fireEventIfSelectionChanged())}_getWordAt(u,C,L=!0,I=!0){if(u[0]>=this._bufferService.cols)return;const R=this._bufferService.buffer,B=R.lines.get(u[1]);if(!B)return;const O=R.translateBufferLineToString(u[1],!1);let $=this._convertViewportColToCharacterIndex(B,u[0]),W=$;const z=u[0]-$;let k=0,D=0,T=0,M=0;if(O.charAt($)===" "){for(;$>0&&O.charAt($-1)===" ";)$--;for(;W<O.length&&O.charAt(W+1)===" ";)W++}else{let q=u[0],K=u[0];B.getWidth(q)===0&&(k++,q--),B.getWidth(K)===2&&(D++,K++);const Q=B.getString(K).length;for(Q>1&&(M+=Q-1,W+=Q-1);q>0&&$>0&&!this._isCharWordSeparator(B.loadCell(q-1,this._workCell));){B.loadCell(q-1,this._workCell);const w=this._workCell.getChars().length;this._workCell.getWidth()===0?(k++,q--):w>1&&(T+=w-1,$-=w-1),$--,q--}for(;K<B.length&&W+1<O.length&&!this._isCharWordSeparator(B.loadCell(K+1,this._workCell));){B.loadCell(K+1,this._workCell);const w=this._workCell.getChars().length;this._workCell.getWidth()===2?(D++,K++):w>1&&(M+=w-1,W+=w-1),W++,K++}}W++;let N=$+z-k+T,U=Math.min(this._bufferService.cols,W-$+k+D-T-M);if(C||O.slice($,W).trim()!==""){if(L&&N===0&&B.getCodePoint(0)!==32){const q=R.lines.get(u[1]-1);if(q&&B.isWrapped&&q.getCodePoint(this._bufferService.cols-1)!==32){const K=this._getWordAt([this._bufferService.cols-1,u[1]-1],!1,!0,!1);if(K){const Q=this._bufferService.cols-K.start;N-=Q,U+=Q}}}if(I&&N+U===this._bufferService.cols&&B.getCodePoint(this._bufferService.cols-1)!==32){const q=R.lines.get(u[1]+1);if(q!=null&&q.isWrapped&&q.getCodePoint(0)!==32){const K=this._getWordAt([0,u[1]+1],!1,!1,!0);K&&(U+=K.length)}}return{start:N,length:U}}}_selectWordAt(u,C){const L=this._getWordAt(u,C);if(L){for(;L.start<0;)L.start+=this._bufferService.cols,u[1]--;this._model.selectionStart=[L.start,u[1]],this._model.selectionStartLength=L.length}}_selectToWordAt(u){const C=this._getWordAt(u,!0);if(C){let L=u[1];for(;C.start<0;)C.start+=this._bufferService.cols,L--;if(!this._model.areSelectionValuesReversed())for(;C.start+C.length>this._bufferService.cols;)C.length-=this._bufferService.cols,L++;this._model.selectionEnd=[this._model.areSelectionValuesReversed()?C.start:C.start+C.length,L]}}_isCharWordSeparator(u){return u.getWidth()!==0&&this._optionsService.rawOptions.wordSeparator.indexOf(u.getChars())>=0}_selectLineAt(u){const C=this._bufferService.buffer.getWrappedRangeForLine(u),L={start:{x:0,y:C.first},end:{x:this._bufferService.cols-1,y:C.last}};this._model.selectionStart=[0,C.first],this._model.selectionEnd=void 0,this._model.selectionStartLength=(0,i.getRangeLength)(L,this._bufferService.cols)}};s.SelectionService=y=l([c(3,h.IBufferService),c(4,h.ICoreService),c(5,v.IMouseService),c(6,h.IOptionsService),c(7,v.IRenderService),c(8,v.ICoreBrowserService)],y)},4725:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.IThemeService=s.ICharacterJoinerService=s.ISelectionService=s.IRenderService=s.IMouseService=s.ICoreBrowserService=s.ICharSizeService=void 0;const l=o(8343);s.ICharSizeService=(0,l.createDecorator)("CharSizeService"),s.ICoreBrowserService=(0,l.createDecorator)("CoreBrowserService"),s.IMouseService=(0,l.createDecorator)("MouseService"),s.IRenderService=(0,l.createDecorator)("RenderService"),s.ISelectionService=(0,l.createDecorator)("SelectionService"),s.ICharacterJoinerService=(0,l.createDecorator)("CharacterJoinerService"),s.IThemeService=(0,l.createDecorator)("ThemeService")},6731:function(x,s,o){var l=this&&this.__decorate||function(y,u,C,L){var I,R=arguments.length,B=R<3?u:L===null?L=Object.getOwnPropertyDescriptor(u,C):L;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")B=Reflect.decorate(y,u,C,L);else for(var O=y.length-1;O>=0;O--)(I=y[O])&&(B=(R<3?I(B):R>3?I(u,C,B):I(u,C))||B);return R>3&&B&&Object.defineProperty(u,C,B),B},c=this&&this.__param||function(y,u){return function(C,L){u(C,L,y)}};Object.defineProperty(s,"__esModule",{value:!0}),s.ThemeService=s.DEFAULT_ANSI_COLORS=void 0;const a=o(7239),f=o(8055),p=o(8460),v=o(844),g=o(2585),t=f.css.toColor("#ffffff"),n=f.css.toColor("#000000"),i=f.css.toColor("#ffffff"),r=f.css.toColor("#000000"),h={css:"rgba(255, 255, 255, 0.3)",rgba:4294967117};s.DEFAULT_ANSI_COLORS=Object.freeze((()=>{const y=[f.css.toColor("#2e3436"),f.css.toColor("#cc0000"),f.css.toColor("#4e9a06"),f.css.toColor("#c4a000"),f.css.toColor("#3465a4"),f.css.toColor("#75507b"),f.css.toColor("#06989a"),f.css.toColor("#d3d7cf"),f.css.toColor("#555753"),f.css.toColor("#ef2929"),f.css.toColor("#8ae234"),f.css.toColor("#fce94f"),f.css.toColor("#729fcf"),f.css.toColor("#ad7fa8"),f.css.toColor("#34e2e2"),f.css.toColor("#eeeeec")],u=[0,95,135,175,215,255];for(let C=0;C<216;C++){const L=u[C/36%6|0],I=u[C/6%6|0],R=u[C%6];y.push({css:f.channels.toCss(L,I,R),rgba:f.channels.toRgba(L,I,R)})}for(let C=0;C<24;C++){const L=8+10*C;y.push({css:f.channels.toCss(L,L,L),rgba:f.channels.toRgba(L,L,L)})}return y})());let _=s.ThemeService=class extends v.Disposable{get colors(){return this._colors}constructor(y){super(),this._optionsService=y,this._contrastCache=new a.ColorContrastCache,this._halfContrastCache=new a.ColorContrastCache,this._onChangeColors=this.register(new p.EventEmitter),this.onChangeColors=this._onChangeColors.event,this._colors={foreground:t,background:n,cursor:i,cursorAccent:r,selectionForeground:void 0,selectionBackgroundTransparent:h,selectionBackgroundOpaque:f.color.blend(n,h),selectionInactiveBackgroundTransparent:h,selectionInactiveBackgroundOpaque:f.color.blend(n,h),ansi:s.DEFAULT_ANSI_COLORS.slice(),contrastCache:this._contrastCache,halfContrastCache:this._halfContrastCache},this._updateRestoreColors(),this._setTheme(this._optionsService.rawOptions.theme),this.register(this._optionsService.onSpecificOptionChange("minimumContrastRatio",()=>this._contrastCache.clear())),this.register(this._optionsService.onSpecificOptionChange("theme",()=>this._setTheme(this._optionsService.rawOptions.theme)))}_setTheme(y={}){const u=this._colors;if(u.foreground=S(y.foreground,t),u.background=S(y.background,n),u.cursor=S(y.cursor,i),u.cursorAccent=S(y.cursorAccent,r),u.selectionBackgroundTransparent=S(y.selectionBackground,h),u.selectionBackgroundOpaque=f.color.blend(u.background,u.selectionBackgroundTransparent),u.selectionInactiveBackgroundTransparent=S(y.selectionInactiveBackground,u.selectionBackgroundTransparent),u.selectionInactiveBackgroundOpaque=f.color.blend(u.background,u.selectionInactiveBackgroundTransparent),u.selectionForeground=y.selectionForeground?S(y.selectionForeground,f.NULL_COLOR):void 0,u.selectionForeground===f.NULL_COLOR&&(u.selectionForeground=void 0),f.color.isOpaque(u.selectionBackgroundTransparent)&&(u.selectionBackgroundTransparent=f.color.opacity(u.selectionBackgroundTransparent,.3)),f.color.isOpaque(u.selectionInactiveBackgroundTransparent)&&(u.selectionInactiveBackgroundTransparent=f.color.opacity(u.selectionInactiveBackgroundTransparent,.3)),u.ansi=s.DEFAULT_ANSI_COLORS.slice(),u.ansi[0]=S(y.black,s.DEFAULT_ANSI_COLORS[0]),u.ansi[1]=S(y.red,s.DEFAULT_ANSI_COLORS[1]),u.ansi[2]=S(y.green,s.DEFAULT_ANSI_COLORS[2]),u.ansi[3]=S(y.yellow,s.DEFAULT_ANSI_COLORS[3]),u.ansi[4]=S(y.blue,s.DEFAULT_ANSI_COLORS[4]),u.ansi[5]=S(y.magenta,s.DEFAULT_ANSI_COLORS[5]),u.ansi[6]=S(y.cyan,s.DEFAULT_ANSI_COLORS[6]),u.ansi[7]=S(y.white,s.DEFAULT_ANSI_COLORS[7]),u.ansi[8]=S(y.brightBlack,s.DEFAULT_ANSI_COLORS[8]),u.ansi[9]=S(y.brightRed,s.DEFAULT_ANSI_COLORS[9]),u.ansi[10]=S(y.brightGreen,s.DEFAULT_ANSI_COLORS[10]),u.ansi[11]=S(y.brightYellow,s.DEFAULT_ANSI_COLORS[11]),u.ansi[12]=S(y.brightBlue,s.DEFAULT_ANSI_COLORS[12]),u.ansi[13]=S(y.brightMagenta,s.DEFAULT_ANSI_COLORS[13]),u.ansi[14]=S(y.brightCyan,s.DEFAULT_ANSI_COLORS[14]),u.ansi[15]=S(y.brightWhite,s.DEFAULT_ANSI_COLORS[15]),y.extendedAnsi){const C=Math.min(u.ansi.length-16,y.extendedAnsi.length);for(let L=0;L<C;L++)u.ansi[L+16]=S(y.extendedAnsi[L],s.DEFAULT_ANSI_COLORS[L+16])}this._contrastCache.clear(),this._halfContrastCache.clear(),this._updateRestoreColors(),this._onChangeColors.fire(this.colors)}restoreColor(y){this._restoreColor(y),this._onChangeColors.fire(this.colors)}_restoreColor(y){if(y!==void 0)switch(y){case 256:this._colors.foreground=this._restoreColors.foreground;break;case 257:this._colors.background=this._restoreColors.background;break;case 258:this._colors.cursor=this._restoreColors.cursor;break;default:this._colors.ansi[y]=this._restoreColors.ansi[y]}else for(let u=0;u<this._restoreColors.ansi.length;++u)this._colors.ansi[u]=this._restoreColors.ansi[u]}modifyColors(y){y(this._colors),this._onChangeColors.fire(this.colors)}_updateRestoreColors(){this._restoreColors={foreground:this._colors.foreground,background:this._colors.background,cursor:this._colors.cursor,ansi:this._colors.ansi.slice()}}};function S(y,u){if(y!==void 0)try{return f.css.toColor(y)}catch{}return u}s.ThemeService=_=l([c(0,g.IOptionsService)],_)},6349:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CircularList=void 0;const l=o(8460),c=o(844);class a extends c.Disposable{constructor(p){super(),this._maxLength=p,this.onDeleteEmitter=this.register(new l.EventEmitter),this.onDelete=this.onDeleteEmitter.event,this.onInsertEmitter=this.register(new l.EventEmitter),this.onInsert=this.onInsertEmitter.event,this.onTrimEmitter=this.register(new l.EventEmitter),this.onTrim=this.onTrimEmitter.event,this._array=new Array(this._maxLength),this._startIndex=0,this._length=0}get maxLength(){return this._maxLength}set maxLength(p){if(this._maxLength===p)return;const v=new Array(p);for(let g=0;g<Math.min(p,this.length);g++)v[g]=this._array[this._getCyclicIndex(g)];this._array=v,this._maxLength=p,this._startIndex=0}get length(){return this._length}set length(p){if(p>this._length)for(let v=this._length;v<p;v++)this._array[v]=void 0;this._length=p}get(p){return this._array[this._getCyclicIndex(p)]}set(p,v){this._array[this._getCyclicIndex(p)]=v}push(p){this._array[this._getCyclicIndex(this._length)]=p,this._length===this._maxLength?(this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1)):this._length++}recycle(){if(this._length!==this._maxLength)throw new Error("Can only recycle when the buffer is full");return this._startIndex=++this._startIndex%this._maxLength,this.onTrimEmitter.fire(1),this._array[this._getCyclicIndex(this._length-1)]}get isFull(){return this._length===this._maxLength}pop(){return this._array[this._getCyclicIndex(this._length---1)]}splice(p,v,...g){if(v){for(let t=p;t<this._length-v;t++)this._array[this._getCyclicIndex(t)]=this._array[this._getCyclicIndex(t+v)];this._length-=v,this.onDeleteEmitter.fire({index:p,amount:v})}for(let t=this._length-1;t>=p;t--)this._array[this._getCyclicIndex(t+g.length)]=this._array[this._getCyclicIndex(t)];for(let t=0;t<g.length;t++)this._array[this._getCyclicIndex(p+t)]=g[t];if(g.length&&this.onInsertEmitter.fire({index:p,amount:g.length}),this._length+g.length>this._maxLength){const t=this._length+g.length-this._maxLength;this._startIndex+=t,this._length=this._maxLength,this.onTrimEmitter.fire(t)}else this._length+=g.length}trimStart(p){p>this._length&&(p=this._length),this._startIndex+=p,this._length-=p,this.onTrimEmitter.fire(p)}shiftElements(p,v,g){if(!(v<=0)){if(p<0||p>=this._length)throw new Error("start argument out of range");if(p+g<0)throw new Error("Cannot shift elements in list beyond index 0");if(g>0){for(let n=v-1;n>=0;n--)this.set(p+n+g,this.get(p+n));const t=p+v+g-this._length;if(t>0)for(this._length+=t;this._length>this._maxLength;)this._length--,this._startIndex++,this.onTrimEmitter.fire(1)}else for(let t=0;t<v;t++)this.set(p+t+g,this.get(p+t))}}_getCyclicIndex(p){return(this._startIndex+p)%this._maxLength}}s.CircularList=a},1439:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.clone=void 0,s.clone=function o(l,c=5){if(typeof l!="object")return l;const a=Array.isArray(l)?[]:{};for(const f in l)a[f]=c<=1?l[f]:l[f]&&o(l[f],c-1);return a}},8055:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.contrastRatio=s.toPaddedHex=s.rgba=s.rgb=s.css=s.color=s.channels=s.NULL_COLOR=void 0;const l=o(6114);let c=0,a=0,f=0,p=0;var v,g,t,n,i;function r(_){const S=_.toString(16);return S.length<2?"0"+S:S}function h(_,S){return _<S?(S+.05)/(_+.05):(_+.05)/(S+.05)}s.NULL_COLOR={css:"#00000000",rgba:0},function(_){_.toCss=function(S,y,u,C){return C!==void 0?`#${r(S)}${r(y)}${r(u)}${r(C)}`:`#${r(S)}${r(y)}${r(u)}`},_.toRgba=function(S,y,u,C=255){return(S<<24|y<<16|u<<8|C)>>>0}}(v||(s.channels=v={})),function(_){function S(y,u){return p=Math.round(255*u),[c,a,f]=i.toChannels(y.rgba),{css:v.toCss(c,a,f,p),rgba:v.toRgba(c,a,f,p)}}_.blend=function(y,u){if(p=(255&u.rgba)/255,p===1)return{css:u.css,rgba:u.rgba};const C=u.rgba>>24&255,L=u.rgba>>16&255,I=u.rgba>>8&255,R=y.rgba>>24&255,B=y.rgba>>16&255,O=y.rgba>>8&255;return c=R+Math.round((C-R)*p),a=B+Math.round((L-B)*p),f=O+Math.round((I-O)*p),{css:v.toCss(c,a,f),rgba:v.toRgba(c,a,f)}},_.isOpaque=function(y){return(255&y.rgba)==255},_.ensureContrastRatio=function(y,u,C){const L=i.ensureContrastRatio(y.rgba,u.rgba,C);if(L)return i.toColor(L>>24&255,L>>16&255,L>>8&255)},_.opaque=function(y){const u=(255|y.rgba)>>>0;return[c,a,f]=i.toChannels(u),{css:v.toCss(c,a,f),rgba:u}},_.opacity=S,_.multiplyOpacity=function(y,u){return p=255&y.rgba,S(y,p*u/255)},_.toColorRGB=function(y){return[y.rgba>>24&255,y.rgba>>16&255,y.rgba>>8&255]}}(g||(s.color=g={})),function(_){let S,y;if(!l.isNode){const u=document.createElement("canvas");u.width=1,u.height=1;const C=u.getContext("2d",{willReadFrequently:!0});C&&(S=C,S.globalCompositeOperation="copy",y=S.createLinearGradient(0,0,1,1))}_.toColor=function(u){if(u.match(/#[\da-f]{3,8}/i))switch(u.length){case 4:return c=parseInt(u.slice(1,2).repeat(2),16),a=parseInt(u.slice(2,3).repeat(2),16),f=parseInt(u.slice(3,4).repeat(2),16),i.toColor(c,a,f);case 5:return c=parseInt(u.slice(1,2).repeat(2),16),a=parseInt(u.slice(2,3).repeat(2),16),f=parseInt(u.slice(3,4).repeat(2),16),p=parseInt(u.slice(4,5).repeat(2),16),i.toColor(c,a,f,p);case 7:return{css:u,rgba:(parseInt(u.slice(1),16)<<8|255)>>>0};case 9:return{css:u,rgba:parseInt(u.slice(1),16)>>>0}}const C=u.match(/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(,\s*(0|1|\d?\.(\d+))\s*)?\)/);if(C)return c=parseInt(C[1]),a=parseInt(C[2]),f=parseInt(C[3]),p=Math.round(255*(C[5]===void 0?1:parseFloat(C[5]))),i.toColor(c,a,f,p);if(!S||!y)throw new Error("css.toColor: Unsupported css format");if(S.fillStyle=y,S.fillStyle=u,typeof S.fillStyle!="string")throw new Error("css.toColor: Unsupported css format");if(S.fillRect(0,0,1,1),[c,a,f,p]=S.getImageData(0,0,1,1).data,p!==255)throw new Error("css.toColor: Unsupported css format");return{rgba:v.toRgba(c,a,f,p),css:u}}}(t||(s.css=t={})),function(_){function S(y,u,C){const L=y/255,I=u/255,R=C/255;return .2126*(L<=.03928?L/12.92:Math.pow((L+.055)/1.055,2.4))+.7152*(I<=.03928?I/12.92:Math.pow((I+.055)/1.055,2.4))+.0722*(R<=.03928?R/12.92:Math.pow((R+.055)/1.055,2.4))}_.relativeLuminance=function(y){return S(y>>16&255,y>>8&255,255&y)},_.relativeLuminance2=S}(n||(s.rgb=n={})),function(_){function S(u,C,L){const I=u>>24&255,R=u>>16&255,B=u>>8&255;let O=C>>24&255,$=C>>16&255,W=C>>8&255,z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));for(;z<L&&(O>0||$>0||W>0);)O-=Math.max(0,Math.ceil(.1*O)),$-=Math.max(0,Math.ceil(.1*$)),W-=Math.max(0,Math.ceil(.1*W)),z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));return(O<<24|$<<16|W<<8|255)>>>0}function y(u,C,L){const I=u>>24&255,R=u>>16&255,B=u>>8&255;let O=C>>24&255,$=C>>16&255,W=C>>8&255,z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));for(;z<L&&(O<255||$<255||W<255);)O=Math.min(255,O+Math.ceil(.1*(255-O))),$=Math.min(255,$+Math.ceil(.1*(255-$))),W=Math.min(255,W+Math.ceil(.1*(255-W))),z=h(n.relativeLuminance2(O,$,W),n.relativeLuminance2(I,R,B));return(O<<24|$<<16|W<<8|255)>>>0}_.ensureContrastRatio=function(u,C,L){const I=n.relativeLuminance(u>>8),R=n.relativeLuminance(C>>8);if(h(I,R)<L){if(R<I){const $=S(u,C,L),W=h(I,n.relativeLuminance($>>8));if(W<L){const z=y(u,C,L);return W>h(I,n.relativeLuminance(z>>8))?$:z}return $}const B=y(u,C,L),O=h(I,n.relativeLuminance(B>>8));if(O<L){const $=S(u,C,L);return O>h(I,n.relativeLuminance($>>8))?B:$}return B}},_.reduceLuminance=S,_.increaseLuminance=y,_.toChannels=function(u){return[u>>24&255,u>>16&255,u>>8&255,255&u]},_.toColor=function(u,C,L,I){return{css:v.toCss(u,C,L,I),rgba:v.toRgba(u,C,L,I)}}}(i||(s.rgba=i={})),s.toPaddedHex=r,s.contrastRatio=h},8969:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CoreTerminal=void 0;const l=o(844),c=o(2585),a=o(4348),f=o(7866),p=o(744),v=o(7302),g=o(6975),t=o(8460),n=o(1753),i=o(1480),r=o(7994),h=o(9282),_=o(5435),S=o(5981),y=o(2660);let u=!1;class C extends l.Disposable{get onScroll(){return this._onScrollApi||(this._onScrollApi=this.register(new t.EventEmitter),this._onScroll.event(I=>{var R;(R=this._onScrollApi)===null||R===void 0||R.fire(I.position)})),this._onScrollApi.event}get cols(){return this._bufferService.cols}get rows(){return this._bufferService.rows}get buffers(){return this._bufferService.buffers}get options(){return this.optionsService.options}set options(I){for(const R in I)this.optionsService.options[R]=I[R]}constructor(I){super(),this._windowsWrappingHeuristics=this.register(new l.MutableDisposable),this._onBinary=this.register(new t.EventEmitter),this.onBinary=this._onBinary.event,this._onData=this.register(new t.EventEmitter),this.onData=this._onData.event,this._onLineFeed=this.register(new t.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onResize=this.register(new t.EventEmitter),this.onResize=this._onResize.event,this._onWriteParsed=this.register(new t.EventEmitter),this.onWriteParsed=this._onWriteParsed.event,this._onScroll=this.register(new t.EventEmitter),this._instantiationService=new a.InstantiationService,this.optionsService=this.register(new v.OptionsService(I)),this._instantiationService.setService(c.IOptionsService,this.optionsService),this._bufferService=this.register(this._instantiationService.createInstance(p.BufferService)),this._instantiationService.setService(c.IBufferService,this._bufferService),this._logService=this.register(this._instantiationService.createInstance(f.LogService)),this._instantiationService.setService(c.ILogService,this._logService),this.coreService=this.register(this._instantiationService.createInstance(g.CoreService)),this._instantiationService.setService(c.ICoreService,this.coreService),this.coreMouseService=this.register(this._instantiationService.createInstance(n.CoreMouseService)),this._instantiationService.setService(c.ICoreMouseService,this.coreMouseService),this.unicodeService=this.register(this._instantiationService.createInstance(i.UnicodeService)),this._instantiationService.setService(c.IUnicodeService,this.unicodeService),this._charsetService=this._instantiationService.createInstance(r.CharsetService),this._instantiationService.setService(c.ICharsetService,this._charsetService),this._oscLinkService=this._instantiationService.createInstance(y.OscLinkService),this._instantiationService.setService(c.IOscLinkService,this._oscLinkService),this._inputHandler=this.register(new _.InputHandler(this._bufferService,this._charsetService,this.coreService,this._logService,this.optionsService,this._oscLinkService,this.coreMouseService,this.unicodeService)),this.register((0,t.forwardEvent)(this._inputHandler.onLineFeed,this._onLineFeed)),this.register(this._inputHandler),this.register((0,t.forwardEvent)(this._bufferService.onResize,this._onResize)),this.register((0,t.forwardEvent)(this.coreService.onData,this._onData)),this.register((0,t.forwardEvent)(this.coreService.onBinary,this._onBinary)),this.register(this.coreService.onRequestScrollToBottom(()=>this.scrollToBottom())),this.register(this.coreService.onUserInput(()=>this._writeBuffer.handleUserInput())),this.register(this.optionsService.onMultipleOptionChange(["windowsMode","windowsPty"],()=>this._handleWindowsPtyOptionChange())),this.register(this._bufferService.onScroll(R=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this.register(this._inputHandler.onScroll(R=>{this._onScroll.fire({position:this._bufferService.buffer.ydisp,source:0}),this._inputHandler.markRangeDirty(this._bufferService.buffer.scrollTop,this._bufferService.buffer.scrollBottom)})),this._writeBuffer=this.register(new S.WriteBuffer((R,B)=>this._inputHandler.parse(R,B))),this.register((0,t.forwardEvent)(this._writeBuffer.onWriteParsed,this._onWriteParsed))}write(I,R){this._writeBuffer.write(I,R)}writeSync(I,R){this._logService.logLevel<=c.LogLevelEnum.WARN&&!u&&(this._logService.warn("writeSync is unreliable and will be removed soon."),u=!0),this._writeBuffer.writeSync(I,R)}resize(I,R){isNaN(I)||isNaN(R)||(I=Math.max(I,p.MINIMUM_COLS),R=Math.max(R,p.MINIMUM_ROWS),this._bufferService.resize(I,R))}scroll(I,R=!1){this._bufferService.scroll(I,R)}scrollLines(I,R,B){this._bufferService.scrollLines(I,R,B)}scrollPages(I){this.scrollLines(I*(this.rows-1))}scrollToTop(){this.scrollLines(-this._bufferService.buffer.ydisp)}scrollToBottom(){this.scrollLines(this._bufferService.buffer.ybase-this._bufferService.buffer.ydisp)}scrollToLine(I){const R=I-this._bufferService.buffer.ydisp;R!==0&&this.scrollLines(R)}registerEscHandler(I,R){return this._inputHandler.registerEscHandler(I,R)}registerDcsHandler(I,R){return this._inputHandler.registerDcsHandler(I,R)}registerCsiHandler(I,R){return this._inputHandler.registerCsiHandler(I,R)}registerOscHandler(I,R){return this._inputHandler.registerOscHandler(I,R)}_setup(){this._handleWindowsPtyOptionChange()}reset(){this._inputHandler.reset(),this._bufferService.reset(),this._charsetService.reset(),this.coreService.reset(),this.coreMouseService.reset()}_handleWindowsPtyOptionChange(){let I=!1;const R=this.optionsService.rawOptions.windowsPty;R&&R.buildNumber!==void 0&&R.buildNumber!==void 0?I=R.backend==="conpty"&&R.buildNumber<21376:this.optionsService.rawOptions.windowsMode&&(I=!0),I?this._enableWindowsWrappingHeuristics():this._windowsWrappingHeuristics.clear()}_enableWindowsWrappingHeuristics(){if(!this._windowsWrappingHeuristics.value){const I=[];I.push(this.onLineFeed(h.updateWindowsModeWrappedState.bind(null,this._bufferService))),I.push(this.registerCsiHandler({final:"H"},()=>((0,h.updateWindowsModeWrappedState)(this._bufferService),!1))),this._windowsWrappingHeuristics.value=(0,l.toDisposable)(()=>{for(const R of I)R.dispose()})}}}s.CoreTerminal=C},8460:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.forwardEvent=s.EventEmitter=void 0,s.EventEmitter=class{constructor(){this._listeners=[],this._disposed=!1}get event(){return this._event||(this._event=o=>(this._listeners.push(o),{dispose:()=>{if(!this._disposed){for(let l=0;l<this._listeners.length;l++)if(this._listeners[l]===o)return void this._listeners.splice(l,1)}}})),this._event}fire(o,l){const c=[];for(let a=0;a<this._listeners.length;a++)c.push(this._listeners[a]);for(let a=0;a<c.length;a++)c[a].call(void 0,o,l)}dispose(){this.clearListeners(),this._disposed=!0}clearListeners(){this._listeners&&(this._listeners.length=0)}},s.forwardEvent=function(o,l){return o(c=>l.fire(c))}},5435:function(x,s,o){var l=this&&this.__decorate||function(z,k,D,T){var M,N=arguments.length,U=N<3?k:T===null?T=Object.getOwnPropertyDescriptor(k,D):T;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")U=Reflect.decorate(z,k,D,T);else for(var q=z.length-1;q>=0;q--)(M=z[q])&&(U=(N<3?M(U):N>3?M(k,D,U):M(k,D))||U);return N>3&&U&&Object.defineProperty(k,D,U),U},c=this&&this.__param||function(z,k){return function(D,T){k(D,T,z)}};Object.defineProperty(s,"__esModule",{value:!0}),s.InputHandler=s.WindowsOptionsReportType=void 0;const a=o(2584),f=o(7116),p=o(2015),v=o(844),g=o(482),t=o(8437),n=o(8460),i=o(643),r=o(511),h=o(3734),_=o(2585),S=o(6242),y=o(6351),u=o(5941),C={"(":0,")":1,"*":2,"+":3,"-":1,".":2},L=131072;function I(z,k){if(z>24)return k.setWinLines||!1;switch(z){case 1:return!!k.restoreWin;case 2:return!!k.minimizeWin;case 3:return!!k.setWinPosition;case 4:return!!k.setWinSizePixels;case 5:return!!k.raiseWin;case 6:return!!k.lowerWin;case 7:return!!k.refreshWin;case 8:return!!k.setWinSizeChars;case 9:return!!k.maximizeWin;case 10:return!!k.fullscreenWin;case 11:return!!k.getWinState;case 13:return!!k.getWinPosition;case 14:return!!k.getWinSizePixels;case 15:return!!k.getScreenSizePixels;case 16:return!!k.getCellSizePixels;case 18:return!!k.getWinSizeChars;case 19:return!!k.getScreenSizeChars;case 20:return!!k.getIconTitle;case 21:return!!k.getWinTitle;case 22:return!!k.pushTitle;case 23:return!!k.popTitle;case 24:return!!k.setWinLines}return!1}var R;(function(z){z[z.GET_WIN_SIZE_PIXELS=0]="GET_WIN_SIZE_PIXELS",z[z.GET_CELL_SIZE_PIXELS=1]="GET_CELL_SIZE_PIXELS"})(R||(s.WindowsOptionsReportType=R={}));let B=0;class O extends v.Disposable{getAttrData(){return this._curAttrData}constructor(k,D,T,M,N,U,q,K,Q=new p.EscapeSequenceParser){super(),this._bufferService=k,this._charsetService=D,this._coreService=T,this._logService=M,this._optionsService=N,this._oscLinkService=U,this._coreMouseService=q,this._unicodeService=K,this._parser=Q,this._parseBuffer=new Uint32Array(4096),this._stringDecoder=new g.StringToUtf32,this._utf8Decoder=new g.Utf8ToUtf32,this._workCell=new r.CellData,this._windowTitle="",this._iconName="",this._windowTitleStack=[],this._iconNameStack=[],this._curAttrData=t.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=t.DEFAULT_ATTR_DATA.clone(),this._onRequestBell=this.register(new n.EventEmitter),this.onRequestBell=this._onRequestBell.event,this._onRequestRefreshRows=this.register(new n.EventEmitter),this.onRequestRefreshRows=this._onRequestRefreshRows.event,this._onRequestReset=this.register(new n.EventEmitter),this.onRequestReset=this._onRequestReset.event,this._onRequestSendFocus=this.register(new n.EventEmitter),this.onRequestSendFocus=this._onRequestSendFocus.event,this._onRequestSyncScrollBar=this.register(new n.EventEmitter),this.onRequestSyncScrollBar=this._onRequestSyncScrollBar.event,this._onRequestWindowsOptionsReport=this.register(new n.EventEmitter),this.onRequestWindowsOptionsReport=this._onRequestWindowsOptionsReport.event,this._onA11yChar=this.register(new n.EventEmitter),this.onA11yChar=this._onA11yChar.event,this._onA11yTab=this.register(new n.EventEmitter),this.onA11yTab=this._onA11yTab.event,this._onCursorMove=this.register(new n.EventEmitter),this.onCursorMove=this._onCursorMove.event,this._onLineFeed=this.register(new n.EventEmitter),this.onLineFeed=this._onLineFeed.event,this._onScroll=this.register(new n.EventEmitter),this.onScroll=this._onScroll.event,this._onTitleChange=this.register(new n.EventEmitter),this.onTitleChange=this._onTitleChange.event,this._onColor=this.register(new n.EventEmitter),this.onColor=this._onColor.event,this._parseStack={paused:!1,cursorStartX:0,cursorStartY:0,decodedLength:0,position:0},this._specialColors=[256,257,258],this.register(this._parser),this._dirtyRowTracker=new $(this._bufferService),this._activeBuffer=this._bufferService.buffer,this.register(this._bufferService.buffers.onBufferActivate(w=>this._activeBuffer=w.activeBuffer)),this._parser.setCsiHandlerFallback((w,P)=>{this._logService.debug("Unknown CSI code: ",{identifier:this._parser.identToString(w),params:P.toArray()})}),this._parser.setEscHandlerFallback(w=>{this._logService.debug("Unknown ESC code: ",{identifier:this._parser.identToString(w)})}),this._parser.setExecuteHandlerFallback(w=>{this._logService.debug("Unknown EXECUTE code: ",{code:w})}),this._parser.setOscHandlerFallback((w,P,F)=>{this._logService.debug("Unknown OSC code: ",{identifier:w,action:P,data:F})}),this._parser.setDcsHandlerFallback((w,P,F)=>{P==="HOOK"&&(F=F.toArray()),this._logService.debug("Unknown DCS code: ",{identifier:this._parser.identToString(w),action:P,payload:F})}),this._parser.setPrintHandler((w,P,F)=>this.print(w,P,F)),this._parser.registerCsiHandler({final:"@"},w=>this.insertChars(w)),this._parser.registerCsiHandler({intermediates:" ",final:"@"},w=>this.scrollLeft(w)),this._parser.registerCsiHandler({final:"A"},w=>this.cursorUp(w)),this._parser.registerCsiHandler({intermediates:" ",final:"A"},w=>this.scrollRight(w)),this._parser.registerCsiHandler({final:"B"},w=>this.cursorDown(w)),this._parser.registerCsiHandler({final:"C"},w=>this.cursorForward(w)),this._parser.registerCsiHandler({final:"D"},w=>this.cursorBackward(w)),this._parser.registerCsiHandler({final:"E"},w=>this.cursorNextLine(w)),this._parser.registerCsiHandler({final:"F"},w=>this.cursorPrecedingLine(w)),this._parser.registerCsiHandler({final:"G"},w=>this.cursorCharAbsolute(w)),this._parser.registerCsiHandler({final:"H"},w=>this.cursorPosition(w)),this._parser.registerCsiHandler({final:"I"},w=>this.cursorForwardTab(w)),this._parser.registerCsiHandler({final:"J"},w=>this.eraseInDisplay(w,!1)),this._parser.registerCsiHandler({prefix:"?",final:"J"},w=>this.eraseInDisplay(w,!0)),this._parser.registerCsiHandler({final:"K"},w=>this.eraseInLine(w,!1)),this._parser.registerCsiHandler({prefix:"?",final:"K"},w=>this.eraseInLine(w,!0)),this._parser.registerCsiHandler({final:"L"},w=>this.insertLines(w)),this._parser.registerCsiHandler({final:"M"},w=>this.deleteLines(w)),this._parser.registerCsiHandler({final:"P"},w=>this.deleteChars(w)),this._parser.registerCsiHandler({final:"S"},w=>this.scrollUp(w)),this._parser.registerCsiHandler({final:"T"},w=>this.scrollDown(w)),this._parser.registerCsiHandler({final:"X"},w=>this.eraseChars(w)),this._parser.registerCsiHandler({final:"Z"},w=>this.cursorBackwardTab(w)),this._parser.registerCsiHandler({final:"`"},w=>this.charPosAbsolute(w)),this._parser.registerCsiHandler({final:"a"},w=>this.hPositionRelative(w)),this._parser.registerCsiHandler({final:"b"},w=>this.repeatPrecedingCharacter(w)),this._parser.registerCsiHandler({final:"c"},w=>this.sendDeviceAttributesPrimary(w)),this._parser.registerCsiHandler({prefix:">",final:"c"},w=>this.sendDeviceAttributesSecondary(w)),this._parser.registerCsiHandler({final:"d"},w=>this.linePosAbsolute(w)),this._parser.registerCsiHandler({final:"e"},w=>this.vPositionRelative(w)),this._parser.registerCsiHandler({final:"f"},w=>this.hVPosition(w)),this._parser.registerCsiHandler({final:"g"},w=>this.tabClear(w)),this._parser.registerCsiHandler({final:"h"},w=>this.setMode(w)),this._parser.registerCsiHandler({prefix:"?",final:"h"},w=>this.setModePrivate(w)),this._parser.registerCsiHandler({final:"l"},w=>this.resetMode(w)),this._parser.registerCsiHandler({prefix:"?",final:"l"},w=>this.resetModePrivate(w)),this._parser.registerCsiHandler({final:"m"},w=>this.charAttributes(w)),this._parser.registerCsiHandler({final:"n"},w=>this.deviceStatus(w)),this._parser.registerCsiHandler({prefix:"?",final:"n"},w=>this.deviceStatusPrivate(w)),this._parser.registerCsiHandler({intermediates:"!",final:"p"},w=>this.softReset(w)),this._parser.registerCsiHandler({intermediates:" ",final:"q"},w=>this.setCursorStyle(w)),this._parser.registerCsiHandler({final:"r"},w=>this.setScrollRegion(w)),this._parser.registerCsiHandler({final:"s"},w=>this.saveCursor(w)),this._parser.registerCsiHandler({final:"t"},w=>this.windowOptions(w)),this._parser.registerCsiHandler({final:"u"},w=>this.restoreCursor(w)),this._parser.registerCsiHandler({intermediates:"'",final:"}"},w=>this.insertColumns(w)),this._parser.registerCsiHandler({intermediates:"'",final:"~"},w=>this.deleteColumns(w)),this._parser.registerCsiHandler({intermediates:'"',final:"q"},w=>this.selectProtected(w)),this._parser.registerCsiHandler({intermediates:"$",final:"p"},w=>this.requestMode(w,!0)),this._parser.registerCsiHandler({prefix:"?",intermediates:"$",final:"p"},w=>this.requestMode(w,!1)),this._parser.setExecuteHandler(a.C0.BEL,()=>this.bell()),this._parser.setExecuteHandler(a.C0.LF,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.VT,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.FF,()=>this.lineFeed()),this._parser.setExecuteHandler(a.C0.CR,()=>this.carriageReturn()),this._parser.setExecuteHandler(a.C0.BS,()=>this.backspace()),this._parser.setExecuteHandler(a.C0.HT,()=>this.tab()),this._parser.setExecuteHandler(a.C0.SO,()=>this.shiftOut()),this._parser.setExecuteHandler(a.C0.SI,()=>this.shiftIn()),this._parser.setExecuteHandler(a.C1.IND,()=>this.index()),this._parser.setExecuteHandler(a.C1.NEL,()=>this.nextLine()),this._parser.setExecuteHandler(a.C1.HTS,()=>this.tabSet()),this._parser.registerOscHandler(0,new S.OscHandler(w=>(this.setTitle(w),this.setIconName(w),!0))),this._parser.registerOscHandler(1,new S.OscHandler(w=>this.setIconName(w))),this._parser.registerOscHandler(2,new S.OscHandler(w=>this.setTitle(w))),this._parser.registerOscHandler(4,new S.OscHandler(w=>this.setOrReportIndexedColor(w))),this._parser.registerOscHandler(8,new S.OscHandler(w=>this.setHyperlink(w))),this._parser.registerOscHandler(10,new S.OscHandler(w=>this.setOrReportFgColor(w))),this._parser.registerOscHandler(11,new S.OscHandler(w=>this.setOrReportBgColor(w))),this._parser.registerOscHandler(12,new S.OscHandler(w=>this.setOrReportCursorColor(w))),this._parser.registerOscHandler(104,new S.OscHandler(w=>this.restoreIndexedColor(w))),this._parser.registerOscHandler(110,new S.OscHandler(w=>this.restoreFgColor(w))),this._parser.registerOscHandler(111,new S.OscHandler(w=>this.restoreBgColor(w))),this._parser.registerOscHandler(112,new S.OscHandler(w=>this.restoreCursorColor(w))),this._parser.registerEscHandler({final:"7"},()=>this.saveCursor()),this._parser.registerEscHandler({final:"8"},()=>this.restoreCursor()),this._parser.registerEscHandler({final:"D"},()=>this.index()),this._parser.registerEscHandler({final:"E"},()=>this.nextLine()),this._parser.registerEscHandler({final:"H"},()=>this.tabSet()),this._parser.registerEscHandler({final:"M"},()=>this.reverseIndex()),this._parser.registerEscHandler({final:"="},()=>this.keypadApplicationMode()),this._parser.registerEscHandler({final:">"},()=>this.keypadNumericMode()),this._parser.registerEscHandler({final:"c"},()=>this.fullReset()),this._parser.registerEscHandler({final:"n"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"o"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"|"},()=>this.setgLevel(3)),this._parser.registerEscHandler({final:"}"},()=>this.setgLevel(2)),this._parser.registerEscHandler({final:"~"},()=>this.setgLevel(1)),this._parser.registerEscHandler({intermediates:"%",final:"@"},()=>this.selectDefaultCharset()),this._parser.registerEscHandler({intermediates:"%",final:"G"},()=>this.selectDefaultCharset());for(const w in f.CHARSETS)this._parser.registerEscHandler({intermediates:"(",final:w},()=>this.selectCharset("("+w)),this._parser.registerEscHandler({intermediates:")",final:w},()=>this.selectCharset(")"+w)),this._parser.registerEscHandler({intermediates:"*",final:w},()=>this.selectCharset("*"+w)),this._parser.registerEscHandler({intermediates:"+",final:w},()=>this.selectCharset("+"+w)),this._parser.registerEscHandler({intermediates:"-",final:w},()=>this.selectCharset("-"+w)),this._parser.registerEscHandler({intermediates:".",final:w},()=>this.selectCharset("."+w)),this._parser.registerEscHandler({intermediates:"/",final:w},()=>this.selectCharset("/"+w));this._parser.registerEscHandler({intermediates:"#",final:"8"},()=>this.screenAlignmentPattern()),this._parser.setErrorHandler(w=>(this._logService.error("Parsing error: ",w),w)),this._parser.registerDcsHandler({intermediates:"$",final:"q"},new y.DcsHandler((w,P)=>this.requestStatusString(w,P)))}_preserveStack(k,D,T,M){this._parseStack.paused=!0,this._parseStack.cursorStartX=k,this._parseStack.cursorStartY=D,this._parseStack.decodedLength=T,this._parseStack.position=M}_logSlowResolvingAsync(k){this._logService.logLevel<=_.LogLevelEnum.WARN&&Promise.race([k,new Promise((D,T)=>setTimeout(()=>T("#SLOW_TIMEOUT"),5e3))]).catch(D=>{if(D!=="#SLOW_TIMEOUT")throw D;console.warn("async parser handler taking longer than 5000 ms")})}_getCurrentLinkId(){return this._curAttrData.extended.urlId}parse(k,D){let T,M=this._activeBuffer.x,N=this._activeBuffer.y,U=0;const q=this._parseStack.paused;if(q){if(T=this._parser.parse(this._parseBuffer,this._parseStack.decodedLength,D))return this._logSlowResolvingAsync(T),T;M=this._parseStack.cursorStartX,N=this._parseStack.cursorStartY,this._parseStack.paused=!1,k.length>L&&(U=this._parseStack.position+L)}if(this._logService.logLevel<=_.LogLevelEnum.DEBUG&&this._logService.debug("parsing data"+(typeof k=="string"?` "${k}"`:` "${Array.prototype.map.call(k,K=>String.fromCharCode(K)).join("")}"`),typeof k=="string"?k.split("").map(K=>K.charCodeAt(0)):k),this._parseBuffer.length<k.length&&this._parseBuffer.length<L&&(this._parseBuffer=new Uint32Array(Math.min(k.length,L))),q||this._dirtyRowTracker.clearRange(),k.length>L)for(let K=U;K<k.length;K+=L){const Q=K+L<k.length?K+L:k.length,w=typeof k=="string"?this._stringDecoder.decode(k.substring(K,Q),this._parseBuffer):this._utf8Decoder.decode(k.subarray(K,Q),this._parseBuffer);if(T=this._parser.parse(this._parseBuffer,w))return this._preserveStack(M,N,w,K),this._logSlowResolvingAsync(T),T}else if(!q){const K=typeof k=="string"?this._stringDecoder.decode(k,this._parseBuffer):this._utf8Decoder.decode(k,this._parseBuffer);if(T=this._parser.parse(this._parseBuffer,K))return this._preserveStack(M,N,K,0),this._logSlowResolvingAsync(T),T}this._activeBuffer.x===M&&this._activeBuffer.y===N||this._onCursorMove.fire(),this._onRequestRefreshRows.fire(this._dirtyRowTracker.start,this._dirtyRowTracker.end)}print(k,D,T){let M,N;const U=this._charsetService.charset,q=this._optionsService.rawOptions.screenReaderMode,K=this._bufferService.cols,Q=this._coreService.decPrivateModes.wraparound,w=this._coreService.modes.insertMode,P=this._curAttrData;let F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._activeBuffer.x&&T-D>0&&F.getWidth(this._activeBuffer.x-1)===2&&F.setCellFromCodePoint(this._activeBuffer.x-1,0,1,P.fg,P.bg,P.extended);for(let H=D;H<T;++H){if(M=k[H],N=this._unicodeService.wcwidth(M),M<127&&U){const V=U[String.fromCharCode(M)];V&&(M=V.charCodeAt(0))}if(q&&this._onA11yChar.fire((0,g.stringFromCodePoint)(M)),this._getCurrentLinkId()&&this._oscLinkService.addLineToLink(this._getCurrentLinkId(),this._activeBuffer.ybase+this._activeBuffer.y),N||!this._activeBuffer.x){if(this._activeBuffer.x+N-1>=K){if(Q){for(;this._activeBuffer.x<K;)F.setCellFromCodePoint(this._activeBuffer.x++,0,1,P.fg,P.bg,P.extended);this._activeBuffer.x=0,this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData(),!0)):(this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!0),F=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y)}else if(this._activeBuffer.x=K-1,N===2)continue}if(w&&(F.insertCells(this._activeBuffer.x,N,this._activeBuffer.getNullCell(P),P),F.getWidth(K-1)===2&&F.setCellFromCodePoint(K-1,i.NULL_CELL_CODE,i.NULL_CELL_WIDTH,P.fg,P.bg,P.extended)),F.setCellFromCodePoint(this._activeBuffer.x++,M,N,P.fg,P.bg,P.extended),N>0)for(;--N;)F.setCellFromCodePoint(this._activeBuffer.x++,0,0,P.fg,P.bg,P.extended)}else F.getWidth(this._activeBuffer.x-1)?F.addCodepointToCell(this._activeBuffer.x-1,M):F.addCodepointToCell(this._activeBuffer.x-2,M)}T-D>0&&(F.loadCell(this._activeBuffer.x-1,this._workCell),this._workCell.getWidth()===2||this._workCell.getCode()>65535?this._parser.precedingCodepoint=0:this._workCell.isCombined()?this._parser.precedingCodepoint=this._workCell.getChars().charCodeAt(0):this._parser.precedingCodepoint=this._workCell.content),this._activeBuffer.x<K&&T-D>0&&F.getWidth(this._activeBuffer.x)===0&&!F.hasContent(this._activeBuffer.x)&&F.setCellFromCodePoint(this._activeBuffer.x,0,1,P.fg,P.bg,P.extended),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}registerCsiHandler(k,D){return k.final!=="t"||k.prefix||k.intermediates?this._parser.registerCsiHandler(k,D):this._parser.registerCsiHandler(k,T=>!I(T.params[0],this._optionsService.rawOptions.windowOptions)||D(T))}registerDcsHandler(k,D){return this._parser.registerDcsHandler(k,new y.DcsHandler(D))}registerEscHandler(k,D){return this._parser.registerEscHandler(k,D)}registerOscHandler(k,D){return this._parser.registerOscHandler(k,new S.OscHandler(D))}bell(){return this._onRequestBell.fire(),!0}lineFeed(){return this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._optionsService.rawOptions.convertEol&&(this._activeBuffer.x=0),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows?this._activeBuffer.y=this._bufferService.rows-1:this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.x>=this._bufferService.cols&&this._activeBuffer.x--,this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._onLineFeed.fire(),!0}carriageReturn(){return this._activeBuffer.x=0,!0}backspace(){var k;if(!this._coreService.decPrivateModes.reverseWraparound)return this._restrictCursor(),this._activeBuffer.x>0&&this._activeBuffer.x--,!0;if(this._restrictCursor(this._bufferService.cols),this._activeBuffer.x>0)this._activeBuffer.x--;else if(this._activeBuffer.x===0&&this._activeBuffer.y>this._activeBuffer.scrollTop&&this._activeBuffer.y<=this._activeBuffer.scrollBottom&&(!((k=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y))===null||k===void 0)&&k.isWrapped)){this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y).isWrapped=!1,this._activeBuffer.y--,this._activeBuffer.x=this._bufferService.cols-1;const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);D.hasWidth(this._activeBuffer.x)&&!D.hasContent(this._activeBuffer.x)&&this._activeBuffer.x--}return this._restrictCursor(),!0}tab(){if(this._activeBuffer.x>=this._bufferService.cols)return!0;const k=this._activeBuffer.x;return this._activeBuffer.x=this._activeBuffer.nextStop(),this._optionsService.rawOptions.screenReaderMode&&this._onA11yTab.fire(this._activeBuffer.x-k),!0}shiftOut(){return this._charsetService.setgLevel(1),!0}shiftIn(){return this._charsetService.setgLevel(0),!0}_restrictCursor(k=this._bufferService.cols-1){this._activeBuffer.x=Math.min(k,Math.max(0,this._activeBuffer.x)),this._activeBuffer.y=this._coreService.decPrivateModes.origin?Math.min(this._activeBuffer.scrollBottom,Math.max(this._activeBuffer.scrollTop,this._activeBuffer.y)):Math.min(this._bufferService.rows-1,Math.max(0,this._activeBuffer.y)),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_setCursor(k,D){this._dirtyRowTracker.markDirty(this._activeBuffer.y),this._coreService.decPrivateModes.origin?(this._activeBuffer.x=k,this._activeBuffer.y=this._activeBuffer.scrollTop+D):(this._activeBuffer.x=k,this._activeBuffer.y=D),this._restrictCursor(),this._dirtyRowTracker.markDirty(this._activeBuffer.y)}_moveCursor(k,D){this._restrictCursor(),this._setCursor(this._activeBuffer.x+k,this._activeBuffer.y+D)}cursorUp(k){const D=this._activeBuffer.y-this._activeBuffer.scrollTop;return D>=0?this._moveCursor(0,-Math.min(D,k.params[0]||1)):this._moveCursor(0,-(k.params[0]||1)),!0}cursorDown(k){const D=this._activeBuffer.scrollBottom-this._activeBuffer.y;return D>=0?this._moveCursor(0,Math.min(D,k.params[0]||1)):this._moveCursor(0,k.params[0]||1),!0}cursorForward(k){return this._moveCursor(k.params[0]||1,0),!0}cursorBackward(k){return this._moveCursor(-(k.params[0]||1),0),!0}cursorNextLine(k){return this.cursorDown(k),this._activeBuffer.x=0,!0}cursorPrecedingLine(k){return this.cursorUp(k),this._activeBuffer.x=0,!0}cursorCharAbsolute(k){return this._setCursor((k.params[0]||1)-1,this._activeBuffer.y),!0}cursorPosition(k){return this._setCursor(k.length>=2?(k.params[1]||1)-1:0,(k.params[0]||1)-1),!0}charPosAbsolute(k){return this._setCursor((k.params[0]||1)-1,this._activeBuffer.y),!0}hPositionRelative(k){return this._moveCursor(k.params[0]||1,0),!0}linePosAbsolute(k){return this._setCursor(this._activeBuffer.x,(k.params[0]||1)-1),!0}vPositionRelative(k){return this._moveCursor(0,k.params[0]||1),!0}hVPosition(k){return this.cursorPosition(k),!0}tabClear(k){const D=k.params[0];return D===0?delete this._activeBuffer.tabs[this._activeBuffer.x]:D===3&&(this._activeBuffer.tabs={}),!0}cursorForwardTab(k){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let D=k.params[0]||1;for(;D--;)this._activeBuffer.x=this._activeBuffer.nextStop();return!0}cursorBackwardTab(k){if(this._activeBuffer.x>=this._bufferService.cols)return!0;let D=k.params[0]||1;for(;D--;)this._activeBuffer.x=this._activeBuffer.prevStop();return!0}selectProtected(k){const D=k.params[0];return D===1&&(this._curAttrData.bg|=536870912),D!==2&&D!==0||(this._curAttrData.bg&=-536870913),!0}_eraseInBufferLine(k,D,T,M=!1,N=!1){const U=this._activeBuffer.lines.get(this._activeBuffer.ybase+k);U.replaceCells(D,T,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData(),N),M&&(U.isWrapped=!1)}_resetBufferLine(k,D=!1){const T=this._activeBuffer.lines.get(this._activeBuffer.ybase+k);T&&(T.fill(this._activeBuffer.getNullCell(this._eraseAttrData()),D),this._bufferService.buffer.clearMarkers(this._activeBuffer.ybase+k),T.isWrapped=!1)}eraseInDisplay(k,D=!1){let T;switch(this._restrictCursor(this._bufferService.cols),k.params[0]){case 0:for(T=this._activeBuffer.y,this._dirtyRowTracker.markDirty(T),this._eraseInBufferLine(T++,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,D);T<this._bufferService.rows;T++)this._resetBufferLine(T,D);this._dirtyRowTracker.markDirty(T);break;case 1:for(T=this._activeBuffer.y,this._dirtyRowTracker.markDirty(T),this._eraseInBufferLine(T,0,this._activeBuffer.x+1,!0,D),this._activeBuffer.x+1>=this._bufferService.cols&&(this._activeBuffer.lines.get(T+1).isWrapped=!1);T--;)this._resetBufferLine(T,D);this._dirtyRowTracker.markDirty(0);break;case 2:for(T=this._bufferService.rows,this._dirtyRowTracker.markDirty(T-1);T--;)this._resetBufferLine(T,D);this._dirtyRowTracker.markDirty(0);break;case 3:const M=this._activeBuffer.lines.length-this._bufferService.rows;M>0&&(this._activeBuffer.lines.trimStart(M),this._activeBuffer.ybase=Math.max(this._activeBuffer.ybase-M,0),this._activeBuffer.ydisp=Math.max(this._activeBuffer.ydisp-M,0),this._onScroll.fire(0))}return!0}eraseInLine(k,D=!1){switch(this._restrictCursor(this._bufferService.cols),k.params[0]){case 0:this._eraseInBufferLine(this._activeBuffer.y,this._activeBuffer.x,this._bufferService.cols,this._activeBuffer.x===0,D);break;case 1:this._eraseInBufferLine(this._activeBuffer.y,0,this._activeBuffer.x+1,!1,D);break;case 2:this._eraseInBufferLine(this._activeBuffer.y,0,this._bufferService.cols,!0,D)}return this._dirtyRowTracker.markDirty(this._activeBuffer.y),!0}insertLines(k){this._restrictCursor();let D=k.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const T=this._activeBuffer.ybase+this._activeBuffer.y,M=this._bufferService.rows-1-this._activeBuffer.scrollBottom,N=this._bufferService.rows-1+this._activeBuffer.ybase-M+1;for(;D--;)this._activeBuffer.lines.splice(N-1,1),this._activeBuffer.lines.splice(T,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}deleteLines(k){this._restrictCursor();let D=k.params[0]||1;if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const T=this._activeBuffer.ybase+this._activeBuffer.y;let M;for(M=this._bufferService.rows-1-this._activeBuffer.scrollBottom,M=this._bufferService.rows-1+this._activeBuffer.ybase-M;D--;)this._activeBuffer.lines.splice(T,1),this._activeBuffer.lines.splice(M,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.y,this._activeBuffer.scrollBottom),this._activeBuffer.x=0,!0}insertChars(k){this._restrictCursor();const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return D&&(D.insertCells(this._activeBuffer.x,k.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}deleteChars(k){this._restrictCursor();const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return D&&(D.deleteCells(this._activeBuffer.x,k.params[0]||1,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}scrollUp(k){let D=k.params[0]||1;for(;D--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,0,this._activeBuffer.getBlankLine(this._eraseAttrData()));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollDown(k){let D=k.params[0]||1;for(;D--;)this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollBottom,1),this._activeBuffer.lines.splice(this._activeBuffer.ybase+this._activeBuffer.scrollTop,0,this._activeBuffer.getBlankLine(t.DEFAULT_ATTR_DATA));return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollLeft(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.deleteCells(0,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}scrollRight(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.insertCells(0,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}insertColumns(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.insertCells(this._activeBuffer.x,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}deleteColumns(k){if(this._activeBuffer.y>this._activeBuffer.scrollBottom||this._activeBuffer.y<this._activeBuffer.scrollTop)return!0;const D=k.params[0]||1;for(let T=this._activeBuffer.scrollTop;T<=this._activeBuffer.scrollBottom;++T){const M=this._activeBuffer.lines.get(this._activeBuffer.ybase+T);M.deleteCells(this._activeBuffer.x,D,this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),M.isWrapped=!1}return this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom),!0}eraseChars(k){this._restrictCursor();const D=this._activeBuffer.lines.get(this._activeBuffer.ybase+this._activeBuffer.y);return D&&(D.replaceCells(this._activeBuffer.x,this._activeBuffer.x+(k.params[0]||1),this._activeBuffer.getNullCell(this._eraseAttrData()),this._eraseAttrData()),this._dirtyRowTracker.markDirty(this._activeBuffer.y)),!0}repeatPrecedingCharacter(k){if(!this._parser.precedingCodepoint)return!0;const D=k.params[0]||1,T=new Uint32Array(D);for(let M=0;M<D;++M)T[M]=this._parser.precedingCodepoint;return this.print(T,0,T.length),!0}sendDeviceAttributesPrimary(k){return k.params[0]>0||(this._is("xterm")||this._is("rxvt-unicode")||this._is("screen")?this._coreService.triggerDataEvent(a.C0.ESC+"[?1;2c"):this._is("linux")&&this._coreService.triggerDataEvent(a.C0.ESC+"[?6c")),!0}sendDeviceAttributesSecondary(k){return k.params[0]>0||(this._is("xterm")?this._coreService.triggerDataEvent(a.C0.ESC+"[>0;276;0c"):this._is("rxvt-unicode")?this._coreService.triggerDataEvent(a.C0.ESC+"[>85;95;0c"):this._is("linux")?this._coreService.triggerDataEvent(k.params[0]+"c"):this._is("screen")&&this._coreService.triggerDataEvent(a.C0.ESC+"[>83;40003;0c")),!0}_is(k){return(this._optionsService.rawOptions.termName+"").indexOf(k)===0}setMode(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 4:this._coreService.modes.insertMode=!0;break;case 20:this._optionsService.options.convertEol=!0}return!0}setModePrivate(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!0;break;case 2:this._charsetService.setgCharset(0,f.DEFAULT_CHARSET),this._charsetService.setgCharset(1,f.DEFAULT_CHARSET),this._charsetService.setgCharset(2,f.DEFAULT_CHARSET),this._charsetService.setgCharset(3,f.DEFAULT_CHARSET);break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(132,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!0,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!0;break;case 12:this._optionsService.options.cursorBlink=!0;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!0;break;case 66:this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire();break;case 9:this._coreMouseService.activeProtocol="X10";break;case 1e3:this._coreMouseService.activeProtocol="VT200";break;case 1002:this._coreMouseService.activeProtocol="DRAG";break;case 1003:this._coreMouseService.activeProtocol="ANY";break;case 1004:this._coreService.decPrivateModes.sendFocus=!0,this._onRequestSendFocus.fire();break;case 1005:this._logService.debug("DECSET 1005 not supported (see #2507)");break;case 1006:this._coreMouseService.activeEncoding="SGR";break;case 1015:this._logService.debug("DECSET 1015 not supported (see #2507)");break;case 1016:this._coreMouseService.activeEncoding="SGR_PIXELS";break;case 25:this._coreService.isCursorHidden=!1;break;case 1048:this.saveCursor();break;case 1049:this.saveCursor();case 47:case 1047:this._bufferService.buffers.activateAltBuffer(this._eraseAttrData()),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!0}return!0}resetMode(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 4:this._coreService.modes.insertMode=!1;break;case 20:this._optionsService.options.convertEol=!1}return!0}resetModePrivate(k){for(let D=0;D<k.length;D++)switch(k.params[D]){case 1:this._coreService.decPrivateModes.applicationCursorKeys=!1;break;case 3:this._optionsService.rawOptions.windowOptions.setWinLines&&(this._bufferService.resize(80,this._bufferService.rows),this._onRequestReset.fire());break;case 6:this._coreService.decPrivateModes.origin=!1,this._setCursor(0,0);break;case 7:this._coreService.decPrivateModes.wraparound=!1;break;case 12:this._optionsService.options.cursorBlink=!1;break;case 45:this._coreService.decPrivateModes.reverseWraparound=!1;break;case 66:this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire();break;case 9:case 1e3:case 1002:case 1003:this._coreMouseService.activeProtocol="NONE";break;case 1004:this._coreService.decPrivateModes.sendFocus=!1;break;case 1005:this._logService.debug("DECRST 1005 not supported (see #2507)");break;case 1006:case 1016:this._coreMouseService.activeEncoding="DEFAULT";break;case 1015:this._logService.debug("DECRST 1015 not supported (see #2507)");break;case 25:this._coreService.isCursorHidden=!0;break;case 1048:this.restoreCursor();break;case 1049:case 47:case 1047:this._bufferService.buffers.activateNormalBuffer(),k.params[D]===1049&&this.restoreCursor(),this._coreService.isCursorInitialized=!0,this._onRequestRefreshRows.fire(0,this._bufferService.rows-1),this._onRequestSyncScrollBar.fire();break;case 2004:this._coreService.decPrivateModes.bracketedPasteMode=!1}return!0}requestMode(k,D){const T=this._coreService.decPrivateModes,{activeProtocol:M,activeEncoding:N}=this._coreMouseService,U=this._coreService,{buffers:q,cols:K}=this._bufferService,{active:Q,alt:w}=q,P=this._optionsService.rawOptions,F=Y=>Y?1:2,H=k.params[0];return V=H,j=D?H===2?4:H===4?F(U.modes.insertMode):H===12?3:H===20?F(P.convertEol):0:H===1?F(T.applicationCursorKeys):H===3?P.windowOptions.setWinLines?K===80?2:K===132?1:0:0:H===6?F(T.origin):H===7?F(T.wraparound):H===8?3:H===9?F(M==="X10"):H===12?F(P.cursorBlink):H===25?F(!U.isCursorHidden):H===45?F(T.reverseWraparound):H===66?F(T.applicationKeypad):H===67?4:H===1e3?F(M==="VT200"):H===1002?F(M==="DRAG"):H===1003?F(M==="ANY"):H===1004?F(T.sendFocus):H===1005?4:H===1006?F(N==="SGR"):H===1015?4:H===1016?F(N==="SGR_PIXELS"):H===1048?1:H===47||H===1047||H===1049?F(Q===w):H===2004?F(T.bracketedPasteMode):0,U.triggerDataEvent(`${a.C0.ESC}[${D?"":"?"}${V};${j}$y`),!0;var V,j}_updateAttrColor(k,D,T,M,N){return D===2?(k|=50331648,k&=-16777216,k|=h.AttributeData.fromColorRGB([T,M,N])):D===5&&(k&=-50331904,k|=33554432|255&T),k}_extractColor(k,D,T){const M=[0,0,-1,0,0,0];let N=0,U=0;do{if(M[U+N]=k.params[D+U],k.hasSubParams(D+U)){const q=k.getSubParams(D+U);let K=0;do M[1]===5&&(N=1),M[U+K+1+N]=q[K];while(++K<q.length&&K+U+1+N<M.length);break}if(M[1]===5&&U+N>=2||M[1]===2&&U+N>=5)break;M[1]&&(N=1)}while(++U+D<k.length&&U+N<M.length);for(let q=2;q<M.length;++q)M[q]===-1&&(M[q]=0);switch(M[0]){case 38:T.fg=this._updateAttrColor(T.fg,M[1],M[3],M[4],M[5]);break;case 48:T.bg=this._updateAttrColor(T.bg,M[1],M[3],M[4],M[5]);break;case 58:T.extended=T.extended.clone(),T.extended.underlineColor=this._updateAttrColor(T.extended.underlineColor,M[1],M[3],M[4],M[5])}return U}_processUnderline(k,D){D.extended=D.extended.clone(),(!~k||k>5)&&(k=1),D.extended.underlineStyle=k,D.fg|=268435456,k===0&&(D.fg&=-268435457),D.updateExtended()}_processSGR0(k){k.fg=t.DEFAULT_ATTR_DATA.fg,k.bg=t.DEFAULT_ATTR_DATA.bg,k.extended=k.extended.clone(),k.extended.underlineStyle=0,k.extended.underlineColor&=-67108864,k.updateExtended()}charAttributes(k){if(k.length===1&&k.params[0]===0)return this._processSGR0(this._curAttrData),!0;const D=k.length;let T;const M=this._curAttrData;for(let N=0;N<D;N++)T=k.params[N],T>=30&&T<=37?(M.fg&=-50331904,M.fg|=16777216|T-30):T>=40&&T<=47?(M.bg&=-50331904,M.bg|=16777216|T-40):T>=90&&T<=97?(M.fg&=-50331904,M.fg|=16777224|T-90):T>=100&&T<=107?(M.bg&=-50331904,M.bg|=16777224|T-100):T===0?this._processSGR0(M):T===1?M.fg|=134217728:T===3?M.bg|=67108864:T===4?(M.fg|=268435456,this._processUnderline(k.hasSubParams(N)?k.getSubParams(N)[0]:1,M)):T===5?M.fg|=536870912:T===7?M.fg|=67108864:T===8?M.fg|=1073741824:T===9?M.fg|=2147483648:T===2?M.bg|=134217728:T===21?this._processUnderline(2,M):T===22?(M.fg&=-134217729,M.bg&=-134217729):T===23?M.bg&=-67108865:T===24?(M.fg&=-268435457,this._processUnderline(0,M)):T===25?M.fg&=-536870913:T===27?M.fg&=-67108865:T===28?M.fg&=-1073741825:T===29?M.fg&=2147483647:T===39?(M.fg&=-67108864,M.fg|=16777215&t.DEFAULT_ATTR_DATA.fg):T===49?(M.bg&=-67108864,M.bg|=16777215&t.DEFAULT_ATTR_DATA.bg):T===38||T===48||T===58?N+=this._extractColor(k,N,M):T===53?M.bg|=1073741824:T===55?M.bg&=-1073741825:T===59?(M.extended=M.extended.clone(),M.extended.underlineColor=-1,M.updateExtended()):T===100?(M.fg&=-67108864,M.fg|=16777215&t.DEFAULT_ATTR_DATA.fg,M.bg&=-67108864,M.bg|=16777215&t.DEFAULT_ATTR_DATA.bg):this._logService.debug("Unknown SGR attribute: %d.",T);return!0}deviceStatus(k){switch(k.params[0]){case 5:this._coreService.triggerDataEvent(`${a.C0.ESC}[0n`);break;case 6:const D=this._activeBuffer.y+1,T=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${a.C0.ESC}[${D};${T}R`)}return!0}deviceStatusPrivate(k){if(k.params[0]===6){const D=this._activeBuffer.y+1,T=this._activeBuffer.x+1;this._coreService.triggerDataEvent(`${a.C0.ESC}[?${D};${T}R`)}return!0}softReset(k){return this._coreService.isCursorHidden=!1,this._onRequestSyncScrollBar.fire(),this._activeBuffer.scrollTop=0,this._activeBuffer.scrollBottom=this._bufferService.rows-1,this._curAttrData=t.DEFAULT_ATTR_DATA.clone(),this._coreService.reset(),this._charsetService.reset(),this._activeBuffer.savedX=0,this._activeBuffer.savedY=this._activeBuffer.ybase,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,this._coreService.decPrivateModes.origin=!1,!0}setCursorStyle(k){const D=k.params[0]||1;switch(D){case 1:case 2:this._optionsService.options.cursorStyle="block";break;case 3:case 4:this._optionsService.options.cursorStyle="underline";break;case 5:case 6:this._optionsService.options.cursorStyle="bar"}const T=D%2==1;return this._optionsService.options.cursorBlink=T,!0}setScrollRegion(k){const D=k.params[0]||1;let T;return(k.length<2||(T=k.params[1])>this._bufferService.rows||T===0)&&(T=this._bufferService.rows),T>D&&(this._activeBuffer.scrollTop=D-1,this._activeBuffer.scrollBottom=T-1,this._setCursor(0,0)),!0}windowOptions(k){if(!I(k.params[0],this._optionsService.rawOptions.windowOptions))return!0;const D=k.length>1?k.params[1]:0;switch(k.params[0]){case 14:D!==2&&this._onRequestWindowsOptionsReport.fire(R.GET_WIN_SIZE_PIXELS);break;case 16:this._onRequestWindowsOptionsReport.fire(R.GET_CELL_SIZE_PIXELS);break;case 18:this._bufferService&&this._coreService.triggerDataEvent(`${a.C0.ESC}[8;${this._bufferService.rows};${this._bufferService.cols}t`);break;case 22:D!==0&&D!==2||(this._windowTitleStack.push(this._windowTitle),this._windowTitleStack.length>10&&this._windowTitleStack.shift()),D!==0&&D!==1||(this._iconNameStack.push(this._iconName),this._iconNameStack.length>10&&this._iconNameStack.shift());break;case 23:D!==0&&D!==2||this._windowTitleStack.length&&this.setTitle(this._windowTitleStack.pop()),D!==0&&D!==1||this._iconNameStack.length&&this.setIconName(this._iconNameStack.pop())}return!0}saveCursor(k){return this._activeBuffer.savedX=this._activeBuffer.x,this._activeBuffer.savedY=this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.savedCurAttrData.fg=this._curAttrData.fg,this._activeBuffer.savedCurAttrData.bg=this._curAttrData.bg,this._activeBuffer.savedCharset=this._charsetService.charset,!0}restoreCursor(k){return this._activeBuffer.x=this._activeBuffer.savedX||0,this._activeBuffer.y=Math.max(this._activeBuffer.savedY-this._activeBuffer.ybase,0),this._curAttrData.fg=this._activeBuffer.savedCurAttrData.fg,this._curAttrData.bg=this._activeBuffer.savedCurAttrData.bg,this._charsetService.charset=this._savedCharset,this._activeBuffer.savedCharset&&(this._charsetService.charset=this._activeBuffer.savedCharset),this._restrictCursor(),!0}setTitle(k){return this._windowTitle=k,this._onTitleChange.fire(k),!0}setIconName(k){return this._iconName=k,!0}setOrReportIndexedColor(k){const D=[],T=k.split(";");for(;T.length>1;){const M=T.shift(),N=T.shift();if(/^\d+$/.exec(M)){const U=parseInt(M);if(W(U))if(N==="?")D.push({type:0,index:U});else{const q=(0,u.parseColor)(N);q&&D.push({type:1,index:U,color:q})}}}return D.length&&this._onColor.fire(D),!0}setHyperlink(k){const D=k.split(";");return!(D.length<2)&&(D[1]?this._createHyperlink(D[0],D[1]):!D[0]&&this._finishHyperlink())}_createHyperlink(k,D){this._getCurrentLinkId()&&this._finishHyperlink();const T=k.split(":");let M;const N=T.findIndex(U=>U.startsWith("id="));return N!==-1&&(M=T[N].slice(3)||void 0),this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=this._oscLinkService.registerLink({id:M,uri:D}),this._curAttrData.updateExtended(),!0}_finishHyperlink(){return this._curAttrData.extended=this._curAttrData.extended.clone(),this._curAttrData.extended.urlId=0,this._curAttrData.updateExtended(),!0}_setOrReportSpecialColor(k,D){const T=k.split(";");for(let M=0;M<T.length&&!(D>=this._specialColors.length);++M,++D)if(T[M]==="?")this._onColor.fire([{type:0,index:this._specialColors[D]}]);else{const N=(0,u.parseColor)(T[M]);N&&this._onColor.fire([{type:1,index:this._specialColors[D],color:N}])}return!0}setOrReportFgColor(k){return this._setOrReportSpecialColor(k,0)}setOrReportBgColor(k){return this._setOrReportSpecialColor(k,1)}setOrReportCursorColor(k){return this._setOrReportSpecialColor(k,2)}restoreIndexedColor(k){if(!k)return this._onColor.fire([{type:2}]),!0;const D=[],T=k.split(";");for(let M=0;M<T.length;++M)if(/^\d+$/.exec(T[M])){const N=parseInt(T[M]);W(N)&&D.push({type:2,index:N})}return D.length&&this._onColor.fire(D),!0}restoreFgColor(k){return this._onColor.fire([{type:2,index:256}]),!0}restoreBgColor(k){return this._onColor.fire([{type:2,index:257}]),!0}restoreCursorColor(k){return this._onColor.fire([{type:2,index:258}]),!0}nextLine(){return this._activeBuffer.x=0,this.index(),!0}keypadApplicationMode(){return this._logService.debug("Serial port requested application keypad."),this._coreService.decPrivateModes.applicationKeypad=!0,this._onRequestSyncScrollBar.fire(),!0}keypadNumericMode(){return this._logService.debug("Switching back to normal keypad."),this._coreService.decPrivateModes.applicationKeypad=!1,this._onRequestSyncScrollBar.fire(),!0}selectDefaultCharset(){return this._charsetService.setgLevel(0),this._charsetService.setgCharset(0,f.DEFAULT_CHARSET),!0}selectCharset(k){return k.length!==2?(this.selectDefaultCharset(),!0):(k[0]==="/"||this._charsetService.setgCharset(C[k[0]],f.CHARSETS[k[1]]||f.DEFAULT_CHARSET),!0)}index(){return this._restrictCursor(),this._activeBuffer.y++,this._activeBuffer.y===this._activeBuffer.scrollBottom+1?(this._activeBuffer.y--,this._bufferService.scroll(this._eraseAttrData())):this._activeBuffer.y>=this._bufferService.rows&&(this._activeBuffer.y=this._bufferService.rows-1),this._restrictCursor(),!0}tabSet(){return this._activeBuffer.tabs[this._activeBuffer.x]=!0,!0}reverseIndex(){if(this._restrictCursor(),this._activeBuffer.y===this._activeBuffer.scrollTop){const k=this._activeBuffer.scrollBottom-this._activeBuffer.scrollTop;this._activeBuffer.lines.shiftElements(this._activeBuffer.ybase+this._activeBuffer.y,k,1),this._activeBuffer.lines.set(this._activeBuffer.ybase+this._activeBuffer.y,this._activeBuffer.getBlankLine(this._eraseAttrData())),this._dirtyRowTracker.markRangeDirty(this._activeBuffer.scrollTop,this._activeBuffer.scrollBottom)}else this._activeBuffer.y--,this._restrictCursor();return!0}fullReset(){return this._parser.reset(),this._onRequestReset.fire(),!0}reset(){this._curAttrData=t.DEFAULT_ATTR_DATA.clone(),this._eraseAttrDataInternal=t.DEFAULT_ATTR_DATA.clone()}_eraseAttrData(){return this._eraseAttrDataInternal.bg&=-67108864,this._eraseAttrDataInternal.bg|=67108863&this._curAttrData.bg,this._eraseAttrDataInternal}setgLevel(k){return this._charsetService.setgLevel(k),!0}screenAlignmentPattern(){const k=new r.CellData;k.content=4194373,k.fg=this._curAttrData.fg,k.bg=this._curAttrData.bg,this._setCursor(0,0);for(let D=0;D<this._bufferService.rows;++D){const T=this._activeBuffer.ybase+this._activeBuffer.y+D,M=this._activeBuffer.lines.get(T);M&&(M.fill(k),M.isWrapped=!1)}return this._dirtyRowTracker.markAllDirty(),this._setCursor(0,0),!0}requestStatusString(k,D){const T=this._bufferService.buffer,M=this._optionsService.rawOptions;return(N=>(this._coreService.triggerDataEvent(`${a.C0.ESC}${N}${a.C0.ESC}\\`),!0))(k==='"q'?`P1$r${this._curAttrData.isProtected()?1:0}"q`:k==='"p'?'P1$r61;1"p':k==="r"?`P1$r${T.scrollTop+1};${T.scrollBottom+1}r`:k==="m"?"P1$r0m":k===" q"?`P1$r${{block:2,underline:4,bar:6}[M.cursorStyle]-(M.cursorBlink?1:0)} q`:"P0$r")}markRangeDirty(k,D){this._dirtyRowTracker.markRangeDirty(k,D)}}s.InputHandler=O;let $=class{constructor(z){this._bufferService=z,this.clearRange()}clearRange(){this.start=this._bufferService.buffer.y,this.end=this._bufferService.buffer.y}markDirty(z){z<this.start?this.start=z:z>this.end&&(this.end=z)}markRangeDirty(z,k){z>k&&(B=z,z=k,k=B),z<this.start&&(this.start=z),k>this.end&&(this.end=k)}markAllDirty(){this.markRangeDirty(0,this._bufferService.rows-1)}};function W(z){return 0<=z&&z<256}$=l([c(0,_.IBufferService)],$)},844:(x,s)=>{function o(l){for(const c of l)c.dispose();l.length=0}Object.defineProperty(s,"__esModule",{value:!0}),s.getDisposeArrayDisposable=s.disposeArray=s.toDisposable=s.MutableDisposable=s.Disposable=void 0,s.Disposable=class{constructor(){this._disposables=[],this._isDisposed=!1}dispose(){this._isDisposed=!0;for(const l of this._disposables)l.dispose();this._disposables.length=0}register(l){return this._disposables.push(l),l}unregister(l){const c=this._disposables.indexOf(l);c!==-1&&this._disposables.splice(c,1)}},s.MutableDisposable=class{constructor(){this._isDisposed=!1}get value(){return this._isDisposed?void 0:this._value}set value(l){var c;this._isDisposed||l===this._value||((c=this._value)===null||c===void 0||c.dispose(),this._value=l)}clear(){this.value=void 0}dispose(){var l;this._isDisposed=!0,(l=this._value)===null||l===void 0||l.dispose(),this._value=void 0}},s.toDisposable=function(l){return{dispose:l}},s.disposeArray=o,s.getDisposeArrayDisposable=function(l){return{dispose:()=>o(l)}}},1505:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.FourKeyMap=s.TwoKeyMap=void 0;class o{constructor(){this._data={}}set(c,a,f){this._data[c]||(this._data[c]={}),this._data[c][a]=f}get(c,a){return this._data[c]?this._data[c][a]:void 0}clear(){this._data={}}}s.TwoKeyMap=o,s.FourKeyMap=class{constructor(){this._data=new o}set(l,c,a,f,p){this._data.get(l,c)||this._data.set(l,c,new o),this._data.get(l,c).set(a,f,p)}get(l,c,a,f){var p;return(p=this._data.get(l,c))===null||p===void 0?void 0:p.get(a,f)}clear(){this._data.clear()}}},6114:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.isChromeOS=s.isLinux=s.isWindows=s.isIphone=s.isIpad=s.isMac=s.getSafariVersion=s.isSafari=s.isLegacyEdge=s.isFirefox=s.isNode=void 0,s.isNode=typeof navigator>"u";const o=s.isNode?"node":navigator.userAgent,l=s.isNode?"node":navigator.platform;s.isFirefox=o.includes("Firefox"),s.isLegacyEdge=o.includes("Edge"),s.isSafari=/^((?!chrome|android).)*safari/i.test(o),s.getSafariVersion=function(){if(!s.isSafari)return 0;const c=o.match(/Version\/(\d+)/);return c===null||c.length<2?0:parseInt(c[1])},s.isMac=["Macintosh","MacIntel","MacPPC","Mac68K"].includes(l),s.isIpad=l==="iPad",s.isIphone=l==="iPhone",s.isWindows=["Windows","Win16","Win32","WinCE"].includes(l),s.isLinux=l.indexOf("Linux")>=0,s.isChromeOS=/\bCrOS\b/.test(o)},6106:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.SortedList=void 0;let o=0;s.SortedList=class{constructor(l){this._getKey=l,this._array=[]}clear(){this._array.length=0}insert(l){this._array.length!==0?(o=this._search(this._getKey(l)),this._array.splice(o,0,l)):this._array.push(l)}delete(l){if(this._array.length===0)return!1;const c=this._getKey(l);if(c===void 0||(o=this._search(c),o===-1)||this._getKey(this._array[o])!==c)return!1;do if(this._array[o]===l)return this._array.splice(o,1),!0;while(++o<this._array.length&&this._getKey(this._array[o])===c);return!1}*getKeyIterator(l){if(this._array.length!==0&&(o=this._search(l),!(o<0||o>=this._array.length)&&this._getKey(this._array[o])===l))do yield this._array[o];while(++o<this._array.length&&this._getKey(this._array[o])===l)}forEachByKey(l,c){if(this._array.length!==0&&(o=this._search(l),!(o<0||o>=this._array.length)&&this._getKey(this._array[o])===l))do c(this._array[o]);while(++o<this._array.length&&this._getKey(this._array[o])===l)}values(){return[...this._array].values()}_search(l){let c=0,a=this._array.length-1;for(;a>=c;){let f=c+a>>1;const p=this._getKey(this._array[f]);if(p>l)a=f-1;else{if(!(p<l)){for(;f>0&&this._getKey(this._array[f-1])===l;)f--;return f}c=f+1}}return c}}},7226:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DebouncedIdleTask=s.IdleTaskQueue=s.PriorityTaskQueue=void 0;const l=o(6114);class c{constructor(){this._tasks=[],this._i=0}enqueue(p){this._tasks.push(p),this._start()}flush(){for(;this._i<this._tasks.length;)this._tasks[this._i]()||this._i++;this.clear()}clear(){this._idleCallback&&(this._cancelCallback(this._idleCallback),this._idleCallback=void 0),this._i=0,this._tasks.length=0}_start(){this._idleCallback||(this._idleCallback=this._requestCallback(this._process.bind(this)))}_process(p){this._idleCallback=void 0;let v=0,g=0,t=p.timeRemaining(),n=0;for(;this._i<this._tasks.length;){if(v=Date.now(),this._tasks[this._i]()||this._i++,v=Math.max(1,Date.now()-v),g=Math.max(v,g),n=p.timeRemaining(),1.5*g>n)return t-v<-20&&console.warn(`task queue exceeded allotted deadline by ${Math.abs(Math.round(t-v))}ms`),void this._start();t=n}this.clear()}}class a extends c{_requestCallback(p){return setTimeout(()=>p(this._createDeadline(16)))}_cancelCallback(p){clearTimeout(p)}_createDeadline(p){const v=Date.now()+p;return{timeRemaining:()=>Math.max(0,v-Date.now())}}}s.PriorityTaskQueue=a,s.IdleTaskQueue=!l.isNode&&"requestIdleCallback"in window?class extends c{_requestCallback(f){return requestIdleCallback(f)}_cancelCallback(f){cancelIdleCallback(f)}}:a,s.DebouncedIdleTask=class{constructor(){this._queue=new s.IdleTaskQueue}set(f){this._queue.clear(),this._queue.enqueue(f)}flush(){this._queue.flush()}}},9282:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.updateWindowsModeWrappedState=void 0;const l=o(643);s.updateWindowsModeWrappedState=function(c){const a=c.buffer.lines.get(c.buffer.ybase+c.buffer.y-1),f=a==null?void 0:a.get(c.cols-1),p=c.buffer.lines.get(c.buffer.ybase+c.buffer.y);p&&f&&(p.isWrapped=f[l.CHAR_DATA_CODE_INDEX]!==l.NULL_CELL_CODE&&f[l.CHAR_DATA_CODE_INDEX]!==l.WHITESPACE_CELL_CODE)}},3734:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ExtendedAttrs=s.AttributeData=void 0;class o{constructor(){this.fg=0,this.bg=0,this.extended=new l}static toColorRGB(a){return[a>>>16&255,a>>>8&255,255&a]}static fromColorRGB(a){return(255&a[0])<<16|(255&a[1])<<8|255&a[2]}clone(){const a=new o;return a.fg=this.fg,a.bg=this.bg,a.extended=this.extended.clone(),a}isInverse(){return 67108864&this.fg}isBold(){return 134217728&this.fg}isUnderline(){return this.hasExtendedAttrs()&&this.extended.underlineStyle!==0?1:268435456&this.fg}isBlink(){return 536870912&this.fg}isInvisible(){return 1073741824&this.fg}isItalic(){return 67108864&this.bg}isDim(){return 134217728&this.bg}isStrikethrough(){return 2147483648&this.fg}isProtected(){return 536870912&this.bg}isOverline(){return 1073741824&this.bg}getFgColorMode(){return 50331648&this.fg}getBgColorMode(){return 50331648&this.bg}isFgRGB(){return(50331648&this.fg)==50331648}isBgRGB(){return(50331648&this.bg)==50331648}isFgPalette(){return(50331648&this.fg)==16777216||(50331648&this.fg)==33554432}isBgPalette(){return(50331648&this.bg)==16777216||(50331648&this.bg)==33554432}isFgDefault(){return(50331648&this.fg)==0}isBgDefault(){return(50331648&this.bg)==0}isAttributeDefault(){return this.fg===0&&this.bg===0}getFgColor(){switch(50331648&this.fg){case 16777216:case 33554432:return 255&this.fg;case 50331648:return 16777215&this.fg;default:return-1}}getBgColor(){switch(50331648&this.bg){case 16777216:case 33554432:return 255&this.bg;case 50331648:return 16777215&this.bg;default:return-1}}hasExtendedAttrs(){return 268435456&this.bg}updateExtended(){this.extended.isEmpty()?this.bg&=-268435457:this.bg|=268435456}getUnderlineColor(){if(268435456&this.bg&&~this.extended.underlineColor)switch(50331648&this.extended.underlineColor){case 16777216:case 33554432:return 255&this.extended.underlineColor;case 50331648:return 16777215&this.extended.underlineColor;default:return this.getFgColor()}return this.getFgColor()}getUnderlineColorMode(){return 268435456&this.bg&&~this.extended.underlineColor?50331648&this.extended.underlineColor:this.getFgColorMode()}isUnderlineColorRGB(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==50331648:this.isFgRGB()}isUnderlineColorPalette(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==16777216||(50331648&this.extended.underlineColor)==33554432:this.isFgPalette()}isUnderlineColorDefault(){return 268435456&this.bg&&~this.extended.underlineColor?(50331648&this.extended.underlineColor)==0:this.isFgDefault()}getUnderlineStyle(){return 268435456&this.fg?268435456&this.bg?this.extended.underlineStyle:1:0}}s.AttributeData=o;class l{get ext(){return this._urlId?-469762049&this._ext|this.underlineStyle<<26:this._ext}set ext(a){this._ext=a}get underlineStyle(){return this._urlId?5:(469762048&this._ext)>>26}set underlineStyle(a){this._ext&=-469762049,this._ext|=a<<26&469762048}get underlineColor(){return 67108863&this._ext}set underlineColor(a){this._ext&=-67108864,this._ext|=67108863&a}get urlId(){return this._urlId}set urlId(a){this._urlId=a}constructor(a=0,f=0){this._ext=0,this._urlId=0,this._ext=a,this._urlId=f}clone(){return new l(this._ext,this._urlId)}isEmpty(){return this.underlineStyle===0&&this._urlId===0}}s.ExtendedAttrs=l},9092:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Buffer=s.MAX_BUFFER_SIZE=void 0;const l=o(6349),c=o(7226),a=o(3734),f=o(8437),p=o(4634),v=o(511),g=o(643),t=o(4863),n=o(7116);s.MAX_BUFFER_SIZE=4294967295,s.Buffer=class{constructor(i,r,h){this._hasScrollback=i,this._optionsService=r,this._bufferService=h,this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.tabs={},this.savedY=0,this.savedX=0,this.savedCurAttrData=f.DEFAULT_ATTR_DATA.clone(),this.savedCharset=n.DEFAULT_CHARSET,this.markers=[],this._nullCell=v.CellData.fromCharData([0,g.NULL_CELL_CHAR,g.NULL_CELL_WIDTH,g.NULL_CELL_CODE]),this._whitespaceCell=v.CellData.fromCharData([0,g.WHITESPACE_CELL_CHAR,g.WHITESPACE_CELL_WIDTH,g.WHITESPACE_CELL_CODE]),this._isClearing=!1,this._memoryCleanupQueue=new c.IdleTaskQueue,this._memoryCleanupPosition=0,this._cols=this._bufferService.cols,this._rows=this._bufferService.rows,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}getNullCell(i){return i?(this._nullCell.fg=i.fg,this._nullCell.bg=i.bg,this._nullCell.extended=i.extended):(this._nullCell.fg=0,this._nullCell.bg=0,this._nullCell.extended=new a.ExtendedAttrs),this._nullCell}getWhitespaceCell(i){return i?(this._whitespaceCell.fg=i.fg,this._whitespaceCell.bg=i.bg,this._whitespaceCell.extended=i.extended):(this._whitespaceCell.fg=0,this._whitespaceCell.bg=0,this._whitespaceCell.extended=new a.ExtendedAttrs),this._whitespaceCell}getBlankLine(i,r){return new f.BufferLine(this._bufferService.cols,this.getNullCell(i),r)}get hasScrollback(){return this._hasScrollback&&this.lines.maxLength>this._rows}get isCursorInViewport(){const i=this.ybase+this.y-this.ydisp;return i>=0&&i<this._rows}_getCorrectBufferLength(i){if(!this._hasScrollback)return i;const r=i+this._optionsService.rawOptions.scrollback;return r>s.MAX_BUFFER_SIZE?s.MAX_BUFFER_SIZE:r}fillViewportRows(i){if(this.lines.length===0){i===void 0&&(i=f.DEFAULT_ATTR_DATA);let r=this._rows;for(;r--;)this.lines.push(this.getBlankLine(i))}}clear(){this.ydisp=0,this.ybase=0,this.y=0,this.x=0,this.lines=new l.CircularList(this._getCorrectBufferLength(this._rows)),this.scrollTop=0,this.scrollBottom=this._rows-1,this.setupTabStops()}resize(i,r){const h=this.getNullCell(f.DEFAULT_ATTR_DATA);let _=0;const S=this._getCorrectBufferLength(r);if(S>this.lines.maxLength&&(this.lines.maxLength=S),this.lines.length>0){if(this._cols<i)for(let u=0;u<this.lines.length;u++)_+=+this.lines.get(u).resize(i,h);let y=0;if(this._rows<r)for(let u=this._rows;u<r;u++)this.lines.length<r+this.ybase&&(this._optionsService.rawOptions.windowsMode||this._optionsService.rawOptions.windowsPty.backend!==void 0||this._optionsService.rawOptions.windowsPty.buildNumber!==void 0?this.lines.push(new f.BufferLine(i,h)):this.ybase>0&&this.lines.length<=this.ybase+this.y+y+1?(this.ybase--,y++,this.ydisp>0&&this.ydisp--):this.lines.push(new f.BufferLine(i,h)));else for(let u=this._rows;u>r;u--)this.lines.length>r+this.ybase&&(this.lines.length>this.ybase+this.y+1?this.lines.pop():(this.ybase++,this.ydisp++));if(S<this.lines.maxLength){const u=this.lines.length-S;u>0&&(this.lines.trimStart(u),this.ybase=Math.max(this.ybase-u,0),this.ydisp=Math.max(this.ydisp-u,0),this.savedY=Math.max(this.savedY-u,0)),this.lines.maxLength=S}this.x=Math.min(this.x,i-1),this.y=Math.min(this.y,r-1),y&&(this.y+=y),this.savedX=Math.min(this.savedX,i-1),this.scrollTop=0}if(this.scrollBottom=r-1,this._isReflowEnabled&&(this._reflow(i,r),this._cols>i))for(let y=0;y<this.lines.length;y++)_+=+this.lines.get(y).resize(i,h);this._cols=i,this._rows=r,this._memoryCleanupQueue.clear(),_>.1*this.lines.length&&(this._memoryCleanupPosition=0,this._memoryCleanupQueue.enqueue(()=>this._batchedMemoryCleanup()))}_batchedMemoryCleanup(){let i=!0;this._memoryCleanupPosition>=this.lines.length&&(this._memoryCleanupPosition=0,i=!1);let r=0;for(;this._memoryCleanupPosition<this.lines.length;)if(r+=this.lines.get(this._memoryCleanupPosition++).cleanupMemory(),r>100)return!0;return i}get _isReflowEnabled(){const i=this._optionsService.rawOptions.windowsPty;return i&&i.buildNumber?this._hasScrollback&&i.backend==="conpty"&&i.buildNumber>=21376:this._hasScrollback&&!this._optionsService.rawOptions.windowsMode}_reflow(i,r){this._cols!==i&&(i>this._cols?this._reflowLarger(i,r):this._reflowSmaller(i,r))}_reflowLarger(i,r){const h=(0,p.reflowLargerGetLinesToRemove)(this.lines,this._cols,i,this.ybase+this.y,this.getNullCell(f.DEFAULT_ATTR_DATA));if(h.length>0){const _=(0,p.reflowLargerCreateNewLayout)(this.lines,h);(0,p.reflowLargerApplyNewLayout)(this.lines,_.layout),this._reflowLargerAdjustViewport(i,r,_.countRemoved)}}_reflowLargerAdjustViewport(i,r,h){const _=this.getNullCell(f.DEFAULT_ATTR_DATA);let S=h;for(;S-- >0;)this.ybase===0?(this.y>0&&this.y--,this.lines.length<r&&this.lines.push(new f.BufferLine(i,_))):(this.ydisp===this.ybase&&this.ydisp--,this.ybase--);this.savedY=Math.max(this.savedY-h,0)}_reflowSmaller(i,r){const h=this.getNullCell(f.DEFAULT_ATTR_DATA),_=[];let S=0;for(let y=this.lines.length-1;y>=0;y--){let u=this.lines.get(y);if(!u||!u.isWrapped&&u.getTrimmedLength()<=i)continue;const C=[u];for(;u.isWrapped&&y>0;)u=this.lines.get(--y),C.unshift(u);const L=this.ybase+this.y;if(L>=y&&L<y+C.length)continue;const I=C[C.length-1].getTrimmedLength(),R=(0,p.reflowSmallerGetNewLineLengths)(C,this._cols,i),B=R.length-C.length;let O;O=this.ybase===0&&this.y!==this.lines.length-1?Math.max(0,this.y-this.lines.maxLength+B):Math.max(0,this.lines.length-this.lines.maxLength+B);const $=[];for(let M=0;M<B;M++){const N=this.getBlankLine(f.DEFAULT_ATTR_DATA,!0);$.push(N)}$.length>0&&(_.push({start:y+C.length+S,newLines:$}),S+=$.length),C.push(...$);let W=R.length-1,z=R[W];z===0&&(W--,z=R[W]);let k=C.length-B-1,D=I;for(;k>=0;){const M=Math.min(D,z);if(C[W]===void 0)break;if(C[W].copyCellsFrom(C[k],D-M,z-M,M,!0),z-=M,z===0&&(W--,z=R[W]),D-=M,D===0){k--;const N=Math.max(k,0);D=(0,p.getWrappedLineTrimmedLength)(C,N,this._cols)}}for(let M=0;M<C.length;M++)R[M]<i&&C[M].setCell(R[M],h);let T=B-O;for(;T-- >0;)this.ybase===0?this.y<r-1?(this.y++,this.lines.pop()):(this.ybase++,this.ydisp++):this.ybase<Math.min(this.lines.maxLength,this.lines.length+S)-r&&(this.ybase===this.ydisp&&this.ydisp++,this.ybase++);this.savedY=Math.min(this.savedY+B,this.ybase+r-1)}if(_.length>0){const y=[],u=[];for(let W=0;W<this.lines.length;W++)u.push(this.lines.get(W));const C=this.lines.length;let L=C-1,I=0,R=_[I];this.lines.length=Math.min(this.lines.maxLength,this.lines.length+S);let B=0;for(let W=Math.min(this.lines.maxLength-1,C+S-1);W>=0;W--)if(R&&R.start>L+B){for(let z=R.newLines.length-1;z>=0;z--)this.lines.set(W--,R.newLines[z]);W++,y.push({index:L+1,amount:R.newLines.length}),B+=R.newLines.length,R=_[++I]}else this.lines.set(W,u[L--]);let O=0;for(let W=y.length-1;W>=0;W--)y[W].index+=O,this.lines.onInsertEmitter.fire(y[W]),O+=y[W].amount;const $=Math.max(0,C+S-this.lines.maxLength);$>0&&this.lines.onTrimEmitter.fire($)}}translateBufferLineToString(i,r,h=0,_){const S=this.lines.get(i);return S?S.translateToString(r,h,_):""}getWrappedRangeForLine(i){let r=i,h=i;for(;r>0&&this.lines.get(r).isWrapped;)r--;for(;h+1<this.lines.length&&this.lines.get(h+1).isWrapped;)h++;return{first:r,last:h}}setupTabStops(i){for(i!=null?this.tabs[i]||(i=this.prevStop(i)):(this.tabs={},i=0);i<this._cols;i+=this._optionsService.rawOptions.tabStopWidth)this.tabs[i]=!0}prevStop(i){for(i==null&&(i=this.x);!this.tabs[--i]&&i>0;);return i>=this._cols?this._cols-1:i<0?0:i}nextStop(i){for(i==null&&(i=this.x);!this.tabs[++i]&&i<this._cols;);return i>=this._cols?this._cols-1:i<0?0:i}clearMarkers(i){this._isClearing=!0;for(let r=0;r<this.markers.length;r++)this.markers[r].line===i&&(this.markers[r].dispose(),this.markers.splice(r--,1));this._isClearing=!1}clearAllMarkers(){this._isClearing=!0;for(let i=0;i<this.markers.length;i++)this.markers[i].dispose(),this.markers.splice(i--,1);this._isClearing=!1}addMarker(i){const r=new t.Marker(i);return this.markers.push(r),r.register(this.lines.onTrim(h=>{r.line-=h,r.line<0&&r.dispose()})),r.register(this.lines.onInsert(h=>{r.line>=h.index&&(r.line+=h.amount)})),r.register(this.lines.onDelete(h=>{r.line>=h.index&&r.line<h.index+h.amount&&r.dispose(),r.line>h.index&&(r.line-=h.amount)})),r.register(r.onDispose(()=>this._removeMarker(r))),r}_removeMarker(i){this._isClearing||this.markers.splice(this.markers.indexOf(i),1)}}},8437:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferLine=s.DEFAULT_ATTR_DATA=void 0;const l=o(3734),c=o(511),a=o(643),f=o(482);s.DEFAULT_ATTR_DATA=Object.freeze(new l.AttributeData);let p=0;class v{constructor(t,n,i=!1){this.isWrapped=i,this._combined={},this._extendedAttrs={},this._data=new Uint32Array(3*t);const r=n||c.CellData.fromCharData([0,a.NULL_CELL_CHAR,a.NULL_CELL_WIDTH,a.NULL_CELL_CODE]);for(let h=0;h<t;++h)this.setCell(h,r);this.length=t}get(t){const n=this._data[3*t+0],i=2097151&n;return[this._data[3*t+1],2097152&n?this._combined[t]:i?(0,f.stringFromCodePoint)(i):"",n>>22,2097152&n?this._combined[t].charCodeAt(this._combined[t].length-1):i]}set(t,n){this._data[3*t+1]=n[a.CHAR_DATA_ATTR_INDEX],n[a.CHAR_DATA_CHAR_INDEX].length>1?(this._combined[t]=n[1],this._data[3*t+0]=2097152|t|n[a.CHAR_DATA_WIDTH_INDEX]<<22):this._data[3*t+0]=n[a.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|n[a.CHAR_DATA_WIDTH_INDEX]<<22}getWidth(t){return this._data[3*t+0]>>22}hasWidth(t){return 12582912&this._data[3*t+0]}getFg(t){return this._data[3*t+1]}getBg(t){return this._data[3*t+2]}hasContent(t){return 4194303&this._data[3*t+0]}getCodePoint(t){const n=this._data[3*t+0];return 2097152&n?this._combined[t].charCodeAt(this._combined[t].length-1):2097151&n}isCombined(t){return 2097152&this._data[3*t+0]}getString(t){const n=this._data[3*t+0];return 2097152&n?this._combined[t]:2097151&n?(0,f.stringFromCodePoint)(2097151&n):""}isProtected(t){return 536870912&this._data[3*t+2]}loadCell(t,n){return p=3*t,n.content=this._data[p+0],n.fg=this._data[p+1],n.bg=this._data[p+2],2097152&n.content&&(n.combinedData=this._combined[t]),268435456&n.bg&&(n.extended=this._extendedAttrs[t]),n}setCell(t,n){2097152&n.content&&(this._combined[t]=n.combinedData),268435456&n.bg&&(this._extendedAttrs[t]=n.extended),this._data[3*t+0]=n.content,this._data[3*t+1]=n.fg,this._data[3*t+2]=n.bg}setCellFromCodePoint(t,n,i,r,h,_){268435456&h&&(this._extendedAttrs[t]=_),this._data[3*t+0]=n|i<<22,this._data[3*t+1]=r,this._data[3*t+2]=h}addCodepointToCell(t,n){let i=this._data[3*t+0];2097152&i?this._combined[t]+=(0,f.stringFromCodePoint)(n):(2097151&i?(this._combined[t]=(0,f.stringFromCodePoint)(2097151&i)+(0,f.stringFromCodePoint)(n),i&=-2097152,i|=2097152):i=n|4194304,this._data[3*t+0]=i)}insertCells(t,n,i,r){if((t%=this.length)&&this.getWidth(t-1)===2&&this.setCellFromCodePoint(t-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),n<this.length-t){const h=new c.CellData;for(let _=this.length-t-n-1;_>=0;--_)this.setCell(t+n+_,this.loadCell(t+_,h));for(let _=0;_<n;++_)this.setCell(t+_,i)}else for(let h=t;h<this.length;++h)this.setCell(h,i);this.getWidth(this.length-1)===2&&this.setCellFromCodePoint(this.length-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs)}deleteCells(t,n,i,r){if(t%=this.length,n<this.length-t){const h=new c.CellData;for(let _=0;_<this.length-t-n;++_)this.setCell(t+_,this.loadCell(t+n+_,h));for(let _=this.length-n;_<this.length;++_)this.setCell(_,i)}else for(let h=t;h<this.length;++h)this.setCell(h,i);t&&this.getWidth(t-1)===2&&this.setCellFromCodePoint(t-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),this.getWidth(t)!==0||this.hasContent(t)||this.setCellFromCodePoint(t,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs)}replaceCells(t,n,i,r,h=!1){if(h)for(t&&this.getWidth(t-1)===2&&!this.isProtected(t-1)&&this.setCellFromCodePoint(t-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),n<this.length&&this.getWidth(n-1)===2&&!this.isProtected(n)&&this.setCellFromCodePoint(n,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs);t<n&&t<this.length;)this.isProtected(t)||this.setCell(t,i),t++;else for(t&&this.getWidth(t-1)===2&&this.setCellFromCodePoint(t-1,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs),n<this.length&&this.getWidth(n-1)===2&&this.setCellFromCodePoint(n,0,1,(r==null?void 0:r.fg)||0,(r==null?void 0:r.bg)||0,(r==null?void 0:r.extended)||new l.ExtendedAttrs);t<n&&t<this.length;)this.setCell(t++,i)}resize(t,n){if(t===this.length)return 4*this._data.length*2<this._data.buffer.byteLength;const i=3*t;if(t>this.length){if(this._data.buffer.byteLength>=4*i)this._data=new Uint32Array(this._data.buffer,0,i);else{const r=new Uint32Array(i);r.set(this._data),this._data=r}for(let r=this.length;r<t;++r)this.setCell(r,n)}else{this._data=this._data.subarray(0,i);const r=Object.keys(this._combined);for(let _=0;_<r.length;_++){const S=parseInt(r[_],10);S>=t&&delete this._combined[S]}const h=Object.keys(this._extendedAttrs);for(let _=0;_<h.length;_++){const S=parseInt(h[_],10);S>=t&&delete this._extendedAttrs[S]}}return this.length=t,4*i*2<this._data.buffer.byteLength}cleanupMemory(){if(4*this._data.length*2<this._data.buffer.byteLength){const t=new Uint32Array(this._data.length);return t.set(this._data),this._data=t,1}return 0}fill(t,n=!1){if(n)for(let i=0;i<this.length;++i)this.isProtected(i)||this.setCell(i,t);else{this._combined={},this._extendedAttrs={};for(let i=0;i<this.length;++i)this.setCell(i,t)}}copyFrom(t){this.length!==t.length?this._data=new Uint32Array(t._data):this._data.set(t._data),this.length=t.length,this._combined={};for(const n in t._combined)this._combined[n]=t._combined[n];this._extendedAttrs={};for(const n in t._extendedAttrs)this._extendedAttrs[n]=t._extendedAttrs[n];this.isWrapped=t.isWrapped}clone(){const t=new v(0);t._data=new Uint32Array(this._data),t.length=this.length;for(const n in this._combined)t._combined[n]=this._combined[n];for(const n in this._extendedAttrs)t._extendedAttrs[n]=this._extendedAttrs[n];return t.isWrapped=this.isWrapped,t}getTrimmedLength(){for(let t=this.length-1;t>=0;--t)if(4194303&this._data[3*t+0])return t+(this._data[3*t+0]>>22);return 0}getNoBgTrimmedLength(){for(let t=this.length-1;t>=0;--t)if(4194303&this._data[3*t+0]||50331648&this._data[3*t+2])return t+(this._data[3*t+0]>>22);return 0}copyCellsFrom(t,n,i,r,h){const _=t._data;if(h)for(let y=r-1;y>=0;y--){for(let u=0;u<3;u++)this._data[3*(i+y)+u]=_[3*(n+y)+u];268435456&_[3*(n+y)+2]&&(this._extendedAttrs[i+y]=t._extendedAttrs[n+y])}else for(let y=0;y<r;y++){for(let u=0;u<3;u++)this._data[3*(i+y)+u]=_[3*(n+y)+u];268435456&_[3*(n+y)+2]&&(this._extendedAttrs[i+y]=t._extendedAttrs[n+y])}const S=Object.keys(t._combined);for(let y=0;y<S.length;y++){const u=parseInt(S[y],10);u>=n&&(this._combined[u-n+i]=t._combined[u])}}translateToString(t=!1,n=0,i=this.length){t&&(i=Math.min(i,this.getTrimmedLength()));let r="";for(;n<i;){const h=this._data[3*n+0],_=2097151&h;r+=2097152&h?this._combined[n]:_?(0,f.stringFromCodePoint)(_):a.WHITESPACE_CELL_CHAR,n+=h>>22||1}return r}}s.BufferLine=v},4841:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.getRangeLength=void 0,s.getRangeLength=function(o,l){if(o.start.y>o.end.y)throw new Error(`Buffer range end (${o.end.x}, ${o.end.y}) cannot be before start (${o.start.x}, ${o.start.y})`);return l*(o.end.y-o.start.y)+(o.end.x-o.start.x+1)}},4634:(x,s)=>{function o(l,c,a){if(c===l.length-1)return l[c].getTrimmedLength();const f=!l[c].hasContent(a-1)&&l[c].getWidth(a-1)===1,p=l[c+1].getWidth(0)===2;return f&&p?a-1:a}Object.defineProperty(s,"__esModule",{value:!0}),s.getWrappedLineTrimmedLength=s.reflowSmallerGetNewLineLengths=s.reflowLargerApplyNewLayout=s.reflowLargerCreateNewLayout=s.reflowLargerGetLinesToRemove=void 0,s.reflowLargerGetLinesToRemove=function(l,c,a,f,p){const v=[];for(let g=0;g<l.length-1;g++){let t=g,n=l.get(++t);if(!n.isWrapped)continue;const i=[l.get(g)];for(;t<l.length&&n.isWrapped;)i.push(n),n=l.get(++t);if(f>=g&&f<t){g+=i.length-1;continue}let r=0,h=o(i,r,c),_=1,S=0;for(;_<i.length;){const u=o(i,_,c),C=u-S,L=a-h,I=Math.min(C,L);i[r].copyCellsFrom(i[_],S,h,I,!1),h+=I,h===a&&(r++,h=0),S+=I,S===u&&(_++,S=0),h===0&&r!==0&&i[r-1].getWidth(a-1)===2&&(i[r].copyCellsFrom(i[r-1],a-1,h++,1,!1),i[r-1].setCell(a-1,p))}i[r].replaceCells(h,a,p);let y=0;for(let u=i.length-1;u>0&&(u>r||i[u].getTrimmedLength()===0);u--)y++;y>0&&(v.push(g+i.length-y),v.push(y)),g+=i.length-1}return v},s.reflowLargerCreateNewLayout=function(l,c){const a=[];let f=0,p=c[f],v=0;for(let g=0;g<l.length;g++)if(p===g){const t=c[++f];l.onDeleteEmitter.fire({index:g-v,amount:t}),g+=t-1,v+=t,p=c[++f]}else a.push(g);return{layout:a,countRemoved:v}},s.reflowLargerApplyNewLayout=function(l,c){const a=[];for(let f=0;f<c.length;f++)a.push(l.get(c[f]));for(let f=0;f<a.length;f++)l.set(f,a[f]);l.length=c.length},s.reflowSmallerGetNewLineLengths=function(l,c,a){const f=[],p=l.map((n,i)=>o(l,i,c)).reduce((n,i)=>n+i);let v=0,g=0,t=0;for(;t<p;){if(p-t<a){f.push(p-t);break}v+=a;const n=o(l,g,c);v>n&&(v-=n,g++);const i=l[g].getWidth(v-1)===2;i&&v--;const r=i?a-1:a;f.push(r),t+=r}return f},s.getWrappedLineTrimmedLength=o},5295:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferSet=void 0;const l=o(8460),c=o(844),a=o(9092);class f extends c.Disposable{constructor(v,g){super(),this._optionsService=v,this._bufferService=g,this._onBufferActivate=this.register(new l.EventEmitter),this.onBufferActivate=this._onBufferActivate.event,this.reset(),this.register(this._optionsService.onSpecificOptionChange("scrollback",()=>this.resize(this._bufferService.cols,this._bufferService.rows))),this.register(this._optionsService.onSpecificOptionChange("tabStopWidth",()=>this.setupTabStops()))}reset(){this._normal=new a.Buffer(!0,this._optionsService,this._bufferService),this._normal.fillViewportRows(),this._alt=new a.Buffer(!1,this._optionsService,this._bufferService),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}),this.setupTabStops()}get alt(){return this._alt}get active(){return this._activeBuffer}get normal(){return this._normal}activateNormalBuffer(){this._activeBuffer!==this._normal&&(this._normal.x=this._alt.x,this._normal.y=this._alt.y,this._alt.clearAllMarkers(),this._alt.clear(),this._activeBuffer=this._normal,this._onBufferActivate.fire({activeBuffer:this._normal,inactiveBuffer:this._alt}))}activateAltBuffer(v){this._activeBuffer!==this._alt&&(this._alt.fillViewportRows(v),this._alt.x=this._normal.x,this._alt.y=this._normal.y,this._activeBuffer=this._alt,this._onBufferActivate.fire({activeBuffer:this._alt,inactiveBuffer:this._normal}))}resize(v,g){this._normal.resize(v,g),this._alt.resize(v,g),this.setupTabStops(v)}setupTabStops(v){this._normal.setupTabStops(v),this._alt.setupTabStops(v)}}s.BufferSet=f},511:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CellData=void 0;const l=o(482),c=o(643),a=o(3734);class f extends a.AttributeData{constructor(){super(...arguments),this.content=0,this.fg=0,this.bg=0,this.extended=new a.ExtendedAttrs,this.combinedData=""}static fromCharData(v){const g=new f;return g.setFromCharData(v),g}isCombined(){return 2097152&this.content}getWidth(){return this.content>>22}getChars(){return 2097152&this.content?this.combinedData:2097151&this.content?(0,l.stringFromCodePoint)(2097151&this.content):""}getCode(){return this.isCombined()?this.combinedData.charCodeAt(this.combinedData.length-1):2097151&this.content}setFromCharData(v){this.fg=v[c.CHAR_DATA_ATTR_INDEX],this.bg=0;let g=!1;if(v[c.CHAR_DATA_CHAR_INDEX].length>2)g=!0;else if(v[c.CHAR_DATA_CHAR_INDEX].length===2){const t=v[c.CHAR_DATA_CHAR_INDEX].charCodeAt(0);if(55296<=t&&t<=56319){const n=v[c.CHAR_DATA_CHAR_INDEX].charCodeAt(1);56320<=n&&n<=57343?this.content=1024*(t-55296)+n-56320+65536|v[c.CHAR_DATA_WIDTH_INDEX]<<22:g=!0}else g=!0}else this.content=v[c.CHAR_DATA_CHAR_INDEX].charCodeAt(0)|v[c.CHAR_DATA_WIDTH_INDEX]<<22;g&&(this.combinedData=v[c.CHAR_DATA_CHAR_INDEX],this.content=2097152|v[c.CHAR_DATA_WIDTH_INDEX]<<22)}getAsCharData(){return[this.fg,this.getChars(),this.getWidth(),this.getCode()]}}s.CellData=f},643:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WHITESPACE_CELL_CODE=s.WHITESPACE_CELL_WIDTH=s.WHITESPACE_CELL_CHAR=s.NULL_CELL_CODE=s.NULL_CELL_WIDTH=s.NULL_CELL_CHAR=s.CHAR_DATA_CODE_INDEX=s.CHAR_DATA_WIDTH_INDEX=s.CHAR_DATA_CHAR_INDEX=s.CHAR_DATA_ATTR_INDEX=s.DEFAULT_EXT=s.DEFAULT_ATTR=s.DEFAULT_COLOR=void 0,s.DEFAULT_COLOR=0,s.DEFAULT_ATTR=256|s.DEFAULT_COLOR<<9,s.DEFAULT_EXT=0,s.CHAR_DATA_ATTR_INDEX=0,s.CHAR_DATA_CHAR_INDEX=1,s.CHAR_DATA_WIDTH_INDEX=2,s.CHAR_DATA_CODE_INDEX=3,s.NULL_CELL_CHAR="",s.NULL_CELL_WIDTH=1,s.NULL_CELL_CODE=0,s.WHITESPACE_CELL_CHAR=" ",s.WHITESPACE_CELL_WIDTH=1,s.WHITESPACE_CELL_CODE=32},4863:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Marker=void 0;const l=o(8460),c=o(844);class a{get id(){return this._id}constructor(p){this.line=p,this.isDisposed=!1,this._disposables=[],this._id=a._nextId++,this._onDispose=this.register(new l.EventEmitter),this.onDispose=this._onDispose.event}dispose(){this.isDisposed||(this.isDisposed=!0,this.line=-1,this._onDispose.fire(),(0,c.disposeArray)(this._disposables),this._disposables.length=0)}register(p){return this._disposables.push(p),p}}s.Marker=a,a._nextId=1},7116:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DEFAULT_CHARSET=s.CHARSETS=void 0,s.CHARSETS={},s.DEFAULT_CHARSET=s.CHARSETS.B,s.CHARSETS[0]={"`":"◆",a:"▒",b:"␉",c:"␌",d:"␍",e:"␊",f:"°",g:"±",h:"␤",i:"␋",j:"┘",k:"┐",l:"┌",m:"└",n:"┼",o:"⎺",p:"⎻",q:"─",r:"⎼",s:"⎽",t:"├",u:"┤",v:"┴",w:"┬",x:"│",y:"≤",z:"≥","{":"π","|":"≠","}":"£","~":"·"},s.CHARSETS.A={"#":"£"},s.CHARSETS.B=void 0,s.CHARSETS[4]={"#":"£","@":"¾","[":"ij","\\":"½","]":"|","{":"¨","|":"f","}":"¼","~":"´"},s.CHARSETS.C=s.CHARSETS[5]={"[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},s.CHARSETS.R={"#":"£","@":"à","[":"°","\\":"ç","]":"§","{":"é","|":"ù","}":"è","~":"¨"},s.CHARSETS.Q={"@":"à","[":"â","\\":"ç","]":"ê","^":"î","`":"ô","{":"é","|":"ù","}":"è","~":"û"},s.CHARSETS.K={"@":"§","[":"Ä","\\":"Ö","]":"Ü","{":"ä","|":"ö","}":"ü","~":"ß"},s.CHARSETS.Y={"#":"£","@":"§","[":"°","\\":"ç","]":"é","`":"ù","{":"à","|":"ò","}":"è","~":"ì"},s.CHARSETS.E=s.CHARSETS[6]={"@":"Ä","[":"Æ","\\":"Ø","]":"Å","^":"Ü","`":"ä","{":"æ","|":"ø","}":"å","~":"ü"},s.CHARSETS.Z={"#":"£","@":"§","[":"¡","\\":"Ñ","]":"¿","{":"°","|":"ñ","}":"ç"},s.CHARSETS.H=s.CHARSETS[7]={"@":"É","[":"Ä","\\":"Ö","]":"Å","^":"Ü","`":"é","{":"ä","|":"ö","}":"å","~":"ü"},s.CHARSETS["="]={"#":"ù","@":"à","[":"é","\\":"ç","]":"ê","^":"î",_:"è","`":"ô","{":"ä","|":"ö","}":"ü","~":"û"}},2584:(x,s)=>{var o,l,c;Object.defineProperty(s,"__esModule",{value:!0}),s.C1_ESCAPED=s.C1=s.C0=void 0,function(a){a.NUL="\0",a.SOH="",a.STX="",a.ETX="",a.EOT="",a.ENQ="",a.ACK="",a.BEL="\x07",a.BS="\b",a.HT="	",a.LF=`
`,a.VT="\v",a.FF="\f",a.CR="\r",a.SO="",a.SI="",a.DLE="",a.DC1="",a.DC2="",a.DC3="",a.DC4="",a.NAK="",a.SYN="",a.ETB="",a.CAN="",a.EM="",a.SUB="",a.ESC="\x1B",a.FS="",a.GS="",a.RS="",a.US="",a.SP=" ",a.DEL=""}(o||(s.C0=o={})),function(a){a.PAD="",a.HOP="",a.BPH="",a.NBH="",a.IND="",a.NEL="",a.SSA="",a.ESA="",a.HTS="",a.HTJ="",a.VTS="",a.PLD="",a.PLU="",a.RI="",a.SS2="",a.SS3="",a.DCS="",a.PU1="",a.PU2="",a.STS="",a.CCH="",a.MW="",a.SPA="",a.EPA="",a.SOS="",a.SGCI="",a.SCI="",a.CSI="",a.ST="",a.OSC="",a.PM="",a.APC=""}(l||(s.C1=l={})),function(a){a.ST=`${o.ESC}\\`}(c||(s.C1_ESCAPED=c={}))},7399:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.evaluateKeyboardEvent=void 0;const l=o(2584),c={48:["0",")"],49:["1","!"],50:["2","@"],51:["3","#"],52:["4","$"],53:["5","%"],54:["6","^"],55:["7","&"],56:["8","*"],57:["9","("],186:[";",":"],187:["=","+"],188:[",","<"],189:["-","_"],190:[".",">"],191:["/","?"],192:["`","~"],219:["[","{"],220:["\\","|"],221:["]","}"],222:["'",'"']};s.evaluateKeyboardEvent=function(a,f,p,v){const g={type:0,cancel:!1,key:void 0},t=(a.shiftKey?1:0)|(a.altKey?2:0)|(a.ctrlKey?4:0)|(a.metaKey?8:0);switch(a.keyCode){case 0:a.key==="UIKeyInputUpArrow"?g.key=f?l.C0.ESC+"OA":l.C0.ESC+"[A":a.key==="UIKeyInputLeftArrow"?g.key=f?l.C0.ESC+"OD":l.C0.ESC+"[D":a.key==="UIKeyInputRightArrow"?g.key=f?l.C0.ESC+"OC":l.C0.ESC+"[C":a.key==="UIKeyInputDownArrow"&&(g.key=f?l.C0.ESC+"OB":l.C0.ESC+"[B");break;case 8:if(a.altKey){g.key=l.C0.ESC+l.C0.DEL;break}g.key=l.C0.DEL;break;case 9:if(a.shiftKey){g.key=l.C0.ESC+"[Z";break}g.key=l.C0.HT,g.cancel=!0;break;case 13:g.key=a.altKey?l.C0.ESC+l.C0.CR:l.C0.CR,g.cancel=!0;break;case 27:g.key=l.C0.ESC,a.altKey&&(g.key=l.C0.ESC+l.C0.ESC),g.cancel=!0;break;case 37:if(a.metaKey)break;t?(g.key=l.C0.ESC+"[1;"+(t+1)+"D",g.key===l.C0.ESC+"[1;3D"&&(g.key=l.C0.ESC+(p?"b":"[1;5D"))):g.key=f?l.C0.ESC+"OD":l.C0.ESC+"[D";break;case 39:if(a.metaKey)break;t?(g.key=l.C0.ESC+"[1;"+(t+1)+"C",g.key===l.C0.ESC+"[1;3C"&&(g.key=l.C0.ESC+(p?"f":"[1;5C"))):g.key=f?l.C0.ESC+"OC":l.C0.ESC+"[C";break;case 38:if(a.metaKey)break;t?(g.key=l.C0.ESC+"[1;"+(t+1)+"A",p||g.key!==l.C0.ESC+"[1;3A"||(g.key=l.C0.ESC+"[1;5A")):g.key=f?l.C0.ESC+"OA":l.C0.ESC+"[A";break;case 40:if(a.metaKey)break;t?(g.key=l.C0.ESC+"[1;"+(t+1)+"B",p||g.key!==l.C0.ESC+"[1;3B"||(g.key=l.C0.ESC+"[1;5B")):g.key=f?l.C0.ESC+"OB":l.C0.ESC+"[B";break;case 45:a.shiftKey||a.ctrlKey||(g.key=l.C0.ESC+"[2~");break;case 46:g.key=t?l.C0.ESC+"[3;"+(t+1)+"~":l.C0.ESC+"[3~";break;case 36:g.key=t?l.C0.ESC+"[1;"+(t+1)+"H":f?l.C0.ESC+"OH":l.C0.ESC+"[H";break;case 35:g.key=t?l.C0.ESC+"[1;"+(t+1)+"F":f?l.C0.ESC+"OF":l.C0.ESC+"[F";break;case 33:a.shiftKey?g.type=2:a.ctrlKey?g.key=l.C0.ESC+"[5;"+(t+1)+"~":g.key=l.C0.ESC+"[5~";break;case 34:a.shiftKey?g.type=3:a.ctrlKey?g.key=l.C0.ESC+"[6;"+(t+1)+"~":g.key=l.C0.ESC+"[6~";break;case 112:g.key=t?l.C0.ESC+"[1;"+(t+1)+"P":l.C0.ESC+"OP";break;case 113:g.key=t?l.C0.ESC+"[1;"+(t+1)+"Q":l.C0.ESC+"OQ";break;case 114:g.key=t?l.C0.ESC+"[1;"+(t+1)+"R":l.C0.ESC+"OR";break;case 115:g.key=t?l.C0.ESC+"[1;"+(t+1)+"S":l.C0.ESC+"OS";break;case 116:g.key=t?l.C0.ESC+"[15;"+(t+1)+"~":l.C0.ESC+"[15~";break;case 117:g.key=t?l.C0.ESC+"[17;"+(t+1)+"~":l.C0.ESC+"[17~";break;case 118:g.key=t?l.C0.ESC+"[18;"+(t+1)+"~":l.C0.ESC+"[18~";break;case 119:g.key=t?l.C0.ESC+"[19;"+(t+1)+"~":l.C0.ESC+"[19~";break;case 120:g.key=t?l.C0.ESC+"[20;"+(t+1)+"~":l.C0.ESC+"[20~";break;case 121:g.key=t?l.C0.ESC+"[21;"+(t+1)+"~":l.C0.ESC+"[21~";break;case 122:g.key=t?l.C0.ESC+"[23;"+(t+1)+"~":l.C0.ESC+"[23~";break;case 123:g.key=t?l.C0.ESC+"[24;"+(t+1)+"~":l.C0.ESC+"[24~";break;default:if(!a.ctrlKey||a.shiftKey||a.altKey||a.metaKey)if(p&&!v||!a.altKey||a.metaKey)!p||a.altKey||a.ctrlKey||a.shiftKey||!a.metaKey?a.key&&!a.ctrlKey&&!a.altKey&&!a.metaKey&&a.keyCode>=48&&a.key.length===1?g.key=a.key:a.key&&a.ctrlKey&&(a.key==="_"&&(g.key=l.C0.US),a.key==="@"&&(g.key=l.C0.NUL)):a.keyCode===65&&(g.type=1);else{const n=c[a.keyCode],i=n==null?void 0:n[a.shiftKey?1:0];if(i)g.key=l.C0.ESC+i;else if(a.keyCode>=65&&a.keyCode<=90){const r=a.ctrlKey?a.keyCode-64:a.keyCode+32;let h=String.fromCharCode(r);a.shiftKey&&(h=h.toUpperCase()),g.key=l.C0.ESC+h}else if(a.keyCode===32)g.key=l.C0.ESC+(a.ctrlKey?l.C0.NUL:" ");else if(a.key==="Dead"&&a.code.startsWith("Key")){let r=a.code.slice(3,4);a.shiftKey||(r=r.toLowerCase()),g.key=l.C0.ESC+r,g.cancel=!0}}else a.keyCode>=65&&a.keyCode<=90?g.key=String.fromCharCode(a.keyCode-64):a.keyCode===32?g.key=l.C0.NUL:a.keyCode>=51&&a.keyCode<=55?g.key=String.fromCharCode(a.keyCode-51+27):a.keyCode===56?g.key=l.C0.DEL:a.keyCode===219?g.key=l.C0.ESC:a.keyCode===220?g.key=l.C0.FS:a.keyCode===221&&(g.key=l.C0.GS)}return g}},482:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Utf8ToUtf32=s.StringToUtf32=s.utf32ToString=s.stringFromCodePoint=void 0,s.stringFromCodePoint=function(o){return o>65535?(o-=65536,String.fromCharCode(55296+(o>>10))+String.fromCharCode(o%1024+56320)):String.fromCharCode(o)},s.utf32ToString=function(o,l=0,c=o.length){let a="";for(let f=l;f<c;++f){let p=o[f];p>65535?(p-=65536,a+=String.fromCharCode(55296+(p>>10))+String.fromCharCode(p%1024+56320)):a+=String.fromCharCode(p)}return a},s.StringToUtf32=class{constructor(){this._interim=0}clear(){this._interim=0}decode(o,l){const c=o.length;if(!c)return 0;let a=0,f=0;if(this._interim){const p=o.charCodeAt(f++);56320<=p&&p<=57343?l[a++]=1024*(this._interim-55296)+p-56320+65536:(l[a++]=this._interim,l[a++]=p),this._interim=0}for(let p=f;p<c;++p){const v=o.charCodeAt(p);if(55296<=v&&v<=56319){if(++p>=c)return this._interim=v,a;const g=o.charCodeAt(p);56320<=g&&g<=57343?l[a++]=1024*(v-55296)+g-56320+65536:(l[a++]=v,l[a++]=g)}else v!==65279&&(l[a++]=v)}return a}},s.Utf8ToUtf32=class{constructor(){this.interim=new Uint8Array(3)}clear(){this.interim.fill(0)}decode(o,l){const c=o.length;if(!c)return 0;let a,f,p,v,g=0,t=0,n=0;if(this.interim[0]){let h=!1,_=this.interim[0];_&=(224&_)==192?31:(240&_)==224?15:7;let S,y=0;for(;(S=63&this.interim[++y])&&y<4;)_<<=6,_|=S;const u=(224&this.interim[0])==192?2:(240&this.interim[0])==224?3:4,C=u-y;for(;n<C;){if(n>=c)return 0;if(S=o[n++],(192&S)!=128){n--,h=!0;break}this.interim[y++]=S,_<<=6,_|=63&S}h||(u===2?_<128?n--:l[g++]=_:u===3?_<2048||_>=55296&&_<=57343||_===65279||(l[g++]=_):_<65536||_>1114111||(l[g++]=_)),this.interim.fill(0)}const i=c-4;let r=n;for(;r<c;){for(;!(!(r<i)||128&(a=o[r])||128&(f=o[r+1])||128&(p=o[r+2])||128&(v=o[r+3]));)l[g++]=a,l[g++]=f,l[g++]=p,l[g++]=v,r+=4;if(a=o[r++],a<128)l[g++]=a;else if((224&a)==192){if(r>=c)return this.interim[0]=a,g;if(f=o[r++],(192&f)!=128){r--;continue}if(t=(31&a)<<6|63&f,t<128){r--;continue}l[g++]=t}else if((240&a)==224){if(r>=c)return this.interim[0]=a,g;if(f=o[r++],(192&f)!=128){r--;continue}if(r>=c)return this.interim[0]=a,this.interim[1]=f,g;if(p=o[r++],(192&p)!=128){r--;continue}if(t=(15&a)<<12|(63&f)<<6|63&p,t<2048||t>=55296&&t<=57343||t===65279)continue;l[g++]=t}else if((248&a)==240){if(r>=c)return this.interim[0]=a,g;if(f=o[r++],(192&f)!=128){r--;continue}if(r>=c)return this.interim[0]=a,this.interim[1]=f,g;if(p=o[r++],(192&p)!=128){r--;continue}if(r>=c)return this.interim[0]=a,this.interim[1]=f,this.interim[2]=p,g;if(v=o[r++],(192&v)!=128){r--;continue}if(t=(7&a)<<18|(63&f)<<12|(63&p)<<6|63&v,t<65536||t>1114111)continue;l[g++]=t}}return g}}},225:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeV6=void 0;const o=[[768,879],[1155,1158],[1160,1161],[1425,1469],[1471,1471],[1473,1474],[1476,1477],[1479,1479],[1536,1539],[1552,1557],[1611,1630],[1648,1648],[1750,1764],[1767,1768],[1770,1773],[1807,1807],[1809,1809],[1840,1866],[1958,1968],[2027,2035],[2305,2306],[2364,2364],[2369,2376],[2381,2381],[2385,2388],[2402,2403],[2433,2433],[2492,2492],[2497,2500],[2509,2509],[2530,2531],[2561,2562],[2620,2620],[2625,2626],[2631,2632],[2635,2637],[2672,2673],[2689,2690],[2748,2748],[2753,2757],[2759,2760],[2765,2765],[2786,2787],[2817,2817],[2876,2876],[2879,2879],[2881,2883],[2893,2893],[2902,2902],[2946,2946],[3008,3008],[3021,3021],[3134,3136],[3142,3144],[3146,3149],[3157,3158],[3260,3260],[3263,3263],[3270,3270],[3276,3277],[3298,3299],[3393,3395],[3405,3405],[3530,3530],[3538,3540],[3542,3542],[3633,3633],[3636,3642],[3655,3662],[3761,3761],[3764,3769],[3771,3772],[3784,3789],[3864,3865],[3893,3893],[3895,3895],[3897,3897],[3953,3966],[3968,3972],[3974,3975],[3984,3991],[3993,4028],[4038,4038],[4141,4144],[4146,4146],[4150,4151],[4153,4153],[4184,4185],[4448,4607],[4959,4959],[5906,5908],[5938,5940],[5970,5971],[6002,6003],[6068,6069],[6071,6077],[6086,6086],[6089,6099],[6109,6109],[6155,6157],[6313,6313],[6432,6434],[6439,6440],[6450,6450],[6457,6459],[6679,6680],[6912,6915],[6964,6964],[6966,6970],[6972,6972],[6978,6978],[7019,7027],[7616,7626],[7678,7679],[8203,8207],[8234,8238],[8288,8291],[8298,8303],[8400,8431],[12330,12335],[12441,12442],[43014,43014],[43019,43019],[43045,43046],[64286,64286],[65024,65039],[65056,65059],[65279,65279],[65529,65531]],l=[[68097,68099],[68101,68102],[68108,68111],[68152,68154],[68159,68159],[119143,119145],[119155,119170],[119173,119179],[119210,119213],[119362,119364],[917505,917505],[917536,917631],[917760,917999]];let c;s.UnicodeV6=class{constructor(){if(this.version="6",!c){c=new Uint8Array(65536),c.fill(1),c[0]=0,c.fill(0,1,32),c.fill(0,127,160),c.fill(2,4352,4448),c[9001]=2,c[9002]=2,c.fill(2,11904,42192),c[12351]=1,c.fill(2,44032,55204),c.fill(2,63744,64256),c.fill(2,65040,65050),c.fill(2,65072,65136),c.fill(2,65280,65377),c.fill(2,65504,65511);for(let a=0;a<o.length;++a)c.fill(0,o[a][0],o[a][1]+1)}}wcwidth(a){return a<32?0:a<127?1:a<65536?c[a]:function(f,p){let v,g=0,t=p.length-1;if(f<p[0][0]||f>p[t][1])return!1;for(;t>=g;)if(v=g+t>>1,f>p[v][1])g=v+1;else{if(!(f<p[v][0]))return!0;t=v-1}return!1}(a,l)?0:a>=131072&&a<=196605||a>=196608&&a<=262141?2:1}}},5981:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.WriteBuffer=void 0;const l=o(8460),c=o(844);class a extends c.Disposable{constructor(p){super(),this._action=p,this._writeBuffer=[],this._callbacks=[],this._pendingData=0,this._bufferOffset=0,this._isSyncWriting=!1,this._syncCalls=0,this._didUserInput=!1,this._onWriteParsed=this.register(new l.EventEmitter),this.onWriteParsed=this._onWriteParsed.event}handleUserInput(){this._didUserInput=!0}writeSync(p,v){if(v!==void 0&&this._syncCalls>v)return void(this._syncCalls=0);if(this._pendingData+=p.length,this._writeBuffer.push(p),this._callbacks.push(void 0),this._syncCalls++,this._isSyncWriting)return;let g;for(this._isSyncWriting=!0;g=this._writeBuffer.shift();){this._action(g);const t=this._callbacks.shift();t&&t()}this._pendingData=0,this._bufferOffset=2147483647,this._isSyncWriting=!1,this._syncCalls=0}write(p,v){if(this._pendingData>5e7)throw new Error("write data discarded, use flow control to avoid losing data");if(!this._writeBuffer.length){if(this._bufferOffset=0,this._didUserInput)return this._didUserInput=!1,this._pendingData+=p.length,this._writeBuffer.push(p),this._callbacks.push(v),void this._innerWrite();setTimeout(()=>this._innerWrite())}this._pendingData+=p.length,this._writeBuffer.push(p),this._callbacks.push(v)}_innerWrite(p=0,v=!0){const g=p||Date.now();for(;this._writeBuffer.length>this._bufferOffset;){const t=this._writeBuffer[this._bufferOffset],n=this._action(t,v);if(n){const r=h=>Date.now()-g>=12?setTimeout(()=>this._innerWrite(0,h)):this._innerWrite(g,h);return void n.catch(h=>(queueMicrotask(()=>{throw h}),Promise.resolve(!1))).then(r)}const i=this._callbacks[this._bufferOffset];if(i&&i(),this._bufferOffset++,this._pendingData-=t.length,Date.now()-g>=12)break}this._writeBuffer.length>this._bufferOffset?(this._bufferOffset>50&&(this._writeBuffer=this._writeBuffer.slice(this._bufferOffset),this._callbacks=this._callbacks.slice(this._bufferOffset),this._bufferOffset=0),setTimeout(()=>this._innerWrite())):(this._writeBuffer.length=0,this._callbacks.length=0,this._pendingData=0,this._bufferOffset=0),this._onWriteParsed.fire()}}s.WriteBuffer=a},5941:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.toRgbString=s.parseColor=void 0;const o=/^([\da-f])\/([\da-f])\/([\da-f])$|^([\da-f]{2})\/([\da-f]{2})\/([\da-f]{2})$|^([\da-f]{3})\/([\da-f]{3})\/([\da-f]{3})$|^([\da-f]{4})\/([\da-f]{4})\/([\da-f]{4})$/,l=/^[\da-f]+$/;function c(a,f){const p=a.toString(16),v=p.length<2?"0"+p:p;switch(f){case 4:return p[0];case 8:return v;case 12:return(v+v).slice(0,3);default:return v+v}}s.parseColor=function(a){if(!a)return;let f=a.toLowerCase();if(f.indexOf("rgb:")===0){f=f.slice(4);const p=o.exec(f);if(p){const v=p[1]?15:p[4]?255:p[7]?4095:65535;return[Math.round(parseInt(p[1]||p[4]||p[7]||p[10],16)/v*255),Math.round(parseInt(p[2]||p[5]||p[8]||p[11],16)/v*255),Math.round(parseInt(p[3]||p[6]||p[9]||p[12],16)/v*255)]}}else if(f.indexOf("#")===0&&(f=f.slice(1),l.exec(f)&&[3,6,9,12].includes(f.length))){const p=f.length/3,v=[0,0,0];for(let g=0;g<3;++g){const t=parseInt(f.slice(p*g,p*g+p),16);v[g]=p===1?t<<4:p===2?t:p===3?t>>4:t>>8}return v}},s.toRgbString=function(a,f=16){const[p,v,g]=a;return`rgb:${c(p,f)}/${c(v,f)}/${c(g,f)}`}},5770:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.PAYLOAD_LIMIT=void 0,s.PAYLOAD_LIMIT=1e7},6351:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DcsHandler=s.DcsParser=void 0;const l=o(482),c=o(8742),a=o(5770),f=[];s.DcsParser=class{constructor(){this._handlers=Object.create(null),this._active=f,this._ident=0,this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=f}registerHandler(v,g){this._handlers[v]===void 0&&(this._handlers[v]=[]);const t=this._handlers[v];return t.push(g),{dispose:()=>{const n=t.indexOf(g);n!==-1&&t.splice(n,1)}}}clearHandler(v){this._handlers[v]&&delete this._handlers[v]}setHandlerFallback(v){this._handlerFb=v}reset(){if(this._active.length)for(let v=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;v>=0;--v)this._active[v].unhook(!1);this._stack.paused=!1,this._active=f,this._ident=0}hook(v,g){if(this.reset(),this._ident=v,this._active=this._handlers[v]||f,this._active.length)for(let t=this._active.length-1;t>=0;t--)this._active[t].hook(g);else this._handlerFb(this._ident,"HOOK",g)}put(v,g,t){if(this._active.length)for(let n=this._active.length-1;n>=0;n--)this._active[n].put(v,g,t);else this._handlerFb(this._ident,"PUT",(0,l.utf32ToString)(v,g,t))}unhook(v,g=!0){if(this._active.length){let t=!1,n=this._active.length-1,i=!1;if(this._stack.paused&&(n=this._stack.loopPosition-1,t=g,i=this._stack.fallThrough,this._stack.paused=!1),!i&&t===!1){for(;n>=0&&(t=this._active[n].unhook(v),t!==!0);n--)if(t instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=n,this._stack.fallThrough=!1,t;n--}for(;n>=0;n--)if(t=this._active[n].unhook(!1),t instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=n,this._stack.fallThrough=!0,t}else this._handlerFb(this._ident,"UNHOOK",v);this._active=f,this._ident=0}};const p=new c.Params;p.addParam(0),s.DcsHandler=class{constructor(v){this._handler=v,this._data="",this._params=p,this._hitLimit=!1}hook(v){this._params=v.length>1||v.params[0]?v.clone():p,this._data="",this._hitLimit=!1}put(v,g,t){this._hitLimit||(this._data+=(0,l.utf32ToString)(v,g,t),this._data.length>a.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}unhook(v){let g=!1;if(this._hitLimit)g=!1;else if(v&&(g=this._handler(this._data,this._params),g instanceof Promise))return g.then(t=>(this._params=p,this._data="",this._hitLimit=!1,t));return this._params=p,this._data="",this._hitLimit=!1,g}}},2015:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.EscapeSequenceParser=s.VT500_TRANSITION_TABLE=s.TransitionTable=void 0;const l=o(844),c=o(8742),a=o(6242),f=o(6351);class p{constructor(n){this.table=new Uint8Array(n)}setDefault(n,i){this.table.fill(n<<4|i)}add(n,i,r,h){this.table[i<<8|n]=r<<4|h}addMany(n,i,r,h){for(let _=0;_<n.length;_++)this.table[i<<8|n[_]]=r<<4|h}}s.TransitionTable=p;const v=160;s.VT500_TRANSITION_TABLE=function(){const t=new p(4095),n=Array.apply(null,Array(256)).map((y,u)=>u),i=(y,u)=>n.slice(y,u),r=i(32,127),h=i(0,24);h.push(25),h.push.apply(h,i(28,32));const _=i(0,14);let S;for(S in t.setDefault(1,0),t.addMany(r,0,2,0),_)t.addMany([24,26,153,154],S,3,0),t.addMany(i(128,144),S,3,0),t.addMany(i(144,152),S,3,0),t.add(156,S,0,0),t.add(27,S,11,1),t.add(157,S,4,8),t.addMany([152,158,159],S,0,7),t.add(155,S,11,3),t.add(144,S,11,9);return t.addMany(h,0,3,0),t.addMany(h,1,3,1),t.add(127,1,0,1),t.addMany(h,8,0,8),t.addMany(h,3,3,3),t.add(127,3,0,3),t.addMany(h,4,3,4),t.add(127,4,0,4),t.addMany(h,6,3,6),t.addMany(h,5,3,5),t.add(127,5,0,5),t.addMany(h,2,3,2),t.add(127,2,0,2),t.add(93,1,4,8),t.addMany(r,8,5,8),t.add(127,8,5,8),t.addMany([156,27,24,26,7],8,6,0),t.addMany(i(28,32),8,0,8),t.addMany([88,94,95],1,0,7),t.addMany(r,7,0,7),t.addMany(h,7,0,7),t.add(156,7,0,0),t.add(127,7,0,7),t.add(91,1,11,3),t.addMany(i(64,127),3,7,0),t.addMany(i(48,60),3,8,4),t.addMany([60,61,62,63],3,9,4),t.addMany(i(48,60),4,8,4),t.addMany(i(64,127),4,7,0),t.addMany([60,61,62,63],4,0,6),t.addMany(i(32,64),6,0,6),t.add(127,6,0,6),t.addMany(i(64,127),6,0,0),t.addMany(i(32,48),3,9,5),t.addMany(i(32,48),5,9,5),t.addMany(i(48,64),5,0,6),t.addMany(i(64,127),5,7,0),t.addMany(i(32,48),4,9,5),t.addMany(i(32,48),1,9,2),t.addMany(i(32,48),2,9,2),t.addMany(i(48,127),2,10,0),t.addMany(i(48,80),1,10,0),t.addMany(i(81,88),1,10,0),t.addMany([89,90,92],1,10,0),t.addMany(i(96,127),1,10,0),t.add(80,1,11,9),t.addMany(h,9,0,9),t.add(127,9,0,9),t.addMany(i(28,32),9,0,9),t.addMany(i(32,48),9,9,12),t.addMany(i(48,60),9,8,10),t.addMany([60,61,62,63],9,9,10),t.addMany(h,11,0,11),t.addMany(i(32,128),11,0,11),t.addMany(i(28,32),11,0,11),t.addMany(h,10,0,10),t.add(127,10,0,10),t.addMany(i(28,32),10,0,10),t.addMany(i(48,60),10,8,10),t.addMany([60,61,62,63],10,0,11),t.addMany(i(32,48),10,9,12),t.addMany(h,12,0,12),t.add(127,12,0,12),t.addMany(i(28,32),12,0,12),t.addMany(i(32,48),12,9,12),t.addMany(i(48,64),12,0,11),t.addMany(i(64,127),12,12,13),t.addMany(i(64,127),10,12,13),t.addMany(i(64,127),9,12,13),t.addMany(h,13,13,13),t.addMany(r,13,13,13),t.add(127,13,0,13),t.addMany([27,156,24,26],13,14,0),t.add(v,0,2,0),t.add(v,8,5,8),t.add(v,6,0,6),t.add(v,11,0,11),t.add(v,13,13,13),t}();class g extends l.Disposable{constructor(n=s.VT500_TRANSITION_TABLE){super(),this._transitions=n,this._parseStack={state:0,handlers:[],handlerPos:0,transition:0,chunkPos:0},this.initialState=0,this.currentState=this.initialState,this._params=new c.Params,this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._printHandlerFb=(i,r,h)=>{},this._executeHandlerFb=i=>{},this._csiHandlerFb=(i,r)=>{},this._escHandlerFb=i=>{},this._errorHandlerFb=i=>i,this._printHandler=this._printHandlerFb,this._executeHandlers=Object.create(null),this._csiHandlers=Object.create(null),this._escHandlers=Object.create(null),this.register((0,l.toDisposable)(()=>{this._csiHandlers=Object.create(null),this._executeHandlers=Object.create(null),this._escHandlers=Object.create(null)})),this._oscParser=this.register(new a.OscParser),this._dcsParser=this.register(new f.DcsParser),this._errorHandler=this._errorHandlerFb,this.registerEscHandler({final:"\\"},()=>!0)}_identifier(n,i=[64,126]){let r=0;if(n.prefix){if(n.prefix.length>1)throw new Error("only one byte as prefix supported");if(r=n.prefix.charCodeAt(0),r&&60>r||r>63)throw new Error("prefix must be in range 0x3c .. 0x3f")}if(n.intermediates){if(n.intermediates.length>2)throw new Error("only two bytes as intermediates are supported");for(let _=0;_<n.intermediates.length;++_){const S=n.intermediates.charCodeAt(_);if(32>S||S>47)throw new Error("intermediate must be in range 0x20 .. 0x2f");r<<=8,r|=S}}if(n.final.length!==1)throw new Error("final must be a single byte");const h=n.final.charCodeAt(0);if(i[0]>h||h>i[1])throw new Error(`final must be in range ${i[0]} .. ${i[1]}`);return r<<=8,r|=h,r}identToString(n){const i=[];for(;n;)i.push(String.fromCharCode(255&n)),n>>=8;return i.reverse().join("")}setPrintHandler(n){this._printHandler=n}clearPrintHandler(){this._printHandler=this._printHandlerFb}registerEscHandler(n,i){const r=this._identifier(n,[48,126]);this._escHandlers[r]===void 0&&(this._escHandlers[r]=[]);const h=this._escHandlers[r];return h.push(i),{dispose:()=>{const _=h.indexOf(i);_!==-1&&h.splice(_,1)}}}clearEscHandler(n){this._escHandlers[this._identifier(n,[48,126])]&&delete this._escHandlers[this._identifier(n,[48,126])]}setEscHandlerFallback(n){this._escHandlerFb=n}setExecuteHandler(n,i){this._executeHandlers[n.charCodeAt(0)]=i}clearExecuteHandler(n){this._executeHandlers[n.charCodeAt(0)]&&delete this._executeHandlers[n.charCodeAt(0)]}setExecuteHandlerFallback(n){this._executeHandlerFb=n}registerCsiHandler(n,i){const r=this._identifier(n);this._csiHandlers[r]===void 0&&(this._csiHandlers[r]=[]);const h=this._csiHandlers[r];return h.push(i),{dispose:()=>{const _=h.indexOf(i);_!==-1&&h.splice(_,1)}}}clearCsiHandler(n){this._csiHandlers[this._identifier(n)]&&delete this._csiHandlers[this._identifier(n)]}setCsiHandlerFallback(n){this._csiHandlerFb=n}registerDcsHandler(n,i){return this._dcsParser.registerHandler(this._identifier(n),i)}clearDcsHandler(n){this._dcsParser.clearHandler(this._identifier(n))}setDcsHandlerFallback(n){this._dcsParser.setHandlerFallback(n)}registerOscHandler(n,i){return this._oscParser.registerHandler(n,i)}clearOscHandler(n){this._oscParser.clearHandler(n)}setOscHandlerFallback(n){this._oscParser.setHandlerFallback(n)}setErrorHandler(n){this._errorHandler=n}clearErrorHandler(){this._errorHandler=this._errorHandlerFb}reset(){this.currentState=this.initialState,this._oscParser.reset(),this._dcsParser.reset(),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0,this._parseStack.state!==0&&(this._parseStack.state=2,this._parseStack.handlers=[])}_preserveStack(n,i,r,h,_){this._parseStack.state=n,this._parseStack.handlers=i,this._parseStack.handlerPos=r,this._parseStack.transition=h,this._parseStack.chunkPos=_}parse(n,i,r){let h,_=0,S=0,y=0;if(this._parseStack.state)if(this._parseStack.state===2)this._parseStack.state=0,y=this._parseStack.chunkPos+1;else{if(r===void 0||this._parseStack.state===1)throw this._parseStack.state=1,new Error("improper continuation due to previous async handler, giving up parsing");const u=this._parseStack.handlers;let C=this._parseStack.handlerPos-1;switch(this._parseStack.state){case 3:if(r===!1&&C>-1){for(;C>=0&&(h=u[C](this._params),h!==!0);C--)if(h instanceof Promise)return this._parseStack.handlerPos=C,h}this._parseStack.handlers=[];break;case 4:if(r===!1&&C>-1){for(;C>=0&&(h=u[C](),h!==!0);C--)if(h instanceof Promise)return this._parseStack.handlerPos=C,h}this._parseStack.handlers=[];break;case 6:if(_=n[this._parseStack.chunkPos],h=this._dcsParser.unhook(_!==24&&_!==26,r),h)return h;_===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0;break;case 5:if(_=n[this._parseStack.chunkPos],h=this._oscParser.end(_!==24&&_!==26,r),h)return h;_===27&&(this._parseStack.transition|=1),this._params.reset(),this._params.addParam(0),this._collect=0}this._parseStack.state=0,y=this._parseStack.chunkPos+1,this.precedingCodepoint=0,this.currentState=15&this._parseStack.transition}for(let u=y;u<i;++u){switch(_=n[u],S=this._transitions.table[this.currentState<<8|(_<160?_:v)],S>>4){case 2:for(let B=u+1;;++B){if(B>=i||(_=n[B])<32||_>126&&_<v){this._printHandler(n,u,B),u=B-1;break}if(++B>=i||(_=n[B])<32||_>126&&_<v){this._printHandler(n,u,B),u=B-1;break}if(++B>=i||(_=n[B])<32||_>126&&_<v){this._printHandler(n,u,B),u=B-1;break}if(++B>=i||(_=n[B])<32||_>126&&_<v){this._printHandler(n,u,B),u=B-1;break}}break;case 3:this._executeHandlers[_]?this._executeHandlers[_]():this._executeHandlerFb(_),this.precedingCodepoint=0;break;case 0:break;case 1:if(this._errorHandler({position:u,code:_,currentState:this.currentState,collect:this._collect,params:this._params,abort:!1}).abort)return;break;case 7:const C=this._csiHandlers[this._collect<<8|_];let L=C?C.length-1:-1;for(;L>=0&&(h=C[L](this._params),h!==!0);L--)if(h instanceof Promise)return this._preserveStack(3,C,L,S,u),h;L<0&&this._csiHandlerFb(this._collect<<8|_,this._params),this.precedingCodepoint=0;break;case 8:do switch(_){case 59:this._params.addParam(0);break;case 58:this._params.addSubParam(-1);break;default:this._params.addDigit(_-48)}while(++u<i&&(_=n[u])>47&&_<60);u--;break;case 9:this._collect<<=8,this._collect|=_;break;case 10:const I=this._escHandlers[this._collect<<8|_];let R=I?I.length-1:-1;for(;R>=0&&(h=I[R](),h!==!0);R--)if(h instanceof Promise)return this._preserveStack(4,I,R,S,u),h;R<0&&this._escHandlerFb(this._collect<<8|_),this.precedingCodepoint=0;break;case 11:this._params.reset(),this._params.addParam(0),this._collect=0;break;case 12:this._dcsParser.hook(this._collect<<8|_,this._params);break;case 13:for(let B=u+1;;++B)if(B>=i||(_=n[B])===24||_===26||_===27||_>127&&_<v){this._dcsParser.put(n,u,B),u=B-1;break}break;case 14:if(h=this._dcsParser.unhook(_!==24&&_!==26),h)return this._preserveStack(6,[],0,S,u),h;_===27&&(S|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0;break;case 4:this._oscParser.start();break;case 5:for(let B=u+1;;B++)if(B>=i||(_=n[B])<32||_>127&&_<v){this._oscParser.put(n,u,B),u=B-1;break}break;case 6:if(h=this._oscParser.end(_!==24&&_!==26),h)return this._preserveStack(5,[],0,S,u),h;_===27&&(S|=1),this._params.reset(),this._params.addParam(0),this._collect=0,this.precedingCodepoint=0}this.currentState=15&S}}}s.EscapeSequenceParser=g},6242:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.OscHandler=s.OscParser=void 0;const l=o(5770),c=o(482),a=[];s.OscParser=class{constructor(){this._state=0,this._active=a,this._id=-1,this._handlers=Object.create(null),this._handlerFb=()=>{},this._stack={paused:!1,loopPosition:0,fallThrough:!1}}registerHandler(f,p){this._handlers[f]===void 0&&(this._handlers[f]=[]);const v=this._handlers[f];return v.push(p),{dispose:()=>{const g=v.indexOf(p);g!==-1&&v.splice(g,1)}}}clearHandler(f){this._handlers[f]&&delete this._handlers[f]}setHandlerFallback(f){this._handlerFb=f}dispose(){this._handlers=Object.create(null),this._handlerFb=()=>{},this._active=a}reset(){if(this._state===2)for(let f=this._stack.paused?this._stack.loopPosition-1:this._active.length-1;f>=0;--f)this._active[f].end(!1);this._stack.paused=!1,this._active=a,this._id=-1,this._state=0}_start(){if(this._active=this._handlers[this._id]||a,this._active.length)for(let f=this._active.length-1;f>=0;f--)this._active[f].start();else this._handlerFb(this._id,"START")}_put(f,p,v){if(this._active.length)for(let g=this._active.length-1;g>=0;g--)this._active[g].put(f,p,v);else this._handlerFb(this._id,"PUT",(0,c.utf32ToString)(f,p,v))}start(){this.reset(),this._state=1}put(f,p,v){if(this._state!==3){if(this._state===1)for(;p<v;){const g=f[p++];if(g===59){this._state=2,this._start();break}if(g<48||57<g)return void(this._state=3);this._id===-1&&(this._id=0),this._id=10*this._id+g-48}this._state===2&&v-p>0&&this._put(f,p,v)}}end(f,p=!0){if(this._state!==0){if(this._state!==3)if(this._state===1&&this._start(),this._active.length){let v=!1,g=this._active.length-1,t=!1;if(this._stack.paused&&(g=this._stack.loopPosition-1,v=p,t=this._stack.fallThrough,this._stack.paused=!1),!t&&v===!1){for(;g>=0&&(v=this._active[g].end(f),v!==!0);g--)if(v instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=g,this._stack.fallThrough=!1,v;g--}for(;g>=0;g--)if(v=this._active[g].end(!1),v instanceof Promise)return this._stack.paused=!0,this._stack.loopPosition=g,this._stack.fallThrough=!0,v}else this._handlerFb(this._id,"END",f);this._active=a,this._id=-1,this._state=0}}},s.OscHandler=class{constructor(f){this._handler=f,this._data="",this._hitLimit=!1}start(){this._data="",this._hitLimit=!1}put(f,p,v){this._hitLimit||(this._data+=(0,c.utf32ToString)(f,p,v),this._data.length>l.PAYLOAD_LIMIT&&(this._data="",this._hitLimit=!0))}end(f){let p=!1;if(this._hitLimit)p=!1;else if(f&&(p=this._handler(this._data),p instanceof Promise))return p.then(v=>(this._data="",this._hitLimit=!1,v));return this._data="",this._hitLimit=!1,p}}},8742:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.Params=void 0;const o=2147483647;class l{static fromArray(a){const f=new l;if(!a.length)return f;for(let p=Array.isArray(a[0])?1:0;p<a.length;++p){const v=a[p];if(Array.isArray(v))for(let g=0;g<v.length;++g)f.addSubParam(v[g]);else f.addParam(v)}return f}constructor(a=32,f=32){if(this.maxLength=a,this.maxSubParamsLength=f,f>256)throw new Error("maxSubParamsLength must not be greater than 256");this.params=new Int32Array(a),this.length=0,this._subParams=new Int32Array(f),this._subParamsLength=0,this._subParamsIdx=new Uint16Array(a),this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}clone(){const a=new l(this.maxLength,this.maxSubParamsLength);return a.params.set(this.params),a.length=this.length,a._subParams.set(this._subParams),a._subParamsLength=this._subParamsLength,a._subParamsIdx.set(this._subParamsIdx),a._rejectDigits=this._rejectDigits,a._rejectSubDigits=this._rejectSubDigits,a._digitIsSub=this._digitIsSub,a}toArray(){const a=[];for(let f=0;f<this.length;++f){a.push(this.params[f]);const p=this._subParamsIdx[f]>>8,v=255&this._subParamsIdx[f];v-p>0&&a.push(Array.prototype.slice.call(this._subParams,p,v))}return a}reset(){this.length=0,this._subParamsLength=0,this._rejectDigits=!1,this._rejectSubDigits=!1,this._digitIsSub=!1}addParam(a){if(this._digitIsSub=!1,this.length>=this.maxLength)this._rejectDigits=!0;else{if(a<-1)throw new Error("values lesser than -1 are not allowed");this._subParamsIdx[this.length]=this._subParamsLength<<8|this._subParamsLength,this.params[this.length++]=a>o?o:a}}addSubParam(a){if(this._digitIsSub=!0,this.length)if(this._rejectDigits||this._subParamsLength>=this.maxSubParamsLength)this._rejectSubDigits=!0;else{if(a<-1)throw new Error("values lesser than -1 are not allowed");this._subParams[this._subParamsLength++]=a>o?o:a,this._subParamsIdx[this.length-1]++}}hasSubParams(a){return(255&this._subParamsIdx[a])-(this._subParamsIdx[a]>>8)>0}getSubParams(a){const f=this._subParamsIdx[a]>>8,p=255&this._subParamsIdx[a];return p-f>0?this._subParams.subarray(f,p):null}getSubParamsAll(){const a={};for(let f=0;f<this.length;++f){const p=this._subParamsIdx[f]>>8,v=255&this._subParamsIdx[f];v-p>0&&(a[f]=this._subParams.slice(p,v))}return a}addDigit(a){let f;if(this._rejectDigits||!(f=this._digitIsSub?this._subParamsLength:this.length)||this._digitIsSub&&this._rejectSubDigits)return;const p=this._digitIsSub?this._subParams:this.params,v=p[f-1];p[f-1]=~v?Math.min(10*v+a,o):a}}s.Params=l},5741:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.AddonManager=void 0,s.AddonManager=class{constructor(){this._addons=[]}dispose(){for(let o=this._addons.length-1;o>=0;o--)this._addons[o].instance.dispose()}loadAddon(o,l){const c={instance:l,dispose:l.dispose,isDisposed:!1};this._addons.push(c),l.dispose=()=>this._wrappedAddonDispose(c),l.activate(o)}_wrappedAddonDispose(o){if(o.isDisposed)return;let l=-1;for(let c=0;c<this._addons.length;c++)if(this._addons[c]===o){l=c;break}if(l===-1)throw new Error("Could not dispose an addon that has not been loaded");o.isDisposed=!0,o.dispose.apply(o.instance),this._addons.splice(l,1)}}},8771:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferApiView=void 0;const l=o(3785),c=o(511);s.BufferApiView=class{constructor(a,f){this._buffer=a,this.type=f}init(a){return this._buffer=a,this}get cursorY(){return this._buffer.y}get cursorX(){return this._buffer.x}get viewportY(){return this._buffer.ydisp}get baseY(){return this._buffer.ybase}get length(){return this._buffer.lines.length}getLine(a){const f=this._buffer.lines.get(a);if(f)return new l.BufferLineApiView(f)}getNullCell(){return new c.CellData}}},3785:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferLineApiView=void 0;const l=o(511);s.BufferLineApiView=class{constructor(c){this._line=c}get isWrapped(){return this._line.isWrapped}get length(){return this._line.length}getCell(c,a){if(!(c<0||c>=this._line.length))return a?(this._line.loadCell(c,a),a):this._line.loadCell(c,new l.CellData)}translateToString(c,a,f){return this._line.translateToString(c,a,f)}}},8285:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.BufferNamespaceApi=void 0;const l=o(8771),c=o(8460),a=o(844);class f extends a.Disposable{constructor(v){super(),this._core=v,this._onBufferChange=this.register(new c.EventEmitter),this.onBufferChange=this._onBufferChange.event,this._normal=new l.BufferApiView(this._core.buffers.normal,"normal"),this._alternate=new l.BufferApiView(this._core.buffers.alt,"alternate"),this._core.buffers.onBufferActivate(()=>this._onBufferChange.fire(this.active))}get active(){if(this._core.buffers.active===this._core.buffers.normal)return this.normal;if(this._core.buffers.active===this._core.buffers.alt)return this.alternate;throw new Error("Active buffer is neither normal nor alternate")}get normal(){return this._normal.init(this._core.buffers.normal)}get alternate(){return this._alternate.init(this._core.buffers.alt)}}s.BufferNamespaceApi=f},7975:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.ParserApi=void 0,s.ParserApi=class{constructor(o){this._core=o}registerCsiHandler(o,l){return this._core.registerCsiHandler(o,c=>l(c.toArray()))}addCsiHandler(o,l){return this.registerCsiHandler(o,l)}registerDcsHandler(o,l){return this._core.registerDcsHandler(o,(c,a)=>l(c,a.toArray()))}addDcsHandler(o,l){return this.registerDcsHandler(o,l)}registerEscHandler(o,l){return this._core.registerEscHandler(o,l)}addEscHandler(o,l){return this.registerEscHandler(o,l)}registerOscHandler(o,l){return this._core.registerOscHandler(o,l)}addOscHandler(o,l){return this.registerOscHandler(o,l)}}},7090:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeApi=void 0,s.UnicodeApi=class{constructor(o){this._core=o}register(o){this._core.unicodeService.register(o)}get versions(){return this._core.unicodeService.versions}get activeVersion(){return this._core.unicodeService.activeVersion}set activeVersion(o){this._core.unicodeService.activeVersion=o}}},744:function(x,s,o){var l=this&&this.__decorate||function(t,n,i,r){var h,_=arguments.length,S=_<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(t,n,i,r);else for(var y=t.length-1;y>=0;y--)(h=t[y])&&(S=(_<3?h(S):_>3?h(n,i,S):h(n,i))||S);return _>3&&S&&Object.defineProperty(n,i,S),S},c=this&&this.__param||function(t,n){return function(i,r){n(i,r,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.BufferService=s.MINIMUM_ROWS=s.MINIMUM_COLS=void 0;const a=o(8460),f=o(844),p=o(5295),v=o(2585);s.MINIMUM_COLS=2,s.MINIMUM_ROWS=1;let g=s.BufferService=class extends f.Disposable{get buffer(){return this.buffers.active}constructor(t){super(),this.isUserScrolling=!1,this._onResize=this.register(new a.EventEmitter),this.onResize=this._onResize.event,this._onScroll=this.register(new a.EventEmitter),this.onScroll=this._onScroll.event,this.cols=Math.max(t.rawOptions.cols||0,s.MINIMUM_COLS),this.rows=Math.max(t.rawOptions.rows||0,s.MINIMUM_ROWS),this.buffers=this.register(new p.BufferSet(t,this))}resize(t,n){this.cols=t,this.rows=n,this.buffers.resize(t,n),this._onResize.fire({cols:t,rows:n})}reset(){this.buffers.reset(),this.isUserScrolling=!1}scroll(t,n=!1){const i=this.buffer;let r;r=this._cachedBlankLine,r&&r.length===this.cols&&r.getFg(0)===t.fg&&r.getBg(0)===t.bg||(r=i.getBlankLine(t,n),this._cachedBlankLine=r),r.isWrapped=n;const h=i.ybase+i.scrollTop,_=i.ybase+i.scrollBottom;if(i.scrollTop===0){const S=i.lines.isFull;_===i.lines.length-1?S?i.lines.recycle().copyFrom(r):i.lines.push(r.clone()):i.lines.splice(_+1,0,r.clone()),S?this.isUserScrolling&&(i.ydisp=Math.max(i.ydisp-1,0)):(i.ybase++,this.isUserScrolling||i.ydisp++)}else{const S=_-h+1;i.lines.shiftElements(h+1,S-1,-1),i.lines.set(_,r.clone())}this.isUserScrolling||(i.ydisp=i.ybase),this._onScroll.fire(i.ydisp)}scrollLines(t,n,i){const r=this.buffer;if(t<0){if(r.ydisp===0)return;this.isUserScrolling=!0}else t+r.ydisp>=r.ybase&&(this.isUserScrolling=!1);const h=r.ydisp;r.ydisp=Math.max(Math.min(r.ydisp+t,r.ybase),0),h!==r.ydisp&&(n||this._onScroll.fire(r.ydisp))}};s.BufferService=g=l([c(0,v.IOptionsService)],g)},7994:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.CharsetService=void 0,s.CharsetService=class{constructor(){this.glevel=0,this._charsets=[]}reset(){this.charset=void 0,this._charsets=[],this.glevel=0}setgLevel(o){this.glevel=o,this.charset=this._charsets[o]}setgCharset(o,l){this._charsets[o]=l,this.glevel===o&&(this.charset=l)}}},1753:function(x,s,o){var l=this&&this.__decorate||function(r,h,_,S){var y,u=arguments.length,C=u<3?h:S===null?S=Object.getOwnPropertyDescriptor(h,_):S;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")C=Reflect.decorate(r,h,_,S);else for(var L=r.length-1;L>=0;L--)(y=r[L])&&(C=(u<3?y(C):u>3?y(h,_,C):y(h,_))||C);return u>3&&C&&Object.defineProperty(h,_,C),C},c=this&&this.__param||function(r,h){return function(_,S){h(_,S,r)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CoreMouseService=void 0;const a=o(2585),f=o(8460),p=o(844),v={NONE:{events:0,restrict:()=>!1},X10:{events:1,restrict:r=>r.button!==4&&r.action===1&&(r.ctrl=!1,r.alt=!1,r.shift=!1,!0)},VT200:{events:19,restrict:r=>r.action!==32},DRAG:{events:23,restrict:r=>r.action!==32||r.button!==3},ANY:{events:31,restrict:r=>!0}};function g(r,h){let _=(r.ctrl?16:0)|(r.shift?4:0)|(r.alt?8:0);return r.button===4?(_|=64,_|=r.action):(_|=3&r.button,4&r.button&&(_|=64),8&r.button&&(_|=128),r.action===32?_|=32:r.action!==0||h||(_|=3)),_}const t=String.fromCharCode,n={DEFAULT:r=>{const h=[g(r,!1)+32,r.col+32,r.row+32];return h[0]>255||h[1]>255||h[2]>255?"":`\x1B[M${t(h[0])}${t(h[1])}${t(h[2])}`},SGR:r=>{const h=r.action===0&&r.button!==4?"m":"M";return`\x1B[<${g(r,!0)};${r.col};${r.row}${h}`},SGR_PIXELS:r=>{const h=r.action===0&&r.button!==4?"m":"M";return`\x1B[<${g(r,!0)};${r.x};${r.y}${h}`}};let i=s.CoreMouseService=class extends p.Disposable{constructor(r,h){super(),this._bufferService=r,this._coreService=h,this._protocols={},this._encodings={},this._activeProtocol="",this._activeEncoding="",this._lastEvent=null,this._onProtocolChange=this.register(new f.EventEmitter),this.onProtocolChange=this._onProtocolChange.event;for(const _ of Object.keys(v))this.addProtocol(_,v[_]);for(const _ of Object.keys(n))this.addEncoding(_,n[_]);this.reset()}addProtocol(r,h){this._protocols[r]=h}addEncoding(r,h){this._encodings[r]=h}get activeProtocol(){return this._activeProtocol}get areMouseEventsActive(){return this._protocols[this._activeProtocol].events!==0}set activeProtocol(r){if(!this._protocols[r])throw new Error(`unknown protocol "${r}"`);this._activeProtocol=r,this._onProtocolChange.fire(this._protocols[r].events)}get activeEncoding(){return this._activeEncoding}set activeEncoding(r){if(!this._encodings[r])throw new Error(`unknown encoding "${r}"`);this._activeEncoding=r}reset(){this.activeProtocol="NONE",this.activeEncoding="DEFAULT",this._lastEvent=null}triggerMouseEvent(r){if(r.col<0||r.col>=this._bufferService.cols||r.row<0||r.row>=this._bufferService.rows||r.button===4&&r.action===32||r.button===3&&r.action!==32||r.button!==4&&(r.action===2||r.action===3)||(r.col++,r.row++,r.action===32&&this._lastEvent&&this._equalEvents(this._lastEvent,r,this._activeEncoding==="SGR_PIXELS"))||!this._protocols[this._activeProtocol].restrict(r))return!1;const h=this._encodings[this._activeEncoding](r);return h&&(this._activeEncoding==="DEFAULT"?this._coreService.triggerBinaryEvent(h):this._coreService.triggerDataEvent(h,!0)),this._lastEvent=r,!0}explainEvents(r){return{down:!!(1&r),up:!!(2&r),drag:!!(4&r),move:!!(8&r),wheel:!!(16&r)}}_equalEvents(r,h,_){if(_){if(r.x!==h.x||r.y!==h.y)return!1}else if(r.col!==h.col||r.row!==h.row)return!1;return r.button===h.button&&r.action===h.action&&r.ctrl===h.ctrl&&r.alt===h.alt&&r.shift===h.shift}};s.CoreMouseService=i=l([c(0,a.IBufferService),c(1,a.ICoreService)],i)},6975:function(x,s,o){var l=this&&this.__decorate||function(i,r,h,_){var S,y=arguments.length,u=y<3?r:_===null?_=Object.getOwnPropertyDescriptor(r,h):_;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")u=Reflect.decorate(i,r,h,_);else for(var C=i.length-1;C>=0;C--)(S=i[C])&&(u=(y<3?S(u):y>3?S(r,h,u):S(r,h))||u);return y>3&&u&&Object.defineProperty(r,h,u),u},c=this&&this.__param||function(i,r){return function(h,_){r(h,_,i)}};Object.defineProperty(s,"__esModule",{value:!0}),s.CoreService=void 0;const a=o(1439),f=o(8460),p=o(844),v=o(2585),g=Object.freeze({insertMode:!1}),t=Object.freeze({applicationCursorKeys:!1,applicationKeypad:!1,bracketedPasteMode:!1,origin:!1,reverseWraparound:!1,sendFocus:!1,wraparound:!0});let n=s.CoreService=class extends p.Disposable{constructor(i,r,h){super(),this._bufferService=i,this._logService=r,this._optionsService=h,this.isCursorInitialized=!1,this.isCursorHidden=!1,this._onData=this.register(new f.EventEmitter),this.onData=this._onData.event,this._onUserInput=this.register(new f.EventEmitter),this.onUserInput=this._onUserInput.event,this._onBinary=this.register(new f.EventEmitter),this.onBinary=this._onBinary.event,this._onRequestScrollToBottom=this.register(new f.EventEmitter),this.onRequestScrollToBottom=this._onRequestScrollToBottom.event,this.modes=(0,a.clone)(g),this.decPrivateModes=(0,a.clone)(t)}reset(){this.modes=(0,a.clone)(g),this.decPrivateModes=(0,a.clone)(t)}triggerDataEvent(i,r=!1){if(this._optionsService.rawOptions.disableStdin)return;const h=this._bufferService.buffer;r&&this._optionsService.rawOptions.scrollOnUserInput&&h.ybase!==h.ydisp&&this._onRequestScrollToBottom.fire(),r&&this._onUserInput.fire(),this._logService.debug(`sending data "${i}"`,()=>i.split("").map(_=>_.charCodeAt(0))),this._onData.fire(i)}triggerBinaryEvent(i){this._optionsService.rawOptions.disableStdin||(this._logService.debug(`sending binary "${i}"`,()=>i.split("").map(r=>r.charCodeAt(0))),this._onBinary.fire(i))}};s.CoreService=n=l([c(0,v.IBufferService),c(1,v.ILogService),c(2,v.IOptionsService)],n)},9074:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.DecorationService=void 0;const l=o(8055),c=o(8460),a=o(844),f=o(6106);let p=0,v=0;class g extends a.Disposable{get decorations(){return this._decorations.values()}constructor(){super(),this._decorations=new f.SortedList(i=>i==null?void 0:i.marker.line),this._onDecorationRegistered=this.register(new c.EventEmitter),this.onDecorationRegistered=this._onDecorationRegistered.event,this._onDecorationRemoved=this.register(new c.EventEmitter),this.onDecorationRemoved=this._onDecorationRemoved.event,this.register((0,a.toDisposable)(()=>this.reset()))}registerDecoration(i){if(i.marker.isDisposed)return;const r=new t(i);if(r){const h=r.marker.onDispose(()=>r.dispose());r.onDispose(()=>{r&&(this._decorations.delete(r)&&this._onDecorationRemoved.fire(r),h.dispose())}),this._decorations.insert(r),this._onDecorationRegistered.fire(r)}return r}reset(){for(const i of this._decorations.values())i.dispose();this._decorations.clear()}*getDecorationsAtCell(i,r,h){var _,S,y;let u=0,C=0;for(const L of this._decorations.getKeyIterator(r))u=(_=L.options.x)!==null&&_!==void 0?_:0,C=u+((S=L.options.width)!==null&&S!==void 0?S:1),i>=u&&i<C&&(!h||((y=L.options.layer)!==null&&y!==void 0?y:"bottom")===h)&&(yield L)}forEachDecorationAtCell(i,r,h,_){this._decorations.forEachByKey(r,S=>{var y,u,C;p=(y=S.options.x)!==null&&y!==void 0?y:0,v=p+((u=S.options.width)!==null&&u!==void 0?u:1),i>=p&&i<v&&(!h||((C=S.options.layer)!==null&&C!==void 0?C:"bottom")===h)&&_(S)})}}s.DecorationService=g;class t extends a.Disposable{get isDisposed(){return this._isDisposed}get backgroundColorRGB(){return this._cachedBg===null&&(this.options.backgroundColor?this._cachedBg=l.css.toColor(this.options.backgroundColor):this._cachedBg=void 0),this._cachedBg}get foregroundColorRGB(){return this._cachedFg===null&&(this.options.foregroundColor?this._cachedFg=l.css.toColor(this.options.foregroundColor):this._cachedFg=void 0),this._cachedFg}constructor(i){super(),this.options=i,this.onRenderEmitter=this.register(new c.EventEmitter),this.onRender=this.onRenderEmitter.event,this._onDispose=this.register(new c.EventEmitter),this.onDispose=this._onDispose.event,this._cachedBg=null,this._cachedFg=null,this.marker=i.marker,this.options.overviewRulerOptions&&!this.options.overviewRulerOptions.position&&(this.options.overviewRulerOptions.position="full")}dispose(){this._onDispose.fire(),super.dispose()}}},4348:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.InstantiationService=s.ServiceCollection=void 0;const l=o(2585),c=o(8343);class a{constructor(...p){this._entries=new Map;for(const[v,g]of p)this.set(v,g)}set(p,v){const g=this._entries.get(p);return this._entries.set(p,v),g}forEach(p){for(const[v,g]of this._entries.entries())p(v,g)}has(p){return this._entries.has(p)}get(p){return this._entries.get(p)}}s.ServiceCollection=a,s.InstantiationService=class{constructor(){this._services=new a,this._services.set(l.IInstantiationService,this)}setService(f,p){this._services.set(f,p)}getService(f){return this._services.get(f)}createInstance(f,...p){const v=(0,c.getServiceDependencies)(f).sort((n,i)=>n.index-i.index),g=[];for(const n of v){const i=this._services.get(n.id);if(!i)throw new Error(`[createInstance] ${f.name} depends on UNKNOWN service ${n.id}.`);g.push(i)}const t=v.length>0?v[0].index:p.length;if(p.length!==t)throw new Error(`[createInstance] First service dependency of ${f.name} at position ${t+1} conflicts with ${p.length} static arguments`);return new f(...p,...g)}}},7866:function(x,s,o){var l=this&&this.__decorate||function(t,n,i,r){var h,_=arguments.length,S=_<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,i):r;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")S=Reflect.decorate(t,n,i,r);else for(var y=t.length-1;y>=0;y--)(h=t[y])&&(S=(_<3?h(S):_>3?h(n,i,S):h(n,i))||S);return _>3&&S&&Object.defineProperty(n,i,S),S},c=this&&this.__param||function(t,n){return function(i,r){n(i,r,t)}};Object.defineProperty(s,"__esModule",{value:!0}),s.traceCall=s.setTraceLogger=s.LogService=void 0;const a=o(844),f=o(2585),p={trace:f.LogLevelEnum.TRACE,debug:f.LogLevelEnum.DEBUG,info:f.LogLevelEnum.INFO,warn:f.LogLevelEnum.WARN,error:f.LogLevelEnum.ERROR,off:f.LogLevelEnum.OFF};let v,g=s.LogService=class extends a.Disposable{get logLevel(){return this._logLevel}constructor(t){super(),this._optionsService=t,this._logLevel=f.LogLevelEnum.OFF,this._updateLogLevel(),this.register(this._optionsService.onSpecificOptionChange("logLevel",()=>this._updateLogLevel())),v=this}_updateLogLevel(){this._logLevel=p[this._optionsService.rawOptions.logLevel]}_evalLazyOptionalParams(t){for(let n=0;n<t.length;n++)typeof t[n]=="function"&&(t[n]=t[n]())}_log(t,n,i){this._evalLazyOptionalParams(i),t.call(console,(this._optionsService.options.logger?"":"xterm.js: ")+n,...i)}trace(t,...n){var i,r;this._logLevel<=f.LogLevelEnum.TRACE&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.trace.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.log,t,n)}debug(t,...n){var i,r;this._logLevel<=f.LogLevelEnum.DEBUG&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.debug.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.log,t,n)}info(t,...n){var i,r;this._logLevel<=f.LogLevelEnum.INFO&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.info.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.info,t,n)}warn(t,...n){var i,r;this._logLevel<=f.LogLevelEnum.WARN&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.warn.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.warn,t,n)}error(t,...n){var i,r;this._logLevel<=f.LogLevelEnum.ERROR&&this._log((r=(i=this._optionsService.options.logger)===null||i===void 0?void 0:i.error.bind(this._optionsService.options.logger))!==null&&r!==void 0?r:console.error,t,n)}};s.LogService=g=l([c(0,f.IOptionsService)],g),s.setTraceLogger=function(t){v=t},s.traceCall=function(t,n,i){if(typeof i.value!="function")throw new Error("not supported");const r=i.value;i.value=function(...h){if(v.logLevel!==f.LogLevelEnum.TRACE)return r.apply(this,h);v.trace(`GlyphRenderer#${r.name}(${h.map(S=>JSON.stringify(S)).join(", ")})`);const _=r.apply(this,h);return v.trace(`GlyphRenderer#${r.name} return`,_),_}}},7302:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.OptionsService=s.DEFAULT_OPTIONS=void 0;const l=o(8460),c=o(844),a=o(6114);s.DEFAULT_OPTIONS={cols:80,rows:24,cursorBlink:!1,cursorStyle:"block",cursorWidth:1,cursorInactiveStyle:"outline",customGlyphs:!0,drawBoldTextInBrightColors:!0,fastScrollModifier:"alt",fastScrollSensitivity:5,fontFamily:"courier-new, courier, monospace",fontSize:15,fontWeight:"normal",fontWeightBold:"bold",ignoreBracketedPasteMode:!1,lineHeight:1,letterSpacing:0,linkHandler:null,logLevel:"info",logger:null,scrollback:1e3,scrollOnUserInput:!0,scrollSensitivity:1,screenReaderMode:!1,smoothScrollDuration:0,macOptionIsMeta:!1,macOptionClickForcesSelection:!1,minimumContrastRatio:1,disableStdin:!1,allowProposedApi:!1,allowTransparency:!1,tabStopWidth:8,theme:{},rightClickSelectsWord:a.isMac,windowOptions:{},windowsMode:!1,windowsPty:{},wordSeparator:" ()[]{}',\"`",altClickMovesCursor:!0,convertEol:!1,termName:"xterm",cancelEvents:!1,overviewRulerWidth:0};const f=["normal","bold","100","200","300","400","500","600","700","800","900"];class p extends c.Disposable{constructor(g){super(),this._onOptionChange=this.register(new l.EventEmitter),this.onOptionChange=this._onOptionChange.event;const t=Object.assign({},s.DEFAULT_OPTIONS);for(const n in g)if(n in t)try{const i=g[n];t[n]=this._sanitizeAndValidateOption(n,i)}catch(i){console.error(i)}this.rawOptions=t,this.options=Object.assign({},t),this._setupOptions()}onSpecificOptionChange(g,t){return this.onOptionChange(n=>{n===g&&t(this.rawOptions[g])})}onMultipleOptionChange(g,t){return this.onOptionChange(n=>{g.indexOf(n)!==-1&&t()})}_setupOptions(){const g=n=>{if(!(n in s.DEFAULT_OPTIONS))throw new Error(`No option with key "${n}"`);return this.rawOptions[n]},t=(n,i)=>{if(!(n in s.DEFAULT_OPTIONS))throw new Error(`No option with key "${n}"`);i=this._sanitizeAndValidateOption(n,i),this.rawOptions[n]!==i&&(this.rawOptions[n]=i,this._onOptionChange.fire(n))};for(const n in this.rawOptions){const i={get:g.bind(this,n),set:t.bind(this,n)};Object.defineProperty(this.options,n,i)}}_sanitizeAndValidateOption(g,t){switch(g){case"cursorStyle":if(t||(t=s.DEFAULT_OPTIONS[g]),!function(n){return n==="block"||n==="underline"||n==="bar"}(t))throw new Error(`"${t}" is not a valid value for ${g}`);break;case"wordSeparator":t||(t=s.DEFAULT_OPTIONS[g]);break;case"fontWeight":case"fontWeightBold":if(typeof t=="number"&&1<=t&&t<=1e3)break;t=f.includes(t)?t:s.DEFAULT_OPTIONS[g];break;case"cursorWidth":t=Math.floor(t);case"lineHeight":case"tabStopWidth":if(t<1)throw new Error(`${g} cannot be less than 1, value: ${t}`);break;case"minimumContrastRatio":t=Math.max(1,Math.min(21,Math.round(10*t)/10));break;case"scrollback":if((t=Math.min(t,4294967295))<0)throw new Error(`${g} cannot be less than 0, value: ${t}`);break;case"fastScrollSensitivity":case"scrollSensitivity":if(t<=0)throw new Error(`${g} cannot be less than or equal to 0, value: ${t}`);break;case"rows":case"cols":if(!t&&t!==0)throw new Error(`${g} must be numeric, value: ${t}`);break;case"windowsPty":t=t??{}}return t}}s.OptionsService=p},2660:function(x,s,o){var l=this&&this.__decorate||function(p,v,g,t){var n,i=arguments.length,r=i<3?v:t===null?t=Object.getOwnPropertyDescriptor(v,g):t;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(p,v,g,t);else for(var h=p.length-1;h>=0;h--)(n=p[h])&&(r=(i<3?n(r):i>3?n(v,g,r):n(v,g))||r);return i>3&&r&&Object.defineProperty(v,g,r),r},c=this&&this.__param||function(p,v){return function(g,t){v(g,t,p)}};Object.defineProperty(s,"__esModule",{value:!0}),s.OscLinkService=void 0;const a=o(2585);let f=s.OscLinkService=class{constructor(p){this._bufferService=p,this._nextId=1,this._entriesWithId=new Map,this._dataByLinkId=new Map}registerLink(p){const v=this._bufferService.buffer;if(p.id===void 0){const h=v.addMarker(v.ybase+v.y),_={data:p,id:this._nextId++,lines:[h]};return h.onDispose(()=>this._removeMarkerFromLink(_,h)),this._dataByLinkId.set(_.id,_),_.id}const g=p,t=this._getEntryIdKey(g),n=this._entriesWithId.get(t);if(n)return this.addLineToLink(n.id,v.ybase+v.y),n.id;const i=v.addMarker(v.ybase+v.y),r={id:this._nextId++,key:this._getEntryIdKey(g),data:g,lines:[i]};return i.onDispose(()=>this._removeMarkerFromLink(r,i)),this._entriesWithId.set(r.key,r),this._dataByLinkId.set(r.id,r),r.id}addLineToLink(p,v){const g=this._dataByLinkId.get(p);if(g&&g.lines.every(t=>t.line!==v)){const t=this._bufferService.buffer.addMarker(v);g.lines.push(t),t.onDispose(()=>this._removeMarkerFromLink(g,t))}}getLinkData(p){var v;return(v=this._dataByLinkId.get(p))===null||v===void 0?void 0:v.data}_getEntryIdKey(p){return`${p.id};;${p.uri}`}_removeMarkerFromLink(p,v){const g=p.lines.indexOf(v);g!==-1&&(p.lines.splice(g,1),p.lines.length===0&&(p.data.id!==void 0&&this._entriesWithId.delete(p.key),this._dataByLinkId.delete(p.id)))}};s.OscLinkService=f=l([c(0,a.IBufferService)],f)},8343:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.createDecorator=s.getServiceDependencies=s.serviceRegistry=void 0;const o="di$target",l="di$dependencies";s.serviceRegistry=new Map,s.getServiceDependencies=function(c){return c[l]||[]},s.createDecorator=function(c){if(s.serviceRegistry.has(c))return s.serviceRegistry.get(c);const a=function(f,p,v){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");(function(g,t,n){t[o]===t?t[l].push({id:g,index:n}):(t[l]=[{id:g,index:n}],t[o]=t)})(a,f,v)};return a.toString=()=>c,s.serviceRegistry.set(c,a),a}},2585:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.IDecorationService=s.IUnicodeService=s.IOscLinkService=s.IOptionsService=s.ILogService=s.LogLevelEnum=s.IInstantiationService=s.ICharsetService=s.ICoreService=s.ICoreMouseService=s.IBufferService=void 0;const l=o(8343);var c;s.IBufferService=(0,l.createDecorator)("BufferService"),s.ICoreMouseService=(0,l.createDecorator)("CoreMouseService"),s.ICoreService=(0,l.createDecorator)("CoreService"),s.ICharsetService=(0,l.createDecorator)("CharsetService"),s.IInstantiationService=(0,l.createDecorator)("InstantiationService"),function(a){a[a.TRACE=0]="TRACE",a[a.DEBUG=1]="DEBUG",a[a.INFO=2]="INFO",a[a.WARN=3]="WARN",a[a.ERROR=4]="ERROR",a[a.OFF=5]="OFF"}(c||(s.LogLevelEnum=c={})),s.ILogService=(0,l.createDecorator)("LogService"),s.IOptionsService=(0,l.createDecorator)("OptionsService"),s.IOscLinkService=(0,l.createDecorator)("OscLinkService"),s.IUnicodeService=(0,l.createDecorator)("UnicodeService"),s.IDecorationService=(0,l.createDecorator)("DecorationService")},1480:(x,s,o)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.UnicodeService=void 0;const l=o(8460),c=o(225);s.UnicodeService=class{constructor(){this._providers=Object.create(null),this._active="",this._onChange=new l.EventEmitter,this.onChange=this._onChange.event;const a=new c.UnicodeV6;this.register(a),this._active=a.version,this._activeProvider=a}dispose(){this._onChange.dispose()}get versions(){return Object.keys(this._providers)}get activeVersion(){return this._active}set activeVersion(a){if(!this._providers[a])throw new Error(`unknown Unicode version "${a}"`);this._active=a,this._activeProvider=this._providers[a],this._onChange.fire(a)}register(a){this._providers[a.version]=a}wcwidth(a){return this._activeProvider.wcwidth(a)}getStringCellWidth(a){let f=0;const p=a.length;for(let v=0;v<p;++v){let g=a.charCodeAt(v);if(55296<=g&&g<=56319){if(++v>=p)return f+this.wcwidth(g);const t=a.charCodeAt(v);56320<=t&&t<=57343?g=1024*(g-55296)+t-56320+65536:f+=this.wcwidth(t)}f+=this.wcwidth(g)}return f}}}},m={};function b(x){var s=m[x];if(s!==void 0)return s.exports;var o=m[x]={exports:{}};return d[x].call(o.exports,o,o.exports,b),o.exports}var A={};return(()=>{var x=A;Object.defineProperty(x,"__esModule",{value:!0}),x.Terminal=void 0;const s=b(9042),o=b(3236),l=b(844),c=b(5741),a=b(8285),f=b(7975),p=b(7090),v=["cols","rows"];class g extends l.Disposable{constructor(n){super(),this._core=this.register(new o.Terminal(n)),this._addonManager=this.register(new c.AddonManager),this._publicOptions=Object.assign({},this._core.options);const i=h=>this._core.options[h],r=(h,_)=>{this._checkReadonlyOptions(h),this._core.options[h]=_};for(const h in this._core.options){const _={get:i.bind(this,h),set:r.bind(this,h)};Object.defineProperty(this._publicOptions,h,_)}}_checkReadonlyOptions(n){if(v.includes(n))throw new Error(`Option "${n}" can only be set in the constructor`)}_checkProposedApi(){if(!this._core.optionsService.rawOptions.allowProposedApi)throw new Error("You must set the allowProposedApi option to true to use proposed API")}get onBell(){return this._core.onBell}get onBinary(){return this._core.onBinary}get onCursorMove(){return this._core.onCursorMove}get onData(){return this._core.onData}get onKey(){return this._core.onKey}get onLineFeed(){return this._core.onLineFeed}get onRender(){return this._core.onRender}get onResize(){return this._core.onResize}get onScroll(){return this._core.onScroll}get onSelectionChange(){return this._core.onSelectionChange}get onTitleChange(){return this._core.onTitleChange}get onWriteParsed(){return this._core.onWriteParsed}get element(){return this._core.element}get parser(){return this._parser||(this._parser=new f.ParserApi(this._core)),this._parser}get unicode(){return this._checkProposedApi(),new p.UnicodeApi(this._core)}get textarea(){return this._core.textarea}get rows(){return this._core.rows}get cols(){return this._core.cols}get buffer(){return this._buffer||(this._buffer=this.register(new a.BufferNamespaceApi(this._core))),this._buffer}get markers(){return this._checkProposedApi(),this._core.markers}get modes(){const n=this._core.coreService.decPrivateModes;let i="none";switch(this._core.coreMouseService.activeProtocol){case"X10":i="x10";break;case"VT200":i="vt200";break;case"DRAG":i="drag";break;case"ANY":i="any"}return{applicationCursorKeysMode:n.applicationCursorKeys,applicationKeypadMode:n.applicationKeypad,bracketedPasteMode:n.bracketedPasteMode,insertMode:this._core.coreService.modes.insertMode,mouseTrackingMode:i,originMode:n.origin,reverseWraparoundMode:n.reverseWraparound,sendFocusMode:n.sendFocus,wraparoundMode:n.wraparound}}get options(){return this._publicOptions}set options(n){for(const i in n)this._publicOptions[i]=n[i]}blur(){this._core.blur()}focus(){this._core.focus()}resize(n,i){this._verifyIntegers(n,i),this._core.resize(n,i)}open(n){this._core.open(n)}attachCustomKeyEventHandler(n){this._core.attachCustomKeyEventHandler(n)}registerLinkProvider(n){return this._core.registerLinkProvider(n)}registerCharacterJoiner(n){return this._checkProposedApi(),this._core.registerCharacterJoiner(n)}deregisterCharacterJoiner(n){this._checkProposedApi(),this._core.deregisterCharacterJoiner(n)}registerMarker(n=0){return this._verifyIntegers(n),this._core.registerMarker(n)}registerDecoration(n){var i,r,h;return this._checkProposedApi(),this._verifyPositiveIntegers((i=n.x)!==null&&i!==void 0?i:0,(r=n.width)!==null&&r!==void 0?r:0,(h=n.height)!==null&&h!==void 0?h:0),this._core.registerDecoration(n)}hasSelection(){return this._core.hasSelection()}select(n,i,r){this._verifyIntegers(n,i,r),this._core.select(n,i,r)}getSelection(){return this._core.getSelection()}getSelectionPosition(){return this._core.getSelectionPosition()}clearSelection(){this._core.clearSelection()}selectAll(){this._core.selectAll()}selectLines(n,i){this._verifyIntegers(n,i),this._core.selectLines(n,i)}dispose(){super.dispose()}scrollLines(n){this._verifyIntegers(n),this._core.scrollLines(n)}scrollPages(n){this._verifyIntegers(n),this._core.scrollPages(n)}scrollToTop(){this._core.scrollToTop()}scrollToBottom(){this._core.scrollToBottom()}scrollToLine(n){this._verifyIntegers(n),this._core.scrollToLine(n)}clear(){this._core.clear()}write(n,i){this._core.write(n,i)}writeln(n,i){this._core.write(n),this._core.write(`\r
`,i)}paste(n){this._core.paste(n)}refresh(n,i){this._verifyIntegers(n,i),this._core.refresh(n,i)}reset(){this._core.reset()}clearTextureAtlas(){this._core.clearTextureAtlas()}loadAddon(n){this._addonManager.loadAddon(this,n)}static get strings(){return s}_verifyIntegers(...n){for(const i of n)if(i===1/0||isNaN(i)||i%1!=0)throw new Error("This API only accepts integers")}_verifyPositiveIntegers(...n){for(const i of n)if(i&&(i===1/0||isNaN(i)||i%1!=0||i<0))throw new Error("This API only accepts positive integers")}}x.Terminal=g})(),A})())})(Ct);var Wt=Ct.exports,yt={exports:{}};(function(E,e){(function(d,m){E.exports=m()})(self,()=>(()=>{var d={};return(()=>{var m=d;Object.defineProperty(m,"__esModule",{value:!0}),m.FitAddon=void 0,m.FitAddon=class{activate(b){this._terminal=b}dispose(){}fit(){const b=this.proposeDimensions();if(!b||!this._terminal||isNaN(b.cols)||isNaN(b.rows))return;const A=this._terminal._core;this._terminal.rows===b.rows&&this._terminal.cols===b.cols||(A._renderService.clear(),this._terminal.resize(b.cols,b.rows))}proposeDimensions(){if(!this._terminal||!this._terminal.element||!this._terminal.element.parentElement)return;const b=this._terminal._core,A=b._renderService.dimensions;if(A.css.cell.width===0||A.css.cell.height===0)return;const x=this._terminal.options.scrollback===0?0:b.viewport.scrollBarWidth,s=window.getComputedStyle(this._terminal.element.parentElement),o=parseInt(s.getPropertyValue("height")),l=Math.max(0,parseInt(s.getPropertyValue("width"))),c=window.getComputedStyle(this._terminal.element),a=o-(parseInt(c.getPropertyValue("padding-top"))+parseInt(c.getPropertyValue("padding-bottom"))),f=l-(parseInt(c.getPropertyValue("padding-right"))+parseInt(c.getPropertyValue("padding-left")))-x;return{cols:Math.max(2,Math.floor(f/A.css.cell.width)),rows:Math.max(1,Math.floor(a/A.css.cell.height))}}}})(),d})())})(yt);var Nt=yt.exports,wt={exports:{}};(function(E,e){(function(d,m){E.exports=m()})(self,()=>(()=>{var d={6:(x,s)=>{Object.defineProperty(s,"__esModule",{value:!0}),s.LinkComputer=s.WebLinkProvider=void 0,s.WebLinkProvider=class{constructor(l,c,a,f={}){this._terminal=l,this._regex=c,this._handler=a,this._options=f}provideLinks(l,c){const a=o.computeLink(l,this._regex,this._terminal,this._handler);c(this._addCallbacks(a))}_addCallbacks(l){return l.map(c=>(c.leave=this._options.leave,c.hover=(a,f)=>{if(this._options.hover){const{range:p}=c;this._options.hover(a,f,p)}},c))}};class o{static computeLink(c,a,f,p){const v=new RegExp(a.source,(a.flags||"")+"g"),[g,t]=o._getWindowedLineStrings(c-1,f),n=g.join("");let i;const r=[];for(;i=v.exec(n);){const h=i[0];try{const L=new URL(h),I=decodeURI(L.toString());if(h!==I&&h+"/"!==I)continue}catch{continue}const[_,S]=o._mapStrIdx(f,t,0,i.index),[y,u]=o._mapStrIdx(f,_,S,h.length);if(_===-1||S===-1||y===-1||u===-1)continue;const C={start:{x:S+1,y:_+1},end:{x:u,y:y+1}};r.push({range:C,text:h,activate:p})}return r}static _getWindowedLineStrings(c,a){let f,p=c,v=c,g=0,t="";const n=[];if(f=a.buffer.active.getLine(c)){const i=f.translateToString(!0);if(f.isWrapped&&i[0]!==" "){for(g=0;(f=a.buffer.active.getLine(--p))&&g<2048&&(t=f.translateToString(!0),g+=t.length,n.push(t),f.isWrapped&&t.indexOf(" ")===-1););n.reverse()}for(n.push(i),g=0;(f=a.buffer.active.getLine(++v))&&f.isWrapped&&g<2048&&(t=f.translateToString(!0),g+=t.length,n.push(t),t.indexOf(" ")===-1););}return[n,p]}static _mapStrIdx(c,a,f,p){const v=c.buffer.active,g=v.getNullCell();let t=f;for(;p;){const n=v.getLine(a);if(!n)return[-1,-1];for(let i=t;i<n.length;++i){n.getCell(i,g);const r=g.getChars();if(g.getWidth()&&(p-=r.length||1,i===n.length-1&&r==="")){const h=v.getLine(a+1);h&&h.isWrapped&&(h.getCell(0,g),g.getWidth()===2&&(p+=1))}if(p<0)return[a,i]}a++,t=0}return[a,t]}}s.LinkComputer=o}},m={};function b(x){var s=m[x];if(s!==void 0)return s.exports;var o=m[x]={exports:{}};return d[x](o,o.exports,b),o.exports}var A={};return(()=>{var x=A;Object.defineProperty(x,"__esModule",{value:!0}),x.WebLinksAddon=void 0;const s=b(6),o=/https?:[/]{2}[^\s"'!*(){}|\\\^<>`]*[^\s"':,.!?{}|\\\^~\[\]`()<>]/;function l(c,a){const f=window.open();if(f){try{f.opener=null}catch{}f.location.href=a}else console.warn("Opening link blocked as opener could not be cleared")}x.WebLinksAddon=class{constructor(c=l,a={}){this._handler=c,this._options=a}activate(c){this._terminal=c;const a=this._options,f=a.urlRegex||o;this._linkProvider=this._terminal.registerLinkProvider(new s.WebLinkProvider(this._terminal,f,this._handler,a))}dispose(){var c;(c=this._linkProvider)===null||c===void 0||c.dispose()}}})(),A})())})(wt);var Ut=wt.exports;class jt{constructor(){this.terminal=null,this.socket=null,this.buffer=[],this.isReady=!1,this.debugLogging=!0}initialize(e,d){this.terminal=e,this.socket=d,this.isReady=!0,this.terminal.onData(m=>{this.buffer.push(m)}),console.log("XTermService initialized")}isServiceReady(){return this.isReady&&this.terminal&&this.socket&&this.socket.readyState===WebSocket.OPEN}async sendRawData(e){if(!this.isServiceReady())return console.error("XTermService not ready"),!1;try{return this.socket.send(JSON.stringify({type:"input",data:e})),!0}catch(d){return console.error("Failed to send data:",d),!1}}async sendKey(e){const m={Enter:"\r",Tab:"	",Backspace:"",Delete:"\x1B[3~",Escape:"\x1B",ArrowUp:"\x1B[A",ArrowDown:"\x1B[B",ArrowRight:"\x1B[C",ArrowLeft:"\x1B[D",Home:"\x1B[H",End:"\x1B[F",PageUp:"\x1B[5~",PageDown:"\x1B[6~",F1:"\x1BOP",F2:"\x1BOQ",F3:"\x1BOR",F4:"\x1BOS",F5:"\x1B[15~",F6:"\x1B[17~",F7:"\x1B[18~",F8:"\x1B[19~",F9:"\x1B[20~",F10:"\x1B[21~",F11:"\x1B[23~",F12:"\x1B[24~"}[e]||e;return await this.sendRawData(m)}async sendKeyCombo(e){const d=e.split("+").map(b=>b.trim());let m="";if(d.length===2){const[b,A]=d,x=A.toLowerCase();switch(b.toLowerCase()){case"ctrl":x>="a"&&x<="z"?m=String.fromCharCode(x.charCodeAt(0)-96):m={c:"",d:"",z:"",l:"\f",r:"",u:"",k:"\v",w:"",y:"",p:"",n:"",f:"",b:"",a:"",e:""}[x]||"";break;case"alt":m="\x1B"+A;break;case"shift":m=A.toUpperCase();break}}return m?await this.sendRawData(m):(console.error("Unsupported key combination:",e),!1)}async sendCommand(e,d=!0){let m=await this.sendRawData(e);return m&&d&&(m=await this.sendKey("Enter")),m}async sendCommands(e,d=100){for(const m of e){if(!await this.sendCommand(m))return console.error("Failed to send command:",m),!1;d>0&&await new Promise(A=>setTimeout(A,d))}return!0}getCurrentLineContent(){if(!this.terminal)return"";const e=this.terminal.buffer.active,d=e.cursorY,m=e.getLine(d);return m?m.translateToString(!0):""}getLineContent(e){if(!this.terminal)return"";const m=this.terminal.buffer.active.getLine(e);return m?m.translateToString(!0):""}getLineRangeContent(e,d){if(!this.terminal)return[];const m=this.terminal.buffer.active,b=[];for(let A=e;A<=d&&A<m.length;A++){const x=m.getLine(A);b.push(x?x.translateToString(!0):"")}return b}getAllContent(){if(this.debugLogging&&console.log("[XTermService] getAllContent called"),!this.terminal)return console.warn("[XTermService] Terminal not available"),[];const e=this.terminal.buffer.active;this.debugLogging&&console.log("[XTermService] Buffer info:",{length:e.length,baseY:e.baseY,cursorY:e.cursorY,cursorX:e.cursorX});const d=[];let m=0;for(let b=0;b<e.length;b++){const A=e.getLine(b),x=A?A.translateToString(!0):"";d.push(x),x.trim()!==""&&m++}return this.debugLogging&&console.log("[XTermService] Content retrieved:",{totalLines:d.length,nonEmptyLines:m,firstLine:d[0]||"(empty)",lastLine:d[d.length-1]||"(empty)",sampleLines:d.slice(0,5).map((b,A)=>`${A}: "${b}"`),totalCharacters:d.join("").length}),{cursorX:e.cursorX,cursorY:e.cursorY,lines:d}}getAllContentAsString(e=`
`){return this.getAllContent().join(e)}async clearScreen(){return await this.sendKeyCombo("Ctrl+L")}getTerminalDimensions(){return this.terminal?{cols:this.terminal.cols,rows:this.terminal.rows}:{cols:0,rows:0}}getCursorPosition(){if(!this.terminal)return{x:0,y:0};const e=this.terminal.buffer.active;return{x:e.cursorX,y:e.cursorY}}setDebugLogging(e){this.debugLogging=e,console.log(`[XTermService] Debug logging ${e?"enabled":"disabled"}`)}}const me=new jt;class Kt{constructor(){this.dialog=null,this.headerElement=null,this.closeBtn=null,this.minimizeBtn=null,this.maximizeBtn=null,this.aiBtn=null,this.pauseResumeBtn=null,this.pauseIcon=null,this.playIcon=null,this.isVisible=!1,this.isMinimized=!1,this.isMaximized=!1,this.isDragging=!1,this.dragOffset={x:0,y:0},this.originalPosition={top:20,right:20},this.originalSize={width:400,height:500},this.onShow=null,this.onHide=null,this.onMinimize=null,this.onMaximize=null,this.onPauseResume=null}initialize(){return this.dialog=document.getElementById("aiDialog"),this.headerElement=document.getElementById("aiDialogHeader"),this.closeBtn=document.getElementById("aiDialogClose"),this.minimizeBtn=document.getElementById("aiDialogMinimize"),this.maximizeBtn=document.getElementById("aiDialogMaximize"),this.aiBtn=document.getElementById("aiBtn"),this.pauseResumeBtn=document.getElementById("aiPauseResume"),this.pauseIcon=document.getElementById("pauseIcon"),this.playIcon=document.getElementById("playIcon"),!this.dialog||!this.headerElement||!this.closeBtn||!this.minimizeBtn||!this.maximizeBtn||!this.aiBtn||!this.pauseResumeBtn||!this.pauseIcon||!this.playIcon?(console.error("Dialog UI elements not found"),!1):(this.setupEventListeners(),console.log("Dialog UI Manager initialized"),!0)}setupEventListeners(){this.aiBtn.addEventListener("click",()=>this.toggleDialog()),this.closeBtn.addEventListener("click",()=>this.hideDialog()),this.minimizeBtn.addEventListener("click",()=>this.toggleMinimize()),this.maximizeBtn.addEventListener("click",()=>this.toggleMaximize()),this.pauseResumeBtn.addEventListener("click",()=>this.togglePauseResume()),this.setupDragging(),document.addEventListener("keydown",e=>{e.key==="Escape"&&this.isVisible&&this.hideDialog()})}toggleDialog(){this.isVisible?this.hideDialog():this.showDialog()}showDialog(){this.isVisible=!0,this.dialog.classList.remove("ai-dialog-hidden"),this.onShow&&this.onShow(),console.log("AI Dialog shown")}hideDialog(){this.isVisible=!1,this.dialog.classList.add("ai-dialog-hidden"),this.onHide&&this.onHide(),console.log("AI Dialog hidden")}toggleMinimize(){this.isMinimized=!this.isMinimized,this.isMinimized?(this.dialog.classList.add("minimized"),this.minimizeBtn.textContent="+",this.minimizeBtn.title="Restore"):(this.dialog.classList.remove("minimized"),this.minimizeBtn.textContent="−",this.minimizeBtn.title="Minimize"),this.onMinimize&&this.onMinimize(this.isMinimized),console.log("AI Dialog minimized:",this.isMinimized)}toggleMaximize(){this.isMaximized=!this.isMaximized,this.isMaximized?(this.dialog.classList.add("maximized"),this.maximizeBtn.textContent="❐",this.maximizeBtn.title="Restore"):(this.dialog.classList.remove("maximized"),this.maximizeBtn.textContent="□",this.maximizeBtn.title="Maximize"),this.onMaximize&&this.onMaximize(this.isMaximized),console.log("AI Dialog maximized:",this.isMaximized)}togglePauseResume(){this.onPauseResume&&this.onPauseResume()}updatePauseResumeButton(e){e?(this.pauseIcon.style.display="none",this.playIcon.style.display="block",this.pauseResumeBtn.classList.add("paused"),this.pauseResumeBtn.title="Resume Agent"):(this.pauseIcon.style.display="block",this.playIcon.style.display="none",this.pauseResumeBtn.classList.remove("paused"),this.pauseResumeBtn.title="Pause Agent")}setPauseResumeButtonEnabled(e){this.pauseResumeBtn.disabled=!e}setSendButtonToTerminate(){const e=document.getElementById("aiSend"),d=document.getElementById("sendIcon"),m=document.getElementById("stopIcon");e&&d&&m&&(d.style.display="none",m.style.display="block",e.classList.add("terminate"),e.title="Terminate Agent")}setSendButtonToSend(){const e=document.getElementById("aiSend"),d=document.getElementById("sendIcon"),m=document.getElementById("stopIcon");e&&d&&m&&(d.style.display="block",m.style.display="none",e.classList.remove("terminate"),e.title="Send message")}isSendButtonInTerminateMode(){const e=document.getElementById("aiSend");return e?e.classList.contains("terminate"):!1}setupDragging(){this.headerElement.addEventListener("mousedown",e=>{(e.target===this.headerElement||e.target.tagName==="H3")&&this.startDragging(e)}),document.addEventListener("mousemove",e=>{this.isDragging&&this.drag(e)}),document.addEventListener("mouseup",()=>{this.isDragging&&this.stopDragging()})}startDragging(e){if(this.isMaximized)return;this.isDragging=!0,this.dialog.classList.add("dragging");const d=this.dialog.getBoundingClientRect();this.dragOffset.x=e.clientX-d.left,this.dragOffset.y=e.clientY-d.top,e.preventDefault()}drag(e){if(!this.isDragging)return;const d=e.clientX-this.dragOffset.x,m=e.clientY-this.dragOffset.y,b=window.innerWidth-this.dialog.offsetWidth,A=window.innerHeight-this.dialog.offsetHeight,x=Math.max(0,Math.min(d,b)),s=Math.max(0,Math.min(m,A));this.dialog.style.left=x+"px",this.dialog.style.top=s+"px",this.dialog.style.right="auto",this.dialog.style.bottom="auto"}stopDragging(){this.isDragging=!1,this.dialog.classList.remove("dragging")}isDialogVisible(){return this.isVisible}setCallbacks(e){this.onShow=e.onShow||null,this.onHide=e.onHide||null,this.onMinimize=e.onMinimize||null,this.onMaximize=e.onMaximize||null,this.onPauseResume=e.onPauseResume||null}}const re=new Kt;function rt(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var Ce=rt();function kt(E){Ce=E}var Ie={exec:()=>null};function J(E,e=""){let d=typeof E=="string"?E:E.source,m={replace:(b,A)=>{let x=typeof A=="string"?A:A.source;return x=x.replace(ne.caret,"$1"),d=d.replace(b,x),m},getRegex:()=>new RegExp(d,e)};return m}var ne={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:E=>new RegExp(`^( {0,3}${E})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}#`),htmlBeginRegex:E=>new RegExp(`^ {0,${Math.min(3,E-1)}}<(?:[a-z].*>|!--)`,"i")},qt=/^(?:[ \t]*(?:\n|$))+/,Vt=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Xt=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,Pe=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Gt=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,nt=/(?:[*+-]|\d{1,9}[.)])/,Et=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,xt=J(Et).replace(/bull/g,nt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),Jt=J(Et).replace(/bull/g,nt).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),ot=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,Yt=/^[^\n]+/,at=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Zt=J(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",at).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Qt=J(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,nt).getRegex(),Ke="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",lt=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,es=J("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",lt).replace("tag",Ke).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),At=J(ot).replace("hr",Pe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ke).getRegex(),ts=J(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",At).getRegex(),ht={blockquote:ts,code:Vt,def:Zt,fences:Xt,heading:Gt,hr:Pe,html:es,lheading:xt,list:Qt,newline:qt,paragraph:At,table:Ie,text:Yt},ft=J("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",Pe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ke).getRegex(),ss={...ht,lheading:Jt,table:ft,paragraph:J(ot).replace("hr",Pe).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",ft).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Ke).getRegex()},is={...ht,html:J(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",lt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Ie,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:J(ot).replace("hr",Pe).replace("heading",` *#{1,6} *[^
]`).replace("lheading",xt).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},rs=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ns=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Lt=/^( {2,}|\\)\n(?!\s*$)/,os=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,qe=/[\p{P}\p{S}]/u,ct=/[\s\p{P}\p{S}]/u,Rt=/[^\s\p{P}\p{S}]/u,as=J(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,ct).getRegex(),Dt=/(?!~)[\p{P}\p{S}]/u,ls=/(?!~)[\s\p{P}\p{S}]/u,hs=/(?:[^\s\p{P}\p{S}]|~)/u,cs=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,Tt=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,us=J(Tt,"u").replace(/punct/g,qe).getRegex(),ds=J(Tt,"u").replace(/punct/g,Dt).getRegex(),Bt="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",fs=J(Bt,"gu").replace(/notPunctSpace/g,Rt).replace(/punctSpace/g,ct).replace(/punct/g,qe).getRegex(),_s=J(Bt,"gu").replace(/notPunctSpace/g,hs).replace(/punctSpace/g,ls).replace(/punct/g,Dt).getRegex(),gs=J("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Rt).replace(/punctSpace/g,ct).replace(/punct/g,qe).getRegex(),ps=J(/\\(punct)/,"gu").replace(/punct/g,qe).getRegex(),ms=J(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),vs=J(lt).replace("(?:-->|$)","-->").getRegex(),Ss=J("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",vs).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),We=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,Cs=J(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",We).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),It=J(/^!?\[(label)\]\[(ref)\]/).replace("label",We).replace("ref",at).getRegex(),Mt=J(/^!?\[(ref)\](?:\[\])?/).replace("ref",at).getRegex(),bs=J("reflink|nolink(?!\\()","g").replace("reflink",It).replace("nolink",Mt).getRegex(),ut={_backpedal:Ie,anyPunctuation:ps,autolink:ms,blockSkip:cs,br:Lt,code:ns,del:Ie,emStrongLDelim:us,emStrongRDelimAst:fs,emStrongRDelimUnd:gs,escape:rs,link:Cs,nolink:Mt,punctuation:as,reflink:It,reflinkSearch:bs,tag:Ss,text:os,url:Ie},ys={...ut,link:J(/^!?\[(label)\]\((.*?)\)/).replace("label",We).getRegex(),reflink:J(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",We).getRegex()},et={...ut,emStrongRDelimAst:_s,emStrongLDelim:ds,url:J(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},ws={...et,br:J(Lt).replace("{2,}","*").getRegex(),text:J(et.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},He={normal:ht,gfm:ss,pedantic:is},Re={normal:ut,gfm:et,breaks:ws,pedantic:ys},ks={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},_t=E=>ks[E];function ue(E,e){if(e){if(ne.escapeTest.test(E))return E.replace(ne.escapeReplace,_t)}else if(ne.escapeTestNoEncode.test(E))return E.replace(ne.escapeReplaceNoEncode,_t);return E}function gt(E){try{E=encodeURI(E).replace(ne.percentDecode,"%")}catch{return null}return E}function pt(E,e){var A;let d=E.replace(ne.findPipe,(x,s,o)=>{let l=!1,c=s;for(;--c>=0&&o[c]==="\\";)l=!l;return l?"|":" |"}),m=d.split(ne.splitPipe),b=0;if(m[0].trim()||m.shift(),m.length>0&&!((A=m.at(-1))!=null&&A.trim())&&m.pop(),e)if(m.length>e)m.splice(e);else for(;m.length<e;)m.push("");for(;b<m.length;b++)m[b]=m[b].trim().replace(ne.slashPipe,"|");return m}function De(E,e,d){let m=E.length;if(m===0)return"";let b=0;for(;b<m&&E.charAt(m-b-1)===e;)b++;return E.slice(0,m-b)}function Es(E,e){if(E.indexOf(e[1])===-1)return-1;let d=0;for(let m=0;m<E.length;m++)if(E[m]==="\\")m++;else if(E[m]===e[0])d++;else if(E[m]===e[1]&&(d--,d<0))return m;return d>0?-2:-1}function mt(E,e,d,m,b){let A=e.href,x=e.title||null,s=E[1].replace(b.other.outputLinkReplace,"$1");m.state.inLink=!0;let o={type:E[0].charAt(0)==="!"?"image":"link",raw:d,href:A,title:x,text:s,tokens:m.inlineTokens(s)};return m.state.inLink=!1,o}function xs(E,e,d){let m=E.match(d.other.indentCodeCompensation);if(m===null)return e;let b=m[1];return e.split(`
`).map(A=>{let x=A.match(d.other.beginningSpace);if(x===null)return A;let[s]=x;return s.length>=b.length?A.slice(b.length):A}).join(`
`)}var Ne=class{constructor(E){Z(this,"options");Z(this,"rules");Z(this,"lexer");this.options=E||Ce}space(E){let e=this.rules.block.newline.exec(E);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(E){let e=this.rules.block.code.exec(E);if(e){let d=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?d:De(d,`
`)}}}fences(E){let e=this.rules.block.fences.exec(E);if(e){let d=e[0],m=xs(d,e[3]||"",this.rules);return{type:"code",raw:d,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:m}}}heading(E){let e=this.rules.block.heading.exec(E);if(e){let d=e[2].trim();if(this.rules.other.endingHash.test(d)){let m=De(d,"#");(this.options.pedantic||!m||this.rules.other.endingSpaceChar.test(m))&&(d=m.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:d,tokens:this.lexer.inline(d)}}}hr(E){let e=this.rules.block.hr.exec(E);if(e)return{type:"hr",raw:De(e[0],`
`)}}blockquote(E){let e=this.rules.block.blockquote.exec(E);if(e){let d=De(e[0],`
`).split(`
`),m="",b="",A=[];for(;d.length>0;){let x=!1,s=[],o;for(o=0;o<d.length;o++)if(this.rules.other.blockquoteStart.test(d[o]))s.push(d[o]),x=!0;else if(!x)s.push(d[o]);else break;d=d.slice(o);let l=s.join(`
`),c=l.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");m=m?`${m}
${l}`:l,b=b?`${b}
${c}`:c;let a=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(c,A,!0),this.lexer.state.top=a,d.length===0)break;let f=A.at(-1);if((f==null?void 0:f.type)==="code")break;if((f==null?void 0:f.type)==="blockquote"){let p=f,v=p.raw+`
`+d.join(`
`),g=this.blockquote(v);A[A.length-1]=g,m=m.substring(0,m.length-p.raw.length)+g.raw,b=b.substring(0,b.length-p.text.length)+g.text;break}else if((f==null?void 0:f.type)==="list"){let p=f,v=p.raw+`
`+d.join(`
`),g=this.list(v);A[A.length-1]=g,m=m.substring(0,m.length-f.raw.length)+g.raw,b=b.substring(0,b.length-p.raw.length)+g.raw,d=v.substring(A.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:m,tokens:A,text:b}}}list(E){let e=this.rules.block.list.exec(E);if(e){let d=e[1].trim(),m=d.length>1,b={type:"list",raw:"",ordered:m,start:m?+d.slice(0,-1):"",loose:!1,items:[]};d=m?`\\d{1,9}\\${d.slice(-1)}`:`\\${d}`,this.options.pedantic&&(d=m?d:"[*+-]");let A=this.rules.other.listItemRegex(d),x=!1;for(;E;){let o=!1,l="",c="";if(!(e=A.exec(E))||this.rules.block.hr.test(E))break;l=e[0],E=E.substring(l.length);let a=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,n=>" ".repeat(3*n.length)),f=E.split(`
`,1)[0],p=!a.trim(),v=0;if(this.options.pedantic?(v=2,c=a.trimStart()):p?v=e[1].length+1:(v=e[2].search(this.rules.other.nonSpaceChar),v=v>4?1:v,c=a.slice(v),v+=e[1].length),p&&this.rules.other.blankLine.test(f)&&(l+=f+`
`,E=E.substring(f.length+1),o=!0),!o){let n=this.rules.other.nextBulletRegex(v),i=this.rules.other.hrRegex(v),r=this.rules.other.fencesBeginRegex(v),h=this.rules.other.headingBeginRegex(v),_=this.rules.other.htmlBeginRegex(v);for(;E;){let S=E.split(`
`,1)[0],y;if(f=S,this.options.pedantic?(f=f.replace(this.rules.other.listReplaceNesting,"  "),y=f):y=f.replace(this.rules.other.tabCharGlobal,"    "),r.test(f)||h.test(f)||_.test(f)||n.test(f)||i.test(f))break;if(y.search(this.rules.other.nonSpaceChar)>=v||!f.trim())c+=`
`+y.slice(v);else{if(p||a.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||r.test(a)||h.test(a)||i.test(a))break;c+=`
`+f}!p&&!f.trim()&&(p=!0),l+=S+`
`,E=E.substring(S.length+1),a=y.slice(v)}}b.loose||(x?b.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(x=!0));let g=null,t;this.options.gfm&&(g=this.rules.other.listIsTask.exec(c),g&&(t=g[0]!=="[ ] ",c=c.replace(this.rules.other.listReplaceTask,""))),b.items.push({type:"list_item",raw:l,task:!!g,checked:t,loose:!1,text:c,tokens:[]}),b.raw+=l}let s=b.items.at(-1);if(s)s.raw=s.raw.trimEnd(),s.text=s.text.trimEnd();else return;b.raw=b.raw.trimEnd();for(let o=0;o<b.items.length;o++)if(this.lexer.state.top=!1,b.items[o].tokens=this.lexer.blockTokens(b.items[o].text,[]),!b.loose){let l=b.items[o].tokens.filter(a=>a.type==="space"),c=l.length>0&&l.some(a=>this.rules.other.anyLine.test(a.raw));b.loose=c}if(b.loose)for(let o=0;o<b.items.length;o++)b.items[o].loose=!0;return b}}html(E){let e=this.rules.block.html.exec(E);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(E){let e=this.rules.block.def.exec(E);if(e){let d=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),m=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",b=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:d,raw:e[0],href:m,title:b}}}table(E){var x;let e=this.rules.block.table.exec(E);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;let d=pt(e[1]),m=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),b=(x=e[3])!=null&&x.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],A={type:"table",raw:e[0],header:[],align:[],rows:[]};if(d.length===m.length){for(let s of m)this.rules.other.tableAlignRight.test(s)?A.align.push("right"):this.rules.other.tableAlignCenter.test(s)?A.align.push("center"):this.rules.other.tableAlignLeft.test(s)?A.align.push("left"):A.align.push(null);for(let s=0;s<d.length;s++)A.header.push({text:d[s],tokens:this.lexer.inline(d[s]),header:!0,align:A.align[s]});for(let s of b)A.rows.push(pt(s,A.header.length).map((o,l)=>({text:o,tokens:this.lexer.inline(o),header:!1,align:A.align[l]})));return A}}lheading(E){let e=this.rules.block.lheading.exec(E);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(E){let e=this.rules.block.paragraph.exec(E);if(e){let d=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:d,tokens:this.lexer.inline(d)}}}text(E){let e=this.rules.block.text.exec(E);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(E){let e=this.rules.inline.escape.exec(E);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(E){let e=this.rules.inline.tag.exec(E);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(E){let e=this.rules.inline.link.exec(E);if(e){let d=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(d)){if(!this.rules.other.endAngleBracket.test(d))return;let A=De(d.slice(0,-1),"\\");if((d.length-A.length)%2===0)return}else{let A=Es(e[2],"()");if(A===-2)return;if(A>-1){let x=(e[0].indexOf("!")===0?5:4)+e[1].length+A;e[2]=e[2].substring(0,A),e[0]=e[0].substring(0,x).trim(),e[3]=""}}let m=e[2],b="";if(this.options.pedantic){let A=this.rules.other.pedanticHrefTitle.exec(m);A&&(m=A[1],b=A[3])}else b=e[3]?e[3].slice(1,-1):"";return m=m.trim(),this.rules.other.startAngleBracket.test(m)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(d)?m=m.slice(1):m=m.slice(1,-1)),mt(e,{href:m&&m.replace(this.rules.inline.anyPunctuation,"$1"),title:b&&b.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(E,e){let d;if((d=this.rules.inline.reflink.exec(E))||(d=this.rules.inline.nolink.exec(E))){let m=(d[2]||d[1]).replace(this.rules.other.multipleSpaceGlobal," "),b=e[m.toLowerCase()];if(!b){let A=d[0].charAt(0);return{type:"text",raw:A,text:A}}return mt(d,b,d[0],this.lexer,this.rules)}}emStrong(E,e,d=""){let m=this.rules.inline.emStrongLDelim.exec(E);if(!(!m||m[3]&&d.match(this.rules.other.unicodeAlphaNumeric))&&(!(m[1]||m[2])||!d||this.rules.inline.punctuation.exec(d))){let b=[...m[0]].length-1,A,x,s=b,o=0,l=m[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(l.lastIndex=0,e=e.slice(-1*E.length+b);(m=l.exec(e))!=null;){if(A=m[1]||m[2]||m[3]||m[4]||m[5]||m[6],!A)continue;if(x=[...A].length,m[3]||m[4]){s+=x;continue}else if((m[5]||m[6])&&b%3&&!((b+x)%3)){o+=x;continue}if(s-=x,s>0)continue;x=Math.min(x,x+s+o);let c=[...m[0]][0].length,a=E.slice(0,b+m.index+c+x);if(Math.min(b,x)%2){let p=a.slice(1,-1);return{type:"em",raw:a,text:p,tokens:this.lexer.inlineTokens(p)}}let f=a.slice(2,-2);return{type:"strong",raw:a,text:f,tokens:this.lexer.inlineTokens(f)}}}}codespan(E){let e=this.rules.inline.code.exec(E);if(e){let d=e[2].replace(this.rules.other.newLineCharGlobal," "),m=this.rules.other.nonSpaceChar.test(d),b=this.rules.other.startingSpaceChar.test(d)&&this.rules.other.endingSpaceChar.test(d);return m&&b&&(d=d.substring(1,d.length-1)),{type:"codespan",raw:e[0],text:d}}}br(E){let e=this.rules.inline.br.exec(E);if(e)return{type:"br",raw:e[0]}}del(E){let e=this.rules.inline.del.exec(E);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(E){let e=this.rules.inline.autolink.exec(E);if(e){let d,m;return e[2]==="@"?(d=e[1],m="mailto:"+d):(d=e[1],m=d),{type:"link",raw:e[0],text:d,href:m,tokens:[{type:"text",raw:d,text:d}]}}}url(E){var d;let e;if(e=this.rules.inline.url.exec(E)){let m,b;if(e[2]==="@")m=e[0],b="mailto:"+m;else{let A;do A=e[0],e[0]=((d=this.rules.inline._backpedal.exec(e[0]))==null?void 0:d[0])??"";while(A!==e[0]);m=e[0],e[1]==="www."?b="http://"+e[0]:b=e[0]}return{type:"link",raw:e[0],text:m,href:b,tokens:[{type:"text",raw:m,text:m}]}}}inlineText(E){let e=this.rules.inline.text.exec(E);if(e){let d=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:d}}}},de=class tt{constructor(e){Z(this,"tokens");Z(this,"options");Z(this,"state");Z(this,"tokenizer");Z(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Ce,this.options.tokenizer=this.options.tokenizer||new Ne,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let d={other:ne,block:He.normal,inline:Re.normal};this.options.pedantic?(d.block=He.pedantic,d.inline=Re.pedantic):this.options.gfm&&(d.block=He.gfm,this.options.breaks?d.inline=Re.breaks:d.inline=Re.gfm),this.tokenizer.rules=d}static get rules(){return{block:He,inline:Re}}static lex(e,d){return new tt(d).lex(e)}static lexInline(e,d){return new tt(d).inlineTokens(e)}lex(e){e=e.replace(ne.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let d=0;d<this.inlineQueue.length;d++){let m=this.inlineQueue[d];this.inlineTokens(m.src,m.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,d=[],m=!1){var b,A,x;for(this.options.pedantic&&(e=e.replace(ne.tabCharGlobal,"    ").replace(ne.spaceLine,""));e;){let s;if((A=(b=this.options.extensions)==null?void 0:b.block)!=null&&A.some(l=>(s=l.call({lexer:this},e,d))?(e=e.substring(s.raw.length),d.push(s),!0):!1))continue;if(s=this.tokenizer.space(e)){e=e.substring(s.raw.length);let l=d.at(-1);s.raw.length===1&&l!==void 0?l.raw+=`
`:d.push(s);continue}if(s=this.tokenizer.code(e)){e=e.substring(s.raw.length);let l=d.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.at(-1).src=l.text):d.push(s);continue}if(s=this.tokenizer.fences(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.heading(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.hr(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.blockquote(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.list(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.html(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.def(e)){e=e.substring(s.raw.length);let l=d.at(-1);(l==null?void 0:l.type)==="paragraph"||(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.raw,this.inlineQueue.at(-1).src=l.text):this.tokens.links[s.tag]||(this.tokens.links[s.tag]={href:s.href,title:s.title});continue}if(s=this.tokenizer.table(e)){e=e.substring(s.raw.length),d.push(s);continue}if(s=this.tokenizer.lheading(e)){e=e.substring(s.raw.length),d.push(s);continue}let o=e;if((x=this.options.extensions)!=null&&x.startBlock){let l=1/0,c=e.slice(1),a;this.options.extensions.startBlock.forEach(f=>{a=f.call({lexer:this},c),typeof a=="number"&&a>=0&&(l=Math.min(l,a))}),l<1/0&&l>=0&&(o=e.substring(0,l+1))}if(this.state.top&&(s=this.tokenizer.paragraph(o))){let l=d.at(-1);m&&(l==null?void 0:l.type)==="paragraph"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):d.push(s),m=o.length!==e.length,e=e.substring(s.raw.length);continue}if(s=this.tokenizer.text(e)){e=e.substring(s.raw.length);let l=d.at(-1);(l==null?void 0:l.type)==="text"?(l.raw+=`
`+s.raw,l.text+=`
`+s.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=l.text):d.push(s);continue}if(e){let l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}else throw new Error(l)}}return this.state.top=!0,d}inline(e,d=[]){return this.inlineQueue.push({src:e,tokens:d}),d}inlineTokens(e,d=[]){var s,o,l;let m=e,b=null;if(this.tokens.links){let c=Object.keys(this.tokens.links);if(c.length>0)for(;(b=this.tokenizer.rules.inline.reflinkSearch.exec(m))!=null;)c.includes(b[0].slice(b[0].lastIndexOf("[")+1,-1))&&(m=m.slice(0,b.index)+"["+"a".repeat(b[0].length-2)+"]"+m.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(b=this.tokenizer.rules.inline.anyPunctuation.exec(m))!=null;)m=m.slice(0,b.index)+"++"+m.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(b=this.tokenizer.rules.inline.blockSkip.exec(m))!=null;)m=m.slice(0,b.index)+"["+"a".repeat(b[0].length-2)+"]"+m.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let A=!1,x="";for(;e;){A||(x=""),A=!1;let c;if((o=(s=this.options.extensions)==null?void 0:s.inline)!=null&&o.some(f=>(c=f.call({lexer:this},e,d))?(e=e.substring(c.raw.length),d.push(c),!0):!1))continue;if(c=this.tokenizer.escape(e)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.tag(e)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.link(e)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(c.raw.length);let f=d.at(-1);c.type==="text"&&(f==null?void 0:f.type)==="text"?(f.raw+=c.raw,f.text+=c.text):d.push(c);continue}if(c=this.tokenizer.emStrong(e,m,x)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.codespan(e)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.br(e)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.del(e)){e=e.substring(c.raw.length),d.push(c);continue}if(c=this.tokenizer.autolink(e)){e=e.substring(c.raw.length),d.push(c);continue}if(!this.state.inLink&&(c=this.tokenizer.url(e))){e=e.substring(c.raw.length),d.push(c);continue}let a=e;if((l=this.options.extensions)!=null&&l.startInline){let f=1/0,p=e.slice(1),v;this.options.extensions.startInline.forEach(g=>{v=g.call({lexer:this},p),typeof v=="number"&&v>=0&&(f=Math.min(f,v))}),f<1/0&&f>=0&&(a=e.substring(0,f+1))}if(c=this.tokenizer.inlineText(a)){e=e.substring(c.raw.length),c.raw.slice(-1)!=="_"&&(x=c.raw.slice(-1)),A=!0;let f=d.at(-1);(f==null?void 0:f.type)==="text"?(f.raw+=c.raw,f.text+=c.text):d.push(c);continue}if(e){let f="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(f);break}else throw new Error(f)}}return d}},Ue=class{constructor(E){Z(this,"options");Z(this,"parser");this.options=E||Ce}space(E){return""}code({text:E,lang:e,escaped:d}){var A;let m=(A=(e||"").match(ne.notSpaceStart))==null?void 0:A[0],b=E.replace(ne.endingNewline,"")+`
`;return m?'<pre><code class="language-'+ue(m)+'">'+(d?b:ue(b,!0))+`</code></pre>
`:"<pre><code>"+(d?b:ue(b,!0))+`</code></pre>
`}blockquote({tokens:E}){return`<blockquote>
${this.parser.parse(E)}</blockquote>
`}html({text:E}){return E}heading({tokens:E,depth:e}){return`<h${e}>${this.parser.parseInline(E)}</h${e}>
`}hr(E){return`<hr>
`}list(E){let e=E.ordered,d=E.start,m="";for(let x=0;x<E.items.length;x++){let s=E.items[x];m+=this.listitem(s)}let b=e?"ol":"ul",A=e&&d!==1?' start="'+d+'"':"";return"<"+b+A+`>
`+m+"</"+b+`>
`}listitem(E){var d;let e="";if(E.task){let m=this.checkbox({checked:!!E.checked});E.loose?((d=E.tokens[0])==null?void 0:d.type)==="paragraph"?(E.tokens[0].text=m+" "+E.tokens[0].text,E.tokens[0].tokens&&E.tokens[0].tokens.length>0&&E.tokens[0].tokens[0].type==="text"&&(E.tokens[0].tokens[0].text=m+" "+ue(E.tokens[0].tokens[0].text),E.tokens[0].tokens[0].escaped=!0)):E.tokens.unshift({type:"text",raw:m+" ",text:m+" ",escaped:!0}):e+=m+" "}return e+=this.parser.parse(E.tokens,!!E.loose),`<li>${e}</li>
`}checkbox({checked:E}){return"<input "+(E?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:E}){return`<p>${this.parser.parseInline(E)}</p>
`}table(E){let e="",d="";for(let b=0;b<E.header.length;b++)d+=this.tablecell(E.header[b]);e+=this.tablerow({text:d});let m="";for(let b=0;b<E.rows.length;b++){let A=E.rows[b];d="";for(let x=0;x<A.length;x++)d+=this.tablecell(A[x]);m+=this.tablerow({text:d})}return m&&(m=`<tbody>${m}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+m+`</table>
`}tablerow({text:E}){return`<tr>
${E}</tr>
`}tablecell(E){let e=this.parser.parseInline(E.tokens),d=E.header?"th":"td";return(E.align?`<${d} align="${E.align}">`:`<${d}>`)+e+`</${d}>
`}strong({tokens:E}){return`<strong>${this.parser.parseInline(E)}</strong>`}em({tokens:E}){return`<em>${this.parser.parseInline(E)}</em>`}codespan({text:E}){return`<code>${ue(E,!0)}</code>`}br(E){return"<br>"}del({tokens:E}){return`<del>${this.parser.parseInline(E)}</del>`}link({href:E,title:e,tokens:d}){let m=this.parser.parseInline(d),b=gt(E);if(b===null)return m;E=b;let A='<a href="'+E+'"';return e&&(A+=' title="'+ue(e)+'"'),A+=">"+m+"</a>",A}image({href:E,title:e,text:d,tokens:m}){m&&(d=this.parser.parseInline(m,this.parser.textRenderer));let b=gt(E);if(b===null)return ue(d);E=b;let A=`<img src="${E}" alt="${d}"`;return e&&(A+=` title="${ue(e)}"`),A+=">",A}text(E){return"tokens"in E&&E.tokens?this.parser.parseInline(E.tokens):"escaped"in E&&E.escaped?E.text:ue(E.text)}},dt=class{strong({text:E}){return E}em({text:E}){return E}codespan({text:E}){return E}del({text:E}){return E}html({text:E}){return E}text({text:E}){return E}link({text:E}){return""+E}image({text:E}){return""+E}br(){return""}},fe=class st{constructor(e){Z(this,"options");Z(this,"renderer");Z(this,"textRenderer");this.options=e||Ce,this.options.renderer=this.options.renderer||new Ue,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new dt}static parse(e,d){return new st(d).parse(e)}static parseInline(e,d){return new st(d).parseInline(e)}parse(e,d=!0){var b,A;let m="";for(let x=0;x<e.length;x++){let s=e[x];if((A=(b=this.options.extensions)==null?void 0:b.renderers)!=null&&A[s.type]){let l=s,c=this.options.extensions.renderers[l.type].call({parser:this},l);if(c!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(l.type)){m+=c||"";continue}}let o=s;switch(o.type){case"space":{m+=this.renderer.space(o);continue}case"hr":{m+=this.renderer.hr(o);continue}case"heading":{m+=this.renderer.heading(o);continue}case"code":{m+=this.renderer.code(o);continue}case"table":{m+=this.renderer.table(o);continue}case"blockquote":{m+=this.renderer.blockquote(o);continue}case"list":{m+=this.renderer.list(o);continue}case"html":{m+=this.renderer.html(o);continue}case"paragraph":{m+=this.renderer.paragraph(o);continue}case"text":{let l=o,c=this.renderer.text(l);for(;x+1<e.length&&e[x+1].type==="text";)l=e[++x],c+=`
`+this.renderer.text(l);d?m+=this.renderer.paragraph({type:"paragraph",raw:c,text:c,tokens:[{type:"text",raw:c,text:c,escaped:!0}]}):m+=c;continue}default:{let l='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return m}parseInline(e,d=this.renderer){var b,A;let m="";for(let x=0;x<e.length;x++){let s=e[x];if((A=(b=this.options.extensions)==null?void 0:b.renderers)!=null&&A[s.type]){let l=this.options.extensions.renderers[s.type].call({parser:this},s);if(l!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(s.type)){m+=l||"";continue}}let o=s;switch(o.type){case"escape":{m+=d.text(o);break}case"html":{m+=d.html(o);break}case"link":{m+=d.link(o);break}case"image":{m+=d.image(o);break}case"strong":{m+=d.strong(o);break}case"em":{m+=d.em(o);break}case"codespan":{m+=d.codespan(o);break}case"br":{m+=d.br(o);break}case"del":{m+=d.del(o);break}case"text":{m+=d.text(o);break}default:{let l='Token with "'+o.type+'" type was not found.';if(this.options.silent)return console.error(l),"";throw new Error(l)}}}return m}},Qe,$e=(Qe=class{constructor(E){Z(this,"options");Z(this,"block");this.options=E||Ce}preprocess(E){return E}postprocess(E){return E}processAllTokens(E){return E}provideLexer(){return this.block?de.lex:de.lexInline}provideParser(){return this.block?fe.parse:fe.parseInline}},Z(Qe,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),Qe),As=class{constructor(...E){Z(this,"defaults",rt());Z(this,"options",this.setOptions);Z(this,"parse",this.parseMarkdown(!0));Z(this,"parseInline",this.parseMarkdown(!1));Z(this,"Parser",fe);Z(this,"Renderer",Ue);Z(this,"TextRenderer",dt);Z(this,"Lexer",de);Z(this,"Tokenizer",Ne);Z(this,"Hooks",$e);this.use(...E)}walkTokens(E,e){var m,b;let d=[];for(let A of E)switch(d=d.concat(e.call(this,A)),A.type){case"table":{let x=A;for(let s of x.header)d=d.concat(this.walkTokens(s.tokens,e));for(let s of x.rows)for(let o of s)d=d.concat(this.walkTokens(o.tokens,e));break}case"list":{let x=A;d=d.concat(this.walkTokens(x.items,e));break}default:{let x=A;(b=(m=this.defaults.extensions)==null?void 0:m.childTokens)!=null&&b[x.type]?this.defaults.extensions.childTokens[x.type].forEach(s=>{let o=x[s].flat(1/0);d=d.concat(this.walkTokens(o,e))}):x.tokens&&(d=d.concat(this.walkTokens(x.tokens,e)))}}return d}use(...E){let e=this.defaults.extensions||{renderers:{},childTokens:{}};return E.forEach(d=>{let m={...d};if(m.async=this.defaults.async||m.async||!1,d.extensions&&(d.extensions.forEach(b=>{if(!b.name)throw new Error("extension name required");if("renderer"in b){let A=e.renderers[b.name];A?e.renderers[b.name]=function(...x){let s=b.renderer.apply(this,x);return s===!1&&(s=A.apply(this,x)),s}:e.renderers[b.name]=b.renderer}if("tokenizer"in b){if(!b.level||b.level!=="block"&&b.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");let A=e[b.level];A?A.unshift(b.tokenizer):e[b.level]=[b.tokenizer],b.start&&(b.level==="block"?e.startBlock?e.startBlock.push(b.start):e.startBlock=[b.start]:b.level==="inline"&&(e.startInline?e.startInline.push(b.start):e.startInline=[b.start]))}"childTokens"in b&&b.childTokens&&(e.childTokens[b.name]=b.childTokens)}),m.extensions=e),d.renderer){let b=this.defaults.renderer||new Ue(this.defaults);for(let A in d.renderer){if(!(A in b))throw new Error(`renderer '${A}' does not exist`);if(["options","parser"].includes(A))continue;let x=A,s=d.renderer[x],o=b[x];b[x]=(...l)=>{let c=s.apply(b,l);return c===!1&&(c=o.apply(b,l)),c||""}}m.renderer=b}if(d.tokenizer){let b=this.defaults.tokenizer||new Ne(this.defaults);for(let A in d.tokenizer){if(!(A in b))throw new Error(`tokenizer '${A}' does not exist`);if(["options","rules","lexer"].includes(A))continue;let x=A,s=d.tokenizer[x],o=b[x];b[x]=(...l)=>{let c=s.apply(b,l);return c===!1&&(c=o.apply(b,l)),c}}m.tokenizer=b}if(d.hooks){let b=this.defaults.hooks||new $e;for(let A in d.hooks){if(!(A in b))throw new Error(`hook '${A}' does not exist`);if(["options","block"].includes(A))continue;let x=A,s=d.hooks[x],o=b[x];$e.passThroughHooks.has(A)?b[x]=l=>{if(this.defaults.async)return Promise.resolve(s.call(b,l)).then(a=>o.call(b,a));let c=s.call(b,l);return o.call(b,c)}:b[x]=(...l)=>{let c=s.apply(b,l);return c===!1&&(c=o.apply(b,l)),c}}m.hooks=b}if(d.walkTokens){let b=this.defaults.walkTokens,A=d.walkTokens;m.walkTokens=function(x){let s=[];return s.push(A.call(this,x)),b&&(s=s.concat(b.call(this,x))),s}}this.defaults={...this.defaults,...m}}),this}setOptions(E){return this.defaults={...this.defaults,...E},this}lexer(E,e){return de.lex(E,e??this.defaults)}parser(E,e){return fe.parse(E,e??this.defaults)}parseMarkdown(E){return(e,d)=>{let m={...d},b={...this.defaults,...m},A=this.onError(!!b.silent,!!b.async);if(this.defaults.async===!0&&m.async===!1)return A(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof e>"u"||e===null)return A(new Error("marked(): input parameter is undefined or null"));if(typeof e!="string")return A(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(e)+", string expected"));b.hooks&&(b.hooks.options=b,b.hooks.block=E);let x=b.hooks?b.hooks.provideLexer():E?de.lex:de.lexInline,s=b.hooks?b.hooks.provideParser():E?fe.parse:fe.parseInline;if(b.async)return Promise.resolve(b.hooks?b.hooks.preprocess(e):e).then(o=>x(o,b)).then(o=>b.hooks?b.hooks.processAllTokens(o):o).then(o=>b.walkTokens?Promise.all(this.walkTokens(o,b.walkTokens)).then(()=>o):o).then(o=>s(o,b)).then(o=>b.hooks?b.hooks.postprocess(o):o).catch(A);try{b.hooks&&(e=b.hooks.preprocess(e));let o=x(e,b);b.hooks&&(o=b.hooks.processAllTokens(o)),b.walkTokens&&this.walkTokens(o,b.walkTokens);let l=s(o,b);return b.hooks&&(l=b.hooks.postprocess(l)),l}catch(o){return A(o)}}}onError(E,e){return d=>{if(d.message+=`
Please report this to https://github.com/markedjs/marked.`,E){let m="<p>An error occurred:</p><pre>"+ue(d.message+"",!0)+"</pre>";return e?Promise.resolve(m):m}if(e)return Promise.reject(d);throw d}}},Se=new As;function G(E,e){return Se.parse(E,e)}G.options=G.setOptions=function(E){return Se.setOptions(E),G.defaults=Se.defaults,kt(G.defaults),G};G.getDefaults=rt;G.defaults=Ce;G.use=function(...E){return Se.use(...E),G.defaults=Se.defaults,kt(G.defaults),G};G.walkTokens=function(E,e){return Se.walkTokens(E,e)};G.parseInline=Se.parseInline;G.Parser=fe;G.parser=fe.parse;G.Renderer=Ue;G.TextRenderer=dt;G.Lexer=de;G.lexer=de.lex;G.Tokenizer=Ne;G.Hooks=$e;G.parse=G;G.options;G.setOptions;G.use;G.walkTokens;G.parseInline;fe.parse;de.lex;class Ls{constructor(){this.messagesContainer=null,this.messages=[],this.currentStreamingMessage=null,this.streamingMessageElement=null,this.currentToolCalls=[],this.toolCallElements=new Map,this.messageIdCounter=0,this.configureMarkdown()}configureMarkdown(){const e=new G.Renderer;e.code=(d,m)=>`<pre class="code-block"><code${m&&/^[a-zA-Z0-9_+-]*$/.test(m)?` class="language-${m}"`:""}>${this.escapeHtml(d)}</code></pre>`,e.codespan=d=>`<code class="inline-code">${this.escapeHtml(d)}</code>`,e.table=(d,m)=>`<div class="table-wrapper"><table class="markdown-table">
        <thead>${d}</thead>
        <tbody>${m}</tbody>
      </table></div>`,G.setOptions({renderer:e,breaks:!0,gfm:!0,sanitize:!1,highlight:null})}escapeHtml(e){const d=document.createElement("div");return d.textContent=e,d.innerHTML}initialize(){return this.messagesContainer=document.getElementById("aiMessages"),this.messagesContainer?(console.log("Message Renderer initialized"),!0):(console.error("Messages container not found"),!1)}addMessage(e,d,m={}){const b={id:`msg_${++this.messageIdCounter}`,content:e,sender:d,timestamp:new Date,chunks:m.chunks||[],toolCalls:m.toolCalls||[],isStreaming:m.isStreaming||!1,metadata:m.metadata||{}};return this.messages.push(b),this.renderMessage(b),this.scrollToBottom(),b}renderMessage(e){const d=document.createElement("div");d.className=`ai-message ai-message-${e.sender}`,d.setAttribute("data-message-id",e.id);const m=document.createElement("div");if(m.className="ai-message-timestamp",m.textContent=this.formatTimestamp(e.timestamp),d.appendChild(m),e.content){const b=document.createElement("div");b.className="ai-message-content",e.sender==="assistant"?b.innerHTML=G.parse(e.content):b.innerHTML=this.formatUserMessage(e.content),d.appendChild(b)}if(e.toolCalls&&e.toolCalls.length>0){const b=this.createToolCallsContainer(e.toolCalls,e.id);d.appendChild(b)}if(e.isStreaming){d.classList.add("streaming");const b=document.createElement("span");b.className="streaming-cursor",b.textContent="▋",d.appendChild(b)}this.messagesContainer.appendChild(d)}formatUserMessage(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"<br>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/`(.*?)`/g,'<code class="inline-code">$1</code>')}createToolCallsContainer(e,d){const m=document.createElement("div");return m.className="tool-calls-container",e.forEach((b,A)=>{const x=this.createToolCallElement(b,`${d}_tool_${A}`);m.appendChild(x)}),m}createToolCallElement(e,d){var l,c;const m=document.createElement("div");m.className="tool-call-item",m.setAttribute("data-tool-call-id",d);const b=document.createElement("div");b.className="tool-call-header",b.addEventListener("click",()=>this.toggleToolCall(d));const A=document.createElement("span");A.className="tool-call-toggle",A.textContent="▼";const x=document.createElement("span");x.className="tool-call-name",x.textContent=((l=e.function)==null?void 0:l.name)||"Tool Call";const s=document.createElement("span");s.className="tool-call-status",s.textContent=e.status||"pending",b.appendChild(A),b.appendChild(x),b.appendChild(s);const o=document.createElement("div");if(o.className="tool-call-content",(c=e.function)!=null&&c.arguments){const a=document.createElement("div");a.className="tool-call-section",a.innerHTML=`
        <div class="tool-call-section-title">Arguments:</div>
        <pre class="tool-call-args"><code>${this.formatToolCallArgs(e.function.arguments)}</code></pre>
      `,o.appendChild(a)}if(e.result){const a=document.createElement("div");a.className="tool-call-section",a.innerHTML=`
        <div class="tool-call-section-title">Result:</div>
        <pre class="tool-call-result"><code>${this.formatToolCallResult(e.result)}</code></pre>
      `,o.appendChild(a)}return m.appendChild(b),m.appendChild(o),m}toggleToolCall(e){const d=document.querySelector(`[data-tool-call-id="${e}"]`);if(!d)return;const m=d.querySelector(".tool-call-content"),b=d.querySelector(".tool-call-toggle");m.style.display==="none"?(m.style.display="block",b.textContent="▼",d.classList.remove("collapsed")):(m.style.display="none",b.textContent="▶",d.classList.add("collapsed"))}formatToolCallArgs(e){try{const d=typeof e=="string"?JSON.parse(e):e;return JSON.stringify(d,null,2)}catch{return String(e)}}formatToolCallResult(e){try{return JSON.stringify(e,null,2)}catch{return String(e)}}formatTimestamp(e){return e.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}startStreamingMessage(){const e=document.createElement("div");e.className="ai-message ai-message-assistant streaming";const d=document.createElement("div");d.className="ai-message-content",d.innerHTML='<span class="streaming-cursor">▋</span>',e.appendChild(d),this.messagesContainer.appendChild(e),this.currentStreamingMessage="",this.streamingMessageElement=d,this.currentToolCalls=[],this.toolCallElements.clear(),this.scrollToBottom()}handleStreamingChunk(e){if(this.streamingMessageElement)if(e.type==="content"){this.currentStreamingMessage+=e.content;const d=G.parse(this.currentStreamingMessage);this.streamingMessageElement.innerHTML=d+'<span class="streaming-cursor">▋</span>',this.scrollToBottom()}else e.type==="tool_call"&&this.handleToolCallChunk(e)}handleToolCallChunk(e){var s,o,l;const d=e.toolCall,m=d.index||0;if(!this.toolCallElements.has(m)){const c=document.createElement("div");c.className="tool-call-container",c.innerHTML=`
        <div class="tool-call-header">
          <span class="tool-call-icon">🔧</span>
          <span class="tool-call-name">${((s=d.function)==null?void 0:s.name)||"Loading..."}</span>
          <span class="tool-call-status">Preparing...</span>
        </div>
        <div class="tool-call-args"></div>
      `,this.streamingMessageElement.appendChild(c),this.toolCallElements.set(m,c)}const b=this.toolCallElements.get(m),A=b.querySelector(".tool-call-name"),x=b.querySelector(".tool-call-args");if((o=d.function)!=null&&o.name&&(A.textContent=d.function.name),(l=d.function)!=null&&l.arguments)try{const c=JSON.parse(d.function.arguments);x.innerHTML=`<pre><code>${JSON.stringify(c,null,2)}</code></pre>`}catch{x.textContent=d.function.arguments}this.scrollToBottom()}handleToolCall(e){e.type==="start"?this.updateToolCallStatus(e.toolCall,"Executing...","executing"):e.type==="complete"?(this.updateToolCallStatus(e.toolCall,"Completed","completed"),this.showToolCallResult(e.toolCall,e.result)):e.type==="error"&&(this.updateToolCallStatus(e.toolCall,"Error","error"),this.showToolCallResult(e.toolCall,{error:e.error}))}updateToolCallStatus(e,d,m){for(const[,b]of this.toolCallElements){const A=b.querySelector(".tool-call-name");if(A&&A.textContent===e.function.name){const x=b.querySelector(".tool-call-status");x.textContent=d,x.className=`tool-call-status ${m}`;break}}}showToolCallResult(e,d){for(const[,m]of this.toolCallElements){const b=m.querySelector(".tool-call-name");if(b&&b.textContent===e.function.name){let A=m.querySelector(".tool-call-result");A||(A=document.createElement("div"),A.className="tool-call-result",m.appendChild(A)),A.innerHTML=`<pre><code>${JSON.stringify(d,null,2)}</code></pre>`;break}}this.scrollToBottom()}handleStreamingComplete(e){if(this.streamingMessageElement){const d=this.streamingMessageElement.querySelector(".streaming-cursor");d&&d.remove(),this.preserveToolCallsAsMessages();const m=this.streamingMessageElement.closest(".ai-message");m&&m.classList.remove("streaming")}this.currentStreamingMessage=null,this.streamingMessageElement=null,this.currentToolCalls=[],this.toolCallElements.clear(),console.log("Streaming complete:",e)}preserveToolCallsAsMessages(){if(this.toolCallElements.size===0)return;const e=this.streamingMessageElement.closest(".ai-message"),d=[];for(let b=0;b<this.toolCallElements.size;b++)this.toolCallElements.has(b)&&d.push(this.toolCallElements.get(b));d.forEach(b=>{b.parentNode===this.streamingMessageElement&&b.remove()});let m=e;d.forEach(b=>{const A=document.createElement("div");A.className="ai-message ai-message-tool";const x=document.createElement("div");x.className="ai-message-content";const s=b.cloneNode(!0);x.appendChild(s),A.appendChild(x),m.parentNode.insertBefore(A,m.nextSibling),m=A,this.messages.push({content:b.outerHTML,sender:"tool",timestamp:new Date})}),this.scrollToBottom()}handleStreamingError(e){if(console.error("Streaming error:",e),this.streamingMessageElement){this.preserveToolCallsAsMessages(),this.streamingMessageElement.innerHTML=`<p class="error-message">Error: ${e.message}</p>`;const d=this.streamingMessageElement.closest(".ai-message");d&&(d.classList.remove("streaming"),d.classList.add("error"))}this.currentStreamingMessage=null,this.streamingMessageElement=null,this.currentToolCalls=[],this.toolCallElements.clear()}scrollToBottom(){this.messagesContainer.scrollTop=this.messagesContainer.scrollHeight}clearMessages(){this.messages=[],this.messagesContainer.innerHTML=`
      <div class="ai-message ai-message-assistant">
        <div class="ai-message-content">
          <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
        </div>
      </div>
    `}getMessages(){return[...this.messages]}}const ce=new Ls;class Rs{constructor(e={}){this.systemPrompt=e.systemPrompt||this.getDefaultSystemPrompt(),this.sessionId=e.sessionId||this.generateSessionId(),this.model=e.model||"gpt-3.5-turbo",this.tools=e.tools||[],this.maxToolCallIterations=e.maxToolCallIterations||30,this.config={apiKey:e.apiKey||"",endpoint:e.endpoint||"https://api.openai.com/v1/chat/completions",maxTokens:e.maxTokens||1e3,temperature:e.temperature||.7,stream:e.stream!==!1},this.messages=[],this.toolCallCount=0,this.isReady=!1,this.isPaused=!1,this.isTerminated=!1,this.pausePromise=null,this.pauseResolve=null,this.isProcessing=!1,this.onChunk=e.onChunk||null,this.onToolCall=e.onToolCall||null,this.onComplete=e.onComplete||null,this.onError=e.onError||null}getDefaultSystemPrompt(){return`你是一个交互式终端（macOS系统/bin/bash）自动化专家，正在与一个交互式终端协同工作，努力完成用户提交的任务。你有一个向终端输入的工具箱，要灵活运用该工具，并尽量优先将命令组合起来批量执行(即：command_group 模式)，尽最大努力完成用户提交的任务，尽量自主决策，不要过多向用户询问。

你的工具箱包含以下能力：
1. **terminal_input** - 向终端发送输入(优先使用：command_group)
   - single_key: 发送单个按键（如 Enter, Tab, Escape, ArrowUp 等）
   - key_combo: 发送组合键（如 Ctrl+C, Ctrl+Z, Alt+F4 等）
   - command_line: 发送单个命令行（如 ls -la, pwd, cat file.txt）
   - command_group: 发送多个命令序列（优先使用本模式）

2. **wait** - 等待指定时间
   - 在需要等待命令执行、界面加载等场景时使用

例如你可以精准使用工具完成以下场景：

**场景1：中断正在运行的程序**
- 使用 terminal_input 工具，key_combo 模式发送 Ctrl+C

**场景2：查找并编辑文件内容**
- 使用 command_line 模式输入：vi /tmp/nginx.conf
- 使用 wait 工具等待 vi 加载完成
- 如果未找到，使用 single_key 模式导航到下一页
- 找到后，使用命令序列移动光标到目标位置

**场景3：SSH连接远程服务器**
- 输入SSH连接命令
- 检查输出是否有信任主机提示
- 如有提示则输入"yes"，否则输入密码
- 执行远程命令

**场景4：文件操作和管理**
- 使用 ls 命令查看目录内容
- 使用 cd 命令切换目录
- 使用 cp, mv, rm 等命令进行文件操作
- 使用 grep, find 等命令搜索文件

**场景5：系统监控和诊断**
- 使用 ps, top 命令查看进程
- 使用 df, du 命令查看磁盘使用
- 使用 netstat, lsof 命令查看网络连接

要点：
- 积极主动，自主决策，高效使用工具完成任务
- 根据终端输出动态调整策略，确保对当前状态有准确理解
- 合理使用等待时间，确保命令执行完成后再获取输出
- 优先使用安全的只读命令进行探索
- 切勿推断终端状态，终端阅读当前屏幕内容决策下一步。例如：在历史会话中ssh到某台机器可能询问是否信任主机，你输入了yes，当再次ssh到同一台机器时，你不能依赖推断是否要再次输入yes，而应当结合当前屏幕内容和光标位置来决策是否要再次输入yes
`}generateSessionId(){return"agent_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)}async initialize(){if(!this.config.apiKey)throw new Error("API key is required");try{return await this.testConnection(),this.isReady=!0,console.log("AI Agent initialized successfully"),!0}catch(e){throw console.error("Failed to initialize AI Agent:",e),e}}async testConnection(){const e=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.model,messages:[{role:"user",content:"Hello"}],max_tokens:10,stream:!1})});if(!e.ok)throw new Error(`API test failed: ${e.status} ${e.statusText}`);return!0}addTool(e){this.tools.push(e)}removeTool(e){this.tools=this.tools.filter(d=>d.function.name!==e)}getToolDefinitions(){return this.tools.map(e=>({type:"function",function:e.function}))}async processMessage(e,d={}){if(!this.isReady)throw new Error("Agent not initialized");if(this.isTerminated)throw new Error("Agent has been terminated");this.isProcessing=!0;try{return this.messages.push({role:"user",content:e,timestamp:new Date().toISOString()}),this.toolCallCount=0,await this.runConversationLoop(d)}catch(m){throw this.onError&&this.onError(m),m}finally{this.isProcessing=!1}}async runConversationLoop(e={}){let d=0;for(;d<this.maxToolCallIterations;){if(this.isTerminated)throw console.log("Conversation loop terminated by user"),new Error("Agent terminated by user");if(this.isPaused&&(console.log("Agent paused, waiting for resume..."),await this.waitForResume(6e5)),this.isTerminated)throw console.log("Conversation loop terminated during pause"),new Error("Agent terminated by user");d++;let m="";if(typeof window<"u"&&window.xtermService&&typeof window.xtermService.getAllContent=="function"){const{lines:x,cursorX:s,cursorY:o}=window.xtermService.getAllContent();m=`

<terminalScreen><![CDATA[${x.join(`
`)}]]></terminalScreen>
<cursor x="${s}" y="${o}"/>
<!-- 上述内容为当前终端屏幕文本和光标位置，要始终以该内容和光标位置为准决策下一步操作，这一点很重要!!! -->
`}const b=[{role:"system",content:this.systemPrompt},...this.messages.map(x=>({role:x.role,content:x.content,tool_calls:x.tool_calls,tool_call_id:x.tool_call_id}))];if(b.length>0){const x=b[b.length-1];x.content+=m}const A=await this.callOpenAI(b);if(this.isTerminated)throw console.log("Conversation loop terminated after API call"),new Error("Agent terminated by user");if(A.finish_reason==="stop")return this.onComplete&&this.onComplete(A),A;if(A.finish_reason==="tool_calls"){await this.executeToolCalls(A.tool_calls);continue}else if(A.finish_reason==="length")return console.warn("Token limit reached"),this.onComplete&&this.onComplete(A),A}throw new Error(`Maximum tool call iterations (${this.maxToolCallIterations}) exceeded`)}async callOpenAI(e){const d={model:this.model,messages:e,max_tokens:this.config.maxTokens,temperature:this.config.temperature,stream:this.config.stream};this.tools.length>0&&(d.tools=this.getToolDefinitions(),d.tool_choice="auto");const m=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify(d)});if(!m.ok)throw new Error(`API request failed: ${m.status} ${m.statusText}`);if(this.config.stream)return await this.handleStreamingResponse(m);{const b=await m.json(),A=b.choices[0].message;return this.messages.push({role:"assistant",content:A.content,tool_calls:A.tool_calls,timestamp:new Date().toISOString()}),{content:A.content,tool_calls:A.tool_calls,finish_reason:b.choices[0].finish_reason}}}async handleStreamingResponse(e){var s,o,l,c;const d=e.body.getReader(),m=new TextDecoder;let b="",A=[],x=null;try{for(;;){const{done:a,value:f}=await d.read();if(a)break;const v=m.decode(f).split(`
`);for(const g of v)if(g.startsWith("data: ")){const t=g.slice(6);if(t==="[DONE]")break;try{const n=JSON.parse(t),i=(s=n.choices[0])==null?void 0:s.delta;if(i&&(i.content&&(b+=i.content,this.onChunk&&this.onChunk({type:"content",content:i.content,fullContent:b})),i.tool_calls))for(const r of i.tool_calls)A[r.index]||(A[r.index]={id:r.id,type:r.type,function:{name:"",arguments:""}}),(o=r.function)!=null&&o.name&&(A[r.index].function.name+=r.function.name),(l=r.function)!=null&&l.arguments&&(A[r.index].function.arguments+=r.function.arguments),this.onChunk&&this.onChunk({type:"tool_call",toolCall:r,allToolCalls:A});(c=n.choices[0])!=null&&c.finish_reason&&(x=n.choices[0].finish_reason)}catch{continue}}}}finally{d.releaseLock()}return this.messages.push({role:"assistant",content:b,tool_calls:A.length>0?A:void 0,timestamp:new Date().toISOString()}),{content:b,tool_calls:A.length>0?A:void 0,finish_reason:x}}async executeToolCalls(e){if(!(!e||e.length===0))for(const d of e){if(this.isTerminated)throw console.log("Tool execution terminated by user"),new Error("Agent terminated by user");try{this.toolCallCount++,this.onToolCall&&this.onToolCall({type:"start",toolCall:d,count:this.toolCallCount});const m=this.tools.find(x=>x.function.name===d.function.name);if(!m)throw new Error(`Tool not found: ${d.function.name}`);let b={};try{b=JSON.parse(d.function.arguments)}catch{throw new Error(`Invalid tool arguments: ${d.function.arguments}`)}const A=await m.execute(b);if(this.isTerminated)throw console.log("Tool execution terminated after tool completion"),new Error("Agent terminated by user");this.messages.push({role:"tool",content:JSON.stringify(A),tool_call_id:d.id,timestamp:new Date().toISOString()}),this.onToolCall&&this.onToolCall({type:"complete",toolCall:d,result:A,count:this.toolCallCount})}catch(m){if(console.error(`Tool execution error for ${d.function.name}:`,m),this.messages.push({role:"tool",content:JSON.stringify({error:m.message}),tool_call_id:d.id,timestamp:new Date().toISOString()}),this.onToolCall&&this.onToolCall({type:"error",toolCall:d,error:m.message,count:this.toolCallCount}),m.message==="Agent terminated by user")throw m}}}clearSession(){this.messages=[],this.toolCallCount=0}getMessages(){return[...this.messages]}updateConfig(e){this.config={...this.config,...e}}getStats(){return{sessionId:this.sessionId,model:this.model,toolCount:this.tools.length,messageCount:this.messages.length,toolCallCount:this.toolCallCount,maxIterations:this.maxToolCallIterations,isReady:this.isReady,isPaused:this.isPaused,isTerminated:this.isTerminated,isProcessing:this.isProcessing}}pause(){if(this.isPaused){console.log("Agent is already paused");return}this.isPaused=!0,this.pausePromise=new Promise(e=>{this.pauseResolve=e}),console.log(`Agent ${this.sessionId} paused`)}resume(){if(!this.isPaused){console.log("Agent is not paused");return}this.isPaused=!1,this.pauseResolve&&(this.pauseResolve(),this.pauseResolve=null),this.pausePromise=null,console.log(`Agent ${this.sessionId} resumed`)}terminate(){this.isTerminated=!0,this.isProcessing=!1,this.isPaused&&this.resume(),console.log(`Agent ${this.sessionId} terminated`)}resetTermination(){this.isTerminated=!1,console.log(`Agent ${this.sessionId} termination reset`)}getIsPaused(){return this.isPaused}getIsTerminated(){return this.isTerminated}getIsProcessing(){return this.isProcessing}async waitForResume(e=6e5){if(!this.isPaused)return;const d=new Promise((m,b)=>{setTimeout(()=>b(new Error("Pause timeout exceeded")),e)});try{await Promise.race([this.pausePromise,d])}catch(m){console.warn(`Agent ${this.sessionId} pause timeout:`,m.message),this.resume()}}}class Ds{constructor(){this.agents=new Map,this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7,stream:!0}}createAgent(e,d={}){const m={...this.defaultConfig,...d},b=new Rs({sessionId:e,...m});return this.agents.set(e,b),b}getAgent(e){return this.agents.get(e)}removeAgent(e){return this.agents.delete(e)}getAllSessions(){return Array.from(this.agents.keys())}updateDefaultConfig(e){this.defaultConfig={...this.defaultConfig,...e}}getStats(){return{agentCount:this.agents.size,sessions:Array.from(this.agents.keys()),defaultConfig:{...this.defaultConfig,apiKey:this.defaultConfig.apiKey?"***":""}}}}let Ts=class{constructor(){this.storageKey="ai-agent-config",this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7,stream:!0,maxToolCallIterations:30}}load(){try{const e=localStorage.getItem(this.storageKey);if(e)return{...this.defaultConfig,...JSON.parse(e)}}catch(e){console.warn("Failed to load AI agent config from localStorage:",e)}return{...this.defaultConfig}}save(e){try{return localStorage.setItem(this.storageKey,JSON.stringify(e)),!0}catch(d){return console.error("Failed to save AI agent config to localStorage:",d),!1}}reset(){try{return localStorage.removeItem(this.storageKey),{...this.defaultConfig}}catch(e){return console.error("Failed to reset AI agent config:",e),{...this.defaultConfig}}}validate(e){const d=[];return(!e.endpoint||!e.endpoint.startsWith("http"))&&d.push("Invalid endpoint URL"),(!e.model||e.model.trim()==="")&&d.push("Model ID is required"),(!e.apiKey||e.apiKey.trim()==="")&&d.push("API Key is required"),e.maxTokens&&(e.maxTokens<1||e.maxTokens>4e3)&&d.push("Max tokens must be between 1 and 4000"),e.temperature&&(e.temperature<0||e.temperature>2)&&d.push("Temperature must be between 0 and 2"),e.maxToolCallIterations&&(e.maxToolCallIterations<1||e.maxToolCallIterations>100)&&d.push("Max tool call iterations must be between 1 and 100"),{valid:d.length===0,errors:d}}};const pe=new Ds,Te=new Ts,Bs={function:{name:"terminal_input",description:"Send input to the terminal. Supports single keys, key combinations, command lines, or command sequences.",parameters:{type:"object",properties:{mode:{type:"string",enum:["single_key","key_combo","command_line","command_group"],description:"Input mode: single_key for individual keys, key_combo for key combinations, command_line for single commands, command_group for multiple commands"},single_key:{type:"string",description:"Single key to send (required when mode is single_key). Examples: Enter, Tab, Escape, ArrowUp, etc."},key_combo:{type:"string",description:"Key combination to send (required when mode is key_combo). Examples: Ctrl+C, Ctrl+Z, Alt+F4, etc."},command_line:{type:"string",description:"Command line to send (required when mode is command_line). Examples: ls -la, pwd, cat file.txt"},command_group:{type:"array",items:{type:"string"},description:"Array of commands to send sequentially (required when mode is command_group)"},execute:{type:"boolean",default:!0,description:"Whether to execute command immediately (press Enter). Only applies to command_line and command_group modes"},delay:{type:"number",default:100,description:"Delay in milliseconds between commands in command_group mode"}},required:["mode"],oneOf:[{properties:{mode:{const:"single_key"}},required:["single_key"]},{properties:{mode:{const:"key_combo"}},required:["key_combo"]},{properties:{mode:{const:"command_line"}},required:["command_line"]},{properties:{mode:{const:"command_group"}},required:["command_group"]}]}},async execute(E){try{if(!me.isServiceReady())return{success:!1,error:"Terminal service is not ready",mode:E.mode};const{mode:e,single_key:d,key_combo:m,command_line:b,command_group:A,execute:x=!0,delay:s=100}=E;let o;switch(e){case"single_key":return d?(await me.sendKey(d),{message:`按键已键入: ${d}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`}):{message:"single_key is required for single_key mode"};case"key_combo":return m?(await me.sendKeyCombo(m),{message:`组合键已键入: ${m}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`}):{message:"key_combo is required for key_combo mode"};case"command_line":return b?(await me.sendCommand(b,x),{message:`命令已键入: ${b}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`}):{message:"command_line is required for command_line mode"};case"command_group":return!A||!Array.isArray(A)?{message:"command_group must be an array for command_group mode"}:(await me.sendCommands(A,s),{message:`命令组已依次键入: ${A.join(" ; ")}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`});default:return{message:`Invalid mode: ${e}. Must be one of: single_key, key_combo, command_line, command_group`}}}catch(e){return{success:!1,error:e.message,mode:E.mode}}}},Is={function:{name:"wait",description:"Wait for a specified duration before continuing. Useful when waiting for commands to complete or UI to load.",parameters:{type:"object",properties:{duration:{type:"number",minimum:100,maximum:3e4,description:"Duration to wait in milliseconds (100ms to 30s)"},reason:{type:"string",description:"Optional reason for waiting (for logging/debugging purposes)"}},required:["duration"]}},async execute(E){try{const{duration:e,reason:d}=E;if(e<100||e>3e4)return{success:!1,error:"Duration must be between 100ms and 30000ms (30 seconds)",duration:e};const m=Date.now();await new Promise(A=>setTimeout(A,e));const b=Date.now()-m;return{success:!0,duration:e,actual_duration:b,reason:d||"No reason specified",message:`Waited for ${b}ms${d?` (${d})`:""}`}}catch(e){return{success:!1,error:e.message,duration:E.duration}}}};function Ms(E){return E.addTool(Bs),E.addTool(Is),E}class Ps{constructor(){this.sessionId=this.generateSessionId(),this.currentAgent=null,this.onChunk=null,this.onToolCall=null,this.onComplete=null,this.onError=null}async initialize(){return console.log("AI Agent Integration initialized"),await this.initializeAgent(),!0}async initializeAgent(){let e=pe.getAgent(this.sessionId);if(!e){const d=Te.load();e=pe.createAgent(this.sessionId,{...d,onChunk:m=>this.handleChunk(m),onToolCall:m=>this.handleToolCall(m),onComplete:m=>this.handleComplete(m),onError:m=>this.handleError(m)}),Ms(e),await e.initialize(),this.currentAgent=e}return e}async sendMessage(e,d={}){let m=this.currentAgent;try{await m.processMessage(e,d)}catch(b){this.handleError(b)}}handleChunk(e){this.onChunk&&this.onChunk(e)}handleToolCall(e){this.onToolCall&&this.onToolCall(e)}handleComplete(e){this.onComplete&&this.onComplete(e)}handleError(e){this.onError&&this.onError(e)}async configureAgent(e){try{const d=Te.validate(e);if(!d.valid)throw new Error(`Invalid configuration: ${d.errors.join(", ")}`);return Te.save(e),pe.updateDefaultConfig(e),this.currentAgent&&(pe.removeAgent(this.sessionId),this.currentAgent=null),{success:!0,message:"AI agent configured successfully"}}catch(d){return console.error("Error configuring AI agent:",d),{success:!1,error:d.message}}}getAgentConfig(){return Te.load()}async testAgentConnection(){try{const e=Te.load(),d=pe.createAgent("test-session",e);await d.initialize();const m=await d.processMessage("Hello, this is a test message.",{});return pe.removeAgent("test-session"),{success:!0,message:"Connection test successful",response:m.content}}catch(e){return console.error("AI agent connection test failed:",e),{success:!1,error:e.message}}}getAgentStats(){return this.currentAgent?this.currentAgent.getStats():pe.getStats()}clearSession(){this.currentAgent&&this.currentAgent.clearSession()}resetSession(){this.currentAgent&&(pe.removeAgent(this.sessionId),this.currentAgent=null),this.sessionId=this.generateSessionId()}getSessionHistory(){return this.currentAgent?this.currentAgent.getMessages():[]}generateSessionId(){return"session_"+Date.now()+"_"+Math.random().toString(36).substring(2,11)}getSessionId(){return this.sessionId}setCallbacks(e){this.onChunk=e.onChunk||null,this.onToolCall=e.onToolCall||null,this.onComplete=e.onComplete||null,this.onError=e.onError||null}isAgentReady(){return this.currentAgent&&this.currentAgent.isReady}pauseAgent(){return this.currentAgent?(this.currentAgent.pause(),!0):!1}resumeAgent(){return this.currentAgent?(this.currentAgent.resume(),!0):!1}terminateAgent(){return this.currentAgent?(this.currentAgent.terminate(),!0):!1}resetAgentTermination(){return this.currentAgent?(this.currentAgent.resetTermination(),!0):!1}isAgentPaused(){return this.currentAgent?this.currentAgent.getIsPaused():!1}isAgentTerminated(){return this.currentAgent?this.currentAgent.getIsTerminated():!1}isAgentProcessing(){return this.currentAgent?this.currentAgent.getIsProcessing():!1}getAgentControlState(){return this.currentAgent?{isPaused:this.currentAgent.getIsPaused(),isTerminated:this.currentAgent.getIsTerminated(),isProcessing:this.currentAgent.getIsProcessing(),isReady:this.currentAgent.isReady}:{isPaused:!1,isTerminated:!1,isProcessing:!1,isReady:!1}}getTerminalContext(){const e={};if(window.xtermService&&window.xtermService.isServiceReady())try{e.currentLine=window.xtermService.getCurrentLineContent(),e.terminalDimensions=window.xtermService.getTerminalDimensions(),e.cursorPosition=window.xtermService.getCursorPosition();const d=window.xtermService.getLineRangeContent(Math.max(0,e.cursorPosition.y-5),e.cursorPosition.y);e.recentOutput=d.join(`
`)}catch(d){console.warn("Error getting terminal context:",d)}return e}}const te=new Ps;class Os{constructor(){}initialize(){return console.log("Terminal Integration initialized"),!0}async executeCommands(e){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for command execution"),[];const d=[];for(const m of e)try{console.log("Executing AI-requested command:",m);const b=window.xtermService.getCursorPosition();m.type==="single_key"?await window.xtermService.sendKey(m.key):m.type==="key_combo"?await window.xtermService.sendKeyCombo(m.combination):m.type==="command"?await window.xtermService.sendCommand(m.command,m.execute!==!1):m.type==="commands"&&await window.xtermService.sendCommands(m.commands,m.delay||100),await new Promise(s=>setTimeout(s,500));const A=window.xtermService.getCursorPosition();let x="";A.y>b.y&&(x=window.xtermService.getLineRangeContent(b.y,A.y).join(`
`).trim()),d.push({command:m,output:x,success:!0}),e.length>1&&await new Promise(s=>setTimeout(s,200))}catch(b){console.error("Error executing command in terminal:",b),d.push({command:m,error:b.message,success:!1})}return console.log("AI command execution results:",d),d}async provideExecutionFeedback(e){try{const m=`Command execution results:

${e.map(b=>b.success?`Command executed: ${JSON.stringify(b.command)}
Output: ${b.output||"(no output)"}`:`Command failed: ${JSON.stringify(b.command)}
Error: ${b.error}`).join(`

`)}`;console.log("Providing execution feedback to AI:",m)}catch(d){console.error("Error providing execution feedback:",d)}}async executeCommand(e){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for command execution"),!1;try{return await window.xtermService.sendCommand(e,!0),console.log("Manual command executed:",e),!0}catch(d){return console.error("Error executing manual command:",d),!1}}async sendTextToTerminal(e){if(!window.xtermService||!window.xtermService.isServiceReady())return console.warn("XTerm service not available for text input"),!1;try{return await window.xtermService.sendRawData(e),console.log("Text sent to terminal:",e),!0}catch(d){return console.error("Error sending text to terminal:",d),!1}}getTerminalOutput(e=10){if(!window.xtermService||!window.xtermService.isServiceReady())return"";try{const d=window.xtermService.getCursorPosition(),m=Math.max(0,d.y-e);return window.xtermService.getLineRangeContent(m,d.y).join(`
`).trim()}catch(d){return console.error("Error getting terminal output:",d),""}}getTerminalContext(){const e={};if(window.xtermService&&window.xtermService.isServiceReady())try{e.currentLine=window.xtermService.getCurrentLineContent(),e.terminalDimensions=window.xtermService.getTerminalDimensions(),e.cursorPosition=window.xtermService.getCursorPosition();const d=window.xtermService.getLineRangeContent(Math.max(0,e.cursorPosition.y-5),e.cursorPosition.y);e.recentOutput=d.join(`
`)}catch(d){console.warn("Error getting terminal context:",d)}return e}isTerminalReady(){return window.xtermService&&window.xtermService.isServiceReady()}}const ye=new Os;class Hs{constructor(e,d,m){this.inputElement=e,this.onSend=d,this.onInput=m,this.isComposing=!1,this.compositionData="",this.setupEventListeners()}setupEventListeners(){this.inputElement.addEventListener("compositionstart",e=>this.handleCompositionStart(e)),this.inputElement.addEventListener("compositionupdate",e=>this.handleCompositionUpdate(e)),this.inputElement.addEventListener("compositionend",e=>this.handleCompositionEnd(e)),this.inputElement.addEventListener("keydown",e=>this.handleKeyDown(e)),this.inputElement.addEventListener("keyup",e=>this.handleKeyUp(e)),this.inputElement.addEventListener("input",e=>this.handleInput(e)),this.setupAutoResize()}handleCompositionStart(e){this.isComposing=!0,this.compositionData="",console.log("Composition started")}handleCompositionUpdate(e){this.compositionData=e.data||"",console.log("Composition update:",this.compositionData)}handleCompositionEnd(e){this.isComposing=!1,this.compositionData=e.data||"",console.log("Composition ended:",this.compositionData),this.onInput&&this.onInput(this.inputElement.value)}handleKeyDown(e){if(this.isComposing){console.log("Key pressed during composition, ignoring:",e.key);return}if(e.key==="Enter"){if(e.shiftKey)return;e.preventDefault(),this.sendMessage()}}handleKeyUp(e){}handleInput(e){this.isComposing||this.onInput&&this.onInput(this.inputElement.value)}sendMessage(){const e=this.inputElement.value.trim();e&&this.onSend&&this.onSend(e)}setupAutoResize(){const e=()=>{this.inputElement.style.height="auto",this.inputElement.style.height=Math.min(this.inputElement.scrollHeight,120)+"px"};this.inputElement.addEventListener("input",e),e()}clearInput(){this.inputElement.value="",this.inputElement.style.height="auto"}focus(){this.inputElement.focus()}getValue(){return this.inputElement.value}setValue(e){this.inputElement.value=e,this.inputElement.style.height="auto",this.inputElement.style.height=Math.min(this.inputElement.scrollHeight,120)+"px"}getIsComposing(){return this.isComposing}destroy(){this.inputElement.removeEventListener("compositionstart",this.handleCompositionStart),this.inputElement.removeEventListener("compositionupdate",this.handleCompositionUpdate),this.inputElement.removeEventListener("compositionend",this.handleCompositionEnd),this.inputElement.removeEventListener("keydown",this.handleKeyDown),this.inputElement.removeEventListener("keyup",this.handleKeyUp),this.inputElement.removeEventListener("input",this.handleInput)}}class Fs{constructor(){this.input=null,this.sendBtn=null,this.inputHandler=null,this.isInitialized=!1}initialize(){if(this.input=document.getElementById("aiInput"),this.sendBtn=document.getElementById("aiSend"),!this.input||!this.sendBtn)return console.error("AI Dialog input elements not found"),!1;const e=re.initialize(),d=ce.initialize(),m=te.initialize(),b=ye.initialize();return!e||!d||!m||!b?(console.error("Failed to initialize AI Dialog modules"),!1):(this.setupEnhancedInput(),this.setupCallbacks(),this.isInitialized=!0,console.log("Refactored AI Dialog Service initialized"),!0)}setupEnhancedInput(){this.inputHandler=new Hs(this.input,e=>this.sendMessage(e),e=>this.handleInputChange(e)),this.sendBtn.addEventListener("click",()=>this.handleSendButtonClick())}handleInputChange(e){}setupCallbacks(){re.setCallbacks({onShow:()=>{this.input.focus(),this.updateUIState()},onHide:()=>{},onPauseResume:()=>{this.handlePauseResume()}}),te.setCallbacks({onChunk:e=>ce.handleStreamingChunk(e),onToolCall:e=>ce.handleToolCall(e),onComplete:e=>{ce.handleStreamingComplete(e),this.handleAgentComplete()},onError:e=>{ce.handleStreamingError(e),this.handleAgentComplete()}})}handleSendButtonClick(){re.isSendButtonInTerminateMode()?this.terminateAgent():this.sendMessage()}async sendMessage(e=null){const d=e||(this.inputHandler?this.inputHandler.getValue().trim():this.input.value.trim());if(d){te.resetAgentTermination(),ce.addMessage(d,"user",{timestamp:new Date,metadata:{source:"user_input"}}),this.inputHandler?this.inputHandler.clearInput():(this.input.value="",this.input.style.height="auto"),re.setSendButtonToTerminate(),ce.startStreamingMessage();try{const m=ye.getTerminalContext();await te.sendMessage(d,m)}catch(m){console.error("Error sending message to AI agent:",m),ce.handleStreamingError(m),this.handleAgentComplete()}}}terminateAgent(){console.log("Terminating agent..."),te.terminateAgent(),this.handleAgentComplete()}handleAgentComplete(){re.setSendButtonToSend(),this.sendBtn.disabled=!1,this.updateUIState()}handlePauseResume(){te.getAgentControlState().isPaused?(te.resumeAgent(),console.log("Agent resumed")):(te.pauseAgent(),console.log("Agent paused")),this.updateUIState()}updateUIState(){const e=te.getAgentControlState();re.updatePauseResumeButton(e.isPaused),re.setPauseResumeButtonEnabled(e.isReady),!e.isProcessing&&!re.isSendButtonInTerminateMode()&&(this.sendBtn.disabled=!1)}show(){re.showDialog(),this.updateUIState()}hide(){re.hideDialog()}toggle(){re.toggleDialog()}isVisible(){return re.isDialogVisible()}clearMessages(){ce.clearMessages()}getMessages(){return ce.getMessages()}async configureAIAgent(e){return await te.configureAgent(e)}getAIAgentConfig(){return te.getAgentConfig()}async testAIAgentConnection(){return await te.testAgentConnection()}getAIAgentStats(){return te.getAgentStats()}clearSession(){te.clearSession(),this.clearMessages()}resetSession(){te.resetSession(),this.clearMessages()}getSessionHistory(){return te.getSessionHistory()}getSessionId(){return te.getSessionId()}async executeCommand(e){return await ye.executeCommand(e)}async sendTextToTerminal(e){return await ye.sendTextToTerminal(e)}getTerminalOutput(e=10){return ye.getTerminalOutput(e)}isServiceInitialized(){return this.isInitialized}isAIAgentReady(){return te.isAgentReady()}isTerminalReady(){return ye.isTerminalReady()}getServiceStatus(){return{initialized:this.isInitialized,dialogVisible:this.isVisible(),aiAgentReady:this.isAIAgentReady(),terminalReady:this.isTerminalReady(),sessionId:this.getSessionId(),messageCount:this.getMessages().length}}getAgentControlState(){return te.getAgentControlState()}pauseAgent(){return te.pauseAgent()}resumeAgent(){return te.resumeAgent()}terminateAgent(){return te.terminateAgent()}setSendButtonToTerminate(){return re.setSendButtonToTerminate()}setSendButtonToSend(){return re.setSendButtonToSend()}isSendButtonInTerminateMode(){return re.isSendButtonInTerminateMode()}}const Me=new Fs;class $s{constructor(){this.config={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7},this.sessions=new Map,this.isReady=!1,this.systemPrompt=`You are a helpful AI assistant for a web terminal application. You can help users with:

1. **Terminal Commands**: Explain, suggest, and help execute terminal commands
2. **File Operations**: Help with file management, navigation, and manipulation  
3. **System Administration**: Provide guidance on system tasks and troubleshooting
4. **Programming**: Help with coding tasks, debugging, and development workflows

You have access to a web terminal where you can suggest commands for the user to execute.
When suggesting commands, always:
- Explain what the command does
- Mention any potential risks or side effects
- Provide context about when and why to use it

You can suggest commands by mentioning them in your response. The system may automatically execute safe commands you suggest.

Current context: Web terminal environment with xterm.js frontend.`}async initialize(e={}){if(this.config={...this.config,...e},!this.config.apiKey)return console.warn("AI Agent: No API key provided. Please set API key before using."),!1;try{return await this.testConnection(),this.isReady=!0,console.log("Frontend AI Agent initialized successfully"),!0}catch(d){return console.error("Failed to initialize AI Agent:",d),this.isReady=!1,!1}}async testConnection(){const e=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model,messages:[{role:"user",content:"Hello"}],max_tokens:10})});if(!e.ok)throw new Error(`API test failed: ${e.status} ${e.statusText}`);return!0}updateConfig(e){this.config={...this.config,...e},console.log("AI Agent configuration updated:",this.config)}getConfig(){const{apiKey:e,...d}=this.config;return{...d,apiKey:e?"***":""}}isAgentReady(){return this.isReady&&this.config.apiKey}async processMessage(e,d={}){var x;if(!this.isAgentReady())throw new Error("AI Agent not ready. Please check configuration.");const m=d.sessionId||"default",b=d.context||{},A=this.getSession(m);A.messages.push({role:"user",content:e,timestamp:new Date().toISOString()});try{const s=[{role:"system",content:this.systemPrompt},...A.messages.map(f=>({role:f.role,content:f.content}))];Object.keys(b).length>0&&s.push({role:"system",content:`Current context: ${JSON.stringify(b,null,2)}`});const o=await fetch(this.config.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.config.apiKey}`},body:JSON.stringify({model:this.config.model,messages:s,max_tokens:this.config.maxTokens,temperature:this.config.temperature})});if(!o.ok)throw new Error(`API request failed: ${o.status} ${o.statusText}`);const l=await o.json(),c=l.choices[0].message.content;A.messages.push({role:"assistant",content:c,timestamp:new Date().toISOString()});const a=this.extractCommandsFromResponse(c);return{content:c,commands:a,sessionId:m,timestamp:new Date().toISOString(),tokensUsed:((x=l.usage)==null?void 0:x.total_tokens)||0}}catch(s){throw console.error("Error processing message:",s),s}}extractCommandsFromResponse(e){const d=[],m=[/```(?:bash|shell|sh)?\s*\n([^`]+)\n```/gi,/`([a-z]+(?:\s+[-\w\.\/]+)*)`/gi];for(const A of m){let x;for(;(x=A.exec(e))!==null;){const s=x[1].trim();this.isValidCommand(s)&&d.push({type:"command",command:s,execute:!0})}}return d.filter((A,x,s)=>x===s.findIndex(o=>o.command===A.command)).slice(0,3)}isValidCommand(e){const d=e.split(" ")[0];return["ls","pwd","whoami","date","uptime","uname","which","cat","head","tail","grep","find","wc","sort","uniq","ps","top","df","du","free","echo","history","clear"].includes(d)&&e.length<100&&!e.includes("&&")&&!e.includes("||")&&!e.includes(";")}getSession(e){this.sessions.has(e)||this.sessions.set(e,{id:e,messages:[],createdAt:new Date().toISOString(),lastActivity:new Date().toISOString()});const d=this.sessions.get(e);return d.lastActivity=new Date().toISOString(),d}getSessionHistory(e){const d=this.sessions.get(e);return d?d.messages:[]}clearSession(e){this.sessions.delete(e)}getAllSessions(){return Array.from(this.sessions.keys())}getStats(){return{ready:this.isReady,model:this.config.model,endpoint:this.config.endpoint,activeSessions:this.sessions.size,totalMessages:Array.from(this.sessions.values()).reduce((e,d)=>e+d.messages.length,0)}}}class zs{constructor(){this.storageKey="ai-agent-config",this.defaultConfig={apiKey:"",endpoint:"https://api.openai.com/v1/chat/completions",model:"gpt-3.5-turbo",maxTokens:1e3,temperature:.7}}load(){try{const e=localStorage.getItem(this.storageKey);if(e)return{...this.defaultConfig,...JSON.parse(e)}}catch(e){console.warn("Failed to load AI agent config from localStorage:",e)}return{...this.defaultConfig}}save(e){try{return localStorage.setItem(this.storageKey,JSON.stringify(e)),!0}catch(d){return console.error("Failed to save AI agent config to localStorage:",d),!1}}reset(){try{return localStorage.removeItem(this.storageKey),{...this.defaultConfig}}catch(e){return console.error("Failed to reset AI agent config:",e),{...this.defaultConfig}}}validate(e){const d=[];return(!e.endpoint||!e.endpoint.startsWith("http"))&&d.push("Invalid endpoint URL"),(!e.model||e.model.trim()==="")&&d.push("Model ID is required"),(!e.apiKey||e.apiKey.trim()==="")&&d.push("API Key is required"),e.maxTokens&&(e.maxTokens<1||e.maxTokens>4e3)&&d.push("Max tokens must be between 1 and 4000"),e.temperature&&(e.temperature<0||e.temperature>2)&&d.push("Temperature must be between 0 and 2"),{valid:d.length===0,errors:d}}}const it=new zs,Ws=new $s;class Ns{constructor(){this.isVisible=!1,this.dialog=null,this.form=null,this.statusElement=null,this.settingsBtn=null,this.closeBtn=null}initialize(){return this.dialog=document.getElementById("settingsDialog"),this.form=document.getElementById("aiSettingsForm"),this.statusElement=document.getElementById("settingsStatus"),this.settingsBtn=document.getElementById("settingsBtn"),this.closeBtn=document.getElementById("settingsDialogClose"),!this.dialog||!this.form||!this.statusElement||!this.settingsBtn||!this.closeBtn?(console.error("Settings dialog elements not found"),!1):(this.setupEventListeners(),this.loadCurrentSettings(),console.log("Settings Dialog Service initialized"),!0)}setupEventListeners(){this.settingsBtn.addEventListener("click",()=>this.show()),this.closeBtn.addEventListener("click",()=>this.hide()),this.dialog.addEventListener("click",e=>{e.target===this.dialog&&this.hide()}),this.form.addEventListener("submit",e=>{e.preventDefault(),this.saveSettings()}),document.getElementById("testConnection").addEventListener("click",()=>{this.testConnection()}),document.getElementById("resetSettings").addEventListener("click",()=>{this.resetSettings()}),document.addEventListener("keydown",e=>{e.key==="Escape"&&this.isVisible&&this.hide()})}loadCurrentSettings(){const e=it.load();document.getElementById("apiKey").value=e.apiKey||"",document.getElementById("endpoint").value=e.endpoint||"",document.getElementById("model").value=e.model||"",document.getElementById("maxTokens").value=e.maxTokens||"",document.getElementById("temperature").value=e.temperature||""}show(){this.isVisible=!0,this.dialog.classList.remove("settings-dialog-hidden"),this.loadCurrentSettings(),this.clearStatus(),document.getElementById("apiKey").focus()}hide(){this.isVisible=!1,this.dialog.classList.add("settings-dialog-hidden"),this.clearStatus()}async saveSettings(){try{const e=new FormData(this.form),d={apiKey:e.get("apiKey").trim(),endpoint:e.get("endpoint").trim(),model:e.get("model").trim(),maxTokens:parseInt(e.get("maxTokens"))||1e3,temperature:parseFloat(e.get("temperature"))||.7},m=await Me.configureAIAgent(d);m.success?(this.showStatus("Settings saved successfully!","success"),setTimeout(()=>{this.hide()},1500)):this.showStatus(`Error: ${m.error}`,"error")}catch(e){console.error("Error saving settings:",e),this.showStatus(`Error saving settings: ${e.message}`,"error")}}async testConnection(){try{this.showStatus("Testing connection...","info");const e=new FormData(this.form),d={apiKey:e.get("apiKey").trim(),endpoint:e.get("endpoint").trim(),model:e.get("model").trim(),maxTokens:parseInt(e.get("maxTokens"))||1e3,temperature:parseFloat(e.get("temperature"))||.7};await Me.configureAIAgent(d);const m=await Me.testAIAgentConnection();m.success?this.showStatus("Connection test successful!","success"):this.showStatus(`Connection test failed: ${m.error}`,"error")}catch(e){console.error("Error testing connection:",e),this.showStatus(`Connection test failed: ${e.message}`,"error")}}resetSettings(){if(confirm("Are you sure you want to reset all settings to defaults?")){const e=it.reset();document.getElementById("apiKey").value=e.apiKey,document.getElementById("endpoint").value=e.endpoint,document.getElementById("model").value=e.model,document.getElementById("maxTokens").value=e.maxTokens,document.getElementById("temperature").value=e.temperature,this.showStatus("Settings reset to defaults","success")}}showStatus(e,d="info"){this.statusElement.textContent=e,this.statusElement.className=`settings-status ${d}`}clearStatus(){this.statusElement.textContent="",this.statusElement.className="settings-status"}isDialogVisible(){return this.isVisible}}const Pt=new Ns;class Us{constructor(e,d={}){this.url=e,this.options={maxReconnectAttempts:d.maxReconnectAttempts||10,reconnectInterval:d.reconnectInterval||1e3,maxReconnectInterval:d.maxReconnectInterval||3e4,reconnectDecay:d.reconnectDecay||1.5,timeoutInterval:d.timeoutInterval||2e3,enableLogging:d.enableLogging!==!1,...d},this.socket=null,this.reconnectAttempts=0,this.reconnectTimer=null,this.isReconnecting=!1,this.shouldReconnect=!0,this.connectionState="disconnected",this.onOpen=null,this.onMessage=null,this.onClose=null,this.onError=null,this.onReconnecting=null,this.onReconnected=null,this.onMaxReconnectAttemptsReached=null,this.log("WebSocket Manager initialized")}connect(){if(this.socket&&(this.socket.readyState===WebSocket.CONNECTING||this.socket.readyState===WebSocket.OPEN)){this.log("Already connected or connecting");return}this.connectionState="connecting",this.log(`Connecting to ${this.url}`);try{this.socket=new WebSocket(this.url),this.setupEventListeners()}catch(e){this.log(`Failed to create WebSocket: ${e.message}`),this.handleConnectionError()}}setupEventListeners(){this.socket&&(this.socket.addEventListener("open",e=>{this.log("WebSocket connected"),this.connectionState="connected",this.reconnectAttempts=0,this.isReconnecting=!1,this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.onOpen&&this.onOpen(e),this.reconnectAttempts>0&&this.onReconnected&&this.onReconnected(e)}),this.socket.addEventListener("message",e=>{this.onMessage&&this.onMessage(e)}),this.socket.addEventListener("close",e=>{this.log(`WebSocket closed: code=${e.code}, reason=${e.reason}, wasClean=${e.wasClean}`),this.connectionState="disconnected",this.onClose&&this.onClose(e),this.shouldReconnect&&!e.wasClean&&this.handleConnectionError()}),this.socket.addEventListener("error",e=>{this.log(`WebSocket error: ${e}`),this.onError&&this.onError(e),this.handleConnectionError()}))}handleConnectionError(){if(!this.shouldReconnect){this.log("Reconnection disabled, not attempting to reconnect");return}if(this.reconnectAttempts>=this.options.maxReconnectAttempts){this.log(`Max reconnection attempts (${this.options.maxReconnectAttempts}) reached`),this.shouldReconnect=!1,this.onMaxReconnectAttemptsReached&&this.onMaxReconnectAttemptsReached();return}this.reconnectAttempts++,this.isReconnecting=!0,this.connectionState="reconnecting";const e=Math.min(this.options.reconnectInterval*Math.pow(this.options.reconnectDecay,this.reconnectAttempts-1),this.options.maxReconnectInterval);this.log(`Attempting to reconnect in ${e}ms (attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`),this.onReconnecting&&this.onReconnecting(this.reconnectAttempts,e),this.reconnectTimer=setTimeout(()=>{this.log(`Reconnection attempt ${this.reconnectAttempts}`),this.connect()},e)}send(e){return this.socket&&this.socket.readyState===WebSocket.OPEN?(this.socket.send(e),!0):(this.log("Cannot send data: WebSocket is not connected"),!1)}close(e=1e3,d="Normal closure"){this.shouldReconnect=!1,this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null),this.socket&&this.socket.close(e,d),this.connectionState="disconnected",this.log("WebSocket connection closed manually")}getConnectionState(){return this.connectionState}isConnected(){return this.socket&&this.socket.readyState===WebSocket.OPEN}enableReconnection(){this.shouldReconnect=!0,this.reconnectAttempts=0}disableReconnection(){this.shouldReconnect=!1,this.reconnectTimer&&(clearTimeout(this.reconnectTimer),this.reconnectTimer=null)}resetReconnectionAttempts(){this.reconnectAttempts=0}getStats(){return{url:this.url,connectionState:this.connectionState,reconnectAttempts:this.reconnectAttempts,isReconnecting:this.isReconnecting,shouldReconnect:this.shouldReconnect,isConnected:this.isConnected(),readyState:this.socket?this.socket.readyState:null}}log(e){this.options.enableLogging&&console.log(`[WebSocketManager] ${e}`)}}class js{constructor(){this.container=null,this.keyboardElement=null,this.isVisible=!1,this.currentLayout="default",this.shiftPressed=!1,this.ctrlPressed=!1,this.altPressed=!1,this.onKeyPress=null,this.layouts={default:[["`","1","2","3","4","5","6","7","8","9","0","-","=","Backspace"],["Tab","q","w","e","r","t","y","u","i","o","p","[","]","\\"],["CapsLock","a","s","d","f","g","h","j","k","l",";","'","Enter"],["Shift","z","x","c","v","b","n","m",",",".","/","Shift"],["Ctrl","Alt","Space","Alt","Ctrl","ArrowLeft","ArrowUp","ArrowDown","ArrowRight"]],shift:[["~","!","@","#","$","%","^","&","*","(",")","_","+","Backspace"],["Tab","Q","W","E","R","T","Y","U","I","O","P","{","}","|"],["CapsLock","A","S","D","F","G","H","J","K","L",":",'"',"Enter"],["Shift","Z","X","C","V","B","N","M","<",">","?","Shift"],["Ctrl","Alt","Space","Alt","Ctrl","ArrowLeft","ArrowUp","ArrowDown","ArrowRight"]],function:[["Esc","F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12","Delete"],["Insert","Home","PageUp","","","","","","","","","","",""],["Delete","End","PageDown","","","","","","","","","","",""],["","","","","","","","","","","","","",""],["","","","","","","","","","","","","",""]]},this.keyDisplayNames={Backspace:"⌫",Tab:"⇥",CapsLock:"⇪",Enter:"⏎",Shift:"⇧",Ctrl:"Ctrl",Alt:"Alt",Space:"␣",ArrowLeft:"←",ArrowUp:"↑",ArrowDown:"↓",ArrowRight:"→",Esc:"Esc",Delete:"Del",Insert:"Ins",Home:"Home",End:"End",PageUp:"PgUp",PageDown:"PgDn"}}initialize(e,d){return this.container=document.getElementById(e),this.onKeyPress=d,this.container?(this.createKeyboard(),this.setupEventListeners(),console.log("Virtual Keyboard Service initialized"),!0):(console.error("Virtual keyboard container not found"),!1)}createKeyboard(){this.container.innerHTML=`
      <div class="virtual-keyboard-header">
        <div class="virtual-keyboard-controls">
          <button class="layout-btn" data-layout="default">ABC</button>
          <button class="layout-btn" data-layout="function">F1-12</button>
        </div>
        <button class="virtual-keyboard-close">×</button>
      </div>
      <div class="virtual-keyboard-body">
        <div class="virtual-keyboard-keys"></div>
      </div>
    `,this.keyboardElement=this.container.querySelector(".virtual-keyboard-keys"),this.renderLayout()}renderLayout(){const e=this.shiftPressed?this.layouts.shift:this.layouts[this.currentLayout];this.keyboardElement.innerHTML="",e.forEach((d,m)=>{const b=document.createElement("div");b.className="keyboard-row",d.forEach((A,x)=>{if(A==="")return;const s=document.createElement("button");s.className="keyboard-key",s.dataset.key=A,["Shift","Ctrl","Alt","CapsLock"].includes(A)&&(s.classList.add("modifier-key"),(A==="Shift"&&this.shiftPressed||A==="Ctrl"&&this.ctrlPressed||A==="Alt"&&this.altPressed)&&s.classList.add("active")),["Backspace","Tab","Enter","Space"].includes(A)&&s.classList.add("special-key"),A.startsWith("Arrow")&&s.classList.add("arrow-key"),A==="Space"?s.classList.add("space-key"):A==="Backspace"||A==="Enter"?s.classList.add("wide-key"):(A==="Tab"||A==="CapsLock")&&s.classList.add("medium-key"),s.textContent=this.keyDisplayNames[A]||A,b.appendChild(s)}),this.keyboardElement.appendChild(b)})}setupEventListeners(){this.keyboardElement.addEventListener("click",e=>{e.target.classList.contains("keyboard-key")&&this.handleKeyPress(e.target.dataset.key)}),this.container.addEventListener("click",e=>{e.target.classList.contains("layout-btn")?this.switchLayout(e.target.dataset.layout):e.target.classList.contains("virtual-keyboard-close")&&this.hide()}),this.container.addEventListener("mousedown",e=>{e.preventDefault()})}handleKeyPress(e){if(console.log("Virtual keyboard key pressed:",e,{shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed}),e==="Shift"){this.shiftPressed=!this.shiftPressed,this.renderLayout(),console.log("Shift toggled:",this.shiftPressed);return}if(e==="Ctrl"){this.ctrlPressed=!this.ctrlPressed,this.renderLayout(),console.log("Ctrl toggled:",this.ctrlPressed);return}if(e==="Alt"){this.altPressed=!this.altPressed,this.renderLayout(),console.log("Alt toggled:",this.altPressed);return}if(e==="CapsLock"){this.shiftPressed=!this.shiftPressed,this.renderLayout(),console.log("CapsLock toggled:",this.shiftPressed);return}let d={key:e,shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed,timestamp:Date.now()};console.log("Sending key data to callback:",d),this.onKeyPress&&this.onKeyPress(d),this.shiftPressed&&e!=="CapsLock"&&(this.shiftPressed=!1,this.renderLayout()),(this.ctrlPressed||this.altPressed)&&(this.ctrlPressed=!1,this.altPressed=!1,this.renderLayout())}switchLayout(e){this.currentLayout=e,this.renderLayout(),this.container.querySelectorAll(".layout-btn").forEach(d=>{d.classList.toggle("active",d.dataset.layout===e)})}show(){this.isVisible=!0,this.container.classList.remove("keyboard-hidden"),this.container.classList.add("keyboard-visible")}hide(){this.isVisible=!1,this.container.classList.remove("keyboard-visible"),this.container.classList.add("keyboard-hidden")}toggle(){this.isVisible?this.hide():this.show()}isKeyboardVisible(){return this.isVisible}typeString(e,d=100){let m=0;const b=()=>{if(m<e.length){const A=e[m];this.handleKeyPress(A),m++,setTimeout(b,d)}};b()}simulateKeyCombo(e){e.includes("Ctrl")&&(this.ctrlPressed=!0,this.renderLayout()),e.includes("Alt")&&(this.altPressed=!0,this.renderLayout()),e.includes("Shift")&&(this.shiftPressed=!0,this.renderLayout());const d=e.find(m=>!["Ctrl","Alt","Shift"].includes(m));d&&this.handleKeyPress(d)}getModifierStates(){return{shift:this.shiftPressed,ctrl:this.ctrlPressed,alt:this.altPressed}}}const ze=new js;let Fe=null,we=0,Ze=!1;const Be=document.getElementById("remainingTime"),ve=document.getElementById("extendBtn");function Ks(E){const e=Math.floor(E/3600),d=Math.floor(E%3600/60),m=E%60;return[e,d,m].map(b=>b.toString().padStart(2,"0")).join(":")}function vt(){Be.textContent=Ks(we),Be.classList.remove("warning","danger"),we<=300?Be.classList.add("danger"):we<=1800&&Be.classList.add("warning")}function Ot(E){Fe&&clearInterval(Fe),we=Math.max(0,E),vt(),Fe=setInterval(()=>{we--,vt(),we<=0&&(clearInterval(Fe),ve.disabled=!0,Be.textContent="Session expired")},1e3)}async function qs(){if(Ze){console.log("extendSession: Already extending, ignoring request");return}console.log("extendSession: Starting session extension...");try{Ze=!0,ve.disabled=!0,ve.textContent="Extending...",console.log("extendSession: Sending extension request to /api/extend?minutes=60");const E=performance.now(),e=await fetch("/api/extend?minutes=60",{method:"POST",headers:{Accept:"application/json"}}),d=performance.now()-E;console.log(`extendSession: Received response in ${d.toFixed(2)}ms`,e);let m;try{m=await e.json(),console.log("extendSession: Response data:",m)}catch(b){throw console.error("extendSession: Failed to parse JSON response:",b),new Error("Invalid response from server")}if(!e.ok){const b=m.error||m.message||"Failed to extend session";throw console.error("extendSession: API error:",b),new Error(b)}if(m.success){const b=Math.max(0,m.remaining_seconds||0);console.log(`extendSession: Extension successful. New remaining time: ${b} seconds`),Ot(b);const A=Math.ceil(b/60);se.writeln(`\r
\x1B[32m✓ Session extended. New remaining time: ${A} minutes\x1B[0m`),se.write(`\r
$ `)}else throw console.error("extendSession: Extension failed:",m.message||"Unknown error"),new Error(m.message||"Failed to extend session")}catch(E){console.error("Error extending session:",E),se.writeln(`\r
\x1B[31mError: ${E.message}\x1B[0m`),se.write(`\r
$ `)}finally{Ze=!1,ve.disabled=!1,ve.textContent="+1h"}}async function Vs(){try{const E=await fetch("/api/status");if(E.ok){const e=await E.json();e.remaining_seconds!==void 0&&Ot(e.remaining_seconds)}}catch(E){console.error("Error initializing session timer:",E)}}let je=null,oe=null;function Xs(E){if(!se||!je||je.readyState!==WebSocket.OPEN){console.log("Terminal or socket not ready for keyboard input");return}let e="";const{key:d,shift:m,ctrl:b,alt:A}=E;switch(d){case"Enter":e="\r";break;case"Tab":e="	";break;case"Backspace":e="";break;case"Delete":e="\x1B[3~";break;case"Esc":e="\x1B";break;case"Space":e=" ";break;case"ArrowLeft":e="\x1B[D";break;case"ArrowUp":e="\x1B[A";break;case"ArrowDown":e="\x1B[B";break;case"ArrowRight":e="\x1B[C";break;case"Home":e="\x1B[H";break;case"End":e="\x1B[F";break;case"PageUp":e="\x1B[5~";break;case"PageDown":e="\x1B[6~";break;case"Insert":e="\x1B[2~";break;default:if(b&&d.length===1){const x=d.toLowerCase().charCodeAt(0);x>=97&&x<=122&&(e=String.fromCharCode(x-96))}else A&&d.length===1?e="\x1B"+d:e=d}e&&(console.log("Sending virtual keyboard input:",e,"from key:",E),oe&&oe.isConnected()?oe.send(JSON.stringify({type:"input",data:e})):console.warn("WebSocket not connected, cannot send virtual keyboard input"))}const Gs=document.getElementById("terminal"),se=new Wt.Terminal({fontFamily:'Menlo, Monaco, "Courier New", monospace',fontSize:14,lineHeight:1.2,cursorBlink:!0,cursorStyle:"block",theme:{background:"#000000",foreground:"#f0f0f0",cursor:"#f0f0f0",selection:"rgba(255, 255, 255, 0.3)"}}),ke=new Nt.FitAddon,Js=new Ut.WebLinksAddon;se.loadAddon(ke);se.loadAddon(Js);se.open(Gs);ke.fit();Vs();function Ys(){console.log("Initializing virtual keyboard...");const E=document.getElementById("keyboardBtn");if(console.log("keyboardBtn:",E),!ze.initialize("keyboardContainer",Xs)){console.error("Failed to initialize virtual keyboard service");return}function d(){console.log("Toggle keyboard clicked, current state:",ze.isKeyboardVisible()),ze.toggle(),ke&&ke.fit&&setTimeout(()=>ke.fit(),100)}E&&E.addEventListener("click",d)}function St(){Ys(),Me.initialize(),Pt.initialize(),ve?(ve.addEventListener("click",qs),console.log("Extend button event listener attached")):console.warn("Extend button not found");const E=document.getElementById("getAllContentBtn");E&&E.addEventListener("click",()=>{if(window.xtermService&&typeof window.xtermService.getAllContent=="function"){const{cursorX:e,cursorY:d,lines:m}=window.xtermService.getAllContent();console.log("[getAllContentBtn] 全屏内容:",m),console.log("[getAllContentBtn] 光标位置:",e,d)}else console.warn("xtermService.getAllContent 不可用")})}document.readyState==="loading"?document.addEventListener("DOMContentLoaded",St):St();const Zs=window.location.protocol==="https:"?"wss:":"ws:",Ht=`${Zs}//${window.location.host}/ws`;console.log(`Connecting to WebSocket at: ${Ht}`);oe=new Us(Ht,{maxReconnectAttempts:10,reconnectInterval:1e3,maxReconnectInterval:3e4,reconnectDecay:1.5,enableLogging:!0});oe.onOpen=()=>{je=oe.socket,console.log("WebSocket connection established"),se.writeln("Connected to terminal server"),me.initialize(se,je),window.xtermService=me,window.aiDialogService=Me,window.virtualKeyboardService=ze,window.frontendAIAgent=Ws,window.aiAgentConfig=it,window.settingsDialogService=Pt;const E=()=>{ke.fit();const e={cols:se.cols,rows:se.rows};oe.send(JSON.stringify({type:"resize",data:JSON.stringify(e)}))};E(),window.addEventListener("resize",E),se.onData(e=>{oe.send(JSON.stringify({type:"input",data:e}))})};oe.onMessage=E=>{try{const e=JSON.parse(E.data);e.type==="output"?(console.log("Received output from server:",e.data),se.write(e.data)):e.type==="error"&&se.writeln(`\r
\x1B[31mError: ${e.data}\x1B[0m`)}catch(e){console.error("Failed to parse message:",e),se.writeln(`\r
\x1B[31mError: Failed to parse server message\x1B[0m`)}};oe.onClose=E=>{console.log("WebSocket connection closed"),E.wasClean?se.writeln(`\r
\x1B[33mConnection closed\x1B[0m`):se.writeln(`\r
\x1B[33mConnection lost. Attempting to reconnect...\x1B[0m`)};oe.onError=E=>{console.error("WebSocket error:",E),se.writeln(`\r
\x1B[31mConnection error\x1B[0m`)};oe.onReconnecting=(E,e)=>{se.writeln(`\r
\x1B[33mReconnecting... (attempt ${E}, delay ${Math.round(e/1e3)}s)\x1B[0m`)};oe.onReconnected=()=>{se.writeln(`\r
\x1B[32m✓ Reconnected to terminal server\x1B[0m`)};oe.onMaxReconnectAttemptsReached=()=>{se.writeln(`\r
\x1B[31m✗ Failed to reconnect after maximum attempts. Please refresh the page.\x1B[0m`)};oe.connect();
