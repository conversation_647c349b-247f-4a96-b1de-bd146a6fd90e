/**
 * AI Dialog Service - Manages the AI chat dialog interface
 * 
 * This service handles:
 * - Dialog visibility and interactions
 * - Message rendering with markdown support
 * - Auto-resizing textarea
 * - Message history management
 */

import { marked } from 'marked';
import frontendAIAgent, { aiAgentConfig } from './frontendAIAgent.js';
import { AIAgent, aiAgentManager, aiAgentConfig as newAIAgentConfig } from './refactoredAIAgent.js';
import { registerTerminalTools } from './terminalTools.js';

class AIDialogService {
  constructor() {
    this.isVisible = false;
    this.messages = [];
    this.dialog = null;
    this.messagesContainer = null;
    this.input = null;
    this.sendBtn = null;
    this.closeBtn = null;
    this.minimizeBtn = null;
    this.maximizeBtn = null;
    this.headerElement = null;
    this.aiBtn = null;
    this.sessionId = this.generateSessionId();
    this.useFrontendAgent = true; // Use frontend AI agent instead of backend
    this.useRefactoredAgent = true; // Use the new refactored agent with streaming

    // Dialog state
    this.isMinimized = false;
    this.isMaximized = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.originalPosition = { top: 20, right: 20 };
    this.originalSize = { width: 400, height: 500 };

    // Streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements = new Map();

    // Configure marked for security
    marked.setOptions({
      breaks: true,
      gfm: true,
      sanitize: false, // We'll handle sanitization manually if needed
    });
  }

  /**
   * Initialize the AI dialog service
   */
  initialize() {
    this.dialog = document.getElementById('aiDialog');
    this.messagesContainer = document.getElementById('aiMessages');
    this.input = document.getElementById('aiInput');
    this.sendBtn = document.getElementById('aiSend');
    this.closeBtn = document.getElementById('aiDialogClose');
    this.minimizeBtn = document.getElementById('aiDialogMinimize');
    this.maximizeBtn = document.getElementById('aiDialogMaximize');
    this.headerElement = document.getElementById('aiDialogHeader');
    this.aiBtn = document.getElementById('aiBtn');

    if (!this.dialog || !this.messagesContainer || !this.input || !this.sendBtn ||
        !this.closeBtn || !this.minimizeBtn || !this.maximizeBtn || !this.headerElement || !this.aiBtn) {
      console.error('AI Dialog elements not found');
      return false;
    }

    this.setupEventListeners();
    this.setupAutoResize();
    
    console.log('AI Dialog Service initialized');
    return true;
  }

  /**
   * Setup event listeners for dialog interactions
   */
  setupEventListeners() {
    // AI button click
    this.aiBtn.addEventListener('click', () => this.toggleDialog());
    
    // Close button click
    this.closeBtn.addEventListener('click', () => this.hideDialog());

    // Minimize button click
    this.minimizeBtn.addEventListener('click', () => this.toggleMinimize());

    // Maximize button click
    this.maximizeBtn.addEventListener('click', () => this.toggleMaximize());

    // Dragging functionality
    this.setupDragging();
    
    // Send button click
    this.sendBtn.addEventListener('click', () => this.sendMessage());
    
    // Enter key in input (Shift+Enter for new line)
    this.input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendMessage();
      }
    });
    
    // Escape key to close dialog
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hideDialog();
      }
    });
  }

  /**
   * Setup auto-resizing textarea
   */
  setupAutoResize() {
    this.input.addEventListener('input', () => {
      this.input.style.height = 'auto';
      this.input.style.height = Math.min(this.input.scrollHeight, 120) + 'px';
    });
  }

  /**
   * Toggle dialog visibility
   */
  toggleDialog() {
    if (this.isVisible) {
      this.hideDialog();
    } else {
      this.showDialog();
    }
  }

  /**
   * Show the AI dialog
   */
  showDialog() {
    this.isVisible = true;
    this.dialog.classList.remove('ai-dialog-hidden');
    this.input.focus();
    console.log('AI Dialog shown');
  }

  /**
   * Hide the AI dialog
   */
  hideDialog() {
    this.isVisible = false;
    this.dialog.classList.add('ai-dialog-hidden');
    console.log('AI Dialog hidden');
  }

  /**
   * Toggle minimize state
   */
  toggleMinimize() {
    this.isMinimized = !this.isMinimized;
    if (this.isMinimized) {
      this.dialog.classList.add('minimized');
      this.minimizeBtn.textContent = '+';
      this.minimizeBtn.title = 'Restore';
    } else {
      this.dialog.classList.remove('minimized');
      this.minimizeBtn.textContent = '−';
      this.minimizeBtn.title = 'Minimize';
    }
    console.log('AI Dialog minimized:', this.isMinimized);
  }

  /**
   * Toggle maximize state
   */
  toggleMaximize() {
    this.isMaximized = !this.isMaximized;
    if (this.isMaximized) {
      this.dialog.classList.add('maximized');
      this.maximizeBtn.textContent = '❐';
      this.maximizeBtn.title = 'Restore';
    } else {
      this.dialog.classList.remove('maximized');
      this.maximizeBtn.textContent = '□';
      this.maximizeBtn.title = 'Maximize';
    }
    console.log('AI Dialog maximized:', this.isMaximized);
  }

  /**
   * Setup dragging functionality
   */
  setupDragging() {
    this.headerElement.addEventListener('mousedown', (e) => {
      if (e.target === this.headerElement || e.target.tagName === 'H3') {
        this.startDragging(e);
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (this.isDragging) {
        this.drag(e);
      }
    });

    document.addEventListener('mouseup', () => {
      if (this.isDragging) {
        this.stopDragging();
      }
    });
  }

  /**
   * Start dragging
   */
  startDragging(e) {
    if (this.isMaximized) return; // Can't drag when maximized

    this.isDragging = true;
    this.dialog.classList.add('dragging');

    const rect = this.dialog.getBoundingClientRect();
    this.dragOffset.x = e.clientX - rect.left;
    this.dragOffset.y = e.clientY - rect.top;

    e.preventDefault();
  }

  /**
   * Drag the dialog
   */
  drag(e) {
    if (!this.isDragging) return;

    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;

    // Constrain to viewport
    const maxX = window.innerWidth - this.dialog.offsetWidth;
    const maxY = window.innerHeight - this.dialog.offsetHeight;

    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));

    this.dialog.style.left = constrainedX + 'px';
    this.dialog.style.top = constrainedY + 'px';
    this.dialog.style.right = 'auto';
    this.dialog.style.bottom = 'auto';
  }

  /**
   * Stop dragging
   */
  stopDragging() {
    this.isDragging = false;
    this.dialog.classList.remove('dragging');
  }

  /**
   * Send a message from the user
   */
  sendMessage() {
    const message = this.input.value.trim();
    if (!message) return;

    // Add user message
    this.addMessage(message, 'user');

    // Clear input
    this.input.value = '';
    this.input.style.height = 'auto';

    // Send to AI agent
    this.sendToAIAgent(message);
  }

  /**
   * Add a message to the chat
   * @param {string} content - Message content
   * @param {string} sender - 'user' or 'assistant'
   */
  addMessage(content, sender) {
    const message = {
      content,
      sender,
      timestamp: new Date()
    };
    
    this.messages.push(message);
    this.renderMessage(message);
    this.scrollToBottom();
  }

  /**
   * Render a message in the chat
   * @param {object} message - Message object
   */
  renderMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ai-message-${message.sender}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-message-content';
    
    if (message.sender === 'assistant') {
      // Render markdown for AI messages
      contentDiv.innerHTML = marked.parse(message.content);
    } else {
      // Simple text for user messages (with basic formatting)
      contentDiv.innerHTML = this.formatUserMessage(message.content);
    }
    
    messageDiv.appendChild(contentDiv);
    this.messagesContainer.appendChild(messageDiv);
  }

  /**
   * Format user message with basic formatting
   * @param {string} content - Raw message content
   * @returns {string} Formatted HTML
   */
  formatUserMessage(content) {
    return content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br>');
  }

  /**
   * Scroll messages container to bottom
   */
  scrollToBottom() {
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
  }

  /**
   * Send message to AI agent and get response
   * @param {string} userMessage - The user's message
   */
  async sendToAIAgent(userMessage) {
    // Show typing indicator
    this.sendBtn.disabled = true;

    try {
      if (this.useRefactoredAgent) {
        // Use refactored AI agent with streaming
        await this.sendToRefactoredAgent(userMessage);
      } else if (this.useFrontendAgent) {
        // Use frontend AI agent
        await this.sendToFrontendAgent(userMessage);
      } else {
        // Use backend AI agent (legacy)
        await this.sendToBackendAgent(userMessage);
      }
    } catch (error) {
      console.error('Error communicating with AI agent:', error);

      let fallbackResponse = '';
      if (error.message.includes('AI Agent not ready')) {
        fallbackResponse = `The AI agent is not configured. Please check the AI settings and ensure you have:

1. **API Key**: A valid OpenAI API key
2. **Endpoint**: The correct API endpoint URL
3. **Model**: A valid model ID

You can configure these in the AI settings. Would you like help with the configuration?`;
      } else if (error.message.includes('API request failed')) {
        fallbackResponse = `I'm having trouble connecting to the AI service. This could be due to:

1. **Invalid API Key**: Please check your API key
2. **Network Issues**: Check your internet connection
3. **API Limits**: You may have exceeded your API quota

Please check your configuration and try again.`;
      } else {
        fallbackResponse = 'Sorry, I encountered an error processing your request. Please try again.';
      }

      this.addMessage(fallbackResponse, 'assistant');
    } finally {
      this.sendBtn.disabled = false;
    }
  }

  /**
   * Send message to refactored AI agent with streaming support
   * @param {string} userMessage - The user's message
   */
  async sendToRefactoredAgent(userMessage) {
    // Get or create agent for this session
    let agent = aiAgentManager.getAgent(this.sessionId);

    if (!agent) {
      // Create new agent with streaming callbacks
      const config = newAIAgentConfig.load();
      agent = aiAgentManager.createAgent(this.sessionId, {
        ...config,
        onChunk: (chunk) => this.handleStreamingChunk(chunk),
        onToolCall: (toolCall) => this.handleToolCall(toolCall),
        onComplete: (response) => this.handleStreamingComplete(response),
        onError: (error) => this.handleStreamingError(error)
      });

      // Register terminal tools with the agent
      registerTerminalTools(agent);

      // Initialize the agent
      await agent.initialize();
    }

    // Start streaming message
    this.startStreamingMessage();

    // Get current terminal context
    const context = this.getTerminalContext();

    try {
      // Process message with streaming
      await agent.processMessage(userMessage, context);
    } catch (error) {
      this.handleStreamingError(error);
    }
  }

  /**
   * Send message to frontend AI agent
   * @param {string} userMessage - The user's message
   */
  async sendToFrontendAgent(userMessage) {
    // Check if agent is ready
    if (!frontendAIAgent.isAgentReady()) {
      // Try to initialize with saved config
      const config = aiAgentConfig.load();
      await frontendAIAgent.initialize(config);

      if (!frontendAIAgent.isAgentReady()) {
        throw new Error('AI Agent not ready. Please configure the AI settings.');
      }
    }

    // Get current terminal context if available
    const context = this.getTerminalContext();

    const response = await frontendAIAgent.processMessage(userMessage, {
      sessionId: this.sessionId,
      context: context
    });

    // Check if the response contains commands to execute
    if (response.commands && response.commands.length > 0) {
      const executionResults = await this.executeCommandsInTerminal(response.commands);

      // If there were command executions, provide feedback to the AI
      if (executionResults && executionResults.length > 0) {
        await this.provideExecutionFeedback(executionResults);
      }
    }

    this.addMessage(response.content, 'assistant');
  }

  /**
   * Send message to backend AI agent (legacy)
   * @param {string} userMessage - The user's message
   */
  async sendToBackendAgent(userMessage) {
    // Get current terminal context if available
    const context = this.getTerminalContext();

    const response = await fetch(`${this.aiAgentUrl}/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: userMessage,
        sessionId: this.sessionId,
        context: context,
        terminalIntegration: true
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success) {
      // Check if the response contains commands to execute
      if (data.commands && data.commands.length > 0) {
        await this.executeCommandsInTerminal(data.commands);
      }

      this.addMessage(data.response, 'assistant');
    } else {
      this.addMessage('Sorry, I encountered an error processing your request.', 'assistant');
    }
  }

  /**
   * Execute commands in the terminal as requested by AI
   * @param {Array} commands - Array of commands to execute
   */
  async executeCommandsInTerminal(commands) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      console.warn('XTerm service not available for command execution');
      return;
    }

    const executedCommands = [];

    for (const command of commands) {
      try {
        console.log('Executing AI-requested command:', command);

        // Store the current cursor position to capture output
        const beforePosition = window.xtermService.getCursorPosition();

        if (command.type === 'single_key') {
          await window.xtermService.sendKey(command.key);
        } else if (command.type === 'key_combo') {
          await window.xtermService.sendKeyCombo(command.combination);
        } else if (command.type === 'command') {
          await window.xtermService.sendCommand(command.command, command.execute !== false);
        } else if (command.type === 'commands') {
          await window.xtermService.sendCommands(command.commands, command.delay || 100);
        }

        // Wait a bit for command to execute and output to appear
        await new Promise(resolve => setTimeout(resolve, 500));

        // Capture the output after command execution
        const afterPosition = window.xtermService.getCursorPosition();
        let output = '';

        if (afterPosition.y > beforePosition.y) {
          // Get the lines between before and after positions
          const outputLines = window.xtermService.getLineRangeContent(
            beforePosition.y,
            afterPosition.y
          );
          output = outputLines.join('\n').trim();
        }

        executedCommands.push({
          command: command,
          output: output,
          success: true
        });

        // Small delay between commands
        if (commands.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      } catch (error) {
        console.error('Error executing command in terminal:', error);
        executedCommands.push({
          command: command,
          error: error.message,
          success: false
        });
      }
    }

    // Log the execution results for debugging
    console.log('AI command execution results:', executedCommands);

    return executedCommands;
  }

  /**
   * Provide execution feedback to the AI agent
   * @param {Array} executionResults - Results from command execution
   */
  async provideExecutionFeedback(executionResults) {
    try {
      // Create a summary of the execution results
      const feedback = executionResults.map(result => {
        if (result.success) {
          return `Command executed: ${JSON.stringify(result.command)}\nOutput: ${result.output || '(no output)'}`;
        } else {
          return `Command failed: ${JSON.stringify(result.command)}\nError: ${result.error}`;
        }
      }).join('\n\n');

      // Send feedback to AI agent as a system message
      const feedbackMessage = `Command execution results:\n\n${feedback}`;

      console.log('Providing execution feedback to AI:', feedbackMessage);

      // This could be enhanced to send the feedback back to the AI agent
      // for now, we just log it for debugging
    } catch (error) {
      console.error('Error providing execution feedback:', error);
    }
  }

  /**
   * Get current terminal context for AI agent
   */
  getTerminalContext() {
    const context = {};

    // Get terminal service context if available
    if (window.xtermService && window.xtermService.isServiceReady()) {
      try {
        context.currentLine = window.xtermService.getCurrentLineContent();
        context.terminalDimensions = window.xtermService.getTerminalDimensions();
        context.cursorPosition = window.xtermService.getCursorPosition();

        // Get last few lines for context
        const recentLines = window.xtermService.getLineRangeContent(
          Math.max(0, context.cursorPosition.y - 5),
          context.cursorPosition.y
        );
        context.recentOutput = recentLines.join('\n');
      } catch (error) {
        console.warn('Error getting terminal context:', error);
      }
    }

    return context;
  }

  /**
   * Start a new streaming message
   */
  startStreamingMessage() {
    // Create a new message element for streaming
    const messageDiv = document.createElement('div');
    messageDiv.className = 'ai-message ai-message-assistant streaming';

    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-message-content';
    contentDiv.innerHTML = '<span class="streaming-cursor">▋</span>';

    messageDiv.appendChild(contentDiv);
    this.messagesContainer.appendChild(messageDiv);

    this.currentStreamingMessage = '';
    this.streamingMessageElement = contentDiv;
    this.currentToolCalls = [];
    this.toolCallElements.clear();

    this.scrollToBottom();
  }

  /**
   * Handle streaming chunk from AI agent
   */
  handleStreamingChunk(chunk) {
    if (!this.streamingMessageElement) return;

    if (chunk.type === 'content') {
      this.currentStreamingMessage += chunk.content;

      // Update the streaming message with markdown rendering
      const renderedContent = marked.parse(this.currentStreamingMessage);
      this.streamingMessageElement.innerHTML = renderedContent + '<span class="streaming-cursor">▋</span>';

      this.scrollToBottom();
    } else if (chunk.type === 'tool_call') {
      this.handleToolCallChunk(chunk);
    }
  }

  /**
   * Handle tool call chunk
   */
  handleToolCallChunk(chunk) {
    const toolCall = chunk.toolCall;
    const index = toolCall.index || 0;

    if (!this.toolCallElements.has(index)) {
      // Create new tool call element
      const toolCallDiv = document.createElement('div');
      toolCallDiv.className = 'tool-call-container';
      toolCallDiv.innerHTML = `
        <div class="tool-call-header">
          <span class="tool-call-icon">🔧</span>
          <span class="tool-call-name">${toolCall.function?.name || 'Loading...'}</span>
          <span class="tool-call-status">Preparing...</span>
        </div>
        <div class="tool-call-args"></div>
      `;

      this.streamingMessageElement.appendChild(toolCallDiv);
      this.toolCallElements.set(index, toolCallDiv);
    }

    const toolCallElement = this.toolCallElements.get(index);
    const nameElement = toolCallElement.querySelector('.tool-call-name');
    const argsElement = toolCallElement.querySelector('.tool-call-args');

    // Update tool call name
    if (toolCall.function?.name) {
      nameElement.textContent = toolCall.function.name;
    }

    // Update arguments if available
    if (toolCall.function?.arguments) {
      try {
        const args = JSON.parse(toolCall.function.arguments);
        argsElement.innerHTML = `<pre><code>${JSON.stringify(args, null, 2)}</code></pre>`;
      } catch (e) {
        argsElement.textContent = toolCall.function.arguments;
      }
    }

    this.scrollToBottom();
  }

  /**
   * Handle tool call events
   */
  handleToolCall(event) {
    if (event.type === 'start') {
      this.updateToolCallStatus(event.toolCall, 'Executing...', 'executing');
    } else if (event.type === 'complete') {
      this.updateToolCallStatus(event.toolCall, 'Completed', 'completed');
      this.showToolCallResult(event.toolCall, event.result);
    } else if (event.type === 'error') {
      this.updateToolCallStatus(event.toolCall, 'Error', 'error');
      this.showToolCallResult(event.toolCall, { error: event.error });
    }
  }

  /**
   * Update tool call status
   */
  updateToolCallStatus(toolCall, status, className) {
    // Find tool call element by ID
    for (const [, element] of this.toolCallElements) {
      const nameElement = element.querySelector('.tool-call-name');
      if (nameElement && nameElement.textContent === toolCall.function.name) {
        const statusElement = element.querySelector('.tool-call-status');
        statusElement.textContent = status;
        statusElement.className = `tool-call-status ${className}`;
        break;
      }
    }
  }

  /**
   * Show tool call result
   */
  showToolCallResult(toolCall, result) {
    // Find tool call element and add result
    for (const [, element] of this.toolCallElements) {
      const nameElement = element.querySelector('.tool-call-name');
      if (nameElement && nameElement.textContent === toolCall.function.name) {
        let resultElement = element.querySelector('.tool-call-result');
        if (!resultElement) {
          resultElement = document.createElement('div');
          resultElement.className = 'tool-call-result';
          element.appendChild(resultElement);
        }

        resultElement.innerHTML = `<pre><code>${JSON.stringify(result, null, 2)}</code></pre>`;
        break;
      }
    }

    this.scrollToBottom();
  }

  /**
   * Handle streaming completion
   */
  handleStreamingComplete(response) {
    if (this.streamingMessageElement) {
      // Remove streaming cursor
      const cursor = this.streamingMessageElement.querySelector('.streaming-cursor');
      if (cursor) {
        cursor.remove();
      }

      // Mark message as complete
      const messageElement = this.streamingMessageElement.closest('.ai-message');
      if (messageElement) {
        messageElement.classList.remove('streaming');
      }
    }

    // Reset streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements.clear();

    console.log('Streaming complete:', response);
  }

  /**
   * Handle streaming error
   */
  handleStreamingError(error) {
    console.error('Streaming error:', error);

    if (this.streamingMessageElement) {
      this.streamingMessageElement.innerHTML = `<p class="error-message">Error: ${error.message}</p>`;

      const messageElement = this.streamingMessageElement.closest('.ai-message');
      if (messageElement) {
        messageElement.classList.remove('streaming');
        messageElement.classList.add('error');
      }
    }

    // Reset streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements.clear();
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    this.messages = [];
    this.messagesContainer.innerHTML = `
      <div class="ai-message ai-message-assistant">
        <div class="ai-message-content">
          <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
        </div>
      </div>
    `;
  }

  /**
   * Get all messages
   * @returns {array} Array of message objects
   */
  getMessages() {
    return [...this.messages];
  }

  /**
   * Set AI agent URL
   * @param {string} url - AI agent service URL
   */
  setAIAgentUrl(url) {
    this.aiAgentUrl = url;
  }

  /**
   * Check AI agent health
   */
  async checkAIAgentHealth() {
    try {
      const response = await fetch(`${this.aiAgentUrl}/health`);
      if (response.ok) {
        const data = await response.json();
        return data;
      }
      return null;
    } catch (error) {
      console.error('AI agent health check failed:', error);
      return null;
    }
  }

  /**
   * Get session history from AI agent
   */
  async getSessionHistory() {
    try {
      const response = await fetch(`${this.aiAgentUrl}/sessions/${this.sessionId}/history`);
      if (response.ok) {
        const data = await response.json();
        return data.history;
      }
      return [];
    } catch (error) {
      console.error('Error getting session history:', error);
      return [];
    }
  }

  /**
   * Clear session on AI agent
   */
  async clearSessionOnAgent() {
    try {
      const response = await fetch(`${this.aiAgentUrl}/sessions/${this.sessionId}`, {
        method: 'DELETE'
      });
      return response.ok;
    } catch (error) {
      console.error('Error clearing session on agent:', error);
      return false;
    }
  }

  /**
   * Set AI response handler (for integration with actual AI service)
   * @param {function} handler - Function to handle AI responses
   */
  setAIResponseHandler(handler) {
    this.aiResponseHandler = handler;
  }

  /**
   * Get current session ID
   */
  getSessionId() {
    return this.sessionId;
  }

  /**
   * Reset session (generate new session ID)
   */
  resetSession() {
    this.sessionId = this.generateSessionId();
    this.clearMessages();
  }

  /**
   * Configure AI agent
   */
  async configureAIAgent(config) {
    try {
      // Validate configuration
      const validation = aiAgentConfig.validate(config);
      if (!validation.valid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }

      // Save configuration
      aiAgentConfig.save(config);

      // Initialize agent with new config
      const success = await frontendAIAgent.initialize(config);
      if (!success) {
        throw new Error('Failed to initialize AI agent with new configuration');
      }

      return { success: true, message: 'AI agent configured successfully' };
    } catch (error) {
      console.error('Error configuring AI agent:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get AI agent configuration
   */
  getAIAgentConfig() {
    return aiAgentConfig.load();
  }

  /**
   * Test AI agent connection
   */
  async testAIAgentConnection() {
    try {
      if (!frontendAIAgent.isAgentReady()) {
        const config = aiAgentConfig.load();
        await frontendAIAgent.initialize(config);
      }

      // Send a test message
      const response = await frontendAIAgent.processMessage('Hello, this is a test message.', {
        sessionId: 'test-session'
      });

      // Clear test session
      frontendAIAgent.clearSession('test-session');

      return { success: true, message: 'Connection test successful', response: response.content };
    } catch (error) {
      console.error('AI agent connection test failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get AI agent statistics
   */
  getAIAgentStats() {
    return frontendAIAgent.getStats();
  }

  /**
   * Switch between frontend and backend AI agent
   */
  setAIAgentMode(useFrontend = true) {
    this.useFrontendAgent = useFrontend;
    console.log('AI agent mode set to:', useFrontend ? 'frontend' : 'backend');
  }

  /**
   * Manually execute a command in the terminal
   * @param {string} command - Command to execute
   */
  async executeCommand(command) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      console.warn('XTerm service not available for command execution');
      return false;
    }

    try {
      await window.xtermService.sendCommand(command, true);
      console.log('Manual command executed:', command);
      return true;
    } catch (error) {
      console.error('Error executing manual command:', error);
      return false;
    }
  }

  /**
   * Send text to terminal without executing
   * @param {string} text - Text to send
   */
  async sendTextToTerminal(text) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      console.warn('XTerm service not available for text input');
      return false;
    }

    try {
      await window.xtermService.sendRawData(text);
      console.log('Text sent to terminal:', text);
      return true;
    } catch (error) {
      console.error('Error sending text to terminal:', error);
      return false;
    }
  }

  /**
   * Get terminal output for AI context
   */
  getTerminalOutput(lines = 10) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      return '';
    }

    try {
      const cursorPos = window.xtermService.getCursorPosition();
      const startLine = Math.max(0, cursorPos.y - lines);
      const outputLines = window.xtermService.getLineRangeContent(startLine, cursorPos.y);
      return outputLines.join('\n').trim();
    } catch (error) {
      console.error('Error getting terminal output:', error);
      return '';
    }
  }
}

// Export singleton instance
export default new AIDialogService();
