/**
 * XTerm Service - Provides input/output operations for xterm terminal
 * 
 * This service encapsulates methods for:
 * - Sending various types of input to the terminal
 * - Retrieving output content from the terminal
 */

class XTermService {
  constructor() {
    this.terminal = null;
    this.socket = null;
    this.buffer = [];
    this.isReady = false;
    this.debugLogging = true;
  }

  /**
   * Initialize the service with terminal and socket instances
   * @param {Terminal} terminal - The xterm.js terminal instance
   * @param {WebSocket} socket - The WebSocket connection
   */
  initialize(terminal, socket) {
    this.terminal = terminal;
    this.socket = socket;
    this.isReady = true;
    
    // Listen to terminal output to maintain buffer
    this.terminal.onData((data) => {
      this.buffer.push(data);
    });
    
    console.log('XTermService initialized');
  }

  /**
   * Check if the service is ready for operations
   * @returns {boolean} True if ready, false otherwise
   */
  isServiceReady() {
    return this.isReady && 
           this.terminal && 
           this.socket && 
           this.socket.readyState === WebSocket.OPEN;
  }

  /**
   * Send raw data to the terminal via WebSocket
   * @param {string} data - Raw data to send
   * @returns {Promise<boolean>} Success status
   */
  async sendRawData(data) {
    if (!this.isServiceReady()) {
      console.error('XTermService not ready');
      return false;
    }

    try {
      this.socket.send(JSON.stringify({
        type: 'input',
        data: data
      }));
      return true;
    } catch (error) {
      console.error('Failed to send data:', error);
      return false;
    }
  }

  /**
   * Send a single key to the terminal
   * @param {string} key - Single character or special key
   * @returns {Promise<boolean>} Success status
   */
  async sendKey(key) {
    const keyMap = {
      'Enter': '\r',
      'Tab': '\t',
      'Backspace': '\x7f',
      'Delete': '\x1b[3~',
      'Escape': '\x1b',
      'ArrowUp': '\x1b[A',
      'ArrowDown': '\x1b[B',
      'ArrowRight': '\x1b[C',
      'ArrowLeft': '\x1b[D',
      'Home': '\x1b[H',
      'End': '\x1b[F',
      'PageUp': '\x1b[5~',
      'PageDown': '\x1b[6~',
      'F1': '\x1bOP',
      'F2': '\x1bOQ',
      'F3': '\x1bOR',
      'F4': '\x1bOS',
      'F5': '\x1b[15~',
      'F6': '\x1b[17~',
      'F7': '\x1b[18~',
      'F8': '\x1b[19~',
      'F9': '\x1b[20~',
      'F10': '\x1b[21~',
      'F11': '\x1b[23~',
      'F12': '\x1b[24~'
    };

    const mappedKey = keyMap[key] || key;
    return await this.sendRawData(mappedKey);
  }

  /**
   * Send a key combination (e.g., Ctrl+C)
   * @param {string} combination - Key combination like 'Ctrl+C', 'Alt+F4', etc.
   * @returns {Promise<boolean>} Success status
   */
  async sendKeyCombo(combination) {
    const parts = combination.split('+').map(part => part.trim());
    let data = '';

    if (parts.length === 2) {
      const [modifier, key] = parts;
      const lowerKey = key.toLowerCase();

      switch (modifier.toLowerCase()) {
        case 'ctrl':
          // Ctrl combinations use ASCII control codes
          if (lowerKey >= 'a' && lowerKey <= 'z') {
            data = String.fromCharCode(lowerKey.charCodeAt(0) - 96); // Ctrl+A = 1, Ctrl+B = 2, etc.
          } else {
            // Special Ctrl combinations
            const ctrlMap = {
              'c': '\x03',
              'd': '\x04',
              'z': '\x1a',
              'l': '\x0c',
              'r': '\x12',
              'u': '\x15',
              'k': '\x0b',
              'w': '\x17',
              'y': '\x19',
              'p': '\x10',
              'n': '\x0e',
              'f': '\x06',
              'b': '\x02',
              'a': '\x01',
              'e': '\x05'
            };
            data = ctrlMap[lowerKey] || '';
          }
          break;
        case 'alt':
          // Alt combinations use ESC prefix
          data = '\x1b' + key;
          break;
        case 'shift':
          // Shift combinations are usually just uppercase
          data = key.toUpperCase();
          break;
      }
    }

    if (data) {
      return await this.sendRawData(data);
    } else {
      console.error('Unsupported key combination:', combination);
      return false;
    }
  }

  /**
   * Send a command line to the terminal
   * @param {string} command - Command to execute
   * @param {boolean} executeImmediately - Whether to press Enter after the command
   * @returns {Promise<boolean>} Success status
   */
  async sendCommand(command, executeImmediately = true) {
    let success = await this.sendRawData(command);
    
    if (success && executeImmediately) {
      success = await this.sendKey('Enter');
    }
    
    return success;
  }

  /**
   * Send multiple commands sequentially
   * @param {string[]} commands - Array of commands to execute
   * @param {number} delay - Delay between commands in milliseconds
   * @returns {Promise<boolean>} Success status
   */
  async sendCommands(commands, delay = 100) {
    for (const command of commands) {
      const success = await this.sendCommand(command);
      if (!success) {
        console.error('Failed to send command:', command);
        return false;
      }
      
      if (delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    return true;
  }

  /**
   * Get the content of the current line where the cursor is located
   * @returns {string} Current line content
   */
  getCurrentLineContent() {
    if (!this.terminal) return '';
    
    const buffer = this.terminal.buffer.active;
    const cursorY = buffer.cursorY;
    const line = buffer.getLine(cursorY);
    
    return line ? line.translateToString(true) : '';
  }

  /**
   * Get the content of a specific line
   * @param {number} lineNumber - Line number (0-based)
   * @returns {string} Line content
   */
  getLineContent(lineNumber) {
    if (!this.terminal) return '';
    
    const buffer = this.terminal.buffer.active;
    const line = buffer.getLine(lineNumber);
    
    return line ? line.translateToString(true) : '';
  }

  /**
   * Get content from a range of lines
   * @param {number} startLine - Start line number (0-based)
   * @param {number} endLine - End line number (0-based)
   * @returns {string[]} Array of line contents
   */
  getLineRangeContent(startLine, endLine) {
    if (!this.terminal) return [];
    
    const buffer = this.terminal.buffer.active;
    const lines = [];
    
    for (let i = startLine; i <= endLine && i < buffer.length; i++) {
      const line = buffer.getLine(i);
      lines.push(line ? line.translateToString(true) : '');
    }
    
    return lines;
  }

  /**
   * Get all terminal content
   * @returns {string[]} Array of all line contents
   */
  getAllContent() {
    // Enable debug logging by calling: window.xtermService.setDebugLogging(true)
    if (this.debugLogging) {
      console.log('[XTermService] getAllContent called');
    }

    if (!this.terminal) {
      console.warn('[XTermService] Terminal not available');
      return [];
    }

    const buffer = this.terminal.buffer.active;
    if (this.debugLogging) {
      console.log('[XTermService] Buffer info:', {
        length: buffer.length,
        baseY: buffer.baseY,
        cursorY: buffer.cursorY,
        cursorX: buffer.cursorX
      });
    }

    const lines = [];
    let nonEmptyLines = 0;

    for (let i = 0; i < buffer.length; i++) {
      const line = buffer.getLine(i);
      const lineContent = line ? line.translateToString(true) : '';
      lines.push(lineContent);

      if (lineContent.trim() !== '') {
        nonEmptyLines++;
      }
    }

    if (this.debugLogging) {
      console.log('[XTermService] Content retrieved:', {
        totalLines: lines.length,
        nonEmptyLines: nonEmptyLines,
        firstLine: lines[0] || '(empty)',
        lastLine: lines[lines.length - 1] || '(empty)',
        sampleLines: lines.slice(0, 5).map((line, idx) => `${idx}: "${line}"`),
        totalCharacters: lines.join('').length
      });
    }

    return { cursorX: buffer.cursorX, cursorY: buffer.cursorY, lines };
  }

  /**
   * Get all content as a single string
   * @param {string} separator - Line separator (default: '\n')
   * @returns {string} All content as string
   */
  getAllContentAsString(separator = '\n') {
    return this.getAllContent().join(separator);
  }

  /**
   * Clear the terminal screen
   * @returns {Promise<boolean>} Success status
   */
  async clearScreen() {
    return await this.sendKeyCombo('Ctrl+L');
  }

  /**
   * Get terminal dimensions
   * @returns {object} Object with cols and rows properties
   */
  getTerminalDimensions() {
    if (!this.terminal) return { cols: 0, rows: 0 };
    
    return {
      cols: this.terminal.cols,
      rows: this.terminal.rows
    };
  }

  /**
   * Get cursor position
   * @returns {object} Object with x and y properties
   */
  getCursorPosition() {
    if (!this.terminal) return { x: 0, y: 0 };
    
    const buffer = this.terminal.buffer.active;
    return {
      x: buffer.cursorX,
      y: buffer.cursorY
    };
  }

  /**
   * Enable or disable debug logging
   * @param {boolean} enabled - Whether to enable debug logging
   */
  setDebugLogging(enabled) {
    this.debugLogging = enabled;
    console.log(`[XTermService] Debug logging ${enabled ? 'enabled' : 'disabled'}`);
  }
}

// Export singleton instance
export default new XTermService();
