/**
 * Terminal Tools for AI Agent
 * 
 * This module provides tools that encapsulate xtermService functionality
 * for use by AI agents. Includes input and output tools for terminal interaction.
 */

import xtermService from './xtermService.js';

// Debug logging flag
let debugLogging = true;

/**
 * Enable or disable debug logging for terminal tools
 * @param {boolean} enabled - Whether to enable debug logging
 */
export function setDebugLogging(enabled) {
  debugLogging = enabled;
  console.log(`[TerminalTools] Debug logging ${enabled ? 'enabled' : 'disabled'}`);
}

/**
 * Debug log function that respects the debug flag
 * @param {string} message - Log message
 * @param {any} data - Optional data to log
 */
function debugLog(message, data = null) {
  if (debugLogging) {
    if (data) {
      console.log(message, data);
    } else {
      console.log(message);
    }
  }
}

/**
 * Terminal Input Tool
 * Allows AI agent to send various types of input to the terminal
 */
export const terminalInputTool = {
  function: {
    name: 'terminal_input',
    description: 'Send input to the terminal. Supports single keys, key combinations, command lines, or command sequences.',
    parameters: {
      type: 'object',
      properties: {
        mode: {
          type: 'string',
          enum: ['single_key', 'key_combo', 'command_line', 'command_group'],
          description: 'Input mode: single_key for individual keys, key_combo for key combinations, command_line for single commands, command_group for multiple commands'
        },
        single_key: {
          type: 'string',
          description: 'Single key to send (required when mode is single_key). Examples: Enter, Tab, Escape, ArrowUp, etc.'
        },
        key_combo: {
          type: 'string',
          description: 'Key combination to send (required when mode is key_combo). Examples: Ctrl+C, Ctrl+Z, Alt+F4, etc.'
        },
        command_line: {
          type: 'string',
          description: 'Command line to send (required when mode is command_line). Examples: ls -la, pwd, cat file.txt'
        },
        command_group: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: 'Array of commands to send sequentially (required when mode is command_group)'
        },
        execute: {
          type: 'boolean',
          default: true,
          description: 'Whether to execute command immediately (press Enter). Only applies to command_line and command_group modes'
        },
        delay: {
          type: 'number',
          default: 100,
          description: 'Delay in milliseconds between commands in command_group mode'
        }
      },
      required: ['mode'],
      oneOf: [
        {
          properties: { mode: { const: 'single_key' } },
          required: ['single_key']
        },
        {
          properties: { mode: { const: 'key_combo' } },
          required: ['key_combo']
        },
        {
          properties: { mode: { const: 'command_line' } },
          required: ['command_line']
        },
        {
          properties: { mode: { const: 'command_group' } },
          required: ['command_group']
        }
      ]
    }
  },
  
  async execute(args) {
    try {
      // Check if xtermService is available
      if (!xtermService.isServiceReady()) {
        return {
          success: false,
          error: 'Terminal service is not ready',
          mode: args.mode
        };
      }

      const { mode, single_key, key_combo, command_line, command_group, execute = true, delay = 100 } = args;

      let result;
      
      switch (mode) {
        case 'single_key':
          if (!single_key) {
            return { message: 'single_key is required for single_key mode' };
          }
          await xtermService.sendKey(single_key);
          return {
            message: `按键已键入: ${single_key}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`
          };

        case 'key_combo':
          if (!key_combo) {
            return { message: 'key_combo is required for key_combo mode' };
          }
          await xtermService.sendKeyCombo(key_combo);
          return {
            message: `组合键已键入: ${key_combo}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`
          };

        case 'command_line':
          if (!command_line) {
            return { message: 'command_line is required for command_line mode' };
          }
          await xtermService.sendCommand(command_line, execute);
          return {
            message: `命令已键入: ${command_line}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`
          };

        case 'command_group':
          if (!command_group || !Array.isArray(command_group)) {
            return { message: 'command_group must be an array for command_group mode' };
          }
          await xtermService.sendCommands(command_group, delay);
          return {
            message: `命令组已依次键入: ${command_group.join(' ; ')}；但不代表执行成功，请以当前屏幕内容<terminalScreen>和光标位置<cursor>为准`
          };

        default:
          return {
            message: `Invalid mode: ${mode}. Must be one of: single_key, key_combo, command_line, command_group`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        mode: args.mode
      };
    }
  }
};

/**
 * Terminal Output Tool
 * Allows AI agent to retrieve terminal output content
 */
export const terminalOutputTool = {
  function: {
    name: 'terminal_output',
    description: 'Get output content from the terminal. Can retrieve single line, multiple lines, or full screen content.',
    parameters: {
      type: 'object',
      properties: {
        mode: {
          type: 'string',
          enum: ['current_line', 'multiple_lines', 'full_screen', 'line_range'],
          description: 'Output mode: current_line for cursor line, multiple_lines for recent lines, full_screen for all content, line_range for specific range'
        },
        line_count: {
          type: 'number',
          minimum: 1,
          maximum: 100,
          description: 'Number of recent lines to retrieve (required for multiple_lines mode)'
        },
        start_line: {
          type: 'number',
          minimum: 0,
          description: 'Starting line number for line_range mode (0-based)'
        },
        end_line: {
          type: 'number',
          minimum: 0,
          description: 'Ending line number for line_range mode (0-based, inclusive)'
        }
      },
      required: ['mode'],
      oneOf: [
        {
          properties: { mode: { const: 'current_line' } }
        },
        {
          properties: { mode: { const: 'multiple_lines' } },
          required: ['line_count']
        },
        {
          properties: { mode: { const: 'full_screen' } }
        },
        {
          properties: { mode: { const: 'line_range' } },
          required: ['start_line', 'end_line']
        }
      ]
    }
  },
  
  async execute(args) {
    try {
      // Check if xtermService is available
      if (!xtermService.isServiceReady()) {
        return {
          success: false,
          error: 'Terminal service is not ready',
          mode: args.mode
        };
      }

      const { mode, line_count, start_line, end_line } = args;

      switch (mode) {
        case 'current_line':
          debugLog('[TerminalTools] Getting current line content...');

          const currentLine = xtermService.getCurrentLineContent();
          const cursorPos = xtermService.getCursorPosition();

          debugLog('[TerminalTools] Current line retrieved:', {
            content: `"${currentLine}"`,
            length: currentLine.length,
            cursorPosition: cursorPos
          });

          return {
            success: true,
            mode: 'current_line',
            content: currentLine,
            cursor_position: cursorPos,
            message: `Retrieved current line content (${currentLine.length} characters at position ${cursorPos.x},${cursorPos.y})`
          };

        case 'multiple_lines':
          if (!line_count || line_count < 1) {
            return { success: false, error: 'line_count must be a positive number for multiple_lines mode' };
          }

          debugLog('[TerminalTools] Getting multiple lines:', { requested: line_count });

          const cursor = xtermService.getCursorPosition();
          const startLine = Math.max(0, cursor.y - line_count + 1);
          const lines = xtermService.getLineRangeContent(startLine, cursor.y);

          debugLog('[TerminalTools] Multiple lines retrieved:', {
            cursorPosition: cursor,
            startLine: startLine,
            endLine: cursor.y,
            retrievedLines: lines.length,
            content: lines.slice(0, 3) // First 3 lines for debugging
          });

          return {
            success: true,
            mode: 'multiple_lines',
            content: lines,
            content_string: lines.join('\n'),
            line_count: lines.length,
            start_line: startLine,
            end_line: cursor.y,
            message: `Retrieved ${lines.length} recent lines (from line ${startLine} to ${cursor.y})`
          };

        case 'full_screen':
          debugLog('[TerminalTools] Getting full screen content...');

          // Check if xtermService methods are available
          if (!xtermService.getAllContent) {
            console.error('[TerminalTools] xtermService.getAllContent method not available');
            return {
              success: false,
              error: 'getAllContent method not available on xtermService'
            };
          }

          if (!xtermService.getTerminalDimensions) {
            console.error('[TerminalTools] xtermService.getTerminalDimensions method not available');
            return {
              success: false,
              error: 'getTerminalDimensions method not available on xtermService'
            };
          }
          const f = xtermService.getAllContent();
          const cursorX = f.cursorX;
          const cursorY = f.cursorY;
          const allLines = f.lines;
          const dimensions = xtermService.getTerminalDimensions();

          debugLog('[TerminalTools] Full screen content retrieved:', {
            lineCount: allLines.length,
            dimensions: dimensions,
            firstFewLines: allLines.slice(0, 3),
            lastFewLines: allLines.slice(-3),
            totalCharacters: allLines.join('\n').length
          });

          // Check for empty or suspicious content
          if (allLines.length === 0) {
            debugLog('[TerminalTools] No lines retrieved from terminal');
          } else if (allLines.every(line => line.trim() === '')) {
            debugLog('[TerminalTools] All lines are empty');
          }

          return {
            cursorX: cursorX,
            cursorY: cursorY,
            success: true,
            mode: 'full_screen',
            content: allLines,
            // content_string: allLines.join('\n'),
            line_count: allLines.length,
            terminal_dimensions: dimensions,
            message: `Retrieved full screen content (${allLines.length} lines, ${dimensions.cols}x${dimensions.rows}, cursor at ${cursorX},${cursorY})`
          };

        case 'line_range':
          if (start_line === undefined || end_line === undefined) {
            return { success: false, error: 'start_line and end_line are required for line_range mode' };
          }
          
          if (start_line < 0 || end_line < start_line) {
            return { success: false, error: 'Invalid line range: start_line must be >= 0 and end_line must be >= start_line' };
          }
          
          const rangeLines = xtermService.getLineRangeContent(start_line, end_line);
          
          return {
            success: true,
            mode: 'line_range',
            content: rangeLines,
            // content_string: rangeLines.join('\n'),
            line_count: rangeLines.length,
            start_line: start_line,
            end_line: end_line,
            message: `Retrieved lines ${start_line} to ${end_line} (${rangeLines.length} lines)`
          };

        default:
          return {
            success: false,
            error: `Invalid mode: ${mode}. Must be one of: current_line, multiple_lines, full_screen, line_range`
          };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message,
        mode: args.mode
      };
    }
  }
};

/**
 * Wait Tool
 * Allows AI agent to pause execution for a specified duration
 */
export const waitTool = {
  function: {
    name: 'wait',
    description: 'Wait for a specified duration before continuing. Useful when waiting for commands to complete or UI to load.',
    parameters: {
      type: 'object',
      properties: {
        duration: {
          type: 'number',
          minimum: 100,
          maximum: 30000,
          description: 'Duration to wait in milliseconds (100ms to 30s)'
        },
        reason: {
          type: 'string',
          description: 'Optional reason for waiting (for logging/debugging purposes)'
        }
      },
      required: ['duration']
    }
  },

  async execute(args) {
    try {
      const { duration, reason } = args;

      if (duration < 100 || duration > 30000) {
        return {
          success: false,
          error: 'Duration must be between 100ms and 30000ms (30 seconds)',
          duration: duration
        };
      }

      const startTime = Date.now();

      // Wait for the specified duration
      await new Promise(resolve => setTimeout(resolve, duration));

      const actualDuration = Date.now() - startTime;

      return {
        success: true,
        duration: duration,
        actual_duration: actualDuration,
        reason: reason || 'No reason specified',
        message: `Waited for ${actualDuration}ms${reason ? ` (${reason})` : ''}`
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        duration: args.duration
      };
    }
  }
};

/**
 * Get all terminal tools
 */
export function getTerminalTools() {
  return [terminalInputTool, /**terminalOutputTool,*/ waitTool];
}

/**
 * Register terminal tools with an AI agent
 */
export function registerTerminalTools(agent) {
  agent.addTool(terminalInputTool);
  // agent.addTool(terminalOutputTool);
  agent.addTool(waitTool);
  return agent;
}
