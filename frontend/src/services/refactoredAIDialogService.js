/**
 * Refactored AI Dialog Service
 * 
 * Main service that coordinates between UI, messaging, AI agent, and terminal integration
 */

import dialogUIManager from './dialog/dialogUIManager.js';
import messageRenderer from './dialog/messageRenderer.js';
import aiAgentIntegration from './dialog/aiAgentIntegration.js';
import terminalIntegration from './dialog/terminalIntegration.js';

class RefactoredAIDialogService {
  constructor() {
    this.input = null;
    this.sendBtn = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the AI dialog service
   */
  initialize() {
    // Initialize input elements
    this.input = document.getElementById('aiInput');
    this.sendBtn = document.getElementById('aiSend');

    if (!this.input || !this.sendBtn) {
      console.error('AI Dialog input elements not found');
      return false;
    }

    // Initialize all modules
    const uiInitialized = dialogUIManager.initialize();
    const messageInitialized = messageRenderer.initialize();
    const agentInitialized = aiAgentIntegration.initialize();
    const terminalInitialized = terminalIntegration.initialize();

    if (!uiInitialized || !messageInitialized || !agentInitialized || !terminalInitialized) {
      console.error('Failed to initialize AI Dialog modules');
      return false;
    }

    this.setupEventListeners();
    this.setupCallbacks();
    this.setupAutoResize();
    
    this.isInitialized = true;
    console.log('Refactored AI Dialog Service initialized');
    return true;
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Send button click (handles both send and terminate)
    this.sendBtn.addEventListener('click', () => this.handleSendButtonClick());

    // Enter key in input (Shift+Enter for new line)
    this.input.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.handleSendButtonClick();
      }
    });
  }

  /**
   * Setup callbacks between modules
   */
  setupCallbacks() {
    // Set up UI callbacks
    dialogUIManager.setCallbacks({
      onShow: () => {
        this.input.focus();
        this.updateUIState();
      },
      onHide: () => {
        // Optional: Clear any ongoing operations
      },
      onPauseResume: () => {
        this.handlePauseResume();
      }
    });

    // Set up AI agent callbacks
    aiAgentIntegration.setCallbacks({
      onChunk: (chunk) => messageRenderer.handleStreamingChunk(chunk),
      onToolCall: (event) => messageRenderer.handleToolCall(event),
      onComplete: (response) => {
        messageRenderer.handleStreamingComplete(response);
        this.handleAgentComplete();
      },
      onError: (error) => {
        messageRenderer.handleStreamingError(error);
        this.handleAgentComplete();
      }
    });
  }

  /**
   * Setup auto-resizing textarea
   */
  setupAutoResize() {
    this.input.addEventListener('input', () => {
      this.input.style.height = 'auto';
      this.input.style.height = Math.min(this.input.scrollHeight, 120) + 'px';
    });
  }

  /**
   * Handle send button click (send or terminate based on current state)
   */
  handleSendButtonClick() {
    if (dialogUIManager.isSendButtonInTerminateMode()) {
      this.terminateAgent();
    } else {
      this.sendMessage();
    }
  }

  /**
   * Send a message from the user
   */
  async sendMessage() {
    const message = this.input.value.trim();
    if (!message) return;

    // Reset agent termination if needed
    aiAgentIntegration.resetAgentTermination();

    // Add user message to display
    messageRenderer.addMessage(message, 'user');

    // Clear input
    this.input.value = '';
    this.input.style.height = 'auto';

    // Transform send button to terminate button
    dialogUIManager.setSendButtonToTerminate();

    // Start streaming message
    messageRenderer.startStreamingMessage();

    try {
      // Get terminal context and send to AI agent
      const context = terminalIntegration.getTerminalContext();
      await aiAgentIntegration.sendMessage(message, context);
    } catch (error) {
      console.error('Error sending message to AI agent:', error);
      messageRenderer.handleStreamingError(error);
      this.handleAgentComplete();
    }
  }

  /**
   * Terminate the current agent operation
   */
  terminateAgent() {
    console.log('Terminating agent...');
    aiAgentIntegration.terminateAgent();
    this.handleAgentComplete();
  }

  /**
   * Handle agent completion (success or error)
   */
  handleAgentComplete() {
    // Restore send button
    dialogUIManager.setSendButtonToSend();
    this.sendBtn.disabled = false;

    // Update UI state
    this.updateUIState();
  }

  /**
   * Handle pause/resume button click
   */
  handlePauseResume() {
    const controlState = aiAgentIntegration.getAgentControlState();

    if (controlState.isPaused) {
      // Resume agent
      aiAgentIntegration.resumeAgent();
      console.log('Agent resumed');
    } else {
      // Pause agent
      aiAgentIntegration.pauseAgent();
      console.log('Agent paused');
    }

    // Update UI state
    this.updateUIState();
  }

  /**
   * Update UI state based on agent status
   */
  updateUIState() {
    const controlState = aiAgentIntegration.getAgentControlState();

    // Update pause/resume button
    dialogUIManager.updatePauseResumeButton(controlState.isPaused);

    // Enable/disable pause/resume button based on agent readiness
    dialogUIManager.setPauseResumeButtonEnabled(controlState.isReady);

    // Update send button state if not processing
    if (!controlState.isProcessing && !dialogUIManager.isSendButtonInTerminateMode()) {
      this.sendBtn.disabled = false;
    }
  }

  /**
   * Show the AI dialog
   */
  show() {
    dialogUIManager.showDialog();
    this.updateUIState();
  }

  /**
   * Hide the AI dialog
   */
  hide() {
    dialogUIManager.hideDialog();
  }

  /**
   * Toggle dialog visibility
   */
  toggle() {
    dialogUIManager.toggleDialog();
  }

  /**
   * Check if dialog is visible
   */
  isVisible() {
    return dialogUIManager.isDialogVisible();
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    messageRenderer.clearMessages();
  }

  /**
   * Get all messages
   */
  getMessages() {
    return messageRenderer.getMessages();
  }

  /**
   * Configure AI agent
   */
  async configureAIAgent(config) {
    return await aiAgentIntegration.configureAgent(config);
  }

  /**
   * Get AI agent configuration
   */
  getAIAgentConfig() {
    return aiAgentIntegration.getAgentConfig();
  }

  /**
   * Test AI agent connection
   */
  async testAIAgentConnection() {
    return await aiAgentIntegration.testAgentConnection();
  }

  /**
   * Get AI agent statistics
   */
  getAIAgentStats() {
    return aiAgentIntegration.getAgentStats();
  }

  /**
   * Clear current session
   */
  clearSession() {
    aiAgentIntegration.clearSession();
    this.clearMessages();
  }

  /**
   * Reset session (generate new session ID)
   */
  resetSession() {
    aiAgentIntegration.resetSession();
    this.clearMessages();
  }

  /**
   * Get session history
   */
  getSessionHistory() {
    return aiAgentIntegration.getSessionHistory();
  }

  /**
   * Get current session ID
   */
  getSessionId() {
    return aiAgentIntegration.getSessionId();
  }

  /**
   * Execute command in terminal
   */
  async executeCommand(command) {
    return await terminalIntegration.executeCommand(command);
  }

  /**
   * Send text to terminal
   */
  async sendTextToTerminal(text) {
    return await terminalIntegration.sendTextToTerminal(text);
  }

  /**
   * Get terminal output
   */
  getTerminalOutput(lines = 10) {
    return terminalIntegration.getTerminalOutput(lines);
  }

  /**
   * Check if service is initialized
   */
  isServiceInitialized() {
    return this.isInitialized;
  }

  /**
   * Check if AI agent is ready
   */
  isAIAgentReady() {
    return aiAgentIntegration.isAgentReady();
  }

  /**
   * Check if terminal is ready
   */
  isTerminalReady() {
    return terminalIntegration.isTerminalReady();
  }

  /**
   * Get service status
   */
  getServiceStatus() {
    return {
      initialized: this.isInitialized,
      dialogVisible: this.isVisible(),
      aiAgentReady: this.isAIAgentReady(),
      terminalReady: this.isTerminalReady(),
      sessionId: this.getSessionId(),
      messageCount: this.getMessages().length
    };
  }

  /**
   * Get agent control state (for testing)
   */
  getAgentControlState() {
    return aiAgentIntegration.getAgentControlState();
  }

  /**
   * Pause agent (for testing)
   */
  pauseAgent() {
    return aiAgentIntegration.pauseAgent();
  }

  /**
   * Resume agent (for testing)
   */
  resumeAgent() {
    return aiAgentIntegration.resumeAgent();
  }

  /**
   * Terminate agent (for testing)
   */
  terminateAgent() {
    return aiAgentIntegration.terminateAgent();
  }

  /**
   * Set send button to terminate mode (for testing)
   */
  setSendButtonToTerminate() {
    return dialogUIManager.setSendButtonToTerminate();
  }

  /**
   * Set send button to send mode (for testing)
   */
  setSendButtonToSend() {
    return dialogUIManager.setSendButtonToSend();
  }

  /**
   * Check if send button is in terminate mode (for testing)
   */
  isSendButtonInTerminateMode() {
    return dialogUIManager.isSendButtonInTerminateMode();
  }
}

// Export singleton instance
export default new RefactoredAIDialogService();
