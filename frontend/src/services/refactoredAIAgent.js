/**
 * Refactored AI Agent Service
 * 
 * This service implements a sophisticated AI agent with:
 * - Streaming support
 * - Multi-turn tool calling
 * - Configurable tools and system prompts
 * - Session management
 */

/**
 * AI Agent Class
 * Encapsulates an AI agent with system prompt, session, model, tools, and iteration limits
 */
export class AIAgent {
  constructor(options = {}) {
    this.systemPrompt = options.systemPrompt || this.getDefaultSystemPrompt();
    this.sessionId = options.sessionId || this.generateSessionId();
    this.model = options.model || 'gpt-3.5-turbo';
    this.tools = options.tools || [];
    this.maxToolCallIterations = options.maxToolCallIterations || 30;
    
    // Configuration
    this.config = {
      apiKey: options.apiKey || '',
      endpoint: options.endpoint || 'https://api.openai.com/v1/chat/completions',
      maxTokens: options.maxTokens || 1000,
      temperature: options.temperature || 0.7,
      stream: options.stream !== false // Default to streaming
    };
    
    // Session state
    this.messages = [];
    this.toolCallCount = 0;
    this.isReady = false;

    // Control state for pause, resume, and terminate
    this.isPaused = false;
    this.isTerminated = false;
    this.pausePromise = null;
    this.pauseResolve = null;
    this.isProcessing = false;

    // Event handlers for streaming
    this.onChunk = options.onChunk || null;
    this.onToolCall = options.onToolCall || null;
    this.onComplete = options.onComplete || null;
    this.onError = options.onError || null;
  }

  /**
   * Get default system prompt
   */
  getDefaultSystemPrompt() {
    return `你是一个交互式终端（macOS系统/bin/bash）自动化专家，正在与一个交互式终端协同工作，努力完成用户提交的任务。你有一个向终端输入的工具箱，要灵活运用该工具，并尽量优先将命令组合起来批量执行(即：command_group 模式)，尽最大努力完成用户提交的任务，尽量自主决策，不要过多向用户询问。

你的工具箱包含以下能力：
1. **terminal_input** - 向终端发送输入(优先使用：command_group)
   - single_key: 发送单个按键（如 Enter, Tab, Escape, ArrowUp 等）
   - key_combo: 发送组合键（如 Ctrl+C, Ctrl+Z, Alt+F4 等）
   - command_line: 发送单个命令行（如 ls -la, pwd, cat file.txt）
   - command_group: 发送多个命令序列（优先使用本模式）

2. **wait** - 等待指定时间
   - 在需要等待命令执行、界面加载等场景时使用

例如你可以精准使用工具完成以下场景：

**场景1：中断正在运行的程序**
- 使用 terminal_input 工具，key_combo 模式发送 Ctrl+C

**场景2：查找并编辑文件内容**
- 使用 command_line 模式输入：vi /tmp/nginx.conf
- 使用 wait 工具等待 vi 加载完成
- 如果未找到，使用 single_key 模式导航到下一页
- 找到后，使用命令序列移动光标到目标位置

**场景3：SSH连接远程服务器**
- 输入SSH连接命令
- 检查输出是否有信任主机提示
- 如有提示则输入"yes"，否则输入密码
- 执行远程命令

**场景4：文件操作和管理**
- 使用 ls 命令查看目录内容
- 使用 cd 命令切换目录
- 使用 cp, mv, rm 等命令进行文件操作
- 使用 grep, find 等命令搜索文件

**场景5：系统监控和诊断**
- 使用 ps, top 命令查看进程
- 使用 df, du 命令查看磁盘使用
- 使用 netstat, lsof 命令查看网络连接

要点：
- 积极主动，自主决策，高效使用工具完成任务
- 根据终端输出动态调整策略，确保对当前状态有准确理解
- 合理使用等待时间，确保命令执行完成后再获取输出
- 优先使用安全的只读命令进行探索
- 切勿推断终端状态，终端阅读当前屏幕内容决策下一步。例如：在历史会话中ssh到某台机器可能询问是否信任主机，你输入了yes，当再次ssh到同一台机器时，你不能依赖推断是否要再次输入yes，而应当结合当前屏幕内容和光标位置来决策是否要再次输入yes
`;
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId() {
    return 'agent_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Initialize the agent
   */
  async initialize() {
    if (!this.config.apiKey) {
      throw new Error('API key is required');
    }
    
    try {
      // Test the API connection
      await this.testConnection();
      this.isReady = true;
      console.log('AI Agent initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize AI Agent:', error);
      throw error;
    }
  }

  /**
   * Test API connection
   */
  async testConnection() {
    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify({
        model: this.model,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
        stream: false
      })
    });

    if (!response.ok) {
      throw new Error(`API test failed: ${response.status} ${response.statusText}`);
    }

    return true;
  }

  /**
   * Add a tool to the agent
   */
  addTool(tool) {
    this.tools.push(tool);
  }

  /**
   * Remove a tool from the agent
   */
  removeTool(toolName) {
    this.tools = this.tools.filter(tool => tool.function.name !== toolName);
  }

  /**
   * Get tool definitions for OpenAI API
   */
  getToolDefinitions() {
    return this.tools.map(tool => ({
      type: 'function',
      function: tool.function
    }));
  }

  /**
   * Process a user message with streaming support and multi-turn tool calling
   */
  async processMessage(userMessage, context = {}) {
    if (!this.isReady) {
      throw new Error('Agent not initialized');
    }

    // Check if agent is terminated
    if (this.isTerminated) {
      throw new Error('Agent has been terminated');
    }

    // Set processing state
    this.isProcessing = true;

    try {
      // Add user message to session
      this.messages.push({
        role: 'user',
        content: userMessage,
        timestamp: new Date().toISOString()
      });

      // Reset tool call count for this interaction
      this.toolCallCount = 0;

      const result = await this.runConversationLoop(context);
      return result;
    } catch (error) {
      if (this.onError) {
        this.onError(error);
      }
      throw error;
    } finally {
      // Clear processing state
      this.isProcessing = false;
    }
  }

  /**
   * Run the conversation loop with tool calling
   */
  async runConversationLoop(context = {}) {
    let iteration = 0;

    while (iteration < this.maxToolCallIterations) {
      // Check for termination at the start of each iteration
      if (this.isTerminated) {
        console.log('Conversation loop terminated by user');
        throw new Error('Agent terminated by user');
      }

      // Check for pause and wait if needed (up to 10 minutes)
      if (this.isPaused) {
        console.log('Agent paused, waiting for resume...');
        await this.waitForResume(600000); // 10 minutes timeout
      }

      // Check for termination again after potential pause
      if (this.isTerminated) {
        console.log('Conversation loop terminated during pause');
        throw new Error('Agent terminated by user');
      }

      iteration++;

      // Prepare messages for API call
      // 动态获取终端屏幕内容和光标位置，插入systemPrompt前
      let terminalScreenXml = '';
      if (typeof window !== 'undefined' && window.xtermService && typeof window.xtermService.getAllContent === 'function') {
        const { lines, cursorX, cursorY } = window.xtermService.getAllContent();
        const screenText = lines.join('\n');
        terminalScreenXml = `\n
<terminalScreen><![CDATA[${screenText}]]></terminalScreen>\n<cursor x="${cursorX}" y="${cursorY}"/>\n<!-- 上述内容为当前终端屏幕文本和光标位置，要始终以该内容和光标位置为准决策下一步操作，这一点很重要!!! -->\n`;
      }
      const messages = [
        { role: 'system', content: this.systemPrompt },
        ...this.messages.map(msg => ({
          role: msg.role,
          content: msg.content,
          tool_calls: msg.tool_calls,
          tool_call_id: msg.tool_call_id
        }))
      ];

      // Add terminal screen content to the last message
      if (messages.length > 0) {
        const lastMessage = messages[messages.length - 1];
        lastMessage.content += terminalScreenXml;
      }


      // Add context if provided
      // if (Object.keys(context).length > 0) {
      //   messages.push({
      //     role: 'system',
      //     content: `Current context: ${JSON.stringify(context, null, 2)}`
      //   });
      // }

      // Make API call
      const response = await this.callOpenAI(messages);

      // Check for termination after API call
      if (this.isTerminated) {
        console.log('Conversation loop terminated after API call');
        throw new Error('Agent terminated by user');
      }

      if (response.finish_reason === 'stop') {
        // Conversation complete
        if (this.onComplete) {
          this.onComplete(response);
        }
        return response;
      } else if (response.finish_reason === 'tool_calls') {
        // Execute tool calls and continue
        await this.executeToolCalls(response.tool_calls);
        continue;
      } else if (response.finish_reason === 'length') {
        // Token limit reached
        console.warn('Token limit reached');
        if (this.onComplete) {
          this.onComplete(response);
        }
        return response;
      }
    }

    throw new Error(`Maximum tool call iterations (${this.maxToolCallIterations}) exceeded`);
  }

  /**
   * Call OpenAI API with streaming support
   */
  async callOpenAI(messages) {
    const requestBody = {
      model: this.model,
      messages: messages,
      max_tokens: this.config.maxTokens,
      temperature: this.config.temperature,
      stream: this.config.stream
    };

    // Add tools if available
    if (this.tools.length > 0) {
      requestBody.tools = this.getToolDefinitions();
      requestBody.tool_choice = 'auto';
    }

    const response = await fetch(this.config.endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.config.apiKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    if (this.config.stream) {
      return await this.handleStreamingResponse(response);
    } else {
      const data = await response.json();
      const message = data.choices[0].message;
      
      // Add assistant message to session
      this.messages.push({
        role: 'assistant',
        content: message.content,
        tool_calls: message.tool_calls,
        timestamp: new Date().toISOString()
      });

      return {
        content: message.content,
        tool_calls: message.tool_calls,
        finish_reason: data.choices[0].finish_reason
      };
    }
  }

  /**
   * Handle streaming response from OpenAI
   */
  async handleStreamingResponse(response) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let content = '';
    let tool_calls = [];
    let finish_reason = null;
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              break;
            }
            
            try {
              const parsed = JSON.parse(data);
              const delta = parsed.choices[0]?.delta;
              
              if (delta) {
                // Handle content chunks
                if (delta.content) {
                  content += delta.content;
                  if (this.onChunk) {
                    this.onChunk({
                      type: 'content',
                      content: delta.content,
                      fullContent: content
                    });
                  }
                }
                
                // Handle tool call chunks
                if (delta.tool_calls) {
                  for (const toolCall of delta.tool_calls) {
                    if (!tool_calls[toolCall.index]) {
                      tool_calls[toolCall.index] = {
                        id: toolCall.id,
                        type: toolCall.type,
                        function: { name: '', arguments: '' }
                      };
                    }
                    
                    if (toolCall.function?.name) {
                      tool_calls[toolCall.index].function.name += toolCall.function.name;
                    }
                    
                    if (toolCall.function?.arguments) {
                      tool_calls[toolCall.index].function.arguments += toolCall.function.arguments;
                    }
                    
                    if (this.onChunk) {
                      this.onChunk({
                        type: 'tool_call',
                        toolCall: toolCall,
                        allToolCalls: tool_calls
                      });
                    }
                  }
                }
              }
              
              // Check for finish reason
              if (parsed.choices[0]?.finish_reason) {
                finish_reason = parsed.choices[0].finish_reason;
              }
            } catch (e) {
              // Skip invalid JSON lines
              continue;
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
    
    // Add assistant message to session
    this.messages.push({
      role: 'assistant',
      content: content,
      tool_calls: tool_calls.length > 0 ? tool_calls : undefined,
      timestamp: new Date().toISOString()
    });

    return {
      content: content,
      tool_calls: tool_calls.length > 0 ? tool_calls : undefined,
      finish_reason: finish_reason
    };
  }

  /**
   * Execute tool calls
   */
  async executeToolCalls(toolCalls) {
    if (!toolCalls || toolCalls.length === 0) {
      return;
    }

    for (const toolCall of toolCalls) {
      // Check for termination before each tool call
      if (this.isTerminated) {
        console.log('Tool execution terminated by user');
        throw new Error('Agent terminated by user');
      }

      try {
        this.toolCallCount++;

        if (this.onToolCall) {
          this.onToolCall({
            type: 'start',
            toolCall: toolCall,
            count: this.toolCallCount
          });
        }

        // Find the tool function
        const tool = this.tools.find(t => t.function.name === toolCall.function.name);

        if (!tool) {
          throw new Error(`Tool not found: ${toolCall.function.name}`);
        }

        // Parse arguments
        let args = {};
        try {
          args = JSON.parse(toolCall.function.arguments);
        } catch (e) {
          throw new Error(`Invalid tool arguments: ${toolCall.function.arguments}`);
        }

        // Execute the tool
        const result = await tool.execute(args);

        // Check for termination after tool execution
        if (this.isTerminated) {
          console.log('Tool execution terminated after tool completion');
          throw new Error('Agent terminated by user');
        }

        // Add tool result to messages
        this.messages.push({
          role: 'tool',
          content: JSON.stringify(result),
          tool_call_id: toolCall.id,
          timestamp: new Date().toISOString()
        });

        if (this.onToolCall) {
          this.onToolCall({
            type: 'complete',
            toolCall: toolCall,
            result: result,
            count: this.toolCallCount
          });
        }

      } catch (error) {
        console.error(`Tool execution error for ${toolCall.function.name}:`, error);

        // Add error result to messages
        this.messages.push({
          role: 'tool',
          content: JSON.stringify({ error: error.message }),
          tool_call_id: toolCall.id,
          timestamp: new Date().toISOString()
        });

        if (this.onToolCall) {
          this.onToolCall({
            type: 'error',
            toolCall: toolCall,
            error: error.message,
            count: this.toolCallCount
          });
        }

        // Re-throw termination errors
        if (error.message === 'Agent terminated by user') {
          throw error;
        }
      }
    }
  }

  /**
   * Clear session messages
   */
  clearSession() {
    this.messages = [];
    this.toolCallCount = 0;
  }

  /**
   * Get session messages
   */
  getMessages() {
    return [...this.messages];
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get agent statistics
   */
  getStats() {
    return {
      sessionId: this.sessionId,
      model: this.model,
      toolCount: this.tools.length,
      messageCount: this.messages.length,
      toolCallCount: this.toolCallCount,
      maxIterations: this.maxToolCallIterations,
      isReady: this.isReady,
      isPaused: this.isPaused,
      isTerminated: this.isTerminated,
      isProcessing: this.isProcessing
    };
  }

  /**
   * Pause the agent (wait)
   * Creates a promise that will be resolved when resume is called
   */
  pause() {
    if (this.isPaused) {
      console.log('Agent is already paused');
      return;
    }

    this.isPaused = true;
    this.pausePromise = new Promise((resolve) => {
      this.pauseResolve = resolve;
    });

    console.log(`Agent ${this.sessionId} paused`);
  }

  /**
   * Resume the agent (clear wait)
   * Resolves the pause promise and allows the agent to continue
   */
  resume() {
    if (!this.isPaused) {
      console.log('Agent is not paused');
      return;
    }

    this.isPaused = false;
    if (this.pauseResolve) {
      this.pauseResolve();
      this.pauseResolve = null;
    }
    this.pausePromise = null;

    console.log(`Agent ${this.sessionId} resumed`);
  }

  /**
   * Terminate the agent
   * Stops all processing and clears any pending operations
   */
  terminate() {
    this.isTerminated = true;
    this.isProcessing = false;

    // If paused, resume to allow termination to proceed
    if (this.isPaused) {
      this.resume();
    }

    console.log(`Agent ${this.sessionId} terminated`);
  }

  /**
   * Reset termination flag
   * Allows the agent to be used again after termination
   */
  resetTermination() {
    this.isTerminated = false;
    console.log(`Agent ${this.sessionId} termination reset`);
  }

  /**
   * Check if agent is paused
   */
  getIsPaused() {
    return this.isPaused;
  }

  /**
   * Check if agent is terminated
   */
  getIsTerminated() {
    return this.isTerminated;
  }

  /**
   * Check if agent is processing
   */
  getIsProcessing() {
    return this.isProcessing;
  }

  /**
   * Wait for pause to be cleared (with timeout)
   * @param {number} timeoutMs - Maximum time to wait in milliseconds (default: 10 minutes)
   */
  async waitForResume(timeoutMs = 600000) { // 10 minutes default
    if (!this.isPaused) {
      return;
    }

    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Pause timeout exceeded')), timeoutMs);
    });

    try {
      await Promise.race([this.pausePromise, timeoutPromise]);
    } catch (error) {
      console.warn(`Agent ${this.sessionId} pause timeout:`, error.message);
      // Auto-resume on timeout
      this.resume();
    }
  }
}

/**
 * AI Agent Manager
 * Manages multiple AI agents and provides a unified interface
 */
export class AIAgentManager {
  constructor() {
    this.agents = new Map();
    this.defaultConfig = {
      apiKey: '',
      endpoint: 'https://api.openai.com/v1/chat/completions',
      model: 'gpt-3.5-turbo',
      maxTokens: 1000,
      temperature: 0.7,
      stream: true
    };
  }

  /**
   * Create a new agent
   */
  createAgent(sessionId, options = {}) {
    const config = { ...this.defaultConfig, ...options };
    const agent = new AIAgent({
      sessionId: sessionId,
      ...config
    });

    this.agents.set(sessionId, agent);
    return agent;
  }

  /**
   * Get an existing agent
   */
  getAgent(sessionId) {
    return this.agents.get(sessionId);
  }

  /**
   * Remove an agent
   */
  removeAgent(sessionId) {
    return this.agents.delete(sessionId);
  }

  /**
   * Get all agent sessions
   */
  getAllSessions() {
    return Array.from(this.agents.keys());
  }

  /**
   * Update default configuration
   */
  updateDefaultConfig(config) {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * Get manager statistics
   */
  getStats() {
    return {
      agentCount: this.agents.size,
      sessions: Array.from(this.agents.keys()),
      defaultConfig: { ...this.defaultConfig, apiKey: this.defaultConfig.apiKey ? '***' : '' }
    };
  }
}

/**
 * AI Agent Configuration Service
 */
export class AIAgentConfig {
  constructor() {
    this.storageKey = 'ai-agent-config';
    this.defaultConfig = {
      apiKey: '',
      endpoint: 'https://api.openai.com/v1/chat/completions',
      model: 'gpt-3.5-turbo',
      maxTokens: 1000,
      temperature: 0.7,
      stream: true,
      maxToolCallIterations: 30
    };
  }

  /**
   * Load configuration from localStorage
   */
  load() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        return { ...this.defaultConfig, ...JSON.parse(stored) };
      }
    } catch (error) {
      console.warn('Failed to load AI agent config from localStorage:', error);
    }
    return { ...this.defaultConfig };
  }

  /**
   * Save configuration to localStorage
   */
  save(config) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(config));
      return true;
    } catch (error) {
      console.error('Failed to save AI agent config to localStorage:', error);
      return false;
    }
  }

  /**
   * Reset to default configuration
   */
  reset() {
    try {
      localStorage.removeItem(this.storageKey);
      return { ...this.defaultConfig };
    } catch (error) {
      console.error('Failed to reset AI agent config:', error);
      return { ...this.defaultConfig };
    }
  }

  /**
   * Validate configuration
   */
  validate(config) {
    const errors = [];

    if (!config.endpoint || !config.endpoint.startsWith('http')) {
      errors.push('Invalid endpoint URL');
    }

    if (!config.model || config.model.trim() === '') {
      errors.push('Model ID is required');
    }

    if (!config.apiKey || config.apiKey.trim() === '') {
      errors.push('API Key is required');
    }

    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 4000)) {
      errors.push('Max tokens must be between 1 and 4000');
    }

    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2');
    }

    if (config.maxToolCallIterations && (config.maxToolCallIterations < 1 || config.maxToolCallIterations > 100)) {
      errors.push('Max tool call iterations must be between 1 and 100');
    }

    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
}

// Export singleton instances
export const aiAgentManager = new AIAgentManager();
export const aiAgentConfig = new AIAgentConfig();
