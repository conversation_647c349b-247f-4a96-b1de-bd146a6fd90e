/**
 * Dialog UI Manager
 * 
 * Handles dialog visibility, positioning, dragging, resizing, and window controls
 */

export class DialogUIManager {
  constructor() {
    // Dialog elements
    this.dialog = null;
    this.headerElement = null;
    this.closeBtn = null;
    this.minimizeBtn = null;
    this.maximizeBtn = null;
    this.aiBtn = null;
    this.pauseResumeBtn = null;
    this.pauseIcon = null;
    this.playIcon = null;
    
    // Dialog state
    this.isVisible = false;
    this.isMinimized = false;
    this.isMaximized = false;
    this.isDragging = false;
    this.dragOffset = { x: 0, y: 0 };
    this.originalPosition = { top: 20, right: 20 };
    this.originalSize = { width: 400, height: 500 };
    
    // Event callbacks
    this.onShow = null;
    this.onHide = null;
    this.onMinimize = null;
    this.onMaximize = null;
    this.onPauseResume = null;
  }

  /**
   * Initialize the dialog UI manager
   */
  initialize() {
    this.dialog = document.getElementById('aiDialog');
    this.headerElement = document.getElementById('aiDialogHeader');
    this.closeBtn = document.getElementById('aiDialogClose');
    this.minimizeBtn = document.getElementById('aiDialogMinimize');
    this.maximizeBtn = document.getElementById('aiDialogMaximize');
    this.aiBtn = document.getElementById('aiBtn');
    this.pauseResumeBtn = document.getElementById('aiPauseResume');
    this.pauseIcon = document.getElementById('pauseIcon');
    this.playIcon = document.getElementById('playIcon');

    if (!this.dialog || !this.headerElement || !this.closeBtn ||
        !this.minimizeBtn || !this.maximizeBtn || !this.aiBtn ||
        !this.pauseResumeBtn || !this.pauseIcon || !this.playIcon) {
      console.error('Dialog UI elements not found');
      return false;
    }

    this.setupEventListeners();
    console.log('Dialog UI Manager initialized');
    return true;
  }

  /**
   * Setup event listeners for dialog interactions
   */
  setupEventListeners() {
    // AI button click
    this.aiBtn.addEventListener('click', () => this.toggleDialog());
    
    // Close button click
    this.closeBtn.addEventListener('click', () => this.hideDialog());
    
    // Minimize button click
    this.minimizeBtn.addEventListener('click', () => this.toggleMinimize());
    
    // Maximize button click
    this.maximizeBtn.addEventListener('click', () => this.toggleMaximize());

    // Pause/Resume button click
    this.pauseResumeBtn.addEventListener('click', () => this.togglePauseResume());

    // Dragging functionality
    this.setupDragging();
    
    // Escape key to close dialog
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hideDialog();
      }
    });
  }

  /**
   * Toggle dialog visibility
   */
  toggleDialog() {
    if (this.isVisible) {
      this.hideDialog();
    } else {
      this.showDialog();
    }
  }

  /**
   * Show the AI dialog
   */
  showDialog() {
    this.isVisible = true;
    this.dialog.classList.remove('ai-dialog-hidden');
    
    if (this.onShow) {
      this.onShow();
    }
    
    console.log('AI Dialog shown');
  }

  /**
   * Hide the AI dialog
   */
  hideDialog() {
    this.isVisible = false;
    this.dialog.classList.add('ai-dialog-hidden');
    
    if (this.onHide) {
      this.onHide();
    }
    
    console.log('AI Dialog hidden');
  }

  /**
   * Toggle minimize state
   */
  toggleMinimize() {
    this.isMinimized = !this.isMinimized;
    if (this.isMinimized) {
      this.dialog.classList.add('minimized');
      this.minimizeBtn.textContent = '+';
      this.minimizeBtn.title = 'Restore';
    } else {
      this.dialog.classList.remove('minimized');
      this.minimizeBtn.textContent = '−';
      this.minimizeBtn.title = 'Minimize';
    }
    
    if (this.onMinimize) {
      this.onMinimize(this.isMinimized);
    }
    
    console.log('AI Dialog minimized:', this.isMinimized);
  }

  /**
   * Toggle maximize state
   */
  toggleMaximize() {
    this.isMaximized = !this.isMaximized;
    if (this.isMaximized) {
      this.dialog.classList.add('maximized');
      this.maximizeBtn.textContent = '❐';
      this.maximizeBtn.title = 'Restore';
    } else {
      this.dialog.classList.remove('maximized');
      this.maximizeBtn.textContent = '□';
      this.maximizeBtn.title = 'Maximize';
    }
    
    if (this.onMaximize) {
      this.onMaximize(this.isMaximized);
    }
    
    console.log('AI Dialog maximized:', this.isMaximized);
  }

  /**
   * Toggle pause/resume state
   */
  togglePauseResume() {
    if (this.onPauseResume) {
      this.onPauseResume();
    }
  }

  /**
   * Update pause/resume button state
   * @param {boolean} isPaused - Whether the agent is paused
   */
  updatePauseResumeButton(isPaused) {
    if (isPaused) {
      // Show play icon (agent is paused)
      this.pauseIcon.style.display = 'none';
      this.playIcon.style.display = 'block';
      this.pauseResumeBtn.classList.add('paused');
      this.pauseResumeBtn.title = 'Resume Agent';
    } else {
      // Show pause icon (agent is running)
      this.pauseIcon.style.display = 'block';
      this.playIcon.style.display = 'none';
      this.pauseResumeBtn.classList.remove('paused');
      this.pauseResumeBtn.title = 'Pause Agent';
    }
  }

  /**
   * Enable/disable pause/resume button
   * @param {boolean} enabled - Whether the button should be enabled
   */
  setPauseResumeButtonEnabled(enabled) {
    this.pauseResumeBtn.disabled = !enabled;
  }

  /**
   * Transform send button to terminate button
   */
  setSendButtonToTerminate() {
    const sendBtn = document.getElementById('aiSend');
    const sendIcon = document.getElementById('sendIcon');
    const stopIcon = document.getElementById('stopIcon');

    if (sendBtn && sendIcon && stopIcon) {
      sendIcon.style.display = 'none';
      stopIcon.style.display = 'block';
      sendBtn.classList.add('terminate');
      sendBtn.title = 'Terminate Agent';
    }
  }

  /**
   * Restore send button from terminate button
   */
  setSendButtonToSend() {
    const sendBtn = document.getElementById('aiSend');
    const sendIcon = document.getElementById('sendIcon');
    const stopIcon = document.getElementById('stopIcon');

    if (sendBtn && sendIcon && stopIcon) {
      sendIcon.style.display = 'block';
      stopIcon.style.display = 'none';
      sendBtn.classList.remove('terminate');
      sendBtn.title = 'Send message';
    }
  }

  /**
   * Check if send button is in terminate mode
   */
  isSendButtonInTerminateMode() {
    const sendBtn = document.getElementById('aiSend');
    return sendBtn ? sendBtn.classList.contains('terminate') : false;
  }

  /**
   * Setup dragging functionality
   */
  setupDragging() {
    this.headerElement.addEventListener('mousedown', (e) => {
      if (e.target === this.headerElement || e.target.tagName === 'H3') {
        this.startDragging(e);
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (this.isDragging) {
        this.drag(e);
      }
    });

    document.addEventListener('mouseup', () => {
      if (this.isDragging) {
        this.stopDragging();
      }
    });
  }

  /**
   * Start dragging
   */
  startDragging(e) {
    if (this.isMaximized) return; // Can't drag when maximized
    
    this.isDragging = true;
    this.dialog.classList.add('dragging');
    
    const rect = this.dialog.getBoundingClientRect();
    this.dragOffset.x = e.clientX - rect.left;
    this.dragOffset.y = e.clientY - rect.top;
    
    e.preventDefault();
  }

  /**
   * Drag the dialog
   */
  drag(e) {
    if (!this.isDragging) return;
    
    const x = e.clientX - this.dragOffset.x;
    const y = e.clientY - this.dragOffset.y;
    
    // Constrain to viewport
    const maxX = window.innerWidth - this.dialog.offsetWidth;
    const maxY = window.innerHeight - this.dialog.offsetHeight;
    
    const constrainedX = Math.max(0, Math.min(x, maxX));
    const constrainedY = Math.max(0, Math.min(y, maxY));
    
    this.dialog.style.left = constrainedX + 'px';
    this.dialog.style.top = constrainedY + 'px';
    this.dialog.style.right = 'auto';
    this.dialog.style.bottom = 'auto';
  }

  /**
   * Stop dragging
   */
  stopDragging() {
    this.isDragging = false;
    this.dialog.classList.remove('dragging');
  }

  /**
   * Check if dialog is visible
   */
  isDialogVisible() {
    return this.isVisible;
  }

  /**
   * Set event callbacks
   */
  setCallbacks(callbacks) {
    this.onShow = callbacks.onShow || null;
    this.onHide = callbacks.onHide || null;
    this.onMinimize = callbacks.onMinimize || null;
    this.onMaximize = callbacks.onMaximize || null;
    this.onPauseResume = callbacks.onPauseResume || null;
  }
}

// Export singleton instance
export default new DialogUIManager();
