/**
 * Enhanced Input Handler
 * 
 * Handles input events with proper IME (Input Method Editor) support
 * for Chinese and other languages that use composition events
 */

export class EnhancedInputHandler {
  constructor(inputElement, onSend, onInput) {
    this.inputElement = inputElement;
    this.onSend = onSend;
    this.onInput = onInput;
    
    // Composition state tracking
    this.isComposing = false;
    this.compositionData = '';
    
    this.setupEventListeners();
  }

  /**
   * Setup all input event listeners
   */
  setupEventListeners() {
    // Composition events for IME support
    this.inputElement.addEventListener('compositionstart', (e) => this.handleCompositionStart(e));
    this.inputElement.addEventListener('compositionupdate', (e) => this.handleCompositionUpdate(e));
    this.inputElement.addEventListener('compositionend', (e) => this.handleCompositionEnd(e));
    
    // Key events
    this.inputElement.addEventListener('keydown', (e) => this.handleKeyDown(e));
    this.inputElement.addEventListener('keyup', (e) => this.handleKeyUp(e));
    
    // Input events
    this.inputElement.addEventListener('input', (e) => this.handleInput(e));
    
    // Auto-resize functionality
    this.setupAutoResize();
  }

  /**
   * Handle composition start (IME input begins)
   */
  handleCompositionStart(e) {
    this.isComposing = true;
    this.compositionData = '';
    console.log('Composition started');
  }

  /**
   * Handle composition update (IME input continues)
   */
  handleCompositionUpdate(e) {
    this.compositionData = e.data || '';
    console.log('Composition update:', this.compositionData);
  }

  /**
   * Handle composition end (IME input finishes)
   */
  handleCompositionEnd(e) {
    this.isComposing = false;
    this.compositionData = e.data || '';
    console.log('Composition ended:', this.compositionData);
    
    // Trigger input event after composition ends
    if (this.onInput) {
      this.onInput(this.inputElement.value);
    }
  }

  /**
   * Handle key down events
   */
  handleKeyDown(e) {
    // Don't process Enter key during composition
    if (this.isComposing) {
      console.log('Key pressed during composition, ignoring:', e.key);
      return;
    }

    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter: Allow new line
        return;
      } else {
        // Enter: Send message
        e.preventDefault();
        this.sendMessage();
      }
    }
  }

  /**
   * Handle key up events
   */
  handleKeyUp(e) {
    // Additional key handling if needed
  }

  /**
   * Handle input events
   */
  handleInput(e) {
    // Don't trigger during composition
    if (this.isComposing) {
      return;
    }

    if (this.onInput) {
      this.onInput(this.inputElement.value);
    }
  }

  /**
   * Send message if conditions are met
   */
  sendMessage() {
    const message = this.inputElement.value.trim();
    if (!message) return;

    if (this.onSend) {
      this.onSend(message);
    }
  }

  /**
   * Setup auto-resizing textarea
   */
  setupAutoResize() {
    const resizeTextarea = () => {
      this.inputElement.style.height = 'auto';
      this.inputElement.style.height = Math.min(this.inputElement.scrollHeight, 120) + 'px';
    };

    this.inputElement.addEventListener('input', resizeTextarea);
    
    // Initial resize
    resizeTextarea();
  }

  /**
   * Clear the input
   */
  clearInput() {
    this.inputElement.value = '';
    this.inputElement.style.height = 'auto';
  }

  /**
   * Focus the input
   */
  focus() {
    this.inputElement.focus();
  }

  /**
   * Get current input value
   */
  getValue() {
    return this.inputElement.value;
  }

  /**
   * Set input value
   */
  setValue(value) {
    this.inputElement.value = value;
    this.inputElement.style.height = 'auto';
    this.inputElement.style.height = Math.min(this.inputElement.scrollHeight, 120) + 'px';
  }

  /**
   * Check if currently composing
   */
  getIsComposing() {
    return this.isComposing;
  }

  /**
   * Destroy the input handler
   */
  destroy() {
    // Remove all event listeners
    this.inputElement.removeEventListener('compositionstart', this.handleCompositionStart);
    this.inputElement.removeEventListener('compositionupdate', this.handleCompositionUpdate);
    this.inputElement.removeEventListener('compositionend', this.handleCompositionEnd);
    this.inputElement.removeEventListener('keydown', this.handleKeyDown);
    this.inputElement.removeEventListener('keyup', this.handleKeyUp);
    this.inputElement.removeEventListener('input', this.handleInput);
  }
}

export default EnhancedInputHandler;
