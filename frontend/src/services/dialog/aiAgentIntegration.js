/**
 * AI Agent Integration
 * 
 * Handles communication with AI agents and manages agent lifecycle
 */

import { aiAgentManager, aiAgentConfig } from '../refactoredAIAgent.js';
import { registerTerminalTools } from '../terminalTools.js';

export class AIAgentIntegration {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.currentAgent = null;
    
    // Event callbacks
    this.onChunk = null;
    this.onToolCall = null;
    this.onComplete = null;
    this.onError = null;
  }

  /**
   * Initialize the AI agent integration
   */
  async initialize() {
    console.log('AI Agent Integration initialized');
    await this.initializeAgent();
    return true;
  }

  async initializeAgent() {
    // Get or create agent for this session
    let agent = aiAgentManager.getAgent(this.sessionId);
    
    if (!agent) {
      // Create new agent with streaming callbacks
      const config = aiAgentConfig.load();
      agent = aiAgentManager.createAgent(this.sessionId, {
        ...config,
        onChunk: (chunk) => this.handleChunk(chunk),
        onToolCall: (toolCall) => this.handleToolCall(toolCall),
        onComplete: (response) => this.handleComplete(response),
        onError: (error) => this.handleError(error)
      });
      
      // Register terminal tools with the agent
      registerTerminalTools(agent);
      
      // Initialize the agent
      await agent.initialize();
      this.currentAgent = agent;
    }

    return agent;
  }

  /**
   * Send message to AI agent
   * @param {string} userMessage - The user's message
   * @param {object} context - Terminal context
   */
  async sendMessage(userMessage, context = {}) {
    // Get or create agent for this session
    let agent = this.currentAgent;

    try {
      // Process message with streaming
      await agent.processMessage(userMessage, context);
    } catch (error) {
      this.handleError(error);
    }
  }

  /**
   * Handle streaming chunk
   */
  handleChunk(chunk) {
    if (this.onChunk) {
      this.onChunk(chunk);
    }
  }

  /**
   * Handle tool call events
   */
  handleToolCall(event) {
    if (this.onToolCall) {
      this.onToolCall(event);
    }
  }

  /**
   * Handle streaming completion
   */
  handleComplete(response) {
    if (this.onComplete) {
      this.onComplete(response);
    }
  }

  /**
   * Handle streaming error
   */
  handleError(error) {
    if (this.onError) {
      this.onError(error);
    }
  }

  /**
   * Configure AI agent
   */
  async configureAgent(config) {
    try {
      // Validate configuration
      const validation = aiAgentConfig.validate(config);
      if (!validation.valid) {
        throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
      }

      // Save configuration
      aiAgentConfig.save(config);
      
      // Update agent manager default config
      aiAgentManager.updateDefaultConfig(config);
      
      // If we have a current agent, recreate it with new config
      if (this.currentAgent) {
        aiAgentManager.removeAgent(this.sessionId);
        this.currentAgent = null;
      }

      return { success: true, message: 'AI agent configured successfully' };
    } catch (error) {
      console.error('Error configuring AI agent:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get AI agent configuration
   */
  getAgentConfig() {
    return aiAgentConfig.load();
  }

  /**
   * Test AI agent connection
   */
  async testAgentConnection() {
    try {
      // Create a temporary agent for testing
      const config = aiAgentConfig.load();
      const testAgent = aiAgentManager.createAgent('test-session', config);
      
      await testAgent.initialize();

      // Send a test message
      const response = await testAgent.processMessage('Hello, this is a test message.', {});

      // Clean up test agent
      aiAgentManager.removeAgent('test-session');

      return { 
        success: true, 
        message: 'Connection test successful', 
        response: response.content 
      };
    } catch (error) {
      console.error('AI agent connection test failed:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get AI agent statistics
   */
  getAgentStats() {
    if (this.currentAgent) {
      return this.currentAgent.getStats();
    }
    return aiAgentManager.getStats();
  }

  /**
   * Clear current session
   */
  clearSession() {
    if (this.currentAgent) {
      this.currentAgent.clearSession();
    }
  }

  /**
   * Reset session (generate new session ID)
   */
  resetSession() {
    if (this.currentAgent) {
      aiAgentManager.removeAgent(this.sessionId);
      this.currentAgent = null;
    }
    this.sessionId = this.generateSessionId();
  }

  /**
   * Get session history
   */
  getSessionHistory() {
    if (this.currentAgent) {
      return this.currentAgent.getMessages();
    }
    return [];
  }

  /**
   * Generate a unique session ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * Get current session ID
   */
  getSessionId() {
    return this.sessionId;
  }

  /**
   * Set event callbacks
   */
  setCallbacks(callbacks) {
    this.onChunk = callbacks.onChunk || null;
    this.onToolCall = callbacks.onToolCall || null;
    this.onComplete = callbacks.onComplete || null;
    this.onError = callbacks.onError || null;
  }

  /**
   * Check if agent is ready
   */
  isAgentReady() {
    return this.currentAgent && this.currentAgent.isReady;
  }

  /**
   * Pause the current agent
   */
  pauseAgent() {
    if (this.currentAgent) {
      this.currentAgent.pause();
      return true;
    }
    return false;
  }

  /**
   * Resume the current agent
   */
  resumeAgent() {
    if (this.currentAgent) {
      this.currentAgent.resume();
      return true;
    }
    return false;
  }

  /**
   * Terminate the current agent
   */
  terminateAgent() {
    if (this.currentAgent) {
      this.currentAgent.terminate();
      return true;
    }
    return false;
  }

  /**
   * Reset termination flag for the current agent
   */
  resetAgentTermination() {
    if (this.currentAgent) {
      this.currentAgent.resetTermination();
      return true;
    }
    return false;
  }

  /**
   * Check if agent is paused
   */
  isAgentPaused() {
    return this.currentAgent ? this.currentAgent.getIsPaused() : false;
  }

  /**
   * Check if agent is terminated
   */
  isAgentTerminated() {
    return this.currentAgent ? this.currentAgent.getIsTerminated() : false;
  }

  /**
   * Check if agent is processing
   */
  isAgentProcessing() {
    return this.currentAgent ? this.currentAgent.getIsProcessing() : false;
  }

  /**
   * Get agent control state
   */
  getAgentControlState() {
    if (!this.currentAgent) {
      return {
        isPaused: false,
        isTerminated: false,
        isProcessing: false,
        isReady: false
      };
    }

    return {
      isPaused: this.currentAgent.getIsPaused(),
      isTerminated: this.currentAgent.getIsTerminated(),
      isProcessing: this.currentAgent.getIsProcessing(),
      isReady: this.currentAgent.isReady
    };
  }

  /**
   * Get terminal context for AI agent
   */
  getTerminalContext() {
    const context = {};

    // Get terminal service context if available
    if (window.xtermService && window.xtermService.isServiceReady()) {
      try {
        context.currentLine = window.xtermService.getCurrentLineContent();
        context.terminalDimensions = window.xtermService.getTerminalDimensions();
        context.cursorPosition = window.xtermService.getCursorPosition();

        // Get last few lines for context
        const recentLines = window.xtermService.getLineRangeContent(
          Math.max(0, context.cursorPosition.y - 5),
          context.cursorPosition.y
        );
        context.recentOutput = recentLines.join('\n');
      } catch (error) {
        console.warn('Error getting terminal context:', error);
      }
    }

    return context;
  }
}

// Export singleton instance
export default new AIAgentIntegration();
