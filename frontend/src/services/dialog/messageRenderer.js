/**
 * Enhanced Message Renderer
 *
 * Handles message display, streaming effects, and tool call visualization
 * with improved markdown rendering and collapsible tool calls
 */

import { marked } from 'marked';

export class MessageRenderer {
  constructor() {
    this.messagesContainer = null;
    this.messages = [];

    // Streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements = new Map();

    // Enhanced message data structure
    this.messageIdCounter = 0;

    // Configure marked with enhanced options
    this.configureMarkdown();
  }

  /**
   * Configure markdown renderer with enhanced features
   */
  configureMarkdown() {
    // Custom renderer for better code highlighting and formatting
    const renderer = new marked.Renderer();

    // Enhanced code block rendering
    renderer.code = (code, language) => {
      const validLanguage = language && /^[a-zA-Z0-9_+-]*$/.test(language);
      const langClass = validLanguage ? ` class="language-${language}"` : '';
      return `<pre class="code-block"><code${langClass}>${this.escapeHtml(code)}</code></pre>`;
    };

    // Enhanced inline code rendering
    renderer.codespan = (code) => {
      return `<code class="inline-code">${this.escapeHtml(code)}</code>`;
    };

    // Enhanced table rendering
    renderer.table = (header, body) => {
      return `<div class="table-wrapper"><table class="markdown-table">
        <thead>${header}</thead>
        <tbody>${body}</tbody>
      </table></div>`;
    };

    marked.setOptions({
      renderer: renderer,
      breaks: true,
      gfm: true,
      sanitize: false,
      highlight: null // We'll handle syntax highlighting separately if needed
    });
  }

  /**
   * Escape HTML to prevent XSS
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Initialize the message renderer
   */
  initialize() {
    this.messagesContainer = document.getElementById('aiMessages');
    
    if (!this.messagesContainer) {
      console.error('Messages container not found');
      return false;
    }

    console.log('Message Renderer initialized');
    return true;
  }

  /**
   * Add a message to the chat with enhanced data structure
   * @param {string} content - Message content
   * @param {string} sender - 'user' or 'assistant'
   * @param {object} options - Additional options (toolCalls, chunks, etc.)
   */
  addMessage(content, sender, options = {}) {
    const message = {
      id: `msg_${++this.messageIdCounter}`,
      content,
      sender,
      timestamp: new Date(),
      chunks: options.chunks || [], // Array of content/tool chunks
      toolCalls: options.toolCalls || [], // Array of tool calls
      isStreaming: options.isStreaming || false,
      metadata: options.metadata || {}
    };

    this.messages.push(message);
    this.renderMessage(message);
    this.scrollToBottom();
    return message;
  }

  /**
   * Enhanced message rendering with support for chunks and tool calls
   * @param {object} message - Enhanced message object
   */
  renderMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ai-message-${message.sender}`;
    messageDiv.setAttribute('data-message-id', message.id);

    // Add timestamp
    const timestampDiv = document.createElement('div');
    timestampDiv.className = 'ai-message-timestamp';
    timestampDiv.textContent = this.formatTimestamp(message.timestamp);
    messageDiv.appendChild(timestampDiv);

    // Render main content
    if (message.content) {
      const contentDiv = document.createElement('div');
      contentDiv.className = 'ai-message-content';

      if (message.sender === 'assistant') {
        // Enhanced markdown rendering for AI messages
        contentDiv.innerHTML = marked.parse(message.content);
      } else {
        // Enhanced user message formatting
        contentDiv.innerHTML = this.formatUserMessage(message.content);
      }

      messageDiv.appendChild(contentDiv);
    }

    // Render tool calls if present
    if (message.toolCalls && message.toolCalls.length > 0) {
      const toolCallsContainer = this.createToolCallsContainer(message.toolCalls, message.id);
      messageDiv.appendChild(toolCallsContainer);
    }

    // Add streaming indicator if needed
    if (message.isStreaming) {
      messageDiv.classList.add('streaming');
      const cursor = document.createElement('span');
      cursor.className = 'streaming-cursor';
      cursor.textContent = '▋';
      messageDiv.appendChild(cursor);
    }

    this.messagesContainer.appendChild(messageDiv);
  }

  /**
   * Format user message with basic formatting
   * @param {string} content - Raw message content
   * @returns {string} Formatted HTML
   */
  formatUserMessage(content) {
    return content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code class="inline-code">$1</code>');
  }

  /**
   * Create collapsible tool calls container
   * @param {Array} toolCalls - Array of tool call objects
   * @param {string} messageId - Message ID for unique identification
   */
  createToolCallsContainer(toolCalls, messageId) {
    const container = document.createElement('div');
    container.className = 'tool-calls-container';

    toolCalls.forEach((toolCall, index) => {
      const toolCallDiv = this.createToolCallElement(toolCall, `${messageId}_tool_${index}`);
      container.appendChild(toolCallDiv);
    });

    return container;
  }

  /**
   * Create a single collapsible tool call element
   * @param {object} toolCall - Tool call object
   * @param {string} toolCallId - Unique tool call ID
   */
  createToolCallElement(toolCall, toolCallId) {
    const toolCallDiv = document.createElement('div');
    toolCallDiv.className = 'tool-call-item';
    toolCallDiv.setAttribute('data-tool-call-id', toolCallId);

    // Create header with collapse/expand functionality
    const headerDiv = document.createElement('div');
    headerDiv.className = 'tool-call-header';
    headerDiv.addEventListener('click', () => this.toggleToolCall(toolCallId));

    const toggleIcon = document.createElement('span');
    toggleIcon.className = 'tool-call-toggle';
    toggleIcon.textContent = '▼';

    const nameSpan = document.createElement('span');
    nameSpan.className = 'tool-call-name';
    nameSpan.textContent = toolCall.function?.name || 'Tool Call';

    const statusSpan = document.createElement('span');
    statusSpan.className = 'tool-call-status';
    statusSpan.textContent = toolCall.status || 'pending';

    headerDiv.appendChild(toggleIcon);
    headerDiv.appendChild(nameSpan);
    headerDiv.appendChild(statusSpan);

    // Create collapsible content
    const contentDiv = document.createElement('div');
    contentDiv.className = 'tool-call-content';

    // Arguments section
    if (toolCall.function?.arguments) {
      const argsDiv = document.createElement('div');
      argsDiv.className = 'tool-call-section';
      argsDiv.innerHTML = `
        <div class="tool-call-section-title">Arguments:</div>
        <pre class="tool-call-args"><code>${this.formatToolCallArgs(toolCall.function.arguments)}</code></pre>
      `;
      contentDiv.appendChild(argsDiv);
    }

    // Result section (if available)
    if (toolCall.result) {
      const resultDiv = document.createElement('div');
      resultDiv.className = 'tool-call-section';
      resultDiv.innerHTML = `
        <div class="tool-call-section-title">Result:</div>
        <pre class="tool-call-result"><code>${this.formatToolCallResult(toolCall.result)}</code></pre>
      `;
      contentDiv.appendChild(resultDiv);
    }

    toolCallDiv.appendChild(headerDiv);
    toolCallDiv.appendChild(contentDiv);

    return toolCallDiv;
  }

  /**
   * Toggle tool call collapse/expand state
   * @param {string} toolCallId - Tool call ID
   */
  toggleToolCall(toolCallId) {
    const toolCallElement = document.querySelector(`[data-tool-call-id="${toolCallId}"]`);
    if (!toolCallElement) return;

    const content = toolCallElement.querySelector('.tool-call-content');
    const toggle = toolCallElement.querySelector('.tool-call-toggle');

    if (content.style.display === 'none') {
      content.style.display = 'block';
      toggle.textContent = '▼';
      toolCallElement.classList.remove('collapsed');
    } else {
      content.style.display = 'none';
      toggle.textContent = '▶';
      toolCallElement.classList.add('collapsed');
    }
  }

  /**
   * Format tool call arguments for display
   * @param {string|object} args - Tool call arguments
   */
  formatToolCallArgs(args) {
    try {
      const parsed = typeof args === 'string' ? JSON.parse(args) : args;
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      return String(args);
    }
  }

  /**
   * Format tool call result for display
   * @param {any} result - Tool call result
   */
  formatToolCallResult(result) {
    try {
      return JSON.stringify(result, null, 2);
    } catch (e) {
      return String(result);
    }
  }

  /**
   * Format timestamp for display
   * @param {Date} timestamp - Message timestamp
   */
  formatTimestamp(timestamp) {
    return timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  }

  /**
   * Start a new streaming message
   */
  startStreamingMessage() {
    // Create a new message element for streaming
    const messageDiv = document.createElement('div');
    messageDiv.className = 'ai-message ai-message-assistant streaming';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-message-content';
    contentDiv.innerHTML = '<span class="streaming-cursor">▋</span>';
    
    messageDiv.appendChild(contentDiv);
    this.messagesContainer.appendChild(messageDiv);
    
    this.currentStreamingMessage = '';
    this.streamingMessageElement = contentDiv;
    this.currentToolCalls = [];
    this.toolCallElements.clear();
    
    this.scrollToBottom();
  }

  /**
   * Handle streaming chunk from AI agent
   */
  handleStreamingChunk(chunk) {
    if (!this.streamingMessageElement) return;
    
    if (chunk.type === 'content') {
      this.currentStreamingMessage += chunk.content;
      
      // Update the streaming message with markdown rendering
      const renderedContent = marked.parse(this.currentStreamingMessage);
      this.streamingMessageElement.innerHTML = renderedContent + '<span class="streaming-cursor">▋</span>';
      
      this.scrollToBottom();
    } else if (chunk.type === 'tool_call') {
      this.handleToolCallChunk(chunk);
    }
  }

  /**
   * Handle tool call chunk
   */
  handleToolCallChunk(chunk) {
    const toolCall = chunk.toolCall;
    const index = toolCall.index || 0;
    
    if (!this.toolCallElements.has(index)) {
      // Create new tool call element
      const toolCallDiv = document.createElement('div');
      toolCallDiv.className = 'tool-call-container';
      toolCallDiv.innerHTML = `
        <div class="tool-call-header">
          <span class="tool-call-icon">🔧</span>
          <span class="tool-call-name">${toolCall.function?.name || 'Loading...'}</span>
          <span class="tool-call-status">Preparing...</span>
        </div>
        <div class="tool-call-args"></div>
      `;
      
      this.streamingMessageElement.appendChild(toolCallDiv);
      this.toolCallElements.set(index, toolCallDiv);
    }
    
    const toolCallElement = this.toolCallElements.get(index);
    const nameElement = toolCallElement.querySelector('.tool-call-name');
    const argsElement = toolCallElement.querySelector('.tool-call-args');
    
    // Update tool call name
    if (toolCall.function?.name) {
      nameElement.textContent = toolCall.function.name;
    }
    
    // Update arguments if available
    if (toolCall.function?.arguments) {
      try {
        const args = JSON.parse(toolCall.function.arguments);
        argsElement.innerHTML = `<pre><code>${JSON.stringify(args, null, 2)}</code></pre>`;
      } catch (e) {
        argsElement.textContent = toolCall.function.arguments;
      }
    }
    
    this.scrollToBottom();
  }

  /**
   * Handle tool call events
   */
  handleToolCall(event) {
    if (event.type === 'start') {
      this.updateToolCallStatus(event.toolCall, 'Executing...', 'executing');
    } else if (event.type === 'complete') {
      this.updateToolCallStatus(event.toolCall, 'Completed', 'completed');
      this.showToolCallResult(event.toolCall, event.result);
    } else if (event.type === 'error') {
      this.updateToolCallStatus(event.toolCall, 'Error', 'error');
      this.showToolCallResult(event.toolCall, { error: event.error });
    }
  }

  /**
   * Update tool call status
   */
  updateToolCallStatus(toolCall, status, className) {
    // Find tool call element by ID
    for (const [, element] of this.toolCallElements) {
      const nameElement = element.querySelector('.tool-call-name');
      if (nameElement && nameElement.textContent === toolCall.function.name) {
        const statusElement = element.querySelector('.tool-call-status');
        statusElement.textContent = status;
        statusElement.className = `tool-call-status ${className}`;
        break;
      }
    }
  }

  /**
   * Show tool call result
   */
  showToolCallResult(toolCall, result) {
    // Find tool call element and add result
    for (const [, element] of this.toolCallElements) {
      const nameElement = element.querySelector('.tool-call-name');
      if (nameElement && nameElement.textContent === toolCall.function.name) {
        let resultElement = element.querySelector('.tool-call-result');
        if (!resultElement) {
          resultElement = document.createElement('div');
          resultElement.className = 'tool-call-result';
          element.appendChild(resultElement);
        }
        
        resultElement.innerHTML = `<pre><code>${JSON.stringify(result, null, 2)}</code></pre>`;
        break;
      }
    }
    
    this.scrollToBottom();
  }

  /**
   * Handle streaming completion
   */
  handleStreamingComplete(response) {
    if (this.streamingMessageElement) {
      // Remove streaming cursor
      const cursor = this.streamingMessageElement.querySelector('.streaming-cursor');
      if (cursor) {
        cursor.remove();
      }

      // Extract and preserve tool call elements as separate messages
      this.preserveToolCallsAsMessages();

      // Mark message as complete
      const messageElement = this.streamingMessageElement.closest('.ai-message');
      if (messageElement) {
        messageElement.classList.remove('streaming');
      }
    }

    // Reset streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements.clear();

    console.log('Streaming complete:', response);
  }

  /**
   * Preserve tool calls as separate message elements
   */
  preserveToolCallsAsMessages() {
    if (this.toolCallElements.size === 0) return;

    // Get the current streaming message element
    const currentMessageElement = this.streamingMessageElement.closest('.ai-message');

    // Extract tool call elements from the streaming message in order
    const toolCallElements = [];
    for (let i = 0; i < this.toolCallElements.size; i++) {
      if (this.toolCallElements.has(i)) {
        toolCallElements.push(this.toolCallElements.get(i));
      }
    }

    // Remove tool calls from the current message
    toolCallElements.forEach(element => {
      if (element.parentNode === this.streamingMessageElement) {
        element.remove();
      }
    });

    // Create separate tool call messages after the current AI message
    let insertAfter = currentMessageElement;

    toolCallElements.forEach((toolCallElement) => {
      const toolMessageDiv = document.createElement('div');
      toolMessageDiv.className = 'ai-message ai-message-tool';

      const toolContentDiv = document.createElement('div');
      toolContentDiv.className = 'ai-message-content';

      // Clone the tool call element
      const clonedToolCall = toolCallElement.cloneNode(true);
      toolContentDiv.appendChild(clonedToolCall);

      toolMessageDiv.appendChild(toolContentDiv);

      // Insert after the previous element
      insertAfter.parentNode.insertBefore(toolMessageDiv, insertAfter.nextSibling);
      insertAfter = toolMessageDiv; // Update for next insertion

      // Add to messages array
      this.messages.push({
        content: toolCallElement.outerHTML,
        sender: 'tool',
        timestamp: new Date()
      });
    });

    // Scroll to bottom after adding tool messages
    this.scrollToBottom();
  }

  /**
   * Handle streaming error
   */
  handleStreamingError(error) {
    console.error('Streaming error:', error);

    if (this.streamingMessageElement) {
      // Preserve any tool calls that were in progress
      this.preserveToolCallsAsMessages();

      this.streamingMessageElement.innerHTML = `<p class="error-message">Error: ${error.message}</p>`;

      const messageElement = this.streamingMessageElement.closest('.ai-message');
      if (messageElement) {
        messageElement.classList.remove('streaming');
        messageElement.classList.add('error');
      }
    }

    // Reset streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements.clear();
  }

  /**
   * Scroll messages container to bottom
   */
  scrollToBottom() {
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    this.messages = [];
    this.messagesContainer.innerHTML = `
      <div class="ai-message ai-message-assistant">
        <div class="ai-message-content">
          <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
        </div>
      </div>
    `;
  }

  /**
   * Get all messages
   * @returns {array} Array of message objects
   */
  getMessages() {
    return [...this.messages];
  }
}

// Export singleton instance
export default new MessageRenderer();
