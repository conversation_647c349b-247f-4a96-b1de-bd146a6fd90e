/**
 * Message Renderer
 * 
 * Handles message display, streaming effects, and tool call visualization
 */

import { marked } from 'marked';

export class MessageRenderer {
  constructor() {
    this.messagesContainer = null;
    this.messages = [];
    
    // Streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements = new Map();
    
    // Configure marked for security
    marked.setOptions({
      breaks: true,
      gfm: true,
      sanitize: false, // We'll handle sanitization manually if needed
    });
  }

  /**
   * Initialize the message renderer
   */
  initialize() {
    this.messagesContainer = document.getElementById('aiMessages');
    
    if (!this.messagesContainer) {
      console.error('Messages container not found');
      return false;
    }

    console.log('Message Renderer initialized');
    return true;
  }

  /**
   * Add a message to the chat
   * @param {string} content - Message content
   * @param {string} sender - 'user' or 'assistant'
   */
  addMessage(content, sender) {
    const message = {
      content,
      sender,
      timestamp: new Date()
    };
    
    this.messages.push(message);
    this.renderMessage(message);
    this.scrollToBottom();
  }

  /**
   * Render a message in the chat
   * @param {object} message - Message object
   */
  renderMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `ai-message ai-message-${message.sender}`;
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-message-content';
    
    if (message.sender === 'assistant') {
      // Render markdown for AI messages
      contentDiv.innerHTML = marked.parse(message.content);
    } else {
      // Simple text for user messages (with basic formatting)
      contentDiv.innerHTML = this.formatUserMessage(message.content);
    }
    
    messageDiv.appendChild(contentDiv);
    this.messagesContainer.appendChild(messageDiv);
  }

  /**
   * Format user message with basic formatting
   * @param {string} content - Raw message content
   * @returns {string} Formatted HTML
   */
  formatUserMessage(content) {
    return content
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/\n/g, '<br>');
  }

  /**
   * Start a new streaming message
   */
  startStreamingMessage() {
    // Create a new message element for streaming
    const messageDiv = document.createElement('div');
    messageDiv.className = 'ai-message ai-message-assistant streaming';
    
    const contentDiv = document.createElement('div');
    contentDiv.className = 'ai-message-content';
    contentDiv.innerHTML = '<span class="streaming-cursor">▋</span>';
    
    messageDiv.appendChild(contentDiv);
    this.messagesContainer.appendChild(messageDiv);
    
    this.currentStreamingMessage = '';
    this.streamingMessageElement = contentDiv;
    this.currentToolCalls = [];
    this.toolCallElements.clear();
    
    this.scrollToBottom();
  }

  /**
   * Handle streaming chunk from AI agent
   */
  handleStreamingChunk(chunk) {
    if (!this.streamingMessageElement) return;
    
    if (chunk.type === 'content') {
      this.currentStreamingMessage += chunk.content;
      
      // Update the streaming message with markdown rendering
      const renderedContent = marked.parse(this.currentStreamingMessage);
      this.streamingMessageElement.innerHTML = renderedContent + '<span class="streaming-cursor">▋</span>';
      
      this.scrollToBottom();
    } else if (chunk.type === 'tool_call') {
      this.handleToolCallChunk(chunk);
    }
  }

  /**
   * Handle tool call chunk
   */
  handleToolCallChunk(chunk) {
    const toolCall = chunk.toolCall;
    const index = toolCall.index || 0;
    
    if (!this.toolCallElements.has(index)) {
      // Create new tool call element
      const toolCallDiv = document.createElement('div');
      toolCallDiv.className = 'tool-call-container';
      toolCallDiv.innerHTML = `
        <div class="tool-call-header">
          <span class="tool-call-icon">🔧</span>
          <span class="tool-call-name">${toolCall.function?.name || 'Loading...'}</span>
          <span class="tool-call-status">Preparing...</span>
        </div>
        <div class="tool-call-args"></div>
      `;
      
      this.streamingMessageElement.appendChild(toolCallDiv);
      this.toolCallElements.set(index, toolCallDiv);
    }
    
    const toolCallElement = this.toolCallElements.get(index);
    const nameElement = toolCallElement.querySelector('.tool-call-name');
    const argsElement = toolCallElement.querySelector('.tool-call-args');
    
    // Update tool call name
    if (toolCall.function?.name) {
      nameElement.textContent = toolCall.function.name;
    }
    
    // Update arguments if available
    if (toolCall.function?.arguments) {
      try {
        const args = JSON.parse(toolCall.function.arguments);
        argsElement.innerHTML = `<pre><code>${JSON.stringify(args, null, 2)}</code></pre>`;
      } catch (e) {
        argsElement.textContent = toolCall.function.arguments;
      }
    }
    
    this.scrollToBottom();
  }

  /**
   * Handle tool call events
   */
  handleToolCall(event) {
    if (event.type === 'start') {
      this.updateToolCallStatus(event.toolCall, 'Executing...', 'executing');
    } else if (event.type === 'complete') {
      this.updateToolCallStatus(event.toolCall, 'Completed', 'completed');
      this.showToolCallResult(event.toolCall, event.result);
    } else if (event.type === 'error') {
      this.updateToolCallStatus(event.toolCall, 'Error', 'error');
      this.showToolCallResult(event.toolCall, { error: event.error });
    }
  }

  /**
   * Update tool call status
   */
  updateToolCallStatus(toolCall, status, className) {
    // Find tool call element by ID
    for (const [, element] of this.toolCallElements) {
      const nameElement = element.querySelector('.tool-call-name');
      if (nameElement && nameElement.textContent === toolCall.function.name) {
        const statusElement = element.querySelector('.tool-call-status');
        statusElement.textContent = status;
        statusElement.className = `tool-call-status ${className}`;
        break;
      }
    }
  }

  /**
   * Show tool call result
   */
  showToolCallResult(toolCall, result) {
    // Find tool call element and add result
    for (const [, element] of this.toolCallElements) {
      const nameElement = element.querySelector('.tool-call-name');
      if (nameElement && nameElement.textContent === toolCall.function.name) {
        let resultElement = element.querySelector('.tool-call-result');
        if (!resultElement) {
          resultElement = document.createElement('div');
          resultElement.className = 'tool-call-result';
          element.appendChild(resultElement);
        }
        
        resultElement.innerHTML = `<pre><code>${JSON.stringify(result, null, 2)}</code></pre>`;
        break;
      }
    }
    
    this.scrollToBottom();
  }

  /**
   * Handle streaming completion
   */
  handleStreamingComplete(response) {
    if (this.streamingMessageElement) {
      // Remove streaming cursor
      const cursor = this.streamingMessageElement.querySelector('.streaming-cursor');
      if (cursor) {
        cursor.remove();
      }

      // Extract and preserve tool call elements as separate messages
      this.preserveToolCallsAsMessages();

      // Mark message as complete
      const messageElement = this.streamingMessageElement.closest('.ai-message');
      if (messageElement) {
        messageElement.classList.remove('streaming');
      }
    }

    // Reset streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements.clear();

    console.log('Streaming complete:', response);
  }

  /**
   * Preserve tool calls as separate message elements
   */
  preserveToolCallsAsMessages() {
    if (this.toolCallElements.size === 0) return;

    // Get the current streaming message element
    const currentMessageElement = this.streamingMessageElement.closest('.ai-message');

    // Extract tool call elements from the streaming message in order
    const toolCallElements = [];
    for (let i = 0; i < this.toolCallElements.size; i++) {
      if (this.toolCallElements.has(i)) {
        toolCallElements.push(this.toolCallElements.get(i));
      }
    }

    // Remove tool calls from the current message
    toolCallElements.forEach(element => {
      if (element.parentNode === this.streamingMessageElement) {
        element.remove();
      }
    });

    // Create separate tool call messages after the current AI message
    let insertAfter = currentMessageElement;

    toolCallElements.forEach((toolCallElement) => {
      const toolMessageDiv = document.createElement('div');
      toolMessageDiv.className = 'ai-message ai-message-tool';

      const toolContentDiv = document.createElement('div');
      toolContentDiv.className = 'ai-message-content';

      // Clone the tool call element
      const clonedToolCall = toolCallElement.cloneNode(true);
      toolContentDiv.appendChild(clonedToolCall);

      toolMessageDiv.appendChild(toolContentDiv);

      // Insert after the previous element
      insertAfter.parentNode.insertBefore(toolMessageDiv, insertAfter.nextSibling);
      insertAfter = toolMessageDiv; // Update for next insertion

      // Add to messages array
      this.messages.push({
        content: toolCallElement.outerHTML,
        sender: 'tool',
        timestamp: new Date()
      });
    });

    // Scroll to bottom after adding tool messages
    this.scrollToBottom();
  }

  /**
   * Handle streaming error
   */
  handleStreamingError(error) {
    console.error('Streaming error:', error);

    if (this.streamingMessageElement) {
      // Preserve any tool calls that were in progress
      this.preserveToolCallsAsMessages();

      this.streamingMessageElement.innerHTML = `<p class="error-message">Error: ${error.message}</p>`;

      const messageElement = this.streamingMessageElement.closest('.ai-message');
      if (messageElement) {
        messageElement.classList.remove('streaming');
        messageElement.classList.add('error');
      }
    }

    // Reset streaming state
    this.currentStreamingMessage = null;
    this.streamingMessageElement = null;
    this.currentToolCalls = [];
    this.toolCallElements.clear();
  }

  /**
   * Scroll messages container to bottom
   */
  scrollToBottom() {
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
  }

  /**
   * Clear all messages
   */
  clearMessages() {
    this.messages = [];
    this.messagesContainer.innerHTML = `
      <div class="ai-message ai-message-assistant">
        <div class="ai-message-content">
          <p>Hello! I'm your AI assistant. How can I help you with your terminal tasks today?</p>
        </div>
      </div>
    `;
  }

  /**
   * Get all messages
   * @returns {array} Array of message objects
   */
  getMessages() {
    return [...this.messages];
  }
}

// Export singleton instance
export default new MessageRenderer();
