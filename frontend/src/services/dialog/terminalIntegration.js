/**
 * Terminal Integration
 * 
 * Handles terminal command execution and output capture for AI dialog
 */

export class TerminalIntegration {
  constructor() {
    // No initialization needed for now
  }

  /**
   * Initialize the terminal integration
   */
  initialize() {
    console.log('Terminal Integration initialized');
    return true;
  }

  /**
   * Execute commands in the terminal as requested by AI
   * @param {Array} commands - Array of commands to execute
   */
  async executeCommands(commands) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      console.warn('XTerm service not available for command execution');
      return [];
    }

    const executedCommands = [];

    for (const command of commands) {
      try {
        console.log('Executing AI-requested command:', command);
        
        // Store the current cursor position to capture output
        const beforePosition = window.xtermService.getCursorPosition();
        
        if (command.type === 'single_key') {
          await window.xtermService.sendKey(command.key);
        } else if (command.type === 'key_combo') {
          await window.xtermService.sendKeyCombo(command.combination);
        } else if (command.type === 'command') {
          await window.xtermService.sendCommand(command.command, command.execute !== false);
        } else if (command.type === 'commands') {
          await window.xtermService.sendCommands(command.commands, command.delay || 100);
        }
        
        // Wait a bit for command to execute and output to appear
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Capture the output after command execution
        const afterPosition = window.xtermService.getCursorPosition();
        let output = '';
        
        if (afterPosition.y > beforePosition.y) {
          // Get the lines between before and after positions
          const outputLines = window.xtermService.getLineRangeContent(
            beforePosition.y,
            afterPosition.y
          );
          output = outputLines.join('\n').trim();
        }
        
        executedCommands.push({
          command: command,
          output: output,
          success: true
        });
        
        // Small delay between commands
        if (commands.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      } catch (error) {
        console.error('Error executing command in terminal:', error);
        executedCommands.push({
          command: command,
          error: error.message,
          success: false
        });
      }
    }

    // Log the execution results for debugging
    console.log('AI command execution results:', executedCommands);
    
    return executedCommands;
  }

  /**
   * Provide execution feedback to the AI agent
   * @param {Array} executionResults - Results from command execution
   */
  async provideExecutionFeedback(executionResults) {
    try {
      // Create a summary of the execution results
      const feedback = executionResults.map(result => {
        if (result.success) {
          return `Command executed: ${JSON.stringify(result.command)}\nOutput: ${result.output || '(no output)'}`;
        } else {
          return `Command failed: ${JSON.stringify(result.command)}\nError: ${result.error}`;
        }
      }).join('\n\n');

      // Send feedback to AI agent as a system message
      const feedbackMessage = `Command execution results:\n\n${feedback}`;
      
      console.log('Providing execution feedback to AI:', feedbackMessage);
      
      // This could be enhanced to send the feedback back to the AI agent
      // for now, we just log it for debugging
    } catch (error) {
      console.error('Error providing execution feedback:', error);
    }
  }

  /**
   * Manually execute a command in the terminal
   * @param {string} command - Command to execute
   */
  async executeCommand(command) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      console.warn('XTerm service not available for command execution');
      return false;
    }

    try {
      await window.xtermService.sendCommand(command, true);
      console.log('Manual command executed:', command);
      return true;
    } catch (error) {
      console.error('Error executing manual command:', error);
      return false;
    }
  }

  /**
   * Send text to terminal without executing
   * @param {string} text - Text to send
   */
  async sendTextToTerminal(text) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      console.warn('XTerm service not available for text input');
      return false;
    }

    try {
      await window.xtermService.sendRawData(text);
      console.log('Text sent to terminal:', text);
      return true;
    } catch (error) {
      console.error('Error sending text to terminal:', error);
      return false;
    }
  }

  /**
   * Get terminal output for AI context
   */
  getTerminalOutput(lines = 10) {
    if (!window.xtermService || !window.xtermService.isServiceReady()) {
      return '';
    }

    try {
      const cursorPos = window.xtermService.getCursorPosition();
      const startLine = Math.max(0, cursorPos.y - lines);
      const outputLines = window.xtermService.getLineRangeContent(startLine, cursorPos.y);
      return outputLines.join('\n').trim();
    } catch (error) {
      console.error('Error getting terminal output:', error);
      return '';
    }
  }

  /**
   * Get current terminal context
   */
  getTerminalContext() {
    const context = {};
    
    // Get terminal service context if available
    if (window.xtermService && window.xtermService.isServiceReady()) {
      try {
        context.currentLine = window.xtermService.getCurrentLineContent();
        context.terminalDimensions = window.xtermService.getTerminalDimensions();
        context.cursorPosition = window.xtermService.getCursorPosition();
        
        // Get last few lines for context
        const recentLines = window.xtermService.getLineRangeContent(
          Math.max(0, context.cursorPosition.y - 5),
          context.cursorPosition.y
        );
        context.recentOutput = recentLines.join('\n');
      } catch (error) {
        console.warn('Error getting terminal context:', error);
      }
    }
    
    return context;
  }

  /**
   * Check if terminal service is ready
   */
  isTerminalReady() {
    return window.xtermService && window.xtermService.isServiceReady();
  }
}

// Export singleton instance
export default new TerminalIntegration();
