/**
 * WebSocket Manager with Auto-Reconnection
 * 
 * Manages WebSocket connections with automatic reconnection, exponential backoff,
 * and connection state management
 */

export class WebSocketManager {
  constructor(url, options = {}) {
    this.url = url;
    this.options = {
      maxReconnectAttempts: options.maxReconnectAttempts || 10,
      reconnectInterval: options.reconnectInterval || 1000, // Start with 1 second
      maxReconnectInterval: options.maxReconnectInterval || 30000, // Max 30 seconds
      reconnectDecay: options.reconnectDecay || 1.5, // Exponential backoff multiplier
      timeoutInterval: options.timeoutInterval || 2000,
      enableLogging: options.enableLogging !== false,
      ...options
    };

    // Connection state
    this.socket = null;
    this.reconnectAttempts = 0;
    this.reconnectTimer = null;
    this.isReconnecting = false;
    this.shouldReconnect = true;
    this.connectionState = 'disconnected'; // disconnected, connecting, connected, reconnecting

    // Event handlers
    this.onOpen = null;
    this.onMessage = null;
    this.onClose = null;
    this.onError = null;
    this.onReconnecting = null;
    this.onReconnected = null;
    this.onMaxReconnectAttemptsReached = null;

    this.log('WebSocket Manager initialized');
  }

  /**
   * Connect to the WebSocket server
   */
  connect() {
    if (this.socket && (this.socket.readyState === WebSocket.CONNECTING || this.socket.readyState === WebSocket.OPEN)) {
      this.log('Already connected or connecting');
      return;
    }

    this.connectionState = 'connecting';
    this.log(`Connecting to ${this.url}`);

    try {
      this.socket = new WebSocket(this.url);
      this.setupEventListeners();
    } catch (error) {
      this.log(`Failed to create WebSocket: ${error.message}`);
      this.handleConnectionError();
    }
  }

  /**
   * Setup WebSocket event listeners
   */
  setupEventListeners() {
    if (!this.socket) return;

    this.socket.addEventListener('open', (event) => {
      this.log('WebSocket connected');
      this.connectionState = 'connected';
      this.reconnectAttempts = 0;
      this.isReconnecting = false;

      // Clear any pending reconnection timer
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }

      if (this.onOpen) {
        this.onOpen(event);
      }

      // Notify if this was a reconnection
      if (this.reconnectAttempts > 0 && this.onReconnected) {
        this.onReconnected(event);
      }
    });

    this.socket.addEventListener('message', (event) => {
      if (this.onMessage) {
        this.onMessage(event);
      }
    });

    this.socket.addEventListener('close', (event) => {
      this.log(`WebSocket closed: code=${event.code}, reason=${event.reason}, wasClean=${event.wasClean}`);
      this.connectionState = 'disconnected';

      if (this.onClose) {
        this.onClose(event);
      }

      // Attempt to reconnect if it wasn't a clean close and we should reconnect
      if (this.shouldReconnect && !event.wasClean) {
        this.handleConnectionError();
      }
    });

    this.socket.addEventListener('error', (event) => {
      this.log(`WebSocket error: ${event}`);
      
      if (this.onError) {
        this.onError(event);
      }

      this.handleConnectionError();
    });
  }

  /**
   * Handle connection errors and attempt reconnection
   */
  handleConnectionError() {
    if (!this.shouldReconnect) {
      this.log('Reconnection disabled, not attempting to reconnect');
      return;
    }

    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.log(`Max reconnection attempts (${this.options.maxReconnectAttempts}) reached`);
      this.shouldReconnect = false;
      
      if (this.onMaxReconnectAttemptsReached) {
        this.onMaxReconnectAttemptsReached();
      }
      return;
    }

    this.reconnectAttempts++;
    this.isReconnecting = true;
    this.connectionState = 'reconnecting';

    // Calculate reconnection delay with exponential backoff
    const delay = Math.min(
      this.options.reconnectInterval * Math.pow(this.options.reconnectDecay, this.reconnectAttempts - 1),
      this.options.maxReconnectInterval
    );

    this.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.options.maxReconnectAttempts})`);

    if (this.onReconnecting) {
      this.onReconnecting(this.reconnectAttempts, delay);
    }

    this.reconnectTimer = setTimeout(() => {
      this.log(`Reconnection attempt ${this.reconnectAttempts}`);
      this.connect();
    }, delay);
  }

  /**
   * Send data through the WebSocket
   */
  send(data) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(data);
      return true;
    } else {
      this.log('Cannot send data: WebSocket is not connected');
      return false;
    }
  }

  /**
   * Close the WebSocket connection
   */
  close(code = 1000, reason = 'Normal closure') {
    this.shouldReconnect = false;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    if (this.socket) {
      this.socket.close(code, reason);
    }

    this.connectionState = 'disconnected';
    this.log('WebSocket connection closed manually');
  }

  /**
   * Get current connection state
   */
  getConnectionState() {
    return this.connectionState;
  }

  /**
   * Check if WebSocket is connected
   */
  isConnected() {
    return this.socket && this.socket.readyState === WebSocket.OPEN;
  }

  /**
   * Enable reconnection
   */
  enableReconnection() {
    this.shouldReconnect = true;
    this.reconnectAttempts = 0;
  }

  /**
   * Disable reconnection
   */
  disableReconnection() {
    this.shouldReconnect = false;
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  /**
   * Reset reconnection attempts
   */
  resetReconnectionAttempts() {
    this.reconnectAttempts = 0;
  }

  /**
   * Get connection statistics
   */
  getStats() {
    return {
      url: this.url,
      connectionState: this.connectionState,
      reconnectAttempts: this.reconnectAttempts,
      isReconnecting: this.isReconnecting,
      shouldReconnect: this.shouldReconnect,
      isConnected: this.isConnected(),
      readyState: this.socket ? this.socket.readyState : null
    };
  }

  /**
   * Log messages if logging is enabled
   */
  log(message) {
    if (this.options.enableLogging) {
      console.log(`[WebSocketManager] ${message}`);
    }
  }
}

export default WebSocketManager;
