/**
 * Settings Dialog Service - Manages AI configuration UI
 * 
 * This service handles the settings dialog for configuring the AI agent.
 */

import { aiAgentConfig } from './frontendAIAgent.js';
import refactoredAIDialogService from './aiDialogService.js';

class SettingsDialogService {
  constructor() {
    this.isVisible = false;
    this.dialog = null;
    this.form = null;
    this.statusElement = null;
    this.settingsBtn = null;
    this.closeBtn = null;
  }

  /**
   * Initialize the settings dialog service
   */
  initialize() {
    this.dialog = document.getElementById('settingsDialog');
    this.form = document.getElementById('aiSettingsForm');
    this.statusElement = document.getElementById('settingsStatus');
    this.settingsBtn = document.getElementById('settingsBtn');
    this.closeBtn = document.getElementById('settingsDialogClose');

    if (!this.dialog || !this.form || !this.statusElement || !this.settingsBtn || !this.closeBtn) {
      console.error('Settings dialog elements not found');
      return false;
    }

    this.setupEventListeners();
    this.loadCurrentSettings();
    
    console.log('Settings Dialog Service initialized');
    return true;
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Settings button click
    this.settingsBtn.addEventListener('click', () => this.show());
    
    // Close button click
    this.closeBtn.addEventListener('click', () => this.hide());
    
    // Dialog backdrop click
    this.dialog.addEventListener('click', (e) => {
      if (e.target === this.dialog) {
        this.hide();
      }
    });
    
    // Form submission
    this.form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.saveSettings();
    });
    
    // Test connection button
    document.getElementById('testConnection').addEventListener('click', () => {
      this.testConnection();
    });
    
    // Reset settings button
    document.getElementById('resetSettings').addEventListener('click', () => {
      this.resetSettings();
    });
    
    // Escape key to close dialog
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.isVisible) {
        this.hide();
      }
    });
  }

  /**
   * Load current settings into the form
   */
  loadCurrentSettings() {
    const config = aiAgentConfig.load();
    
    document.getElementById('apiKey').value = config.apiKey || '';
    document.getElementById('endpoint').value = config.endpoint || '';
    document.getElementById('model').value = config.model || '';
    document.getElementById('maxTokens').value = config.maxTokens || '';
    document.getElementById('temperature').value = config.temperature || '';
  }

  /**
   * Show the settings dialog
   */
  show() {
    this.isVisible = true;
    this.dialog.classList.remove('settings-dialog-hidden');
    this.loadCurrentSettings();
    this.clearStatus();
    
    // Focus on first input
    document.getElementById('apiKey').focus();
  }

  /**
   * Hide the settings dialog
   */
  hide() {
    this.isVisible = false;
    this.dialog.classList.add('settings-dialog-hidden');
    this.clearStatus();
  }

  /**
   * Save settings
   */
  async saveSettings() {
    try {
      const formData = new FormData(this.form);
      const config = {
        apiKey: formData.get('apiKey').trim(),
        endpoint: formData.get('endpoint').trim(),
        model: formData.get('model').trim(),
        maxTokens: parseInt(formData.get('maxTokens')) || 1000,
        temperature: parseFloat(formData.get('temperature')) || 0.7
      };

      // Configure the AI agent
      const result = await refactoredAIDialogService.configureAIAgent(config);
      
      if (result.success) {
        this.showStatus('Settings saved successfully!', 'success');
        setTimeout(() => {
          this.hide();
        }, 1500);
      } else {
        this.showStatus(`Error: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      this.showStatus(`Error saving settings: ${error.message}`, 'error');
    }
  }

  /**
   * Test connection
   */
  async testConnection() {
    try {
      this.showStatus('Testing connection...', 'info');
      
      // Get current form values
      const formData = new FormData(this.form);
      const config = {
        apiKey: formData.get('apiKey').trim(),
        endpoint: formData.get('endpoint').trim(),
        model: formData.get('model').trim(),
        maxTokens: parseInt(formData.get('maxTokens')) || 1000,
        temperature: parseFloat(formData.get('temperature')) || 0.7
      };

      // Temporarily configure the agent for testing
      await refactoredAIDialogService.configureAIAgent(config);

      // Test the connection
      const result = await refactoredAIDialogService.testAIAgentConnection();
      
      if (result.success) {
        this.showStatus('Connection test successful!', 'success');
      } else {
        this.showStatus(`Connection test failed: ${result.error}`, 'error');
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      this.showStatus(`Connection test failed: ${error.message}`, 'error');
    }
  }

  /**
   * Reset settings to defaults
   */
  resetSettings() {
    if (confirm('Are you sure you want to reset all settings to defaults?')) {
      const defaultConfig = aiAgentConfig.reset();
      
      document.getElementById('apiKey').value = defaultConfig.apiKey;
      document.getElementById('endpoint').value = defaultConfig.endpoint;
      document.getElementById('model').value = defaultConfig.model;
      document.getElementById('maxTokens').value = defaultConfig.maxTokens;
      document.getElementById('temperature').value = defaultConfig.temperature;
      
      this.showStatus('Settings reset to defaults', 'success');
    }
  }

  /**
   * Show status message
   */
  showStatus(message, type = 'info') {
    this.statusElement.textContent = message;
    this.statusElement.className = `settings-status ${type}`;
  }

  /**
   * Clear status message
   */
  clearStatus() {
    this.statusElement.textContent = '';
    this.statusElement.className = 'settings-status';
  }

  /**
   * Check if dialog is visible
   */
  isDialogVisible() {
    return this.isVisible;
  }
}

// Export singleton instance
export default new SettingsDialogService();
