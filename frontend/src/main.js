import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { WebLinksAddon } from 'xterm-addon-web-links';
import 'xterm/css/xterm.css';
import xtermService from './services/xtermService.js';
import aiDialogService from './services/aiDialogService.js';
import frontendAIAgent, { aiAgentConfig } from './services/frontendAIAgent.js';
import settingsDialogService from './services/settingsDialogService.js';
import WebSocketManager from './services/websocketManager.js';

// Session management
let sessionTimer = null;
let remainingSeconds = 0;
let isExtending = false;

// DOM Elements
const remainingTimeEl = document.getElementById('remainingTime');
const extendBtn = document.getElementById('extendBtn');

// Format time as HH:MM:SS
function formatTime(seconds) {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return [h, m, s].map(v => v.toString().padStart(2, '0')).join(':');
}

// Update the timer display
function updateTimerDisplay() {
  remainingTimeEl.textContent = formatTime(remainingSeconds);
  
  // Update styles based on remaining time
  remainingTimeEl.classList.remove('warning', 'danger');
  if (remainingSeconds <= 300) { // 5 minutes or less
    remainingTimeEl.classList.add('danger');
  } else if (remainingSeconds <= 1800) { // 30 minutes or less
    remainingTimeEl.classList.add('warning');
  }
}

// Start or update the session timer
function updateSessionTimer(seconds) {
  // Clear existing timer if any
  if (sessionTimer) {
    clearInterval(sessionTimer);
  }
  
  remainingSeconds = Math.max(0, seconds);
  updateTimerDisplay();
  
  // Update every second
  sessionTimer = setInterval(() => {
    remainingSeconds--;
    updateTimerDisplay();
    
    if (remainingSeconds <= 0) {
      clearInterval(sessionTimer);
      extendBtn.disabled = true;
      remainingTimeEl.textContent = 'Session expired';
    }
  }, 1000);
}

// Extend session by 1 hour
async function extendSession() {
  if (isExtending) {
    console.log('extendSession: Already extending, ignoring request');
    return;
  }
  
  console.log('extendSession: Starting session extension...');
  
  try {
    isExtending = true;
    extendBtn.disabled = true;
    extendBtn.textContent = 'Extending...';
    
    console.log('extendSession: Sending extension request to /api/extend?minutes=60');
    const startTime = performance.now();
    
    const response = await fetch('/api/extend?minutes=60', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    const requestTime = performance.now() - startTime;
    console.log(`extendSession: Received response in ${requestTime.toFixed(2)}ms`, response);
    
    let data;
    try {
      data = await response.json();
      console.log('extendSession: Response data:', data);
    } catch (parseError) {
      console.error('extendSession: Failed to parse JSON response:', parseError);
      throw new Error('Invalid response from server');
    }
    
    if (!response.ok) {
      const errorMsg = data.error || data.message || 'Failed to extend session';
      console.error('extendSession: API error:', errorMsg);
      throw new Error(errorMsg);
    }
    
    if (data.success) {
      // Calculate remaining seconds from the response
      const remaining = Math.max(0, data.remaining_seconds || 0);
      console.log(`extendSession: Extension successful. New remaining time: ${remaining} seconds`);
      
      // Update the UI with the new remaining time
      updateSessionTimer(remaining);
      
      // Show success message in terminal
      const minutes = Math.ceil(remaining / 60);
      term.writeln(`\r\n\x1b[32m✓ Session extended. New remaining time: ${minutes} minutes\x1b[0m`);
      term.write('\r\n$ ');
    } else {
      console.error('extendSession: Extension failed:', data.message || 'Unknown error');
      throw new Error(data.message || 'Failed to extend session');
    }
  } catch (error) {
    console.error('Error extending session:', error);
    term.writeln(`\r\n\x1b[31mError: ${error.message}\x1b[0m`);
    term.write('\r\n$ ');
  } finally {
    isExtending = false;
    extendBtn.disabled = false;
    extendBtn.textContent = '+1h';
  }
}

// Initialize session timer from server
async function initializeSessionTimer() {
  try {
    const response = await fetch('/api/status');
    if (response.ok) {
      const data = await response.json();
      if (data.remaining_seconds !== undefined) {
        updateSessionTimer(data.remaining_seconds);
      }
    }
  } catch (error) {
    console.error('Error initializing session timer:', error);
  }
}

// Import virtual keyboard service
import virtualKeyboardService from './services/virtualKeyboardService.js';
// WebSocket connection (declared at top level for keyboard access)
let socket = null;
let wsManager = null;

// Handle keyboard button press
function onKeyPress(keyData) {
  if (!term || !socket || socket.readyState !== WebSocket.OPEN) {
    console.log('Terminal or socket not ready for keyboard input');
    return;
  }

  let inputData = '';
  const { key, shift, ctrl, alt } = keyData;

  // Handle special keys
  switch (key) {
    case 'Enter':
      inputData = '\r';
      break;
    case 'Tab':
      inputData = '\t';
      break;
    case 'Backspace':
      inputData = '\x7f'; // DEL character for backspace
      break;
    case 'Delete':
      inputData = '\x1b[3~';
      break;
    case 'Esc':
      inputData = '\x1b';
      break;
    case 'Space':
      inputData = ' ';
      break;
    case 'ArrowLeft':
      inputData = '\x1b[D';
      break;
    case 'ArrowUp':
      inputData = '\x1b[A';
      break;
    case 'ArrowDown':
      inputData = '\x1b[B';
      break;
    case 'ArrowRight':
      inputData = '\x1b[C';
      break;
    case 'Home':
      inputData = '\x1b[H';
      break;
    case 'End':
      inputData = '\x1b[F';
      break;
    case 'PageUp':
      inputData = '\x1b[5~';
      break;
    case 'PageDown':
      inputData = '\x1b[6~';
      break;
    case 'Insert':
      inputData = '\x1b[2~';
      break;
    default:
      // Handle regular characters and key combinations
      if (ctrl && key.length === 1) {
        // Ctrl combinations
        const charCode = key.toLowerCase().charCodeAt(0);
        if (charCode >= 97 && charCode <= 122) { // a-z
          inputData = String.fromCharCode(charCode - 96); // Ctrl+A = 1, etc.
        }
      } else if (alt && key.length === 1) {
        // Alt combinations
        inputData = '\x1b' + key;
      } else {
        // Regular character
        inputData = key;
      }
  }

  // Send input through WebSocket like regular keyboard input
  if (inputData) {
    console.log('Sending virtual keyboard input:', inputData, 'from key:', keyData);
    if (wsManager && wsManager.isConnected()) {
      wsManager.send(JSON.stringify({
        type: 'input',
        data: inputData
      }));
    } else {
      console.warn('WebSocket not connected, cannot send virtual keyboard input');
    }
  }
}


// Get the DOM element where the terminal will be rendered
const terminalElement = document.getElementById('terminal');

// Create a new terminal instance
const term = new Terminal({
  fontFamily: 'Menlo, Monaco, "Courier New", monospace',
  fontSize: 14,
  lineHeight: 1.2,
  cursorBlink: true,
  cursorStyle: 'block',
  theme: {
    background: '#000000',
    foreground: '#f0f0f0',
    cursor: '#f0f0f0',
    selection: 'rgba(255, 255, 255, 0.3)',
  }
});

// Create addons
const fitAddon = new FitAddon();
const webLinksAddon = new WebLinksAddon();

// Load addons
term.loadAddon(fitAddon);
term.loadAddon(webLinksAddon);

// Open the terminal in the specified DOM element
term.open(terminalElement);

// Fit the terminal to the container size
fitAddon.fit();

// Initialize session timer when page loads
initializeSessionTimer();

// Initialize virtual keyboard after terminal is ready
function initializeVirtualKeyboard() {
  console.log('Initializing virtual keyboard...');
  const keyboardBtn = document.getElementById('keyboardBtn');

  // Debug: check if elements are found
  console.log('keyboardBtn:', keyboardBtn);

  // Initialize virtual keyboard service
  const success = virtualKeyboardService.initialize('keyboardContainer', onKeyPress);

  if (!success) {
    console.error('Failed to initialize virtual keyboard service');
    return;
  }

  // Toggle virtual keyboard visibility
  function toggleKeyboard() {
    console.log('Toggle keyboard clicked, current state:', virtualKeyboardService.isKeyboardVisible());
    virtualKeyboardService.toggle();

    // Resize terminal to fit the remaining space
    if (fitAddon && fitAddon.fit) {
      setTimeout(() => fitAddon.fit(), 100);
    }
  }

  if (keyboardBtn) {
    keyboardBtn.addEventListener('click', toggleKeyboard);
  }
}

// Initialize components when DOM is ready
function initializeComponents() {
  initializeVirtualKeyboard();
  aiDialogService.initialize();
  settingsDialogService.initialize();

  // Extend button event listener
  if (extendBtn) {
    extendBtn.addEventListener('click', extendSession);
    console.log('Extend button event listener attached');
  } else {
    console.warn('Extend button not found');
  }

  // 调试按钮绑定
  const getAllContentBtn = document.getElementById('getAllContentBtn');
  if (getAllContentBtn) {
    getAllContentBtn.addEventListener('click', () => {
      if (window.xtermService && typeof window.xtermService.getAllContent === 'function') {
        const { cursorX, cursorY, lines } = window.xtermService.getAllContent();
        console.log('[getAllContentBtn] 全屏内容:', lines);
        console.log('[getAllContentBtn] 光标位置:', cursorX, cursorY);
      } else {
        console.warn('xtermService.getAllContent 不可用');
      }
    });
  }
}

if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeComponents);
} else {
  initializeComponents();
}

// Create WebSocket connection with auto-reconnection
const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
const wsUrl = `${protocol}//${window.location.host}/ws`;
console.log(`Connecting to WebSocket at: ${wsUrl}`);

// Create WebSocket manager with auto-reconnection
wsManager = new WebSocketManager(wsUrl, {
  maxReconnectAttempts: 10,
  reconnectInterval: 1000,
  maxReconnectInterval: 30000,
  reconnectDecay: 1.5,
  enableLogging: true
});

// Setup WebSocket event handlers
wsManager.onOpen = () => {
  socket = wsManager.socket; // Store reference for compatibility
  console.log('WebSocket connection established');
  term.writeln('Connected to terminal server');

  // Initialize XTermService
  xtermService.initialize(term, socket);

  // Expose services globally for testing and external access
  window.xtermService = xtermService;
  window.aiDialogService = aiDialogService;
  window.virtualKeyboardService = virtualKeyboardService;
  window.frontendAIAgent = frontendAIAgent;
  window.aiAgentConfig = aiAgentConfig;
  window.settingsDialogService = settingsDialogService;

  // Set up terminal resize event
  const onResize = () => {
    fitAddon.fit();
    const dimensions = { cols: term.cols, rows: term.rows };
    wsManager.send(JSON.stringify({
      type: 'resize',
      data: JSON.stringify(dimensions)
    }));
  };

  // Initial resize
  onResize();

  // Listen for window resize events
  window.addEventListener('resize', onResize);

  // Handle terminal input and send it to the server
  term.onData(data => {
    // console.log('Sending data to server:', data);
    wsManager.send(JSON.stringify({
      type: 'input',
      data: data
    }));
  });
};

// Handle WebSocket messages
wsManager.onMessage = (event) => {
  try {
    const message = JSON.parse(event.data);
    if (message.type === 'output') {
      console.log('Received output from server:', message.data);
      term.write(message.data);
    } else if (message.type === 'error') {
      term.writeln(`\r\n\x1b[31mError: ${message.data}\x1b[0m`);
    }
  } catch (error) {
    console.error('Failed to parse message:', error);
    term.writeln('\r\n\x1b[31mError: Failed to parse server message\x1b[0m');
  }
};

// Handle WebSocket connection close
wsManager.onClose = (event) => {
  console.log('WebSocket connection closed');
  if (!event.wasClean) {
    term.writeln('\r\n\x1b[33mConnection lost. Attempting to reconnect...\x1b[0m');
  } else {
    term.writeln('\r\n\x1b[33mConnection closed\x1b[0m');
  }
};

// Handle WebSocket errors
wsManager.onError = (error) => {
  console.error('WebSocket error:', error);
  term.writeln('\r\n\x1b[31mConnection error\x1b[0m');
};

// Handle reconnection attempts
wsManager.onReconnecting = (attempt, delay) => {
  term.writeln(`\r\n\x1b[33mReconnecting... (attempt ${attempt}, delay ${Math.round(delay/1000)}s)\x1b[0m`);
};

// Handle successful reconnection
wsManager.onReconnected = () => {
  term.writeln('\r\n\x1b[32m✓ Reconnected to terminal server\x1b[0m');
};

// Handle max reconnection attempts reached
wsManager.onMaxReconnectAttemptsReached = () => {
  term.writeln('\r\n\x1b[31m✗ Failed to reconnect after maximum attempts. Please refresh the page.\x1b[0m');
};

// Start the WebSocket connection
wsManager.connect();
