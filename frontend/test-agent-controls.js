/**
 * Test script for AI Agent pause, resume, and terminate functionality
 * 
 * This script can be run in the browser console to test the agent controls
 */

// Test functions for AI Agent controls
window.testAgentControls = {
  
  /**
   * Test pause functionality
   */
  testPause() {
    console.log('=== Testing Pause Functionality ===');
    
    if (!window.aiDialogService) {
      console.error('AI Dialog Service not available');
      return false;
    }
    
    const controlState = window.aiDialogService.getAgentControlState();
    console.log('Current agent state:', controlState);
    
    if (!controlState.isReady) {
      console.warn('Agent is not ready. Please configure the AI agent first.');
      return false;
    }
    
    if (controlState.isPaused) {
      console.log('Agent is already paused');
      return true;
    }
    
    // Pause the agent
    const result = window.aiDialogService.pauseAgent();
    console.log('Pause result:', result);
    
    // Check state after pause
    const newState = window.aiDialogService.getAgentControlState();
    console.log('State after pause:', newState);
    
    return newState.isPaused;
  },
  
  /**
   * Test resume functionality
   */
  testResume() {
    console.log('=== Testing Resume Functionality ===');
    
    if (!window.aiDialogService) {
      console.error('AI Dialog Service not available');
      return false;
    }
    
    const controlState = window.aiDialogService.getAgentControlState();
    console.log('Current agent state:', controlState);
    
    if (!controlState.isPaused) {
      console.log('Agent is not paused');
      return true;
    }
    
    // Resume the agent
    const result = window.aiDialogService.resumeAgent();
    console.log('Resume result:', result);
    
    // Check state after resume
    const newState = window.aiDialogService.getAgentControlState();
    console.log('State after resume:', newState);
    
    return !newState.isPaused;
  },
  
  /**
   * Test terminate functionality
   */
  testTerminate() {
    console.log('=== Testing Terminate Functionality ===');
    
    if (!window.aiDialogService) {
      console.error('AI Dialog Service not available');
      return false;
    }
    
    const controlState = window.aiDialogService.getAgentControlState();
    console.log('Current agent state:', controlState);
    
    // Terminate the agent
    const result = window.aiDialogService.terminateAgent();
    console.log('Terminate result:', result);
    
    // Check state after terminate
    const newState = window.aiDialogService.getAgentControlState();
    console.log('State after terminate:', newState);
    
    return newState.isTerminated;
  },
  
  /**
   * Test UI button states
   */
  testUIButtons() {
    console.log('=== Testing UI Button States ===');
    
    const pauseResumeBtn = document.getElementById('aiPauseResume');
    const sendBtn = document.getElementById('aiSend');
    const pauseIcon = document.getElementById('pauseIcon');
    const playIcon = document.getElementById('playIcon');
    const sendIcon = document.getElementById('sendIcon');
    const stopIcon = document.getElementById('stopIcon');
    
    console.log('UI Elements found:', {
      pauseResumeBtn: !!pauseResumeBtn,
      sendBtn: !!sendBtn,
      pauseIcon: !!pauseIcon,
      playIcon: !!playIcon,
      sendIcon: !!sendIcon,
      stopIcon: !!stopIcon
    });
    
    if (pauseResumeBtn) {
      console.log('Pause/Resume button classes:', pauseResumeBtn.className);
      console.log('Pause/Resume button disabled:', pauseResumeBtn.disabled);
    }
    
    if (sendBtn) {
      console.log('Send button classes:', sendBtn.className);
      console.log('Send button disabled:', sendBtn.disabled);
    }
    
    return true;
  },
  
  /**
   * Test button transformations
   */
  testButtonTransformations() {
    console.log('=== Testing Button Transformations ===');
    
    if (!window.aiDialogService) {
      console.error('AI Dialog Service not available');
      return false;
    }
    
    // Test send button to terminate transformation
    console.log('Transforming send button to terminate...');
    window.aiDialogService.setSendButtonToTerminate();
    
    setTimeout(() => {
      console.log('Send button is in terminate mode:', window.aiDialogService.isSendButtonInTerminateMode());
      
      // Restore send button
      console.log('Restoring send button...');
      window.aiDialogService.setSendButtonToSend();
      
      setTimeout(() => {
        console.log('Send button is in terminate mode:', window.aiDialogService.isSendButtonInTerminateMode());
      }, 100);
    }, 100);
    
    return true;
  },
  
  /**
   * Run all tests
   */
  runAllTests() {
    console.log('🚀 Starting AI Agent Controls Tests...\n');
    
    const tests = [
      { name: 'UI Buttons', fn: this.testUIButtons },
      { name: 'Button Transformations', fn: this.testButtonTransformations },
      { name: 'Pause', fn: this.testPause },
      { name: 'Resume', fn: this.testResume },
      { name: 'Terminate', fn: this.testTerminate }
    ];
    
    const results = {};
    
    tests.forEach((test, index) => {
      try {
        console.log(`\n${index + 1}. Running ${test.name} test...`);
        results[test.name] = test.fn.call(this);
        console.log(`✅ ${test.name} test completed`);
      } catch (error) {
        console.error(`❌ ${test.name} test failed:`, error);
        results[test.name] = false;
      }
    });
    
    console.log('\n📊 Test Results Summary:');
    Object.entries(results).forEach(([name, result]) => {
      console.log(`${result ? '✅' : '❌'} ${name}: ${result ? 'PASSED' : 'FAILED'}`);
    });
    
    const passedTests = Object.values(results).filter(r => r).length;
    const totalTests = Object.keys(results).length;
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
    
    return results;
  }
};

// Auto-run tests when script is loaded (optional)
console.log('AI Agent Controls Test Suite loaded. Run testAgentControls.runAllTests() to start testing.');
