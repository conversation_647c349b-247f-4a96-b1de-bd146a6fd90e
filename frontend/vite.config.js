import { defineConfig } from 'vite';

export default defineConfig({
  server: {
    proxy: {
      // Proxy WebSocket connections to the backend during development
      '/ws': {
        target: 'ws://localhost:8082',
        ws: true,
      },
      // Proxy API calls to the backend during development
      '/api': {
        target: 'http://localhost:8082',
        changeOrigin: true,
      }
    }
  }
});
