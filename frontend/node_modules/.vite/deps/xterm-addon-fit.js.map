{"version": 3, "sources": ["../../xterm-addon-fit/lib/webpack:/FitAddon/webpack/universalModuleDefinition", "../../xterm-addon-fit/lib/webpack:/FitAddon/src/FitAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"FitAddon\"] = factory();\n\telse\n\t\troot[\"FitAddon\"] = factory();\n})(self, () => {\nreturn ", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Terminal, ITerminalAddon } from 'xterm';\nimport { IRenderDimensions } from 'browser/renderer/shared/Types';\n\ninterface ITerminalDimensions {\n  /**\n   * The number of rows in the terminal.\n   */\n  rows: number;\n\n  /**\n   * The number of columns in the terminal.\n   */\n  cols: number;\n}\n\nconst MINIMUM_COLS = 2;\nconst MINIMUM_ROWS = 1;\n\nexport class FitAddon implements ITerminalAddon {\n  private _terminal: Terminal | undefined;\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n  }\n\n  public dispose(): void {}\n\n  public fit(): void {\n    const dims = this.proposeDimensions();\n    if (!dims || !this._terminal || isNaN(dims.cols) || isNaN(dims.rows)) {\n      return;\n    }\n\n    // TODO: Remove reliance on private API\n    const core = (this._terminal as any)._core;\n\n    // Force a full render\n    if (this._terminal.rows !== dims.rows || this._terminal.cols !== dims.cols) {\n      core._renderService.clear();\n      this._terminal.resize(dims.cols, dims.rows);\n    }\n  }\n\n  public proposeDimensions(): ITerminalDimensions | undefined {\n    if (!this._terminal) {\n      return undefined;\n    }\n\n    if (!this._terminal.element || !this._terminal.element.parentElement) {\n      return undefined;\n    }\n\n    // TODO: Remove reliance on private API\n    const core = (this._terminal as any)._core;\n    const dims: IRenderDimensions = core._renderService.dimensions;\n\n    if (dims.css.cell.width === 0 || dims.css.cell.height === 0) {\n      return undefined;\n    }\n\n    const scrollbarWidth = this._terminal.options.scrollback === 0 ?\n      0 : core.viewport.scrollBarWidth;\n\n    const parentElementStyle = window.getComputedStyle(this._terminal.element.parentElement);\n    const parentElementHeight = parseInt(parentElementStyle.getPropertyValue('height'));\n    const parentElementWidth = Math.max(0, parseInt(parentElementStyle.getPropertyValue('width')));\n    const elementStyle = window.getComputedStyle(this._terminal.element);\n    const elementPadding = {\n      top: parseInt(elementStyle.getPropertyValue('padding-top')),\n      bottom: parseInt(elementStyle.getPropertyValue('padding-bottom')),\n      right: parseInt(elementStyle.getPropertyValue('padding-right')),\n      left: parseInt(elementStyle.getPropertyValue('padding-left'))\n    };\n    const elementPaddingVer = elementPadding.top + elementPadding.bottom;\n    const elementPaddingHor = elementPadding.right + elementPadding.left;\n    const availableHeight = parentElementHeight - elementPaddingVer;\n    const availableWidth = parentElementWidth - elementPaddingHor - scrollbarWidth;\n    const geometry = {\n      cols: Math.max(MINIMUM_COLS, Math.floor(availableWidth / dims.css.cell.width)),\n      rows: Math.max(MINIMUM_ROWS, Math.floor(availableHeight / dims.css.cell.height))\n    };\n    return geometry;\n  }\n}\n"], "mappings": ";;;;;;;KAAA,SAA2CA,GAAMC,GAAAA;AAC1B,kBAAA,OAAZC,WAA0C,YAAA,OAAXC,SACxCA,OAAOD,UAAUD,EAAAA,IACQ,cAAA,OAAXG,UAAyBA,OAAOC,MAC9CD,OAAO,CAAA,GAAIH,CAAAA,IACe,YAAA,OAAZC,UACdA,QAAkB,WAAID,EAAAA,IAEtBD,EAAe,WAAIC,EAAAA;IACpB,EAAEK,MAAM,OAAA,MAAA;AAAA;AAAA,UAAA,IAAA,CAAA;AAAA,cAAA,MAAA;AAAA,YAAA,IAAA;AAAA,eAAA,eAAA,GAAA,cAAA,EAAA,OAAA,KAAA,CAAA,GAAA,EAAA,WAAA,QCcT,EAAA,WAAA,MAAA;UAGS,SAASC,IAAAA;AACdC,iBAAKC,YAAYF;UACnB;UAEO,UAAAG;UAAiB;UAEjB,MAAAC;AACL,kBAAMC,KAAOJ,KAAKK,kBAAAA;AAClB,gBAAA,CAAKD,MAAAA,CAASJ,KAAKC,aAAaK,MAAMF,GAAKG,IAAAA,KAASD,MAAMF,GAAKI,IAAAA,EAC7D;AAIF,kBAAMC,KAAQT,KAAKC,UAAkBS;AAGjCV,iBAAKC,UAAUO,SAASJ,GAAKI,QAAQR,KAAKC,UAAUM,SAASH,GAAKG,SACpEE,GAAKE,eAAeC,MAAAA,GACpBZ,KAAKC,UAAUY,OAAOT,GAAKG,MAAMH,GAAKI,IAAAA;UAE1C;UAEO,oBAAAH;AACL,gBAAA,CAAKL,KAAKC,UACR;AAGF,gBAAA,CAAKD,KAAKC,UAAUa,WAAAA,CAAYd,KAAKC,UAAUa,QAAQC,cACrD;AAIF,kBAAMN,KAAQT,KAAKC,UAAkBS,OAC/BN,KAA0BK,GAAKE,eAAeK;AAEpD,gBAA4B,MAAxBZ,GAAKa,IAAIC,KAAKC,SAAwC,MAAzBf,GAAKa,IAAIC,KAAKE,OAC7C;AAGF,kBAAMC,IAAuD,MAAtCrB,KAAKC,UAAUqB,QAAQC,aAC5C,IAAId,GAAKe,SAASC,gBAEdC,IAAqBC,OAAOC,iBAAiB5B,KAAKC,UAAUa,QAAQC,aAAAA,GACpEc,IAAsBC,SAASJ,EAAmBK,iBAAiB,QAAA,CAAA,GACnEC,IAAqBC,KAAKC,IAAI,GAAGJ,SAASJ,EAAmBK,iBAAiB,OAAA,CAAA,CAAA,GAC9EI,IAAeR,OAAOC,iBAAiB5B,KAAKC,UAAUa,OAAAA,GAStDsB,IAAkBP,KAPjBC,SAASK,EAAaJ,iBAAiB,aAAA,CAAA,IACpCD,SAASK,EAAaJ,iBAAiB,gBAAA,CAAA,IAO3CM,IAAiBL,KANdF,SAASK,EAAaJ,iBAAiB,eAAA,CAAA,IACxCD,SAASK,EAAaJ,iBAAiB,cAAA,CAAA,KAKiBV;AAKhE,mBAJiB,EACfd,MAAM0B,KAAKC,IA/DI,GA+DcD,KAAKK,MAAMD,IAAiBjC,GAAKa,IAAIC,KAAKC,KAAAA,CAAAA,GACvEX,MAAMyB,KAAKC,IA/DI,GA+DcD,KAAKK,MAAMF,IAAkBhC,GAAKa,IAAIC,KAAKE,MAAAA,CAAAA,EAAAA;UAG5E;QAAA;MAAA,GAAA,GAAA;IAAA,GAAA,CAAA;;;", "names": ["root", "factory", "exports", "module", "define", "amd", "self", "terminal", "this", "_terminal", "dispose", "fit", "dims", "proposeDimensions", "isNaN", "cols", "rows", "core", "_core", "_renderService", "clear", "resize", "element", "parentElement", "dimensions", "css", "cell", "width", "height", "scrollbarWidth", "options", "scrollback", "viewport", "scrollBarWidth", "parentElementStyle", "window", "getComputedStyle", "parentElementHeight", "parseInt", "getPropertyValue", "parent<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "max", "elementStyle", "availableHeight", "availableWidth", "floor"]}